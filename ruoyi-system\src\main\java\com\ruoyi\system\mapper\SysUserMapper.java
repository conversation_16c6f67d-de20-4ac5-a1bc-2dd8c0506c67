package com.ruoyi.system.mapper;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.customer.domain.Customer;
import com.ruoyi.customer.domain.TCustomerAssist;
import com.ruoyi.hr.domain.SysHrUser;
import com.ruoyi.project.domain.TProjectExecutionUser;
import com.ruoyi.system.domain.SysActTaskNodeAssignee;
import com.ruoyi.system.domain.excel.UserApplyExcel;
import com.ruoyi.work.domain.UserParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户表 数据层
 *
 * <AUTHOR>
 */
public interface SysUserMapper
{

    public List<SysUser> selectUserList(SysUser sysUser);

    /**
     * 根据条件分页查询未已配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectAllocatedList(SysUser user);

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUnallocatedList(SysUser user);

    public SysUser selectUserByUserName(String userName);

    SysUser selectUserByNickName(String nickName);

    public Long selectUserIdByNikeName(String nickName);

    public SysUser selectUserById(Long userId);

    public SysUser selectUserLiteById(Long userId);

    public int insertUser(SysUser user);

    public int updateUser(SysUser user);

    public int updateUserAvatar(@Param("userName") String userName, @Param("avatar") String avatar);

    public int resetUserPwd(@Param("userName") String userName, @Param("password") String password);

    public int deleteUserByIds(Long[] userIds);

    public int checkUserCodeUnique(String userCode);

    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    public int checkUserNameUnique(String userName);

    /**
     * 校验手机号码是否唯一
     *
     * @param phonenumber 手机号码
     * @return 结果
     */
    public SysUser checkPhoneUnique(String phonenumber);

    /**
     * 校验email是否唯一
     *
     * @param email 用户邮箱
     * @return 结果
     */
    public SysUser checkEmailUnique(String email);

    String selectUserNamesByUserIds(@Param("userIds") Set<Long> userIds);

    String selectNickNamesByUserIds(@Param("userIds") Set<Long> userIds);

    String selectUserNamesByRoleIds(@Param("roleIds") Set<Long> roleIds);

    String selectUserNamesByDeptId(@Param("deptId")Long deptId);

    String selectUserNamesByPostIds(@Param("postIds")Set<Long> postIds);

    List<SysActTaskNodeAssignee> selectAssigneeByUserIds(@Param("userIds") Set<Long> userIds);

    List<SysActTaskNodeAssignee> selectAssigneeByRoleIds(@Param("roleIds") Set<String> roleIds);

    List<SysActTaskNodeAssignee> selectAssigneeByDeptId(@Param("deptId")Long deptId);

    List<SysActTaskNodeAssignee> selectAssigneeByPostIds(@Param("postIds")Set<Long> postIds);

    List<String> selectUserRoleByRoleCodes(Long userId);
    List<String> selectUserRoleByRoleCodesByType(@Param("userId") Long userId,@Param("typeIds") List<Long> typeIds);
    /**
     * 查询直接上级用户ID
     * @param userId
     * @return
     */
    Integer queryDirectSuperiorInfo(Long userId);

    /**
     * 部门总监/主管
     * @param userParams
     * @return
     */
    List<Long> queryDepartmentDirectorInfo(Map<String,Object> userParams);

    /**
     * 人事总监/总经理
     * @param userParams
     * @return
     */
    Integer queryHRDirectorInfo(Map<String,Object> userParams);

    /**
     * 获取部门ID
     * @param userParams
     * @return
     */
    Map<String,Object> queryUserParentDeptInfo(Map<String, Object> userParams);

    String selectPasswordByUserName(@Param("userName") String userName);

    List<SysUser> selectAllUserList(SysUser user);

    List<SysUser> queryCustomerUserList(Customer tCustomer);

    List<SysUser> queryCustomerAllOrderUser(Customer tCustomer);

    List<Long> queryDepartmentDirectorInfoByAncestors(Map<String, Object> userParams);

    List<SysUser> selectLowerList(@Param("userId") Long userId);

    List<Long> queryCustomerAssistUserList(Long customerId);

    List<Long> queryUserIdList(List<String> phonenumber);

    List<TProjectExecutionUser> queryProjectExecutionDataList(List<Long> userIdList);

    List<Long> queryMyExecutionDataList(Long userId);

    void batchExecutionUserDataList(List<TProjectExecutionUser> insertExecutionUserDataList);

    List<SysUser> selectAllProcessUserList(@Param("allUserId") Set<Long> allUserId);

    List<SysUser> selectIncludeGroupNameList(SysUser user);

    SysUser selectUserByAssist(TCustomerAssist assistParams);

    List<String> queryUserPostDataList(Long userId);

    Set<Long> selectUserIdByRoleNames(@Param("roleNames") Set<String> roleNames);

    List<SysUser> selectUserAll(SysUser sysUser);

    List<SysUser> selectUserTreeAll(SysUser sysUser);

    List<Long> selectUserDeptIds(SysUser sysUser);

    List<SysUser> selectAllProcessUserRoleList();

    List<UserApplyExcel> selectUserApplyListNew();

    List<SysUser> selectUserBaseList(SysUser sysUser);

    List<SysUser> selectUserBaseListNew(SysUser sysUser);

    Set<Long> selectUserIdsByDeptId(@Param("deptId") Long deptId);

    Set<String> selectWorkUserIdsByDeptId(@Param("deptId") Long deptId);


    List<SysUser> selectUserListByUserIds(@Param("userIds")Set<Long> userIds);

    String queryUserDataInfo(@Param("userIds") Set<Long> idList);
    /**
     * 查询今日离职用户
     */
    List<SysUser> selectLeaveUserDay();

    void updateUserPayslipPwd(SysHrUser sysHrUser);

    void updateAttendanceDeptInfo();

    Integer queryUserRoleCount(Long userId);

    List<SysUser> selectWorkUserList(SysUser sysUser);

    SysUser selectWorkUserByUserId(@Param("userId") Long userId);

    Long queryUserParentDeptIdInfo(Long userId);

    JSONObject queryUserParentDeptIdInfoNew(Long userId);

    List<UserParams> selectAsyncWorkUserList();

    Set<String> selectUserIdByNickNames(@Param("nickNames") Set<String> nickNames);

    List<JSONObject> queryResignataionsDataList();

    void updateUserResignataionsDataInfo(Long userId);

    SysUser selectUserByMobileNo(@Param("mobileNo") String mobileNo);

    void updateUserWorkUserInfo(SysUser sysUser);

    List<JSONObject> queryExpenseDataList();

    Integer queryAttendanceExceptionCount(Map<String,Object> params);

    SysUser selectUserByPhoneNumber(@Param("phoneNumber") String phoneNumber);

    SysUser selectUserByEntryUserId(@Param("entryUserId") Long entryUserId);

    List<Long> queryFirstDeptUserInfo(Long deptId);

    /**
     * 在职且有企业微信账号的,可接收消息的
     */
    List<SysUser> selectIncumbencyList(SysUser user);

    void updateUserDDuserIdInfo();

    List<String> selectPermsByUserId(@Param("userId") Long userId);

    List<String> getWorkUserIdByRoleKey(@Param("roleKey") String roleKey);

    List<SysUser> selectUserByUserCodes(@Param("userCodes") Set<String> userCodes);

    SysUser selectUserByUserCode(@Param("userCode")String userCode);

    /**
     * 根据产品类别id，获取对应研发可用的用户列表
     * @param categoryId 产品类别id
     * @return 可用工程师列表
     */
    List<SysUser> selectEngineersByCategoryId(@Param("categoryId")Long categoryId,
                                              @Param("projectOrderId") Long projectOrderId);

    /**
     * 根据部门，获取对应的用户列表
     * @param DeptId 部门ID
     * @return 用户列表
     */
    List<SysUser> selectUsersByDeptId(@Param("DeptId")Long DeptId);

}
