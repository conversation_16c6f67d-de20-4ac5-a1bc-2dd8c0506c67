package com.ruoyi.software.service;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.software.domain.EngineerWorkRecord;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 工程师工作记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface IEngineerWorkRecordService 
{

    public void insertEngineerWorkRecord(EngineerWorkRecord engineerWorkRecord);

    public void updateEngineerWorkRecord(EngineerWorkRecord engineerWorkRecord);

    /**
     * 根据难度等级和品类ID获取对应的工程师列表
     *  如果对应等级没有可用工程师，就查询更高等级的工程师
     *  如果没有更高等级的工程师，就将本组别内的最高等级的工程师返回
     *
     * @param difficultyLevelId 难度等级
     * @param categoryId 品类ID
     * @param projectOrderId 项目ID
     * @return 工程师列表
     */
    public List<SysUser> getEngineersByDifficultyLevel(String difficultyLevelId, Long categoryId, Long projectOrderId);

    /**
     * 扣除工程师指定日期的工时
     * @param userId 工程师ID
     * @param workDate 工作日期
     * @param hours 要扣除的工时
     * @return 是否扣除成功
     */
    public boolean deductWorkHours(Long userId, String workDate, BigDecimal hours);

    /**
     * 恢复工程师指定日期的工时
     * @param userId 工程师ID
     * @param workDate 工作日期
     * @param hours 要恢复的工时
     * @return 是否恢复成功
     */
    public boolean restoreWorkHours(Long userId, String workDate, BigDecimal hours);

    /**
     * 查询工程师从指定日期开始的最近可用工时日期
     * @param userId 工程师ID
     * @param startDate 开始日期
     * @param requiredHours 需要的工时
     * @param range 时间窗口
     * @return 可用的工作日期
     */
    public Date findNextAvailableWorkDate(Long userId, String startDate, BigDecimal requiredHours, int range);

    /**
     * 获取指定工程师在指定天数范围内的工作时间（日期、可用工时，剩余工时）
     *
     * @param userId 工程师ID
     * @param days   天数范围
     * @return 工程师工作记录列表
     */
    List<EngineerWorkRecord> getEngineerWorkTime(Long userId, int days);

    /**
     * 获取指定工程的最近一周的工时情况
     * @param userId 工程师
     * @return
     */
    public Map<String, Object> weeklyByUserId(Long userId);

    /**
     * 获取工程师协助人
     * 1. 打样单的难度高于当前工程师的职级
     * 2. 当前用户所在的组别下is_engineer字段为1的用户工程师作为协助人，
     *
     * @param userId 用户ID
     * @param categoryId 产品类别ID
     * @param difficultyLevelId 难度等级
     * @return 协助人用户信息，如果没有符合条件的协助人则返回null
     */
    public SysUser getEngineerAssistant(Long userId, Long categoryId, String difficultyLevelId);

}
