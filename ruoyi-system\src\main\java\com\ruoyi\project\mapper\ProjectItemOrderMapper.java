package com.ruoyi.project.mapper;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.project.domain.*;
import com.ruoyi.project.domain.excel.ProjectItemOrderExcel;
import com.ruoyi.project.domain.excel.ProjectSjbaDataExport;
import com.ruoyi.project.vo.ProjectOfferOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 子项目订单Mapper接口
 *
 * <AUTHOR>
 * @date 2021-09-23
 */
public interface ProjectItemOrderMapper {

    public ProjectItemOrder selectProjectItemOrderById(Long id);
    public ProjectItemOrder selectProjectItemOrderPackagingMaterialsById(Long id);

    public List<ProjectItemOrder> selectProjectItemOrderList(ProjectItemOrder projectItemOrder);

    public List<ProjectItemOrder> selectProjectItemOrderListBySamplePrice(ProjectItemOrder projectItemOrder);

    public List<ProjectItemOrder> selectProjectItemOrderReleaseList(ProjectItemOrder projectItemOrder);
    public List<JSONObject> selectProjectItemOrderFeeList(ProjectItemOrder projectItemOrder);

    public int insertProjectItemOrder(ProjectItemOrder projectItemOrder);

    public int updateProjectItemOrder(ProjectItemOrder projectItemOrder);
    public int updateProjectItemOrderOfferRevoke(ProjectItemOrder projectItemOrder);

    public int deleteProjectItemOrderByIds(Long[] ids);

    Integer queryProjectItemOrderCount(ProjectItemOrder projectItemOrder);

    Integer selectItemIsComplete(Long id);

    List<ProjectItemOrder> queryProjectItemOrderDataInfo(ProjectItem projectItem);

    List<Map<String, Object>> getProjectHistroyItemBom(Long id);

    void updateProjectItemOrderStatus(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> projectItemOfferDataList(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> projectItemBomDataList(ProjectItemOrder projectItemOrder);

    List<ProjectRemind> selectOrderRemindList();

    Project queryProjectInfo(Long projectOrderId);

    List<Map<String, Object>> queryNrwArrangementDataList(ProjectItemOrder projectItemOrder);

    Integer selectProjectOrderConfirmCodeNum(@Param("confirmCode") String confirmCode,@Param("customerId") Long customerId);

    void batchInsertConfirmCodeInfo(List<Map<String, Object>> dataList);

    void batchDeleteConfirmCodeInfo(TProjectExecution tProjectExecution);

    Integer selectNrwCount(@Param("itemId") Long itemId);

    List<ProjectItemOrder> projectItemOrderConfirmCodeByItemId(ProjectItemOrder projectItemOrder);

    ProjectItemOrder projectItemOrderInfoBySysbm(ProjectItemOrder projectItemOrder);

    int updateProjectItemOrderZdy(ProjectItemOrder projectItemOrder);

    List<ProjectItemOrderExcel> selectProjectItemOrderExcelList(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> queryProjectItemOrderExecutionDataList(List<Long> orderIdList);

    List<Map<String, Object>> queryProjectItemOrderFeedbackDataList(List<Long> orderIdList);

    void updateProjectExecutionStatus(ProjectItemOrder projectItemOrder);

    List<ProjectItemOrder> selectShipmentsOrder(@Param("ids") List<Long> ids);

    List<Map<String, Object>> prjectItemOrderOfferNrwPriceInfo(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> projectItemOrderBcBomDataList(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> projectItemOfferDataListNew(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> projectItemOfferDataListFinidshNew(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> projectItemOfferBcDataList(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> projectItemOfferDataListArrNew(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> projectItemOrderSckxxGwtDataList(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> projectItemOfferJgfDataList(ProjectItemOrder projectItemOrder);

    List<ProjectItemOrder> queryProjectItemOrderDataList(@Param("itemType") String itemType);

    ProjectItemOrderFeedback selectProjectItemOrderFeedInfo(ProjectItemOrder projectItemOrder);

    int updateProjectItemOrderRevoke(ProjectItemOrder projectItemOrder);
    int updateProjectItemOrderRevokeNrw(ProjectItemOrder projectItemOrder);

    ProjectItemOrder queryProjectItemOrderFeedData(Long id);

    Integer selectProjectOrderCustomerConfirmCodeNum(@Param("confirmCode") String confirmCode, @Param("customerId") Long customerId);

    List<ProjectItemOrder> selectBaOrderByLabCode(@Param("labCode") String labCode);

    List<ProjectItemOrder> selectProjectItemOrderLiteList(ProjectItemOrder projectItemOrder);

    void updateProjectItemOrderMergeOrderFields(ProjectItemOrder projectItemOrderInfo);

    List<ProjectItemOrder> queryProjectMergeItemOrderDataList(String sjbaApply);

    Integer queryProjectSjbaOrderCount();

    List<ProjectSjbaDataExport> queryProjectSjbaOrderDataList(@Param("start") int start, @Param("size") int size);

    void batchInsertProjectSjbaData(@Param("dataList") List<ProjectSjbaDataExport> dataList);

    void deleteProjectSjbaData(@Param("idList") List<Long> orderIdList);

    void updateProjectItemOrderSync(ProjectSjbaDataExport projectSjbaDataExport);

    //获取送检备案复审阶段数据
    List<JSONObject> queryProjectSjbaSecondOrderDataList();

    //获取已提交的送检信息
    List<TProjectExecution> queryProjectItemSjOrderDataList(Long projectId);

    //获取已提交的送检-工艺部信息
    List<TProjectExecution> queryProjectItemGongyiOrderDataList(Long projectId);

    List<ProjectItemOrder> selectLabCodes(ProjectItemOrder projectItemOrder);

    void updateProjectItemOrderApplyInfo(Map<String, Object> params);

    void updateProjectItemOrderConfirmCode(ProjectItemOrder finishedProjectItemOrder);

    TProjectExecution queryProjectItemOrderExecutionDataInfo(Map<String,Object> params);

    List<ProjectItemOrder> queryProjectItemOrderOfferDataList(ProjectItemOrder projectItemOrder);

    TProjectExecution queryProjectItemOrderExecutionOtherDataInfo(Map<String, Object> params);

    List<ProjectItemOrder> selectBcDevProjectOrderList(ProjectItemOrder projectItemOrder);

    //获取报价订单对应配方数据
    List<JSONObject> queryProjectItemOrderFormulaDataList(Map<String,Object> params);

    TProjectExecution queryProjectItemOrderExecutionList(Long id);

    TProjectExecution queryProjectItemOrderExecutionByMergeList(Map<String, Object> queryParams);

    List<ProjectOfferOrder> queryProjectOfferFormulaDataList(Long id);
    List<ProjectOfferOrder> queryProjectOfferChangeRecordDataList(Long id);
    List<ProjectOfferOrder> queryMultiProjectOfferChangeRecordDataList(ProjectOfferOrder params);
    JSONObject queryProjectOfferFormulaDataDetailById(Long id);
    List<ProjectOfferOrder> queryProjectOfferFormulaDataDetailsList(Long id);
    List<ProjectOfferOrder> queryMultiProjectOfferFormulaDetailsDataList(ProjectOfferOrder params);

    List<ProjectItemOrder> selectSopProjectOrderList(ProjectItemOrder projectItemOrder);

    List<ProjectBc> queryProjectOfferPackagingMaterialDataList(Long id);

    void updateProjectItemOrderPackagingDatas(ProjectItemOrder projectItemOrder);

    List<ProjectItemOrder> selectConfirmCodeOrderList(@Param("projectId") Long projectId);
    //获取包材报价详情信息
    ProjectOfferOrder queryProjectOfferPackagingMaterialDataObj(ProjectOfferOrder projectOfferOrder);
    //获取多产品信息
    ProjectItemOrder selectProjectItemOrderProductTabsById(Long projectOrderId);

    void updateProjectOfferOrderPackagingDatas(ProjectItemOrder projectItemOrder);

    //更新报价记录数据
    void updateProjectOfferOrderDataInfo(JSONObject obj);

    //保存下载记录
    void batchProjectOfferDownloadLogData(Map<String,Object> params);

    void updateProcessProjectNrwOrder(TProjectExecution tProjectExecution);

    Integer queryProjectItemOrderApplyCount(ProjectItemOrder projectItemOrder);

    void deleteProjectItemOrderPriceData(Long projectOrderId);

    void batchInsertProjectItemOrderPriceData(@Param("list") List<JSONObject> dataList);

    List<ProjectItemOrder> queryProjectItemOrderNrwOffer();

    //获取内容报价
    JSONObject queryProjectOfferNrwHistoryDataInfo(String labNo);

    List<ProjectItemOrder> queryProjectItemOrderInfo();

    void updateProjectItemOrderDownload(Long itemId);

    Integer selectProjectDyNumsByProjectId(@Param("projectId") Long projectId);

    Integer selectProjectProductDyNums(ProjectProduct projectProduct);

    //获取排单信息
    JSONObject queryAutoScheduleSampleOrder(Long projectOrderId);
    JSONObject queryAutoScheduleSampleOrderSoftware(Long projectOrderId);
    JSONObject queryAutoScheduleSampleBeforeOrder(Long projectOrderId);

    Integer queryProjectItemOrderAutoApplyCount(Long projectOrderId);

    /**
     * 更新项目订单样品报价
     * @param projectItemOrderId 项目订单ID
     * @param samplePrice 样品报价
     * @return 更新结果
     */
    int updateProjectItemOrderSamplePrice(@Param("projectItemOrderId") Long projectItemOrderId, @Param("samplePrice") String samplePrice);

    List<JSONObject> queryAutoEngineerDataList();

}
