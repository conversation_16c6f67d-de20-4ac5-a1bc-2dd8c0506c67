package com.ruoyi.software.mapper;

import com.ruoyi.software.domain.SampleOrderBatch;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 打样批次记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface SampleOrderBatchMapper
{
    /**
     * 查询打样批次记录
     *
     * @param id 打样批次记录主键
     * @return 打样批次记录
     */
    public SampleOrderBatch selectSampleOrderBatchById(Long id);

    /**
     * 查询打样批次记录列表
     *
     * @param sampleOrderBatch 打样批次记录
     * @return 打样批次记录集合
     */
    public List<SampleOrderBatch> selectSampleOrderBatchList(SampleOrderBatch sampleOrderBatch);

    /**
     * 新增打样批次记录
     *
     * @param sampleOrderBatch 打样批次记录
     * @return 结果
     */
    public int insertSampleOrderBatch(SampleOrderBatch sampleOrderBatch);

    /**
     * 修改打样批次记录
     *
     * @param sampleOrderBatch 打样批次记录
     * @return 结果
     */
    public int updateSampleOrderBatch(SampleOrderBatch sampleOrderBatch);

    /**
     * 删除打样批次记录
     *
     * @param id 打样批次记录主键
     * @return 结果
     */
    public int deleteSampleOrderBatchById(Long id);

    /**
     * 批量删除打样批次记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSampleOrderBatchByIds(Long[] ids);

    /**
     * 根据工程师打样单ID查询所有批次记录
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 批次记录列表
     */
    public List<SampleOrderBatch> selectBatchesByOrderId(@Param("engineerSampleOrderId") Long engineerSampleOrderId);

    /**
     * 根据工程师打样单ID查询当前批次
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 当前批次记录
     */
    public SampleOrderBatch selectCurrentBatchByOrderId(@Param("engineerSampleOrderId") Long engineerSampleOrderId);

    /**
     * 查询指定打样单的所有进行中批次
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 进行中的批次记录列表
     */
    public List<SampleOrderBatch> selectActiveBatchesByOrderId(@Param("engineerSampleOrderId") Long engineerSampleOrderId);

    /**
     * 获取指定工程师打样单的下一个批次序号
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 下一个批次序号
     */
    public Integer selectNextBatchIndex(@Param("engineerSampleOrderId") Long engineerSampleOrderId);
}
