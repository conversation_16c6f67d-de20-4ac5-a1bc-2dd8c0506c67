package com.ruoyi.software.service;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.software.domain.ExperimentRecord;

import java.util.List;

/**
 * 实验编号记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IExperimentRecordService 
{
    /**
     * 查询实验编号记录
     * 
     * @param id 实验编号记录主键
     * @return 实验编号记录
     */
    public ExperimentRecord selectExperimentRecordById(Long id);

    /**
     * 查询实验编号记录列表
     * 
     * @param experimentRecord 实验编号记录
     * @return 实验编号记录集合
     */
    public List<ExperimentRecord> selectExperimentRecordList(ExperimentRecord experimentRecord);

    /**
     * 新增实验编号记录
     * 
     * @param experimentRecord 实验编号记录
     * @return 结果
     */
    public int insertExperimentRecord(ExperimentRecord experimentRecord);

    /**
     * 修改实验编号记录
     * 
     * @param experimentRecord 实验编号记录
     * @return 结果
     */
    public int updateExperimentRecord(ExperimentRecord experimentRecord);

    /**
     * 批量删除实验编号记录
     * 
     * @param ids 需要删除的实验编号记录主键集合
     * @return 结果
     */
    public int deleteExperimentRecordByIds(Long[] ids);

    /**
     * 删除实验编号记录信息
     * 
     * @param id 实验编号记录主键
     * @return 结果
     */
    public int deleteExperimentRecordById(Long id);

    /**
     * 根据批次ID查询实验记录列表
     *
     * @param batchId 批次ID
     * @return 实验记录列表
     */
    public List<ExperimentRecord> selectExperimentRecordsByBatchId(Long batchId);

    /**
     * 为指定批次添加实验记录
     *
     * @param experimentRecord 实验记录对象
     * @return 新创建的实验记录
     */
    public ExperimentRecord addExperimentRecord(ExperimentRecord experimentRecord);

    /**
     * 为指定批次添加实验记录（小程序）
     *
     * @param experimentRecord 实验记录对象
     * @param sysUser 小程序用户信息
     * @return 新创建的实验记录
     */
    public ExperimentRecord addExperimentRecord(ExperimentRecord experimentRecord, SysUser sysUser);

    /**
     * 根据工程师打样单ID查询所有批次实验室编号
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 实验室编号列表
     */
    public List<ExperimentRecord> getBatchExperimentCodeList(Long engineerSampleOrderId);

}
