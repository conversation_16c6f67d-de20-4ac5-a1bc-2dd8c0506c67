package com.ruoyi.software.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 仪表盘查询请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
public class DashboardQueryDTO {

    /** 单个组别ID */
    private Long deptId;

    /** 多个组别ID列表（用于查询时指定多个组别，忽略用户权限） */
    private Long[] deptIds;

    /** 开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String beginDateRange;

    /** 结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String endDateRange;

    /** 当前登录用户ID（用于权限控制，不参与数据库操作） */
    private Long currentUserId;

    /** 当前用户可查看的部门ID（用于权限控制，不参与数据库操作） */
    private Long currentUserDeptId;

    /**
     * 判断是否传递了多个组别ID
     * @return true表示传递了多个组别ID，应忽略用户权限控制
     */
    public boolean hasMultipleDeptIds() {
        return deptIds != null && deptIds.length > 0;
    }

    /**
     * 设置用户权限信息
     * @param currentUserId 当前用户ID
     * @param currentUserDeptId 当前用户部门ID
     */
    public void setUserPermissions(Long currentUserId, Long currentUserDeptId) {
        this.currentUserId = currentUserId;
        this.currentUserDeptId = currentUserDeptId;
    }

    /**
     * 清除用户权限信息（用于忽略权限控制）
     */
    public void clearUserPermissions() {
        this.currentUserId = null;
        this.currentUserDeptId = null;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Long[] getDeptIds() {
        return deptIds;
    }

    public void setDeptIds(Long[] deptIds) {
        this.deptIds = deptIds;
    }

    public String getBeginDateRange() {
        return beginDateRange;
    }

    public void setBeginDateRange(String beginDateRange) {
        this.beginDateRange = beginDateRange;
    }

    public String getEndDateRange() {
        return endDateRange;
    }

    public void setEndDateRange(String endDateRange) {
        this.endDateRange = endDateRange;
    }

    public Long getCurrentUserId() {
        return currentUserId;
    }

    public void setCurrentUserId(Long currentUserId) {
        this.currentUserId = currentUserId;
    }

    public Long getCurrentUserDeptId() {
        return currentUserDeptId;
    }

    public void setCurrentUserDeptId(Long currentUserDeptId) {
        this.currentUserDeptId = currentUserDeptId;
    }

    @Override
    public String toString() {
        return "DashboardQueryDTO{" +
                "deptId=" + deptId +
                ", deptIds=" + java.util.Arrays.toString(deptIds) +
                ", beginDateRange='" + beginDateRange + '\'' +
                ", endDateRange='" + endDateRange + '\'' +
                ", currentUserId=" + currentUserId +
                ", currentUserDeptId=" + currentUserDeptId +
                '}';
    }
}
