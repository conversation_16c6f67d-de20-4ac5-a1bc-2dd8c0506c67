package com.ruoyi.software.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.hr.mapper.SysHrUserMapper;
import com.ruoyi.product.service.ITCategoryService;
import com.ruoyi.project.domain.ProjectItemOrder;
import com.ruoyi.project.mapper.ProjectMapper;
import com.ruoyi.project.service.ITProjectExecutionService;
import com.ruoyi.software.domain.EngineerSampleOrder;
import com.ruoyi.software.domain.SampleOrderBatch;
import com.ruoyi.software.domain.dto.ChangeEngineerDTO;
import com.ruoyi.software.domain.dto.DashboardQueryDTO;
import com.ruoyi.software.domain.dto.UpdateSampleOrderStatusDTO;
import com.ruoyi.software.domain.vo.*;
import com.ruoyi.software.mapper.EngineerSampleOrderMapper;
import com.ruoyi.software.mapper.EngineerWorkRecordMapper;
import com.ruoyi.software.service.IEngineerSampleOrderService;
import com.ruoyi.software.service.IEngineerWorkRecordService;
import com.ruoyi.software.service.ISampleOrderBatchService;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工程师打样单关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
public class EngineerSampleOrderServiceImpl implements IEngineerSampleOrderService
{
    private static final Logger log = LoggerFactory.getLogger(EngineerSampleOrderServiceImpl.class);
    @Resource
    private EngineerSampleOrderMapper engineerSampleOrderMapper;

    @Resource
    private EngineerWorkRecordMapper engineerWorkRecordMapper;

    @Resource
    private IEngineerWorkRecordService engineerWorkRecordService;

    @Resource
    private ITCategoryService categoryService;

    @Resource
    private ISampleOrderBatchService sampleOrderBatchService;

    @Resource
    private ISysUserService userService;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private SysHrUserMapper sysHrUserMapper;

    @Resource
    private ITProjectExecutionService tProjectExecutionService;

    @Resource
    private com.ruoyi.ai.service.FlowTools flowTools;
    @Autowired
    private ProjectMapper projectMapper;

    @Override
    public void insertEngineerSampleOrder(EngineerSampleOrder engineerSampleOrder)
    {
        // 设置标准工时
        if (engineerSampleOrder.getStandardManHours() == null || engineerSampleOrder.getStandardManHours().compareTo(BigDecimal.ZERO) <= 0) {
            // 如果没有设置工时，则根据类别和难度等级获取标准工时
            if (engineerSampleOrder.getCategoryId() == null || engineerSampleOrder.getDifficultyLevelId() == null) {
                throw new ServiceException("类别ID和难度等级ID不能为空");
            }
            // 获取标准工时
            BigDecimal standardHours = categoryService.selectSampleTimeByCategoryAndLevel(engineerSampleOrder.getCategoryId(), engineerSampleOrder.getDifficultyLevelId());
            if (standardHours == null) {
                throw new ServiceException("类别：" + engineerSampleOrder.getCategoryId() + "，难度:" + engineerSampleOrder.getDifficultyLevelId() + "未找到对应的标准工时");
            }
            engineerSampleOrder.setStandardManHours(standardHours);
        }

        // 判断是否有工程师
        if (engineerSampleOrder.getUserId() != null) {
            SysUser sysUser = userService.selectUserById(engineerSampleOrder.getUserId());
            engineerSampleOrder.setRank(sysUser.getRank()); // 设置用户等级
        }

        engineerSampleOrder.setCreateTime(DateUtils.getNowDate());
        engineerSampleOrder.setCreateBy(SecurityUtils.getUsername());
        engineerSampleOrderMapper.insertEngineerSampleOrder(engineerSampleOrder);
    }

    @Override
    public void updateEngineerSampleOrder(EngineerSampleOrder engineerSampleOrder)
    {
        // 判断是否有工程师
        if (engineerSampleOrder.getUserId() != null) {
            SysUser sysUser = userService.selectUserById(engineerSampleOrder.getUserId());
            engineerSampleOrder.setRank(sysUser.getRank()); // 设置用户等级
        }
        engineerSampleOrder.setUpdateTime(DateUtils.getNowDate());
        engineerSampleOrder.setUpdateBy(SecurityUtils.getUsername());
        engineerSampleOrderMapper.updateEngineerSampleOrder(engineerSampleOrder);
    }

    /**
     * 删除打样单关联信息
     * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteEngineerSampleOrderByIds(Long[] ids) {
        if (ids == null || ids.length == 0) {
            return 0;
        }

        log.info("开始删除打样单关联信息，ID数组：{}", java.util.Arrays.toString(ids));

        // 1. 查询要删除的关联信息
        int deletedCount = 0;
        for (Long id : ids) {
            EngineerSampleOrder order = engineerSampleOrderMapper.selectEngineerSampleOrderById(id);
            if (order == null) {
                log.warn("未找到ID为{}的打样单关联记录", id);
                continue;
            }

            log.info("处理打样单关联记录：ID[{}]，工程师[{}]，打样单编号[{}]，关联状态[{}]",
                    id, order.getUserId(), order.getSampleOrderCode(), order.getAssociationStatus());

            // 2. 判断association_status 状态，关联状态（1：分配成功 0：分配失败）
            if (order.getAssociationStatus() != null && order.getAssociationStatus() == 1) {
                // 2.1 如果是1（分配成功），退回相应的工时
                log.info("关联状态为分配成功，需要退回工时：工程师[{}]，排单日期[{}]，预估工时[{}]小时",
                        order.getUserId(), order.getScheduledDate(), order.getEstimatedManHours());

                if (order.getEstimatedManHours() != null && order.getEstimatedManHours().compareTo(BigDecimal.ZERO) > 0) {
                    try {
                        restoreWorkHoursForOrder(order);
                        log.info("成功退回工时：工程师[{}]，工时[{}]小时", order.getUserId(), order.getEstimatedManHours());
                    } catch (Exception e) {
                        log.error("退回工时失败：工程师[{}]，工时[{}]小时，错误信息：{}",
                                order.getUserId(), order.getEstimatedManHours(), e.getMessage());
                        throw new ServiceException("退回工时失败：" + e.getMessage());
                    }
                }
            } else {
                // 2.2 如果是0（分配失败），则直接删除，无需退回工时
                log.info("关联状态为分配失败，直接删除，无需退回工时");
            }
        }

        // 3. 执行批量删除操作
        int result = engineerSampleOrderMapper.deleteEngineerSampleOrderByIds(ids);
        log.info("批量删除操作完成，影响行数：{}", result);

        return result;
    }

    /**
     * 更新打样单关联表状态
     * @param updateDTO 更新打样单状态请求DTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSampleOrderStatus(UpdateSampleOrderStatusDTO updateDTO) {
        try {
            log.info("开始更新打样单状态（DTO版本），请求参数：{}", updateDTO);

            // 参数验证
            if (updateDTO == null) {
                throw new ServiceException("请求参数不能为空");
            }

            if (updateDTO.getId() == null) {
                throw new ServiceException("打样单关联表ID不能为空");
            }

            if (StringUtils.isEmpty(updateDTO.getStatus())) {
                throw new ServiceException("状态不能为空");
            }

            // 状态为已完成时，实验编号必填
            if ("2".equals(updateDTO.getStatus()) && StringUtils.isEmpty(updateDTO.getLaboratoryCode())) {
                throw new ServiceException("状态为已完成时，实验编号不能为空");
            }

            Long id = updateDTO.getId();
            String status = updateDTO.getStatus();
            EngineerSampleOrder engineerSampleOrder = engineerSampleOrderMapper.selectEngineerSampleOrderById(id);
            if (engineerSampleOrder == null) {
                log.warn("未找到打样单关联记录，ID：{}", id);
                return false;
            }

            int newStatus = Integer.parseInt(status);
            Integer currentStatus = engineerSampleOrder.getCompletionStatus();

            Date now = DateUtils.getNowDate();

            if (newStatus == 1) { // 进行中状态
                log.info("更新状态为进行中，开始处理批次逻辑");

                // 设置打样单关联表的实际开始时间
                engineerSampleOrder.setActualStartTime(now);
                // 锁定工程师&打样单关联表状态
                engineerSampleOrder.setIsLocked(1);

                // 开始新的打样批次
                if (!sampleOrderBatchService.hasCurrentBatch(id)) {
                    sampleOrderBatchService.startNewBatch(id, "");
                    log.info("自动开始新批次，工程师打样单ID：{}，开始时间：{}", id, now);
                } else {
                    log.info("已存在进行中的批次，无需创建新批次");
                }
                // 更新状态
                engineerSampleOrder.setCompletionStatus(newStatus);
            } else if (newStatus == 2) { // 已完成状态
                log.info("更新状态为已完成，开始处理完成逻辑");
                // 如果提供了实验编号，则保存到数据库
                if (updateDTO.getLaboratoryCode() == null || updateDTO.getLaboratoryCode().trim().isEmpty()) {
                    throw new ServiceException("请输入实验室编号");
                }

                JSONObject params = new JSONObject();
                params.put("projectOrderId",engineerSampleOrder.getProjectOrderId());
                params.put("status",1);
                params.put("labNo",updateDTO.getLaboratoryCode());
                params.put("remark", updateDTO.getRemark().trim());
                JSONObject returnObj =  tProjectExecutionService.processOrderExecution(params);
                // 判断是否200，不是200一律抛出异常
                String returnStatus = returnObj.getString("status");
                if (!"200".equals(returnStatus)) {
                    String errorMsg = returnObj.getString("msg");
                    if (StringUtils.isNotEmpty(errorMsg)) {
                        throw new ServiceException(errorMsg);
                    } else {
                        throw new ServiceException("处理打样单失败，状态码：" + returnStatus);
                    }
                }
                engineerSampleOrder.setLaboratoryCode(updateDTO.getLaboratoryCode().trim());
                log.info("保存实验编号，ID：{}，实验编号：{}", id, updateDTO.getLaboratoryCode().trim());

                // 设置备注
                engineerSampleOrder.setRemark(updateDTO.getRemark().trim());
                // 设置打样单关联表的实际完成时间
                engineerSampleOrder.setActualFinishTime(now);
                // 锁定工程师&打样单关联表状态
                engineerSampleOrder.setIsLocked(1);

                // 计算打样单关联表的总实际工时（基于所有批次的工时汇总）
                BigDecimal totalActualHours = calculateTotalActualHours(id);
                engineerSampleOrder.setActualManHours(totalActualHours);

                // 确保有实际开始时间
                if (engineerSampleOrder.getActualStartTime() == null) {
                    // 如果没有开始时间，使用创建时间作为默认值
                    Date startTime = engineerSampleOrder.getCreateTime();
                    if (startTime == null) {
                        startTime = now; // 最后兜底使用当前时间
                    }
                    engineerSampleOrder.setActualStartTime(startTime);
                    log.info("设置默认开始时间：{}", startTime);
                }

                // 结束此打样单所有进行中的批次
                if (sampleOrderBatchService.hasCurrentBatch(id)) {
                    sampleOrderBatchService.finishAllActiveBatches(id);
                    log.info("自动结束所有进行中批次，工程师打样单ID：{}，结束时间：{}，总工时：{}小时",
                        id, now, totalActualHours);
                } else {
                    log.info("没有进行中的批次需要结束");
                }

                // 更新批次统计信息
                sampleOrderBatchService.updateSampleOrderBatchStatistics(id);
                // 更新状态
                engineerSampleOrder.setCompletionStatus(newStatus);
            }else if (newStatus == 3) { // 驳回打样单
                log.info("驳回：工程师打样单ID：{}，打样单编号：{}，状态：{}，驳回理由：{}", id, engineerSampleOrder.getSampleOrderCode(), status, updateDTO.getRejectReason());
                JSONObject params = new JSONObject();
                params.put("projectOrderId",engineerSampleOrder.getProjectOrderId());
                params.put("status",2);
                // 如果提供了驳回理由，则保存到数据库
                if (updateDTO.getRejectReason() != null && !updateDTO.getRejectReason().trim().isEmpty()) {
                    engineerSampleOrder.setRejectReason(updateDTO.getRejectReason().trim());
                    params.put("remark",updateDTO.getRejectReason());
                    log.info("保存驳回理由，ID：{}，驳回理由：{}", id, updateDTO.getRejectReason().trim());
                }
                JSONObject returnObj =  tProjectExecutionService.processOrderExecution(params);
                // 判断是否200，不是200一律抛出异常
                String returnStatus = returnObj.getString("status");
                if (!"200".equals(returnStatus)) {
                    String errorMsg = returnObj.getString("msg");
                    if (StringUtils.isNotEmpty(errorMsg)) {
                        throw new ServiceException(errorMsg);
                    } else {
                        throw new ServiceException("处理打样单失败，状态码：" + returnStatus);
                    }
                }
                // 判断并退回工时（如果需要的话）
                checkAndRestoreWorkHours(engineerSampleOrder, "驳回");
                // 更新状态
                engineerSampleOrder.setCompletionStatus(newStatus);
            } else if (newStatus == 4) {
                log.info("撤回：工程师打样单ID：{}，打样单编号：{}，状态：{}", id, engineerSampleOrder.getSampleOrderCode(), status);
                // 判断并退回工时（如果需要的话）
                checkAndRestoreWorkHours(engineerSampleOrder, "撤回");
                // 更新状态
                engineerSampleOrder.setCompletionStatus(newStatus);
            } else if (newStatus == 11) { // 特殊处理，只更新“逾期情况”填写的字段
                // 如果提供了未出样原因，则更新到数据库
                if (updateDTO.getReasonForNoSample() != null && !updateDTO.getReasonForNoSample().trim().isEmpty()) {
                    engineerSampleOrder.setReasonForNoSample(updateDTO.getReasonForNoSample().trim());
                    log.info("保存未出样原因，ID：{}，未出样原因：{}", id, updateDTO.getReasonForNoSample().trim());
                }
                // 如果提供了解决方案，则更新到数据库
                if (updateDTO.getSolution() != null && !updateDTO.getSolution().trim().isEmpty()) {
                    engineerSampleOrder.setSolution(updateDTO.getSolution().trim());
                    log.info("保存解决方案，ID：{}，解决方案：{}", id, updateDTO.getSolution().trim());
                }
                // 如果提供了预计时间，则更新到数据库
                if (updateDTO.getExpectedSampleTime() != null) {
                    engineerSampleOrder.setExpectedSampleTime(updateDTO.getExpectedSampleTime());

                    //更新打样单信息
                    ProjectItemOrder projectOrderInfo = new ProjectItemOrder();
                    projectOrderInfo.setDelayYjTime(updateDTO.getExpectedSampleTime());
                    projectOrderInfo.setId(engineerSampleOrder.getProjectOrderId());
                    engineerSampleOrderMapper.updateProjectItemOrderDelayInfo(projectOrderInfo);
                    log.info("保存预计时间，ID：{}，预计时间：{}", id, updateDTO.getExpectedSampleTime());

                }
            }

            // 更新
            this.updateEngineerSampleOrder(engineerSampleOrder);

            log.info("成功更新打样单状态，ID：{}，从状态{}更新为状态{}", id, currentStatus, newStatus);
            return true;

        } catch (Exception e) {
            log.error("更新打样单状态失败，请求参数：{}，错误：{}", updateDTO, e.getMessage(), e);
            throw new ServiceException("更新打样单状态失败: " + e.getMessage());
        }
    }

    /**
     * 完成打样单
     * @param id 打样单关联表ID
     * @param laboratoryCode 实验编号
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean endSampleOrderStatus(Long id, String laboratoryCode) {
        try {
            log.info("完成打样单状态，ID：{}，实验室编码：{}", id, laboratoryCode);

            EngineerSampleOrder engineerSampleOrder = engineerSampleOrderMapper.selectEngineerSampleOrderById(id);
            if (engineerSampleOrder == null) {
                log.warn("未找到打样单关联记录，ID：{}", id);
                return false;
            }

            Date now = DateUtils.getNowDate();

            // 如果提供了实验编号，则保存到数据库
            if (laboratoryCode == null || laboratoryCode.trim().isEmpty()) {
                throw new ServiceException("请输入实验室编号");
            }

            engineerSampleOrder.setLaboratoryCode(laboratoryCode.trim());
            log.info("保存实验编号，ID：{}，实验编号：{}", id, laboratoryCode.trim());

            // 设置打样单关联表的实际完成时间
            engineerSampleOrder.setActualFinishTime(now);
            // 锁定工程师&打样单关联表状态
            engineerSampleOrder.setIsLocked(1);

            // 计算打样单关联表的总实际工时（基于所有批次的工时汇总）
            BigDecimal totalActualHours = calculateTotalActualHours(id);
            engineerSampleOrder.setActualManHours(totalActualHours);

            // 确保有实际开始时间
            if (engineerSampleOrder.getActualStartTime() == null) {
                // 如果没有开始时间，使用创建时间作为默认值
                Date startTime = engineerSampleOrder.getCreateTime();
                if (startTime == null) {
                    startTime = now; // 最后兜底使用当前时间
                }
                engineerSampleOrder.setActualStartTime(startTime);
                log.info("设置默认开始时间：{}", startTime);
            }

            // 结束所有进行中的批次
            if (sampleOrderBatchService.hasCurrentBatch(id)) {
                sampleOrderBatchService.finishAllActiveBatches(id);
                log.info("自动结束所有进行中批次，工程师打样单ID：{}，结束时间：{}，总工时：{}小时", id, now, totalActualHours);
            } else {
                log.info("没有进行中的批次需要结束");
            }
            // 更新批次统计信息
            sampleOrderBatchService.updateSampleOrderBatchStatistics(id);
            // 设置为已完成状态
            engineerSampleOrder.setCompletionStatus(2);
            // 更新
            this.updateEngineerSampleOrder(engineerSampleOrder);
            log.info("成功更新打样单状态，ID：{}，实验室编码{}", id, laboratoryCode);
            return true;
        } catch (Exception e) {
            log.error("完成打样单状态失败，ID：{}，实验室编码：{}，错误：{}", id, laboratoryCode, e.getMessage(), e);
            throw new ServiceException("完成打样单状态失败: " + e.getMessage());
        }
    }

    /**
     * 计算打样单的总实际工时（基于所有批次的工时汇总）
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 总实际工时
     */
    private BigDecimal calculateTotalActualHours(Long engineerSampleOrderId) {
        try {
            List<SampleOrderBatch> batches = sampleOrderBatchService.selectBatchesByOrderId(engineerSampleOrderId);
            BigDecimal totalHours = BigDecimal.ZERO;

            for (SampleOrderBatch batch : batches) {
                if (batch.getActualManHours() != null) {
                    totalHours = totalHours.add(batch.getActualManHours());
                }
            }

            log.info("计算总实际工时，工程师打样单ID：{}，批次数：{}，总工时：{}小时",
                engineerSampleOrderId, batches.size(), totalHours);
            return totalHours;
        } catch (Exception e) {
            log.error("计算总实际工时失败，工程师打样单ID：{}，错误：{}", engineerSampleOrderId, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 撤回打样单
     * @param id 打样单关联表ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean withdrawSampleOrder(Long id) {
        log.info("开始撤回打样单，ID：{}", id);
        UpdateSampleOrderStatusDTO updateDTO = new UpdateSampleOrderStatusDTO();
        updateDTO.setId(id);
        updateDTO.setStatus("4"); // 设置状态为撤回
        return updateSampleOrderStatus(updateDTO);
    }

    /**
     * 获取未锁定的打样单列表
     * @param userId 工程师ID
     * @param days 时间范围（单位：天），默认7天
     * @param limit 查询最大条数，默认20条
     * @return 未锁定的打样单列表
     */
    @Override
    public List<EngineerSampleOrder> getUnlockedSampleOrders(Long userId, Integer days, Integer limit) {
        return engineerSampleOrderMapper.selectUnlockedSampleOrders(userId, days, limit);
    }

    /**
     * 查询指定工程师在指定日期以后的未锁定打样单列表（按排单日期排序）
     * @param userId 工程师ID
     * @param scheduledDate 指定日期
     * @return 未锁定的打样单列表
     */
    @Override
    public List<EngineerSampleOrder> getUnlockedSampleOrdersAfterDate(Long userId, Date scheduledDate) {
        return engineerSampleOrderMapper.selectUnlockedSampleOrdersAfterDate(userId, scheduledDate);
    }

    /**
     * 查询指定工程师在指定日期范围内的未锁定打样单列表（按排单日期排序）
     * @param userId 工程师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 未锁定的打样单列表
     */
    @Override
    public List<EngineerSampleOrder> getUnlockedSampleOrdersInDateRange(Long userId, Date startDate, Date endDate) {
        return engineerSampleOrderMapper.selectUnlockedSampleOrdersInDateRange(userId, startDate, endDate);
    }

    /**
     * 撤回指定工程师和指定日期范围的打样单
     * @param userId 工程师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param reschedule 是否重新排班（true：重新排班，false：不重新排班）
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean withdrawSampleOrdersInDateRange(Long userId, Date startDate, Date endDate, Boolean reschedule) {
        // 1. 基本信息校验
        if (userId == null) {
            throw new ServiceException("工程师ID不能为空");
        }
        if (startDate == null) {
            throw new ServiceException("开始日期不能为空");
        }
        if (endDate == null) {
            throw new ServiceException("结束日期不能为空");
        }
        if (startDate.after(endDate)) {
            throw new ServiceException("开始日期不能晚于结束日期");
        }
        if (reschedule == null) {
            reschedule = false; // 默认不重新排班
        }

        try {
            log.info("开始撤回工程师[{}]在日期范围[{} - {}]的未锁定打样单，是否重新排班：{}",
                    userId, startDate, endDate, reschedule);

            // 2. 查询范围内未锁定的打样单信息
            List<EngineerSampleOrder> unlockedOrders = getUnlockedSampleOrdersInDateRange(userId, startDate, endDate);

            if (unlockedOrders.isEmpty()) {
                log.info("没有找到需要撤回的未锁定打样单");
                return true;
            }

            log.info("找到{}个未锁定的打样单需要撤回", unlockedOrders.size());

            // 3. 返回对应打样单对应的工时并更改打样单的分配状态（合并为一个循环）
            for (EngineerSampleOrder order : unlockedOrders) {
                // 退回工时
                checkAndRestoreWorkHours(order, "撤回");
                log.debug("撤回打样单[{}]的工时[{}]小时", order.getSampleOrderCode(), order.getEstimatedManHours());

                // 更新关联状态为分配失败：association_status关联状态（1：分配成功 0：分配失败）
                EngineerSampleOrder updateOrder = new EngineerSampleOrder();
                updateOrder.setId(order.getId());
                updateOrder.setAssociationStatus(0); // 设置为分配失败
                updateOrder.setUpdateTime(DateUtils.getNowDate());
                updateOrder.setUpdateBy(SecurityUtils.getUsername());
                this.updateEngineerSampleOrder(updateOrder);

                // 4. 根据是否重新安排(调用进行接口重新排班)
                if (reschedule) {
                    // 调用排单接口重新安排
                    Boolean b = rescheduleEngineerSampleOrder(order);
                    if (b) {
                        throw new ServiceException("重新排单失败：打样单关联ID：[" + order.getId() + "],打样单编号：[" + order.getSampleOrderCode() + "]。");
                    }
                }
            }
            log.info("撤回操作完成，共处理{}个打样单", unlockedOrders.size());
            return true;
        } catch (Exception e) {
            log.error("撤回工程师[{}]在日期范围[{} - {}]的打样单失败：{}", userId, startDate, endDate, e.getMessage(), e);
            throw new ServiceException("撤回打样单失败: " + e.getMessage());
        }
    }

    /**
     * 打样单重新排单
     * @param sampleOrder 打样单关联表ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean rescheduleEngineerSampleOrder(EngineerSampleOrder sampleOrder) {
        // 1. 基本信息校验
        if (sampleOrder == null) {
            throw new ServiceException("打样单关联表信息能不为空");
        }

        try {
            log.info("开始重新排单，打样单关联表ID：{}", sampleOrder);

            // 2. 构建传递给 FlowTools 的数据对象
            JSONObject dataObj = new JSONObject();
            dataObj.put("projectOrderId",sampleOrder.getProjectOrderId());
            dataObj.put("code",sampleOrder.getSampleOrderCode());
            String dylb = sampleOrder.getDifficultyLevelId();
            dataObj.put("dyld",dylb);
            dataObj.put("dylbLabel", DictUtils.getDictLabel("project_nrw_dylb",dylb));
            dataObj.put("serviceMode",sampleOrder.getServiceMode());
            // 重新计算标准工时
            dataObj.put("standardHours",categoryService.selectSampleTimeByCategoryAndLevel(sampleOrder.getCategoryId(), sampleOrder.getDifficultyLevelId()));
            dataObj.put("checkType",sampleOrder.getCheckType());
            dataObj.put("categoryId",sampleOrder.getCategoryId());
            // 开始时间
            dataObj.put("startDate",sampleOrder.getStartDate());
            // 结束时间
            dataObj.put("endDate",sampleOrder.getEndDate());
            dataObj.put("remark",sampleOrder.getSampleOrderRemark());

            // 3. 调用 FlowTools 进行自动排单
            flowTools.autoScheduleSampleOrder(dataObj);
            return true;
        } catch (Exception e) {
            log.error("重新排单失败，错误：{}，打样单关联表：{}", e.getMessage(), sampleOrder, e);
            throw new ServiceException("重新排单失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean assignWorkOrderToEngineer(EngineerSampleOrder engineerSampleOrder) {
        // 参数验证
        if (engineerSampleOrder == null) {
            throw new ServiceException("工程师打样单关联对象不能为空");
        }
        if (engineerSampleOrder.getUserId() == null) {
            throw new ServiceException("工程师ID不能为空");
        }
        if (engineerSampleOrder.getSampleOrderCode() == null) {
            throw new ServiceException("打样单编号不能为空");
        }
        if (engineerSampleOrder.getScheduledDate() == null) {
            throw new ServiceException("排单日期不能为空");
        }
        if (engineerSampleOrder.getEndDate() == null) {
            throw new ServiceException("最晚截至日期不能为空");
        }
        if (engineerSampleOrder.getAdjustWorkSchedule() == 1){
            // 重新计算预估工时
            BigDecimal standardHours = categoryService.selectSampleTimeByCategoryAndLevel(engineerSampleOrder.getCategoryId(), engineerSampleOrder.getDifficultyLevelId());
            engineerSampleOrder.setEstimatedManHours(standardHours);
        }

        try {
            log.info("开始为工程师[{}]排单，打样单编号[{}]，排单日期[{}]，预估工时[{}]小时",
                engineerSampleOrder.getUserId(),
                engineerSampleOrder.getSampleOrderCode(),
                engineerSampleOrder.getScheduledDate(),
                engineerSampleOrder.getEstimatedManHours());

            // 1.获取工程师指定日期内是否有足够的工时
            // 工时向上取整转int
            int roundedUp = engineerSampleOrder.getEstimatedManHours().setScale(0, RoundingMode.CEILING).intValue();
            // 计算查询时间范围
            int range = (roundedUp - 1) / 8;

            // 创建 SimpleDateFormat 实例，指定目标格式
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            // 格式化日期
            String formattedDate = formatter.format(engineerSampleOrder.getScheduledDate());

            // 传入工程师id和工作日期和时间范围,输出时间段内查询到可用的工时
            BigDecimal availableHours = engineerWorkRecordMapper.selectAvailableRecordsInRange(
                engineerSampleOrder.getUserId(), formattedDate, range);

            // 2. 计算最晚开始日期
            engineerSampleOrder.setLatestStartTime(flowTools.findTargetWorkday(engineerSampleOrder.getEndDate(),range,-1));

            log.info("工程师[{}]在指定日期范围内可用工时：{}小时，需要工时：{}小时",
                engineerSampleOrder.getUserId(), availableHours, engineerSampleOrder.getEstimatedManHours());

            if (availableHours.compareTo(engineerSampleOrder.getEstimatedManHours()) >= 0) {
                // 3.1 有足够的工时，直接排单
                log.info("工时充足，执行直接排单");
                return directAssignWorkOrder(engineerSampleOrder);
            } else {
                // 3.2 没有足够的工时，需要插单
                log.info("工时不足，执行插单操作");
                return insertWorkOrder(engineerSampleOrder);
            }
        } catch (Exception e) {
            log.error("排单失败，工程师ID[{}]，打样单编号[{}]，错误信息：{}",
                engineerSampleOrder.getUserId(),
                engineerSampleOrder.getSampleOrderCode(),
                e.getMessage(), e);
            throw new ServiceException("排单失败: " + e.getMessage());
        }
    }

    /**
     * 直接排单（有足够工时的情况）
     * @param engineerSampleOrder 工程师打样单关联对象
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean directAssignWorkOrder(EngineerSampleOrder engineerSampleOrder) {
        try {
            log.info("开始直接排单，工程师[{}]，打样单[{}]", engineerSampleOrder.getUserId(), engineerSampleOrder.getSampleOrderCode());

            // 1. 检查是否已存在关联记录
            EngineerSampleOrder existingOrder = null;
            if (engineerSampleOrder.getProjectOrderId() != null) {
                existingOrder = engineerSampleOrderMapper.selectByProjectOrderId(engineerSampleOrder.getProjectOrderId());
            }

            // 2. 设置打样单状态和锁定标志
            SysUser sysUser = userService.selectUserById(engineerSampleOrder.getUserId());
            engineerSampleOrder.setCompletionStatus(0); // 待开始状态
            engineerSampleOrder.setIsLocked(0); // 未锁定状态


            // 3. 设置组长（协助人）
            SysUser engineerAssistant = engineerWorkRecordService.getEngineerAssistant(engineerSampleOrder.getUserId(), engineerSampleOrder.getCategoryId(), engineerSampleOrder.getDifficultyLevelId());

            // 如果有协助人，设置组长（协助人）的姓名
            if (engineerAssistant != null) {
                engineerSampleOrder.setAssistantName(engineerAssistant.getNickName());
            } else {
                engineerSampleOrder.setAssistantName(null);
            }

            if (existingOrder != null) {
                // 4.1 更新现有记录
                log.info("发现已存在的关联记录，ID[{}]，执行更新操作", existingOrder.getId());
                if (existingOrder.getAssociationStatus() == 1){
                    // 如果是历史记录中是已分配的状态。就使用改单的逻辑（涉及到调整工程师的日期，工时情况）
                    ChangeEngineerDTO changeEngineerDTO = new ChangeEngineerDTO();
                    changeEngineerDTO.setSampleOrderId(existingOrder.getId());
                    changeEngineerDTO.setOldEngineerId(existingOrder.getUserId());
                    changeEngineerDTO.setNewEngineerId(engineerSampleOrder.getUserId());
                    changeEngineerDTO.setScheduledDate(engineerSampleOrder.getScheduledDate());
                    changeEngineerDTO.setAdjustWorkSchedule(engineerSampleOrder.getAdjustWorkSchedule());
                    changeEngineer(changeEngineerDTO);
                }else {
                    // 如果是历史记录中是未分配的状态。直接更改
                    engineerSampleOrder.setAssociationStatus(1);
                    engineerSampleOrder.setId(existingOrder.getId()); // 设置ID用于更新
                    engineerSampleOrder.setUpdateTime(DateUtils.getNowDate());
                    engineerSampleOrder.setUpdateBy(SecurityUtils.getUsername());
                    // 保留原有的创建信息
                    engineerSampleOrder.setCreateTime(existingOrder.getCreateTime());
                    engineerSampleOrder.setCreateBy(existingOrder.getCreateBy());

                    // 分配工时并预估扣除
                    boolean allocated = allocateWorkHours(engineerSampleOrder.getUserId(),
                            engineerSampleOrder.getScheduledDate(),
                            engineerSampleOrder.getEstimatedManHours());
                    if (!allocated) {
                        throw new ServiceException("工时分配失败：请检查对应工程师是否设置正确的工时");
                    }

                    this.updateEngineerSampleOrder(engineerSampleOrder);
                }
            } else {
                // 4.2 插入新记录
                log.info("未发现已存在的关联记录，执行插入操作");
                // 分配工时并预估扣除
                boolean allocated = allocateWorkHours(engineerSampleOrder.getUserId(),
                        engineerSampleOrder.getScheduledDate(),
                        engineerSampleOrder.getEstimatedManHours());
                if (!allocated) {
                    throw new ServiceException("工时分配失败：请检查对应工程师是否设置正确的工时");
                }

                insertEngineerSampleOrder(engineerSampleOrder);
            }

            return true;
        } catch (Exception e) {
            log.error("直接排单失败，工程师[{}]，打样单[{}]，错误：{}",
                engineerSampleOrder.getUserId(),
                engineerSampleOrder.getSampleOrderCode(),
                e.getMessage(), e);
            throw new ServiceException("直接排单失败: " + e.getMessage());
        }
    }

    /**
     * 分配工时（从指定日期开始按需分配，支持跨天分配）
     * @param userId 工程师ID
     * @param startDate 开始日期
     * @param requiredHours 需要的工时
     * @return 是否分配成功
     */
    @Transactional
    public boolean allocateWorkHours(Long userId, Date startDate, BigDecimal requiredHours) throws ParseException {
        BigDecimal remainingHours = requiredHours;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        // 创建 SimpleDateFormat 实例，指定目标格式
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

        // 最多尝试30天
        for (int i = 0; i < 30 && remainingHours.compareTo(BigDecimal.ZERO) > 0; i++) {
            // 格式化 Date 对象为字符串
            String currentDate = formatter.format(calendar.getTime());

            // 查询当天可用工时
            BigDecimal availableHours = engineerWorkRecordMapper.selectAvailableRecordsInRange(userId, currentDate, 0);

            if (availableHours.compareTo(BigDecimal.ZERO) > 0) {
                // 计算本日分配的工时
                BigDecimal allocateToday = remainingHours.min(availableHours);

                // 扣除工时
                boolean deducted = engineerWorkRecordService.deductWorkHours(userId, currentDate, allocateToday);
                if (!deducted) {
                    // 如果扣除失败，回滚之前的操作
                    rollbackAllocatedHours(userId, startDate, currentDate, requiredHours.subtract(remainingHours));
                    return false;
                }

                remainingHours = remainingHours.subtract(allocateToday);
            }

            // 移动到下一天
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }

        return remainingHours.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 回滚已分配的工时
     * @param userId 工程师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param allocatedHours 已分配的工时
     */
    public void rollbackAllocatedHours(Long userId, Date startDate, String endDate, BigDecimal allocatedHours) throws ParseException {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date date = formatter.parse(endDate);

        while (!calendar.getTime().after(date)) {
            String currentDate = formatter.format(calendar.getTime());
            engineerWorkRecordService.restoreWorkHours(userId, currentDate, allocatedHours);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
    }

    /**
     * 插单（工时不足的情况）
     * @param engineerSampleOrder 工程师打样单关联对象
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertWorkOrder(EngineerSampleOrder engineerSampleOrder) {
        try {
            log.info("开始插单操作，工程师[{}]，打样单[{}]", engineerSampleOrder.getUserId(), engineerSampleOrder.getSampleOrderCode());

            // 1. 查询该工程师指定日期以后所有的未锁定的打样单信息（按照排单日期排序）
            List<EngineerSampleOrder> unlockedOrders = getUnlockedSampleOrdersAfterDate(
                engineerSampleOrder.getUserId(), engineerSampleOrder.getScheduledDate());

            log.info("查询到{}个未锁定的打样单需要重新安排", unlockedOrders.size());

            // 2. 退回查询到的打样单的预估工时
            for (EngineerSampleOrder order : unlockedOrders) {
                restoreWorkHoursForOrder(order);
            }

            // 3. 插入当前要排入的打样单（新建关联管理，扣除相应工时）
            boolean currentOrderAssigned = directAssignWorkOrder(engineerSampleOrder);
            if (!currentOrderAssigned) {
                throw new ServiceException("当前工单排单失败");
            }

            // 4. 将之前的打样单按照最后空闲时间重新排序插入
            int reassignedCount = 0;
            for (EngineerSampleOrder order : unlockedOrders) {
                reassignWorkOrder(order);
                reassignedCount++;
            }
            log.info("插单操作完成，成功重新安排{}个打样单", reassignedCount);
            return true;
        } catch (Exception e) {
            log.error("插单失败，工程师[{}]，打样单[{}]，错误：{}",
                engineerSampleOrder.getUserId(),
                engineerSampleOrder.getSampleOrderCode(),
                e.getMessage(), e);
            throw new ServiceException("插单失败: " + e.getMessage());
        }
    }

    /**
     * 退回打样单的工时
     * @param order 打样单
     */
    private void restoreWorkHoursForOrder(EngineerSampleOrder order) {
        // 创建 SimpleDateFormat 实例，指定目标格式
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        engineerWorkRecordService.restoreWorkHours(
            order.getUserId(),
            formatter.format(order.getScheduledDate()),
            order.getEstimatedManHours()
        );
    }

    /**
     * 判断并退回打样单工时（如果需要的话）
     * @param engineerSampleOrder 工程师打样单对象
     * @param operationType 操作类型（用于日志记录，如"驳回"、"撤回"）
     */
    private void checkAndRestoreWorkHours(EngineerSampleOrder engineerSampleOrder, String operationType) {
        // 判断association_status状态，如果是已分配状态就退回对应工时
        if (engineerSampleOrder.getAssociationStatus() != null && engineerSampleOrder.getAssociationStatus() == 1) {
            // 如果是1（分配成功），退回相应的工时
            log.info("{}打样单，关联状态为分配成功，需要退回工时：工程师[{}]，排单日期[{}]，预估工时[{}]小时",
                    operationType, engineerSampleOrder.getUserId(), engineerSampleOrder.getScheduledDate(), engineerSampleOrder.getEstimatedManHours());

            if (engineerSampleOrder.getEstimatedManHours() != null && engineerSampleOrder.getEstimatedManHours().compareTo(BigDecimal.ZERO) > 0) {
                try {
                    restoreWorkHoursForOrder(engineerSampleOrder);
                    log.info("{}打样单成功退回工时：工程师[{}]，工时[{}]小时",
                            operationType, engineerSampleOrder.getUserId(), engineerSampleOrder.getEstimatedManHours());
                } catch (Exception e) {
                    log.error("{}打样单退回工时失败：工程师[{}]，工时[{}]小时，错误信息：{}",
                            operationType, engineerSampleOrder.getUserId(), engineerSampleOrder.getEstimatedManHours(), e.getMessage());
                    throw new ServiceException(operationType + "打样单退回工时失败：" + e.getMessage());
                }
            }
        } else {
            // 如果是0（分配失败）或未分配，则无需退回工时
            log.info("{}打样单，关联状态为分配失败或未分配，无需退回工时", operationType);
        }
    }

    /**
     * 重新安排工单
     * @param order 要重新安排的工单
     */
    private void reassignWorkOrder(EngineerSampleOrder order) throws ParseException {
        int roundedUp = order.getEstimatedManHours().setScale(0, RoundingMode.CEILING).intValue();
        // 计算查询时间范围
        int range = (roundedUp - 1) / 8 + 1;
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        if (order.getEstimatedManHours().compareTo(BigDecimal.ZERO) <= 0){
            return;
        }
        // 查找下一个可用的工作日期
        Date nextAvailableDate = engineerWorkRecordService.findNextAvailableWorkDate(
            order.getUserId(),
            formatter.format(order.getScheduledDate()),
            order.getEstimatedManHours(),
            range
        );

        if (nextAvailableDate != null) {
            // 更新排单日期
            order.setScheduledDate(nextAvailableDate);
            // 重新分配工时
            boolean allocated = allocateWorkHours(
                order.getUserId(),
                nextAvailableDate,
                order.getEstimatedManHours()
            );

            if (allocated) {
                // 重新更新关联表的打样排单时间
                order.setUpdateTime(DateUtils.getNowDate());
                order.setUpdateBy(SecurityUtils.getUsername());
                this.updateEngineerSampleOrder(order);
            }else {
                throw new ServiceException(order.getNickName() +"，在" + nextAvailableDate +  " 没有足够的工时！" );
            }
        }else {
            throw new ServiceException("未找到可用的工作日期，请检查工程师的工作安排");
        }
    }

    /**
     * 追加排单功能
     * 传入用户id，工单id，预估工时
     * 获取当前用户最近的空闲时间，新增/修改打样单的日期，扣除相应的工时
     *
     * @param userId 工程师ID
     * @param sampleOrderCode 打样单编号
     * @param estimatedManHours 预估工时
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean appendWorkOrderToEngineer(Long userId, String sampleOrderCode, BigDecimal estimatedManHours) {
        // 参数验证
        if (userId == null) {
            throw new ServiceException("工程师ID不能为空");
        }
        if (sampleOrderCode == null) {
            throw new ServiceException("打样单编号不能为空");
        }
        if (estimatedManHours == null || estimatedManHours.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("预估工时必须大于0");
        }

        try {
            log.info("开始追加排单，工程师[{}]，打样单[{}]，预估工时[{}]小时",
                userId, sampleOrderCode, estimatedManHours);

            int roundedUp = estimatedManHours.setScale(0, RoundingMode.CEILING).intValue();
            // 计算查询时间范围
            int range = (roundedUp - 1) / 8 + 1;
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            // 1. 查找最近的可用工作日期
            Date nextAvailableDate = engineerWorkRecordService.findNextAvailableWorkDate(userId, formatter.format(new Date()), estimatedManHours, range);
            if (nextAvailableDate == null) {
                throw new ServiceException("未找到可用的工作日期，请检查工程师的工作安排");
            }

            log.info("找到可用工作日期：{}", nextAvailableDate);

            // 2. 创建工程师打样单关联对象
            EngineerSampleOrder engineerSampleOrder = new EngineerSampleOrder();
            engineerSampleOrder.setUserId(userId);
            engineerSampleOrder.setSampleOrderCode(sampleOrderCode);
            engineerSampleOrder.setScheduledDate(nextAvailableDate);
            engineerSampleOrder.setEstimatedManHours(estimatedManHours);

            // 3. 执行直接排单
            boolean result = directAssignWorkOrder(engineerSampleOrder);

            if (result) {
                log.info("追加排单成功，工程师[{}]，打样单[{}]，排单日期[{}]",
                    userId, sampleOrderCode, nextAvailableDate);
            }

            return result;
        } catch (Exception e) {
            log.error("追加排单失败，工程师[{}]，打样单[{}]，错误：{}",
                userId, sampleOrderCode, e.getMessage(), e);
            throw new ServiceException("追加排单失败: " + e.getMessage());
        }
    }

    /**
     * 清空指定日期以后未锁定的打样单信息
     * 传入日期，用户id
     * 退回这个日期后所有的打样单的预估时间
     *
     * @param userId 工程师ID
     * @param fromDate 指定日期
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean clearUnlockedSampleOrdersAfterDate(Long userId, Date fromDate) {
        // 参数验证
        if (userId == null) {
            throw new ServiceException("工程师ID不能为空");
        }
        if (fromDate == null) {
            throw new ServiceException("指定日期不能为空");
        }

        try {
            log.info("开始清空工程师[{}]从日期[{}]以后的未锁定打样单", userId, fromDate);

            // 1. 查询指定日期以后的未锁定打样单
            List<EngineerSampleOrder> unlockedOrders = getUnlockedSampleOrdersAfterDate(userId, fromDate);

            if (unlockedOrders.isEmpty()) {
                log.info("没有找到需要清空的未锁定打样单");
                return true;
            }

            log.info("找到{}个未锁定的打样单需要清空", unlockedOrders.size());

            // 2. 退回所有打样单的预估工时
            for (EngineerSampleOrder order : unlockedOrders) {
                restoreWorkHoursForOrder(order);
                log.debug("退回打样单[{}]的工时[{}]小时",
                    order.getSampleOrderCode(), order.getEstimatedManHours());
            }

            // 3. 删除这些未锁定的打样单记录
            List<Long> orderIds = unlockedOrders.stream()
                .map(EngineerSampleOrder::getId)
                .collect(java.util.stream.Collectors.toList());
            engineerSampleOrderMapper.batchDeleteEngineerSampleOrders(orderIds);

            log.info("成功清空工程师[{}]的{}个未锁定打样单", userId, orderIds.size());
            return true;
        } catch (Exception e) {
            log.error("清空未锁定打样单失败，工程师[{}]，指定日期[{}]，错误：{}",
                userId, fromDate, e.getMessage(), e);
            throw new ServiceException("清空未锁定打样单失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeEngineer(ChangeEngineerDTO changeEngineerDTO) {
        if (changeEngineerDTO == null) {
            throw new ServiceException("更改工程师参数对象不能为空");
        }
        Long sampleOrderId = changeEngineerDTO.getSampleOrderId();
        Long oldEngineerId = changeEngineerDTO.getOldEngineerId();
        Long newEngineerId = changeEngineerDTO.getNewEngineerId();
        Date scheduledDate = changeEngineerDTO.getScheduledDate();
        int adjustWorkSchedule = changeEngineerDTO.getAdjustWorkSchedule();

        // 参数验证
        if (sampleOrderId == null) {
            throw new ServiceException("打样单关联表ID不能为空");
        }
        if (newEngineerId == null) {
            throw new ServiceException("目标工程师ID不能为空");
        }
        if (scheduledDate == null) {
            throw new ServiceException("排单日期不能为空");
        }

        try {
            // 1. 检查打样单关联表是否存在
            EngineerSampleOrder existingOrder = engineerSampleOrderMapper.selectEngineerSampleOrderById(sampleOrderId);
            if (existingOrder == null) {
                throw new ServiceException("打样单关联记录不存在，ID: " + sampleOrderId);
            }

            existingOrder.setAdjustWorkSchedule(adjustWorkSchedule);
            Integer associationStatus = existingOrder.getAssociationStatus();
            if (!(associationStatus != null && associationStatus == 0)){
                // 是已分配的打样单，进行调整工程师
                if (oldEngineerId == null) {
                    throw new ServiceException("原工程师ID不能为空");
                }
                log.info("开始更改打样单工程师，打样单关联ID[{}]，原工程师[{}] -> 新工程师[{}]，排单日期[{}]，是否调整工作安排[{}]",
                        sampleOrderId, oldEngineerId, newEngineerId, scheduledDate, adjustWorkSchedule);

                // 验证原工程师ID是否匹配
                if (!oldEngineerId.equals(existingOrder.getUserId())) {
                    throw new ServiceException("原工程师ID不匹配，期望: " + oldEngineerId + "，实际: " + existingOrder.getUserId());
                }

                // 检查打样单是否已锁定
                if (existingOrder.getIsLocked() != null && existingOrder.getIsLocked() == 1) {
                    throw new ServiceException("打样单已锁定，无法更改工程师");
                }
                log.info("找到打样单关联记录，原排单日期[{}]，预估工时[{}]小时",
                        existingOrder.getScheduledDate(), existingOrder.getEstimatedManHours());

                // 2. 返回旧工程师的对应工时
                restoreWorkHoursForOrder(existingOrder);
                log.info("已返回原工程师[{}]的工时[{}]小时", oldEngineerId, existingOrder.getEstimatedManHours());

                // 2.1 退回旧的工时后，将该工程师的排期重新整理。
            }else {
                // 未分配的打样单手动分配指定工程师
                log.info("开始分配打样单工程师，打样单关联ID[{}]，目标工程师[{}]，排单日期[{}]，是否调整工作安排[{}]",
                        sampleOrderId, newEngineerId, scheduledDate, adjustWorkSchedule);
                existingOrder.setAssociationStatus(1);
            }

            // 3. 根据adjustWorkSchedule状态进行不同处理
            if (adjustWorkSchedule == 1) {
                // 需要重新调整工作安排，进行排单/插单操作
                return handleChangeEngineerWithScheduleAdjustment(existingOrder, newEngineerId, scheduledDate);
            } else {
                // 不需要重新排单，直接更改
                return handleChangeEngineerWithoutScheduleAdjustment(existingOrder, newEngineerId, scheduledDate);
            }

        } catch (Exception e) {
            log.error("更改打样单工程师失败，打样单关联ID[{}]，原工程师[{}] -> 新工程师[{}]，错误：{}",
                sampleOrderId, oldEngineerId, newEngineerId, e.getMessage(), e);
            throw new ServiceException("更改打样单工程师失败: " + e.getMessage());
        }
    }

    /**
     * 处理需要重新调整工作安排的工程师更改
     * @param existingOrder 现有的打样单关联记录
     * @param newEngineerId 新工程师ID
     * @param scheduledDate 新的排单日期
     * @return 是否成功
     */
    private Boolean handleChangeEngineerWithScheduleAdjustment(EngineerSampleOrder existingOrder, Long newEngineerId, Date scheduledDate) {
        try {
            log.info("执行重新调整工作安排的工程师更改");

            // 创建新的打样单关联对象
            EngineerSampleOrder newOrder = engineerSampleOrderMapper.selectEngineerSampleOrderById(existingOrder.getId());

            // 删除原有记录
            engineerSampleOrderMapper.deleteEngineerSampleOrderByIds(new Long[]{existingOrder.getId()});

            newOrder.setId(null);
            newOrder.setUserId(newEngineerId);
            newOrder.setScheduledDate(scheduledDate);
//            newOrder.setSampleOrderCode(existingOrder.getSampleOrderCode());
//            newOrder.setEstimatedManHours(existingOrder.getEstimatedManHours());
//            newOrder.setDifficultyLevelId(existingOrder.getDifficultyLevelId());
//            newOrder.setCategoryId(existingOrder.getCategoryId());
//            newOrder.setStartDate(existingOrder.getStartDate());
//            newOrder.setEndDate(existingOrder.getEndDate());
//            newOrder.setSampleOrderRemark(existingOrder.getSampleOrderRemark());
//            newOrder.setAdjustWorkSchedule(existingOrder.getAdjustWorkSchedule());

            // 使用现有的排单/插单逻辑
            boolean result = assignWorkOrderToEngineer(newOrder);

            if (result) {
                log.info("重新调整工作安排成功，新工程师[{}]，新排单日期[{}]", newEngineerId, scheduledDate);
            }

            return result;
        } catch (Exception e) {
            log.error("重新调整工作安排失败：{}", e.getMessage(), e);
            throw new ServiceException("重新调整工作安排失败: " + e.getMessage());
        }
    }

    /**
     * 处理不需要重新调整工作安排的工程师更改
     * @param existingOrder 现有的打样单关联记录
     * @param newEngineerId 新工程师ID
     * @param scheduledDate 新的排单日期
     * @return 是否成功
     */
    private Boolean handleChangeEngineerWithoutScheduleAdjustment(EngineerSampleOrder existingOrder, Long newEngineerId, Date scheduledDate) {
        try {
            log.info("执行不重新调整工作安排的工程师更改");

            // 直接更改打样单关联表的信息
            existingOrder.setUserId(newEngineerId);
            existingOrder.setScheduledDate(scheduledDate);
            existingOrder.setEstimatedManHours(BigDecimal.ZERO); // 将预估工时更改为0
            existingOrder.setCompletionStatus(0); // 初始为未开始状态
            existingOrder.setIsLocked(0); // 未锁定状态
            existingOrder.setUpdateTime(DateUtils.getNowDate());
            existingOrder.setUpdateBy(SecurityUtils.getUsername());
            existingOrder.setAdjustWorkSchedule(existingOrder.getAdjustWorkSchedule());

            // 更新记录
            this.updateEngineerSampleOrder(existingOrder);

            log.info("直接更改成功，新工程师[{}]，新排单日期[{}]，预估工时设为0", newEngineerId, scheduledDate);
            return true;
        } catch (Exception e) {
            log.error("直接更改工程师失败：{}", e.getMessage(), e);
            throw new ServiceException("直接更改工程师失败: " + e.getMessage());
        }
    }

    /**
     * 获取组别项目汇总数据
     * @return 组别项目汇总数据列表
     */
    @Override
    public List<GroupSummaryVo> getGroupSummary(DashboardQueryDTO dashboardQueryDTO) {
        return engineerSampleOrderMapper.selectGroupSummary(dashboardQueryDTO);
    }

    /**
     * 获取工程师项目汇总数据
     * @return 工程师项目汇总数据列表
     */
    @Override
    public List<EngineerSummaryVo> getEngineerSummary(DashboardQueryDTO dashboardQueryDTO) {
        return engineerSampleOrderMapper.selectEngineerSummary(dashboardQueryDTO);
    }

    /**
     * 获取打样进度明细数据
     * @return 打样进度明细数据列表
     */
    @Override
    public List<SampleDetailVo> getSampleDetail(DashboardQueryDTO dashboardQueryDTO) {
        return engineerSampleOrderMapper.selectSampleDetail(dashboardQueryDTO);
    }

    /**
     * 获取报警信息数据
     * @return 报警信息数据列表
     */
    @Override
    public List<AlertInfoVo> getAlertInfo(DashboardQueryDTO dashboardQueryDTO) {
        return engineerSampleOrderMapper.selectAlertInfo(dashboardQueryDTO);
    }

    /**
     * 获取当前用户可查看/操作的部门ID和当前登录的用户ID
     *
     * @return 用户部门信息VO
     */
    @Override
    public UserDeptInfoVo getCurrentUserDeptInfo() {
        Long userId = SecurityUtils.getUserId();
        UserDeptInfoVo userDeptInfoVo = new UserDeptInfoVo();
        userDeptInfoVo.setUserId(userId);
        if(!SecurityUtils.isFormulaAdmin(SecurityUtils.getUserId())){
            // 获取角色列表
            List<String> rolesData = sysUserMapper.selectUserRoleByRoleCodes(userId);
            if(rolesData!=null && !rolesData.isEmpty() && rolesData.contains("20250701102456648")){
                // 排班管理员
                userDeptInfoVo.setDeptId(609L);
            }else if(rolesData!=null && !rolesData.isEmpty() && rolesData.contains("20250701102456647")){
                // 排班人员 获取所在一级部门
                List<Long> deptIdList = sysHrUserMapper.queryUserFormulaRoleDeptDataList(userId);
                if(deptIdList!=null && deptIdList.size() > 0){
                    if(deptIdList.contains(7L) && deptIdList.contains(8L)){
                        // 拥有宜侬研发和瀛彩研发
                        userDeptInfoVo.setDeptId(609L);
                    } else if(deptIdList.contains(7L)){
                        // 拥有宜侬研发
                        userDeptInfoVo.setDeptId(464L);
                    } else if (!(deptIdList.contains(8L) || deptIdList.contains(32L))) {
                        // 拥有瀛彩研发
                        userDeptInfoVo.setDeptId(465L);
                    }
                } else {
                    // 兜底，如果没有设置所在一级目录就设置当前用户的组别
                    userDeptInfoVo.setDeptId(SecurityUtils.getDeptId());
                }
            }else {
                //工程师
                // 获取当用户所在的部门
                userDeptInfoVo.setDeptId(SecurityUtils.getDeptId());
            }
        }else {
            // 系统管理员
            userDeptInfoVo.setDeptId(609L);
            userDeptInfoVo.setUserId(null);
        }
        return userDeptInfoVo;
    }

    /**
     * 获取当前用户可查看/操作的部门ID和当前登录的用户ID（小程序api使用）
     *
     * @return 用户部门信息VO
     */
    @Override
    public UserDeptInfoVo getApiCurrentUserDeptInfo(SysUser sysUser) {
        // 获取用户id
        Long userId = sysUser.getUserId();

        UserDeptInfoVo userDeptInfoVo = new UserDeptInfoVo();
        userDeptInfoVo.setUserId(userId);
        if(!SecurityUtils.isFormulaAdmin(userId)){
            // 获取角色列表
            List<String> rolesData = sysUserMapper.selectUserRoleByRoleCodes(userId);
            if(rolesData!=null && !rolesData.isEmpty() && rolesData.contains("20250701102456648")){
                // 排班管理员
                userDeptInfoVo.setDeptId(609L);
            }else if(rolesData!=null && !rolesData.isEmpty() && rolesData.contains("20250701102456647")){
                // 排班人员 获取所在一级部门
                List<Long> deptIdList = sysHrUserMapper.queryUserFormulaRoleDeptDataList(userId);
                if(deptIdList!=null && deptIdList.size() > 0){
                    if(deptIdList.contains(7L) && deptIdList.contains(8L)){
                        // 拥有宜侬研发和瀛彩研发
                        userDeptInfoVo.setDeptId(609L);
                    } else if(deptIdList.contains(7L)){
                        // 拥有宜侬研发
                        userDeptInfoVo.setDeptId(464L);
                    } else if (!(deptIdList.contains(8L) || deptIdList.contains(32L))) {
                        // 拥有瀛彩研发
                        userDeptInfoVo.setDeptId(465L);
                    }
                } else {
                    // 兜底，如果没有设置所在一级目录就设置当前用户的组别
                    userDeptInfoVo.setDeptId(sysUser.getDeptId());
                }
            }else {
                //工程师
                // 获取当用户所在的部门
                userDeptInfoVo.setDeptId(sysUser.getDeptId());
            }
        }else {
            // 系统管理员
            userDeptInfoVo.setDeptId(609L);
            userDeptInfoVo.setUserId(null);
        }
        return userDeptInfoVo;
    }


    /**
     * 保存分配异常的打样单信息（分配失败时调用）
     *
     * @param engineerSampleOrder 包含打样单编号、失败原因等关键信息
     * @return 保存结果
     */
    @Override
    public Boolean saveFailedAssignment(EngineerSampleOrder engineerSampleOrder) {

        try {
            if (StringUtils.isNull(engineerSampleOrder.getAdjustWorkSchedule())) {
                engineerSampleOrder.setAdjustWorkSchedule(1);
            }

            // 设置关联状态为失败(0)
            engineerSampleOrder.setAssociationStatus(0);

            // 获取标准工时
            int standardManHours = engineerSampleOrder.getEstimatedManHours().setScale(0, RoundingMode.CEILING).intValue();
            // 计算查询时间范围
            int range = (standardManHours - 1) / 8;
            // 计算最晚开始日期
            engineerSampleOrder.setLatestStartTime(flowTools.findTargetWorkday(engineerSampleOrder.getEndDate(),range,-1));

            // 判断如果这个打样单是否存在
            // 1. 检查是否已存在关联记录
            EngineerSampleOrder existingOrder = null;
            if (engineerSampleOrder.getProjectOrderId() != null) {
                existingOrder = engineerSampleOrderMapper.selectByProjectOrderId(engineerSampleOrder.getProjectOrderId());
            }

            if (existingOrder != null) {
                // 更新现有记录
                log.info("发现已存在的关联记录，ID[{}]，执行更新操作", existingOrder.getId());
                if (existingOrder.getAssociationStatus() == 1){
                    // 如果是历史记录中是已分配的状态。退回对应的预估工时
                    restoreWorkHoursForOrder(existingOrder);
                }
                // 更改关联表的失败信息
                engineerSampleOrder.setId(existingOrder.getId());// 设置ID用于更新
                engineerSampleOrder.setUpdateTime(DateUtils.getNowDate());
                engineerSampleOrder.setUpdateBy(SecurityUtils.getUsername());

                // 保留原有的创建信息
                engineerSampleOrder.setCreateTime(existingOrder.getCreateTime());
                engineerSampleOrder.setCreateBy(existingOrder.getCreateBy());

                this.updateEngineerSampleOrder(engineerSampleOrder);
            } else {
                // 如果不存在，插入新异常情况的记录
                log.info("插入新异常情况的记录，打样单编号[{}]，状态设置为失败", engineerSampleOrder.getSampleOrderCode());
                insertEngineerSampleOrder(engineerSampleOrder);
            }
            return true;
        } catch (Exception e) {
            log.error("保存分配异常的打样单信息：{}", e.getMessage(), e);
            throw new ServiceException("保存分配异常的打样单信息: " + e.getMessage());
        }
    }


    @Override
    public Map<String,String> getzrqAndfpqByprojectNo(String projectNo){
        Map <String,String> map = new HashMap<>();
        Set<String> zrqSet = new HashSet<>();
        Set<String> allocatorSet = new HashSet<>();
        if (StringUtils.isNotNull(projectNo)){
            //通过项目编码获取项目编号
            Long projectId = projectMapper.gettProjectInfoByProjectNo(projectNo);
            if (projectId != null && projectId > 0) {
                //通过项目编号获取主容器编号和分配器编号
                List<Map<String, String>>  tProjectBcInfoList= projectMapper.gettProjectBcInfoByProjectId(projectId);
                if (tProjectBcInfoList != null && tProjectBcInfoList.size() > 0){
                    //去重
                    tProjectBcInfoList.stream().forEach(tProjectBcInfo -> {
                        if (StringUtils.isNotNull(tProjectBcInfo.get("zrq_type")) ){
                            zrqSet.add(tProjectBcInfo.get("zrq_type"));
                        }
                        if (StringUtils.isNotNull(tProjectBcInfo.get("allocator")))
                            allocatorSet.add(tProjectBcInfo.get("allocator"));
                    });
                }

                if (zrqSet != null && zrqSet.size() > 0){
                    String zrqType = zrqSet.stream()
                            .collect(Collectors.joining(","));
                    //调用缓存获取主容器中文名称
                    if (StringUtils.isNotNull(zrqType)){
                        String zrqName = DictUtils.getDictLabel("bc-zrq", zrqType, ",");
                        if (StringUtils.isNotNull(zrqName)){
                            map.put("zrqName", zrqName);
                        }
                    }

                }

                if (allocatorSet != null && allocatorSet.size() > 0){
                    String allocator = allocatorSet.stream()
                            .collect(Collectors.joining(","));
                    //调用缓存获取分配器中文名称
                    if (StringUtils.isNotNull(allocator)){
                        String fpqName = DictUtils.getDictLabel("bc-fpq", allocator, ",");
                        if (StringUtils.isNotNull(fpqName)){
                            map.put("fpqName", fpqName);
                        }
                    }

                }

            }
        }
        return map;
    }

    //【】

}
