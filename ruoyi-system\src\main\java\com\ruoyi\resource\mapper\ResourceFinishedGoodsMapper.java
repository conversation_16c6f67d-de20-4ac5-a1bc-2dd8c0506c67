package com.ruoyi.resource.mapper;

import com.ruoyi.resource.domain.ResourceFinishedGoods;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 成品库商品Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-06
 */
public interface ResourceFinishedGoodsMapper
{

    public ResourceFinishedGoods selectResourceFinishedGoodsById(Long id);

    public List<ResourceFinishedGoods> selectResourceFinishedGoodsList(ResourceFinishedGoods resourceFinishedGoods);

    public int insertResourceFinishedGoods(ResourceFinishedGoods resourceFinishedGoods);

    public int updateResourceFinishedGoods(ResourceFinishedGoods resourceFinishedGoods);

    public int deleteResourceFinishedGoodsByIds(Long[] ids);

    void addNum(ResourceFinishedGoods resourceFinishedGoods);

    void delNum(ResourceFinishedGoods resourceFinishedGoods);

    ResourceFinishedGoods selectResourceFinishedGoodsDraft(ResourceFinishedGoods resourceFinishedGoods);

    void clearDraft(@Param("userId") Long userId);

    void updateBatchPriceJson(@Param("itemList") List<ResourceFinishedGoods> goods);

    String selectSerialNumber();

    List<ResourceFinishedGoods> selectResourceFinishedGoodsBaseList(ResourceFinishedGoods resourceFinishedGoods);

    public String selectOtherIcpnByProductCode(@Param("productCode") String productCode);

    List<ResourceFinishedGoods> selectSimilarListByProductName(ResourceFinishedGoods resourceFinishedGoods);

    void updateBomEnByProductCode(@Param("productCode") String productCode);

    Integer queryResourceFinishedCount(ResourceFinishedGoods resourceFinishedGoods);

    ResourceFinishedGoods selectResourceFinishedGoodsByProductCode(@Param("productCode") String productCode);
}
