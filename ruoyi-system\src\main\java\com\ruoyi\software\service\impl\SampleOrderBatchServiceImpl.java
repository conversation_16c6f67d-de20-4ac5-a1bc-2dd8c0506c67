package com.ruoyi.software.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.software.domain.EngineerSampleOrder;
import com.ruoyi.software.domain.SampleOrderBatch;
import com.ruoyi.software.mapper.SampleOrderBatchMapper;
import com.ruoyi.software.mapper.EngineerSampleOrderMapper;
import com.ruoyi.software.service.IExperimentRecordService;
import com.ruoyi.software.service.ISampleOrderBatchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

/**
 * 打样批次记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
public class SampleOrderBatchServiceImpl implements ISampleOrderBatchService 
{
    private static final Logger log = LoggerFactory.getLogger(SampleOrderBatchServiceImpl.class);

    @Autowired
    private SampleOrderBatchMapper sampleOrderBatchMapper;
    @Autowired
    private IExperimentRecordService experimentRecordService;
    @Autowired
    private EngineerSampleOrderMapper engineerSampleOrderMapper;

    /**
     * 查询打样批次记录
     * 
     * @param id 打样批次记录主键
     * @return 打样批次记录
     */
    @Override
    public SampleOrderBatch selectSampleOrderBatchById(Long id)
    {
        return sampleOrderBatchMapper.selectSampleOrderBatchById(id);
    }

    /**
     * 查询打样批次记录列表
     * 
     * @param sampleOrderBatch 打样批次记录
     * @return 打样批次记录
     */
    @Override
    public List<SampleOrderBatch> selectSampleOrderBatchList(SampleOrderBatch sampleOrderBatch)
    {
        return sampleOrderBatchMapper.selectSampleOrderBatchList(sampleOrderBatch);
    }

    /**
     * 新增打样批次记录
     * 
     * @param sampleOrderBatch 打样批次记录
     * @return 结果
     */
    @Override
    public int insertSampleOrderBatch(SampleOrderBatch sampleOrderBatch)
    {
        // 验证打样单状态是否允许编辑
        if (!checkSampleOrderStatusForEdit(sampleOrderBatch.getEngineerSampleOrderId())) {
            throw new RuntimeException("当前打样单状态不允许添加批次，只有进行中状态(completion_status=1)才可以编辑批次信息");
        }

        sampleOrderBatch.setCreateTime(DateUtils.getNowDate());
        return sampleOrderBatchMapper.insertSampleOrderBatch(sampleOrderBatch);
    }

    /**
     * 修改打样批次记录
     * 
     * @param sampleOrderBatch 打样批次记录
     * @return 结果
     */
    @Override
    public int updateSampleOrderBatch(SampleOrderBatch sampleOrderBatch)
    {
        // 验证打样单状态是否允许编辑
        if (!checkSampleOrderStatusForEdit(sampleOrderBatch.getEngineerSampleOrderId())) {
            throw new RuntimeException("当前打样单状态不允许修改批次，只有进行中状态(completion_status=1)才可以编辑批次信息");
        }

        sampleOrderBatch.setUpdateTime(DateUtils.getNowDate());
        return sampleOrderBatchMapper.updateSampleOrderBatch(sampleOrderBatch);
    }

    /**
     * 批量删除打样批次记录
     * 
     * @param ids 需要删除的打样批次记录主键
     * @return 结果
     */
    @Override
    public int deleteSampleOrderBatchByIds(Long[] ids)
    {
        // 验证每个批次对应的打样单状态
        for (Long id : ids) {
            SampleOrderBatch batch = sampleOrderBatchMapper.selectSampleOrderBatchById(id);
            if (batch != null && !checkSampleOrderStatusForEdit(batch.getEngineerSampleOrderId())) {
                throw new RuntimeException("当前打样单状态不允许删除批次，只有进行中状态(completion_status=1)才可以编辑批次信息");
            }
        }
        return sampleOrderBatchMapper.deleteSampleOrderBatchByIds(ids);
    }

    /**
     * 删除打样批次记录信息
     * 
     * @param id 打样批次记录主键
     * @return 结果
     */
    @Override
    public int deleteSampleOrderBatchById(Long id)
    {
        // 验证打样单状态是否允许删除
        SampleOrderBatch batch = sampleOrderBatchMapper.selectSampleOrderBatchById(id);
        if (batch != null && !checkSampleOrderStatusForEdit(batch.getEngineerSampleOrderId())) {
            throw new RuntimeException("当前打样单状态不允许删除批次，只有进行中状态(completion_status=1)才可以编辑批次信息");
        }
        return sampleOrderBatchMapper.deleteSampleOrderBatchById(id);
    }

    /**
     * 根据工程师打样单ID查询所有批次记录
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 批次记录列表
     */
    @Override
    public List<SampleOrderBatch> selectBatchesByOrderId(Long engineerSampleOrderId)
    {
        return sampleOrderBatchMapper.selectBatchesByOrderId(engineerSampleOrderId);
    }

    /**
     * 根据工程师打样单ID查询当前批次
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 当前批次记录
     */
    @Override
    public SampleOrderBatch selectCurrentBatchByOrderId(Long engineerSampleOrderId)
    {
        return sampleOrderBatchMapper.selectCurrentBatchByOrderId(engineerSampleOrderId);
    }

    /**
     * 开始新的打样批次
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @param remark 备注信息
     * @return 新创建的批次记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SampleOrderBatch startNewBatch(Long engineerSampleOrderId, String remark)
    {
        try {
            log.info("开始新的打样批次，工程师打样单ID：{}", engineerSampleOrderId);

            // 1. 检查是否已有进行中的批次（多批次并发模式下允许多个进行中批次）
            // 这里不再需要将其他批次设置为历史状态

            // 2. 获取下一个批次序号
            Integer nextBatchIndex = sampleOrderBatchMapper.selectNextBatchIndex(engineerSampleOrderId);

            // 3. 创建新的批次记录
            SampleOrderBatch newBatch = new SampleOrderBatch();
            newBatch.setEngineerSampleOrderId(engineerSampleOrderId);
            newBatch.setBatchIndex(nextBatchIndex);
            newBatch.setStartTime(DateUtils.getNowDate());
            newBatch.setBatchStatus(1); // 设置为进行中状态
            newBatch.setDelFlag(0);
            newBatch.setRemark(remark);
            newBatch.setCreateBy(SecurityUtils.getUsername());
            newBatch.setCreateTime(DateUtils.getNowDate());

            int result = sampleOrderBatchMapper.insertSampleOrderBatch(newBatch);

            // todo 更新关联表信息
            updateSampleOrderBatchStatistics(engineerSampleOrderId);

            if (result > 0) {
                log.info("成功创建新批次，批次序号：{}，批次ID：{}", nextBatchIndex, newBatch.getId());
                return newBatch;
            } else {
                throw new RuntimeException("创建新批次失败");
            }
        } catch (Exception e) {
            log.error("开始新批次失败，工程师打样单ID：{}，错误：{}", engineerSampleOrderId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 结束当前批次
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @param qualityEvaluation 质量评价
     * @param remark 备注信息
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean finishCurrentBatch(Long engineerSampleOrderId, String qualityEvaluation, String remark)
    {
        return finishCurrentBatch(engineerSampleOrderId, qualityEvaluation, remark, SecurityUtils.getUsername());
    }

    /**
     * 结束当前批次（小程序适配）
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @param qualityEvaluation 质量评价
     * @param remark 备注信息
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean finishCurrentBatch(Long engineerSampleOrderId, String qualityEvaluation, String remark, String nickName)
    {
        try {
            log.info("结束当前批次，工程师打样单ID：{}", engineerSampleOrderId);

            // 1. 查询当前批次
            SampleOrderBatch currentBatch = sampleOrderBatchMapper.selectCurrentBatchByOrderId(engineerSampleOrderId);
            if (currentBatch == null) {
                log.warn("未找到当前进行中的批次，工程师打样单ID：{}", engineerSampleOrderId);
                return false;
            }
            updateSampleOrderBatchStatistics(engineerSampleOrderId, nickName);
            return finishBatch(currentBatch.getId(), qualityEvaluation, remark);
        } catch (Exception e) {
            log.error("结束当前批次失败，工程师打样单ID：{}，错误：{}", engineerSampleOrderId, e.getMessage(), e);
            throw e;
        }
    }


    /**
     * 结束指定批次
     *
     * @param batchId 批次ID
     * @param qualityEvaluation 质量评价
     * @param remark 备注信息
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean finishBatch(Long batchId, String qualityEvaluation, String remark)
    {
        try {
            log.info("结束指定批次，批次ID：{}", batchId);

            // 1. 查询批次信息
            SampleOrderBatch batch = sampleOrderBatchMapper.selectSampleOrderBatchById(batchId);
            if (batch == null) {
                log.warn("未找到指定批次，批次ID：{}", batchId);
                return false;
            }

            // 2. 计算实际工时
            BigDecimal actualHours = calculateBatchActualHours(batchId);

            // 3. 更新批次信息
            batch.setEndTime(DateUtils.getNowDate());
            batch.setActualManHours(actualHours);
            batch.setQualityEvaluation(qualityEvaluation);
//            batch.setIsCurrentBatch(0); // 设置为历史批次
            batch.setUpdateBy(SecurityUtils.getUsername());
            batch.setUpdateTime(DateUtils.getNowDate());
            if (remark != null && !remark.trim().isEmpty()) {
                batch.setRemark(remark);
            }

            int result = sampleOrderBatchMapper.updateSampleOrderBatch(batch);
            if (result > 0) {
                log.info("成功结束批次，批次ID：{}，实际工时：{}小时", batchId, actualHours);
                return true;
            } else {
                throw new RuntimeException("更新批次信息失败");
            }
        } catch (Exception e) {
            log.error("结束批次失败，批次ID：{}，错误：{}", batchId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 检查是否有进行中的批次
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 是否有进行中的批次
     */
    @Override
    public Boolean hasCurrentBatch(Long engineerSampleOrderId)
    {
        List<SampleOrderBatch> activeBatches = sampleOrderBatchMapper.selectActiveBatchesByOrderId(engineerSampleOrderId);
        return activeBatches != null && !activeBatches.isEmpty();
    }

    /**
     * 获取打样单的总批次数
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 总批次数
     */
    @Override
    public Integer getTotalBatchCount(Long engineerSampleOrderId)
    {
        List<SampleOrderBatch> batches = sampleOrderBatchMapper.selectBatchesByOrderId(engineerSampleOrderId);
        return batches != null ? batches.size() : 0;
    }

    /**
     * 计算批次的实际工时
     *
     * @param batchId 批次ID
     * @return 实际工时
     */
    @Override
    public BigDecimal calculateBatchActualHours(Long batchId)
    {
        SampleOrderBatch batch = sampleOrderBatchMapper.selectSampleOrderBatchById(batchId);
        if (batch == null || batch.getStartTime() == null) {
            return BigDecimal.ZERO;
        }

        Date startTime = batch.getStartTime();
        Date endTime = batch.getEndTime() != null ? batch.getEndTime() : DateUtils.getNowDate();

        // 计算时间差（毫秒）
        long durationMillis = endTime.getTime() - startTime.getTime();

        // 转换为小时并保留两位小数
        double hours = durationMillis / (1000.0 * 60 * 60);
        return BigDecimal.valueOf(hours).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 更新工程师打样单的批次统计信息
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSampleOrderBatchStatistics(Long engineerSampleOrderId)
    {
        return updateSampleOrderBatchStatistics(engineerSampleOrderId, SecurityUtils.getUsername());
    }

    /**
     * 更新工程师打样单的批次统计信息
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @param nickName 工程师昵称
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSampleOrderBatchStatistics(Long engineerSampleOrderId, String nickName)
    {
        try {
            log.info("更新工程师打样单批次统计信息，ID：{}", engineerSampleOrderId);

            // 1. 获取工程师打样单记录
            EngineerSampleOrder engineerSampleOrder = engineerSampleOrderMapper.selectEngineerSampleOrderById(engineerSampleOrderId);
            if (engineerSampleOrder == null) {
                log.warn("未找到工程师打样单记录，ID：{}", engineerSampleOrderId);
                return false;
            }

            // 2. 获取所有批次记录
            List<SampleOrderBatch> allBatches = selectBatchesByOrderId(engineerSampleOrderId);

            // 3. 计算统计信息
            Integer totalBatches = allBatches.size();
            Integer currentBatch = 0;
            BigDecimal totalActualHours = BigDecimal.ZERO;

            // 查找当前进行中的批次
            SampleOrderBatch currentBatchRecord = selectCurrentBatchByOrderId(engineerSampleOrderId);
            if (currentBatchRecord != null) {
                currentBatch = currentBatchRecord.getBatchIndex();
            }

            // 计算总实际工时（只计算已结束的批次）
            for (SampleOrderBatch batch : allBatches) {
                if (batch.getActualManHours() != null) {
                    totalActualHours = totalActualHours.add(batch.getActualManHours());
                }
            }

            // 4. 更新工程师打样单的统计字段
            engineerSampleOrder.setTotalBatches(totalBatches);
            engineerSampleOrder.setCurrentBatch(currentBatch);

            // 如果打样单已完成，更新总实际工时
            if (engineerSampleOrder.getCompletionStatus() != null && engineerSampleOrder.getCompletionStatus() == 2) {
                engineerSampleOrder.setActualManHours(totalActualHours);

                // 更新实际开始时间和结束时间（基于第一个和最后一个批次）
                if (!allBatches.isEmpty()) {
                    // 按批次序号排序
                    allBatches.sort((b1, b2) -> Integer.compare(b1.getBatchIndex(), b2.getBatchIndex()));

                    SampleOrderBatch firstBatch = allBatches.get(0);
                    SampleOrderBatch lastBatch = allBatches.get(allBatches.size() - 1);

                    if (firstBatch.getStartTime() != null && engineerSampleOrder.getActualStartTime() == null) {
                        engineerSampleOrder.setActualStartTime(firstBatch.getStartTime());
                    }

                    if (lastBatch.getEndTime() != null && engineerSampleOrder.getActualFinishTime() == null) {
                        engineerSampleOrder.setActualFinishTime(lastBatch.getEndTime());
                    }
                }
            }

            // 5. 保存更新
            engineerSampleOrder.setUpdateTime(DateUtils.getNowDate());
            engineerSampleOrder.setUpdateBy(nickName);
            engineerSampleOrderMapper.updateEngineerSampleOrder(engineerSampleOrder);

            log.info("成功更新工程师打样单批次统计信息，ID：{}，总批次：{}，当前批次：{}，总工时：{}小时",
                    engineerSampleOrderId, totalBatches, currentBatch, totalActualHours);
            return true;
        } catch (Exception e) {
            log.error("更新工程师打样单批次统计信息失败，ID：{}，错误：{}", engineerSampleOrderId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查打样单状态是否允许编辑批次
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 是否允许编辑
     */
    @Override
    public Boolean checkSampleOrderStatusForEdit(Long engineerSampleOrderId)
    {
        try {
            // 获取工程师打样单记录
            EngineerSampleOrder engineerSampleOrder = engineerSampleOrderMapper.selectEngineerSampleOrderById(engineerSampleOrderId);
            if (engineerSampleOrder == null) {
                log.warn("未找到工程师打样单记录，ID：{}", engineerSampleOrderId);
                return false;
            }

            // 检查completion_status是否为1（进行中）
            Integer completionStatus = engineerSampleOrder.getCompletionStatus();
            boolean canEdit = completionStatus != null && completionStatus == 1;

            log.debug("打样单状态检查，ID：{}，completion_status：{}，是否可编辑：{}",
                engineerSampleOrderId, completionStatus, canEdit);

            return canEdit;
        } catch (Exception e) {
            log.error("检查打样单状态失败，ID：{}，错误：{}", engineerSampleOrderId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 查询指定打样单的所有进行中批次
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 进行中的批次记录列表
     */
    @Override
    public List<SampleOrderBatch> selectActiveBatchesByOrderId(Long engineerSampleOrderId)
    {
        return sampleOrderBatchMapper.selectActiveBatchesByOrderId(engineerSampleOrderId);
    }

    /**
     * 独立开始指定批次
     *
     * @param batchId 批次ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean startBatch(Long batchId)
    {
        try {
            log.info("独立开始指定批次，批次ID：{}", batchId);

            // 1. 查询批次信息
            SampleOrderBatch batch = sampleOrderBatchMapper.selectSampleOrderBatchById(batchId);
            if (batch == null) {
                return false;
            }

            // 2. 检查批次状态
            if (batch.getBatchStatus() != null && batch.getBatchStatus() == 1) {
                log.warn("批次已经是进行中状态，批次ID：{}", batchId);
                return true;
            }

            // 3. 更新批次状态为进行中
            batch.setBatchStatus(1);
            batch.setStartTime(DateUtils.getNowDate());
            batch.setUpdateTime(DateUtils.getNowDate());
            batch.setUpdateBy(SecurityUtils.getUsername());

            int result = sampleOrderBatchMapper.updateSampleOrderBatch(batch);
            if (result > 0) {
                log.info("成功开始批次，批次ID：{}", batchId);
                // 更新打样单统计信息
                updateSampleOrderBatchStatistics(batch.getEngineerSampleOrderId());
                return true;
            } else {
                log.warn("开始批次失败，批次ID：{}", batchId);
                return false;
            }
        } catch (Exception e) {
            log.error("独立开始批次失败，批次ID：{}，错误：{}", batchId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 独立结束指定批次（重载方法，支持实验室编号）
     *
     * @param batchId 批次ID
     * @param qualityEvaluation 质量评价
     * @param remark 备注信息
     * @param laboratoryCode 实验室编号
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean finishBatch(Long batchId, String qualityEvaluation, String remark, String laboratoryCode)
    {
        return finishBatch(batchId, qualityEvaluation, remark, laboratoryCode, SecurityUtils.getUsername());
    }

    /**
     * 独立结束指定批次（重载方法，支持实验室编号，适配小程序）
     *
     * @param batchId 批次ID
     * @param qualityEvaluation 质量评价
     * @param remark 备注信息
     * @param laboratoryCode 实验室编号
     * @param nickName 登录用户昵称
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean finishBatch(Long batchId, String qualityEvaluation, String remark, String laboratoryCode, String nickName)
    {
        try {
            log.info("独立结束指定批次，批次ID：{}", batchId);

            // 1. 查询批次信息
            SampleOrderBatch batch = sampleOrderBatchMapper.selectSampleOrderBatchById(batchId);
            if (batch == null) {
                return false;
            }

            // 2. 检查批次状态
            if (batch.getBatchStatus() != null && batch.getBatchStatus() == 2) {
                log.warn("批次已经是已完成状态，批次ID：{}", batchId);
                return true;
            }

            // 3. 计算实际工时
            Date endTime = DateUtils.getNowDate();
            BigDecimal actualHours = calculateBatchActualHours(batch.getId());

            // 4. 更新批次信息
            batch.setEndTime(endTime);
            batch.setActualManHours(actualHours);
            batch.setQualityEvaluation(qualityEvaluation);
            batch.setRemark(remark);
            batch.setLaboratoryCode(laboratoryCode); // 设置实验室编号
            batch.setBatchStatus(2); // 设置为已完成状态
            batch.setUpdateTime(endTime);
            batch.setUpdateBy(nickName);

            int result = sampleOrderBatchMapper.updateSampleOrderBatch(batch);
            if (result > 0) {
                log.info("成功结束批次，批次ID：{}，实际工时：{}小时", batchId, actualHours);
                // 更新打样单统计信息
                updateSampleOrderBatchStatistics(batch.getEngineerSampleOrderId(), nickName);
                return true;
            } else {
                log.warn("结束批次失败，批次ID：{}", batchId);
                return false;
            }
        } catch (Exception e) {
            log.error("独立结束批次失败，批次ID：{}，错误：{}", batchId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 结束所有进行中的批次
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean finishAllActiveBatches(Long engineerSampleOrderId)
    {
        return finishAllActiveBatches(engineerSampleOrderId, SecurityUtils.getUsername());
    }

    /**
     * 结束所有进行中的批次
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @param nickName 登录用户昵称
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean finishAllActiveBatches(Long engineerSampleOrderId, String nickName)
    {
        try {
            log.info("结束所有进行中的批次，工程师打样单ID：{}", engineerSampleOrderId);

            // 1. 查询所有进行中的批次
            List<SampleOrderBatch> activeBatches = selectActiveBatchesByOrderId(engineerSampleOrderId);
            if (activeBatches == null || activeBatches.isEmpty()) {
                log.info("没有进行中的批次需要结束，工程师打样单ID：{}", engineerSampleOrderId);
                return true;
            }

            // 2. 逐个结束批次，确保正确计算工时
            Date endTime = DateUtils.getNowDate();
            int successCount = 0;

            for (SampleOrderBatch batch : activeBatches) {
                try {
                    // 计算实际工时
                    BigDecimal actualHours = calculateBatchActualHours(batch.getId());

                    // 更新批次信息
                    batch.setEndTime(endTime);
                    batch.setActualManHours(actualHours);
                    batch.setBatchStatus(2); // 设置为已完成状态
                    batch.setUpdateTime(endTime);
                    batch.setUpdateBy(nickName);

                    int result = sampleOrderBatchMapper.updateSampleOrderBatch(batch);
                    if (result > 0) {
                        successCount++;
                        log.info("成功结束批次，批次ID：{}，实际工时：{}小时", batch.getId(), actualHours);
                    } else {
                        log.warn("结束批次失败，批次ID：{}", batch.getId());
                    }
                } catch (Exception e) {
                    log.error("结束批次失败，批次ID：{}，错误：{}", batch.getId(), e.getMessage());
                }
            }

            if (successCount > 0) {
                log.info("成功结束{}个进行中的批次，工程师打样单ID：{}", successCount, engineerSampleOrderId);
                // 更新打样单统计信息
                updateSampleOrderBatchStatistics(engineerSampleOrderId, nickName);
                return true;
            } else {
                log.warn("结束进行中批次失败，工程师打样单ID：{}", engineerSampleOrderId);
                return false;
            }
        } catch (Exception e) {
            log.error("结束所有进行中批次失败，工程师打样单ID：{}，错误：{}", engineerSampleOrderId, e.getMessage(), e);
            throw e;
        }
    }
}
