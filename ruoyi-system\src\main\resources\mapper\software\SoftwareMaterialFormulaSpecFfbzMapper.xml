<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.software.mapper.SoftwareMaterialFormulaSpecFfbzMapper">

    <sql id="selectSoftwareMaterialFormulaSpecFfbzVo">
        select ID, FORMULA_ID, WAIGUAN, YANSE, QIWEI, SHIYONGGAN, BIZHONG, PH, NIANDU, YINGDU, DIELUOCESHI, TUCACESHI, DUANLIEQIANGDU, RONGDIAN, SHUSHUIXING, XIDU, ZIDINGYI1, ZIDINGYI2, REMARK, OPERATOR, CREATED_TIME, IS_DEL, LAST_MODIFIED_TIME, NOTE, LIXIN from t_un_material_formula_spec_ffbz
    </sql>

    <select id="selectSoftwareMaterialFormulaSpecFfbzList" parameterType="SoftwareMaterialFormulaSpecFfbz" resultType="SoftwareMaterialFormulaSpecFfbz">
        <include refid="selectSoftwareMaterialFormulaSpecFfbzVo"/>
        <where>
            del_flag = 0
            <if test="formulaId != null "> and FORMULA_ID = #{formulaId}</if>
            <if test="waiguan != null  and waiguan != ''"> and WAIGUAN = #{waiguan}</if>
            <if test="yanse != null  and yanse != ''"> and YANSE = #{yanse}</if>
            <if test="qiwei != null  and qiwei != ''"> and QIWEI = #{qiwei}</if>
            <if test="shiyonggan != null  and shiyonggan != ''"> and SHIYONGGAN = #{shiyonggan}</if>
            <if test="bizhong != null  and bizhong != ''"> and BIZHONG = #{bizhong}</if>
            <if test="ph != null  and ph != ''"> and PH = #{ph}</if>
            <if test="niandu != null  and niandu != ''"> and NIANDU = #{niandu}</if>
            <if test="yingdu != null  and yingdu != ''"> and YINGDU = #{yingdu}</if>
            <if test="dieluoceshi != null  and dieluoceshi != ''"> and DIELUOCESHI = #{dieluoceshi}</if>
            <if test="tucaceshi != null  and tucaceshi != ''"> and TUCACESHI = #{tucaceshi}</if>
            <if test="duanlieqiangdu != null  and duanlieqiangdu != ''"> and DUANLIEQIANGDU = #{duanlieqiangdu}</if>
            <if test="rongdian != null  and rongdian != ''"> and RONGDIAN = #{rongdian}</if>
            <if test="shushuixing != null  and shushuixing != ''"> and SHUSHUIXING = #{shushuixing}</if>
            <if test="xidu != null  and xidu != ''"> and XIDU = #{xidu}</if>
            <if test="zidingyi1 != null  and zidingyi1 != ''"> and ZIDINGYI1 = #{zidingyi1}</if>
            <if test="zidingyi2 != null  and zidingyi2 != ''"> and ZIDINGYI2 = #{zidingyi2}</if>
            <if test="remark != null  and remark != ''"> and REMARK = #{remark}</if>
            <if test="operator != null  and operator != ''"> and OPERATOR = #{operator}</if>
            <if test="createdTime != null "> and CREATED_TIME = #{createdTime}</if>
            <if test="isDel != null "> and IS_DEL = #{isDel}</if>
            <if test="lastModifiedTime != null "> and LAST_MODIFIED_TIME = #{lastModifiedTime}</if>
            <if test="note != null  and note != ''"> and NOTE = #{note}</if>
            <if test="lixin != null  and lixin != ''"> and LIXIN = #{lixin}</if>
        </where>
        order by id desc
    </select>

    <select id="selectSoftwareMaterialFormulaSpecFfbzById" parameterType="Long" resultType="SoftwareMaterialFormulaSpecFfbz" >
        <include refid="selectSoftwareMaterialFormulaSpecFfbzVo"/>
        where ID = #{id}
    </select>

    <select id="selectSoftwareMaterialFormulaSpecFfbzDetailByFormulaId" parameterType="Long" resultType="SoftwareMaterialFormulaSpecFfbz" >
        <include refid="selectSoftwareMaterialFormulaSpecFfbzVo"/>
        where FORMULA_ID = #{id} order by id desc limit 1
    </select>

    <delete id="deleteSoftwareMaterialFormulaSpecFfbzByFormulaId" parameterType="java.lang.Long">
        delete from t_un_material_formula_spec_ffbz where FORMULA_ID = #{formulaId}
    </delete>


    <select id="queryFormulaSpecCount" resultType="java.lang.Integer">
        select count(*) from t_un_material_formula_spec
        where FORMULA_ID = #{formulaId}
          and CESHIYANGPIHAO like concat(#{prefix},'%')
    </select>

    <insert id="insertSoftwareMaterialFormulaSpec" parameterType="SoftwareMaterialFormulaSpecFfbz">
        insert into t_un_material_formula_spec(FORMULA_ID,`type`,JCXM_JSON,CESHIYANGPIHAO,OPERATOR,CREATED_TIME)
        values
        (#{formulaId},#{zidingyi1},#{jcxmJson},#{zidingyi2},#{operator},now())
    </insert>

    <insert id="updateSoftwareMaterialFormulaSpec" parameterType="SoftwareMaterialFormulaSpecFfbz">
        update t_un_material_formula_spec set JCXM_JSON = #{jcxmJson},LAST_MODIFIED_TIME = now(),NOTE = #{note} where id = #{id}
    </insert>

    <insert id="insertSoftwareMaterialFormulaSpecFfbz" parameterType="SoftwareMaterialFormulaSpecFfbz" useGeneratedKeys="true" keyProperty="id">
        insert into t_un_material_formula_spec_ffbz
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="formulaId != null">FORMULA_ID,</if>
            <if test="waiguan != null">WAIGUAN,</if>
            <if test="yanse != null">YANSE,</if>
            <if test="qiwei != null">QIWEI,</if>
            <if test="shiyonggan != null">SHIYONGGAN,</if>
            <if test="bizhong != null">BIZHONG,</if>
            <if test="ph != null">PH,</if>
            <if test="niandu != null">NIANDU,</if>
            <if test="yingdu != null">YINGDU,</if>
            <if test="dieluoceshi != null">DIELUOCESHI,</if>
            <if test="tucaceshi != null">TUCACESHI,</if>
            <if test="duanlieqiangdu != null">DUANLIEQIANGDU,</if>
            <if test="rongdian != null">RONGDIAN,</if>
            <if test="shushuixing != null">SHUSHUIXING,</if>
            <if test="xidu != null">XIDU,</if>
            <if test="zidingyi1 != null">ZIDINGYI1,</if>
            <if test="zidingyi2 != null">ZIDINGYI2,</if>
            <if test="remark != null">REMARK,</if>
            <if test="operator != null">OPERATOR,</if>
            <if test="createdTime != null">CREATED_TIME,</if>
            <if test="isDel != null">IS_DEL,</if>
            <if test="lastModifiedTime != null">LAST_MODIFIED_TIME,</if>
            <if test="note != null">NOTE,</if>
            <if test="lixin != null">LIXIN,</if>
            <if test="templateId != null">TEMPLATE_ID,</if>
            <if test="jcxmJson != null">JCXM_JSON,</if>
            <if test="wxId != null">wx_id,</if>
            <if test="inspectBasis != null">inspect_basis,</if>
            <if test="jlPl != null">jl_pl,</if>
            <if test="jqPl != null">jq_pl,</if>
            <if test="jlStandard != null">jl_standard,</if>
            <if test="mjStandard != null">mj_standard,</if>
            <if test="microbeRemark != null">microbe_remark,</if>
            <if test="periodOfValidity != null">period_Of_Validity,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="formulaId != null">#{formulaId},</if>
            <if test="waiguan != null">#{waiguan},</if>
            <if test="yanse != null">#{yanse},</if>
            <if test="qiwei != null">#{qiwei},</if>
            <if test="shiyonggan != null">#{shiyonggan},</if>
            <if test="bizhong != null">#{bizhong},</if>
            <if test="ph != null">#{ph},</if>
            <if test="niandu != null">#{niandu},</if>
            <if test="yingdu != null">#{yingdu},</if>
            <if test="dieluoceshi != null">#{dieluoceshi},</if>
            <if test="tucaceshi != null">#{tucaceshi},</if>
            <if test="duanlieqiangdu != null">#{duanlieqiangdu},</if>
            <if test="rongdian != null">#{rongdian},</if>
            <if test="shushuixing != null">#{shushuixing},</if>
            <if test="xidu != null">#{xidu},</if>
            <if test="zidingyi1 != null">#{zidingyi1},</if>
            <if test="zidingyi2 != null">#{zidingyi2},</if>
            <if test="remark != null">#{remark},</if>
            <if test="operator != null">#{operator},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="lastModifiedTime != null">#{lastModifiedTime},</if>
            <if test="note != null">#{note},</if>
            <if test="lixin != null">#{lixin},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="jcxmJson != null">#{jcxmJson},</if>
            <if test="wxId != null">#{wxId},</if>
            <if test="inspectBasis != null">#{inspectBasis},</if>
            <if test="jlPl != null">#{jlPl},</if>
            <if test="jqPl != null">#{jqPl},</if>
            <if test="jlStandard != null">#{jlStandard},</if>
            <if test="mjStandard != null">#{mjStandard},</if>
            <if test="microbeRemark != null">#{microbeRemark},</if>
            <if test="periodOfValidity != null">#{periodOfValidity},</if>
         </trim>
    </insert>

    <update id="updateSoftwareMaterialFormulaSpecFfbz" parameterType="SoftwareMaterialFormulaSpecFfbz">
        update t_un_material_formula_spec_ffbz
        <trim prefix="SET" suffixOverrides=",">
            <if test="formulaId != null">FORMULA_ID = #{formulaId},</if>
            <if test="waiguan != null">WAIGUAN = #{waiguan},</if>
            <if test="yanse != null">YANSE = #{yanse},</if>
            <if test="qiwei != null">QIWEI = #{qiwei},</if>
            <if test="shiyonggan != null">SHIYONGGAN = #{shiyonggan},</if>
            <if test="bizhong != null">BIZHONG = #{bizhong},</if>
            <if test="ph != null">PH = #{ph},</if>
            <if test="niandu != null">NIANDU = #{niandu},</if>
            <if test="yingdu != null">YINGDU = #{yingdu},</if>
            <if test="dieluoceshi != null">DIELUOCESHI = #{dieluoceshi},</if>
            <if test="tucaceshi != null">TUCACESHI = #{tucaceshi},</if>
            <if test="duanlieqiangdu != null">DUANLIEQIANGDU = #{duanlieqiangdu},</if>
            <if test="rongdian != null">RONGDIAN = #{rongdian},</if>
            <if test="shushuixing != null">SHUSHUIXING = #{shushuixing},</if>
            <if test="xidu != null">XIDU = #{xidu},</if>
            <if test="zidingyi1 != null">ZIDINGYI1 = #{zidingyi1},</if>
            <if test="zidingyi2 != null">ZIDINGYI2 = #{zidingyi2},</if>
            <if test="remark != null">REMARK = #{remark},</if>
            <if test="operator != null">OPERATOR = #{operator},</if>
            <if test="createdTime != null">CREATED_TIME = #{createdTime},</if>
            <if test="isDel != null">IS_DEL = #{isDel},</if>
            <if test="lastModifiedTime != null">LAST_MODIFIED_TIME = #{lastModifiedTime},</if>
            <if test="note != null">NOTE = #{note},</if>
            <if test="lixin != null">LIXIN = #{lixin},</if>
            <if test="templateId != null">TEMPLATE_ID = #{templateId},</if>
            <if test="jcxmJson != null">JCXM_JSON = #{jcxmJson},</if>
            <if test="wxId != null">wx_id = #{wxId},</if>
            <if test="inspectBasis != null">inspect_basis = #{inspectBasis},</if>
            <if test="jlPl != null">jl_pl = #{jlPl},</if>
            <if test="jqPl != null">jq_pl = #{jqPl},</if>
            <if test="jlStandard != null">jl_standard = #{jlStandard},</if>
            <if test="mjStandard != null">mj_standard = #{mjStandard},</if>
            <if test="microbeRemark != null">microbe_remark = #{microbeRemark},</if>
            <if test="periodOfValidity != null">period_Of_Validity = #{periodOfValidity},</if>
        </trim>
        where ID = #{id}
    </update>

    <update id="deleteSoftwareMaterialFormulaSpecFfbzByIds" parameterType="String">
        update  t_un_material_formula_spec_ffbz set del_flag = 2,update_time = now() where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
