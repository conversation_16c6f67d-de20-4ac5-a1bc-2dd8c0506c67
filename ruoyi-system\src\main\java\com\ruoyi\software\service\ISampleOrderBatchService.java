package com.ruoyi.software.service;

import com.ruoyi.software.domain.SampleOrderBatch;

import java.math.BigDecimal;
import java.util.List;

/**
 * 打样批次记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ISampleOrderBatchService 
{
    /**
     * 查询打样批次记录
     * 
     * @param id 打样批次记录主键
     * @return 打样批次记录
     */
    public SampleOrderBatch selectSampleOrderBatchById(Long id);

    /**
     * 查询打样批次记录列表
     * 
     * @param sampleOrderBatch 打样批次记录
     * @return 打样批次记录集合
     */
    public List<SampleOrderBatch> selectSampleOrderBatchList(SampleOrderBatch sampleOrderBatch);

    /**
     * 新增打样批次记录
     * 
     * @param sampleOrderBatch 打样批次记录
     * @return 结果
     */
    public int insertSampleOrderBatch(SampleOrderBatch sampleOrderBatch);

    /**
     * 修改打样批次记录
     * 
     * @param sampleOrderBatch 打样批次记录
     * @return 结果
     */
    public int updateSampleOrderBatch(SampleOrderBatch sampleOrderBatch);

    /**
     * 批量删除打样批次记录
     * 
     * @param ids 需要删除的打样批次记录主键集合
     * @return 结果
     */
    public int deleteSampleOrderBatchByIds(Long[] ids);

    /**
     * 删除打样批次记录信息
     * 
     * @param id 打样批次记录主键
     * @return 结果
     */
    public int deleteSampleOrderBatchById(Long id);

    /**
     * 根据工程师打样单ID查询所有批次记录
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 批次记录列表
     */
    public List<SampleOrderBatch> selectBatchesByOrderId(Long engineerSampleOrderId);

    /**
     * 根据工程师打样单ID查询当前批次
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 当前批次记录
     */
    public SampleOrderBatch selectCurrentBatchByOrderId(Long engineerSampleOrderId);

    /**
     * 开始新的打样批次
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @param remark 备注信息
     * @return 新创建的批次记录
     */
    public SampleOrderBatch startNewBatch(Long engineerSampleOrderId, String remark);

    /**
     * 结束当前批次
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @param qualityEvaluation 质量评价
     * @param remark 备注信息
     * @return 是否成功
     */
    public Boolean finishCurrentBatch(Long engineerSampleOrderId, String qualityEvaluation, String remark);

    /**
     * 结束当前批次(小程序适配)
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @param qualityEvaluation 质量评价
     * @param remark 备注信息
     * @return 是否成功
     */
    public Boolean finishCurrentBatch(Long engineerSampleOrderId, String qualityEvaluation, String remark, String nickName);

    /**
     * 结束指定批次
     *
     * @param batchId 批次ID
     * @param qualityEvaluation 质量评价
     * @param remark 备注信息
     * @return 是否成功
     */
    public Boolean finishBatch(Long batchId, String qualityEvaluation, String remark);

    /**
     * 检查是否有进行中的批次
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 是否有进行中的批次
     */
    public Boolean hasCurrentBatch(Long engineerSampleOrderId);

    /**
     * 获取打样单的总批次数
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 总批次数
     */
    public Integer getTotalBatchCount(Long engineerSampleOrderId);

    /**
     * 计算批次的实际工时
     *
     * @param batchId 批次ID
     * @return 实际工时
     */
    public BigDecimal calculateBatchActualHours(Long batchId);

    /**
     * 更新工程师打样单的批次统计信息
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 是否成功
     */
    public Boolean updateSampleOrderBatchStatistics(Long engineerSampleOrderId);

    /**
     * 更新工程师打样单的批次统计信息
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @param nickName 登录用户昵称
     * @return 是否成功
     */
    public Boolean updateSampleOrderBatchStatistics(Long engineerSampleOrderId, String nickName);


    /**
     * 检查打样单状态是否允许编辑批次
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 是否允许编辑
     */
    public Boolean checkSampleOrderStatusForEdit(Long engineerSampleOrderId);

    /**
     * 查询指定打样单的所有进行中批次
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 进行中的批次记录列表
     */
    public List<SampleOrderBatch> selectActiveBatchesByOrderId(Long engineerSampleOrderId);

    /**
     * 独立开始指定批次
     *
     * @param batchId 批次ID
     * @return 是否成功
     */
    public Boolean startBatch(Long batchId);

    /**
     * 独立结束指定批次
     *
     * @param batchId 批次ID
     * @param qualityEvaluation 质量评价
     * @param remark 备注信息
     * @param laboratoryCode 实验室编号
     * @return 是否成功
     */
    public Boolean finishBatch(Long batchId, String qualityEvaluation, String remark, String laboratoryCode);

    /**
     * 独立结束指定批次
     *
     * @param batchId 批次ID
     * @param qualityEvaluation 质量评价
     * @param remark 备注信息
     * @param laboratoryCode 实验室编号
     * @param nickName 登录用户昵称
     * @return 是否成功
     */
    public Boolean finishBatch(Long batchId, String qualityEvaluation, String remark, String laboratoryCode, String nickName);

    /**
     * 结束所有进行中的批次
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 是否成功
     */
    public Boolean finishAllActiveBatches(Long engineerSampleOrderId);

    /**
     * 结束所有进行中的批次(适配小程序)
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 是否成功
     */
    public Boolean finishAllActiveBatches(Long engineerSampleOrderId, String nickName);
}
