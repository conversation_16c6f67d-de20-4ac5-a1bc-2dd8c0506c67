package com.ruoyi.qc.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.qc.domain.ProductAudit;
import com.ruoyi.qc.mapper.ProductAuditMapper;
import com.ruoyi.qc.service.IProductAuditService;
import com.ruoyi.resource.domain.ResourceFinishedGoods;
import com.ruoyi.resource.mapper.ResourceFinishedGoodsMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 产品准入检查Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Service
public class ProductAuditServiceImpl implements IProductAuditService
{
    @Resource
    private ProductAuditMapper productAuditMapper;
    @Resource
    private ResourceFinishedGoodsMapper resourceFinishedGoodsMapper;

    @Override
    public void insertProductAudit(ProductAudit productAudit)
    {
        if (productAudit == null || productAudit.getProductCode() == null) {
            throw new IllegalArgumentException("产品准入检查信息不能为空");
        }
        // 设置产品准入检查的基本信息
        ResourceFinishedGoods resourceFinishedGoods = resourceFinishedGoodsMapper.selectResourceFinishedGoodsByProductCode(productAudit.getProductCode());
        // 实验室编号       laboratory_code
        productAudit.setLaboratoryCode(resourceFinishedGoods.getLabCode());
        // 产品名称        product_name
        productAudit.setProductName(resourceFinishedGoods.getProductName());
        // 项目号          project_no
        productAudit.setProjectNo(resourceFinishedGoods.getProjectNo());
        // 规格             spec
        productAudit.setSpec(resourceFinishedGoods.getSpec());

        // 生产企业        manufacturer
        // productAudit.setManufacturer("生产企业");
        // 产品类型        product_type
        // productAudit.setProductType("类型");

        // 订单数量        order_quantity
        // productAudit.setOrderQuantity(999L);
        // 预计生产日期       planned_production_date
        // productAudit.setPlannedProductionDate(new Date());
        // 产品交期        delivery_date
        // productAudit.setDeliveryDate(new Date());

        productAudit.setCreateTime(DateUtils.getNowDate());
        productAudit.setCreateBy(SecurityUtils.getUsername());
        productAuditMapper.insertProductAudit(productAudit);

        // 添加后获取对应的状态信息内容 todo

    }

    @Override
    public void updateProductAudit(ProductAudit productAudit)
    {
        productAudit.setUpdateTime(DateUtils.getNowDate());
        productAudit.setUpdateBy(SecurityUtils.getUsername());
        productAuditMapper.updateProductAudit(productAudit);
    }

    /**
     * 刷新产品准入检查状态
     *
     * @param id 产品准入检查ID
     * @param flag 状态标识字符串
     */
    @Override
    public void refreshStatus(Long id, String flag) {
        // 查询产品准入检查记录
        ProductAudit productAudit = productAuditMapper.selectProductAuditById(id);
        if (productAudit == null) {
            throw new IllegalArgumentException("产品准入检查记录不存在");
        }

        // 更新状态
        switch (flag) {
            case "formula_stability_report":
                updateFormulaStabilityReport(productAudit);
                break;
            case "formula_feasibility_assessment":
                updateFormulaFeasibilityAssessment(productAudit);
                break;
            case "standard_formula_process":
                updateStandardFormulaProcess(productAudit);
                break;
            case "mold_tool_confirmation":
                updateMoldToolConfirmation(productAudit);
                break;
            case "filling_packaging_feasibility":
                updateFillingPackagingFeasibility(productAudit);
                break;
            case "filling_packaging_sop":
                updateFillingPackagingSop(productAudit);
                break;
            case "finished_product_standard":
                updateFinishedProductStandard(productAudit);
                break;
            case "quality_agreement":
                updateQualityAgreement(productAudit);
                break;
            case "packaging_material_standard":
                updatePackagingMaterialStandard(productAudit);
                break;
            case "liquid_sample":
                updateLiquidSample(productAudit);
                break;
            case "packaging_material_sample":
                updatePackagingMaterialSample(productAudit);
                break;
            case "finished_product_sample":
                updateFinishedProductSample(productAudit);
                break;
            case "excessive_packaging_confirmation":
                updateExcessivePackagingConfirmation(productAudit);
                break;
            case "registration_completion":
                updateRegistrationCompletion(productAudit);
                break;
            case "formula_process_consistency":
                updateFormulaProcessConsistency(productAudit);
                break;
            case "documentation_consistency":
                updateDocumentationConsistency(productAudit);
                break;
            case "internal_standard_compliance":
                updateInternalStandardCompliance(productAudit);
                break;
            case "all":
                // 更新所有状态
                updateFormulaStabilityReport(productAudit);
                updateFormulaFeasibilityAssessment(productAudit);
                updateStandardFormulaProcess(productAudit);
                updateMoldToolConfirmation(productAudit);
                updateFillingPackagingFeasibility(productAudit);
                updateFillingPackagingSop(productAudit);
                updateFinishedProductStandard(productAudit);
                updateQualityAgreement(productAudit);
                updatePackagingMaterialStandard(productAudit);
                updateLiquidSample(productAudit);
                updatePackagingMaterialSample(productAudit);
                updateFinishedProductSample(productAudit);
                updateExcessivePackagingConfirmation(productAudit);
                updateRegistrationCompletion(productAudit);
                updateFormulaProcessConsistency(productAudit);
                updateDocumentationConsistency(productAudit);
                updateInternalStandardCompliance(productAudit);
                break;
            default:
                throw new IllegalArgumentException("未知的状态标识: " + flag);
        }

        // 保存更新后的记录
        updateProductAudit(productAudit);
    }

    /**
     * 更新配方稳定性报告状态
     *
     * @param productAudit 产品准入检查对象
     */
    private void updateFormulaStabilityReport(ProductAudit productAudit) {
        // TODO 根据productAudit获取配方稳定性报告内容

        // 更新状态和风险提示
        productAudit.setFormulaStabilityReport(1);
        productAudit.setFormulaStabilityReportHoverTip("风险提示内容");
    }

    /**
     * 更新配制可行性评估状态
     *
     * @param productAudit 产品准入检查对象
     */
    private void updateFormulaFeasibilityAssessment(ProductAudit productAudit) {
        // TODO 根据productAudit获取配制可行性评估内容

        // 更新状态和风险提示
        productAudit.setFormulaFeasibilityAssessment(1);
        productAudit.setFormulaFeasibilityAssessmentHoverTip("风险提示内容");
    }

    /**
     * 更新标准配制工艺单状态
     *
     * @param productAudit 产品准入检查对象
     */
    private void updateStandardFormulaProcess(ProductAudit productAudit) {
        // TODO 根据productAudit获取标准配制工艺单内容

        // 更新状态
        productAudit.setStandardFormulaProcess(1);
    }

    /**
     * 更新生产模具治具确认状态
     *
     * @param productAudit 产品准入检查对象
     */
    private void updateMoldToolConfirmation(ProductAudit productAudit) {
        // TODO 根据productAudit获取生产模具治具确认内容

        // 更新状态和预计时间
        productAudit.setMoldToolConfirmation(1);
        productAudit.setMoldToolConfirmationHoverTip("预计时间内容");
    }

    /**
     * 更新灌包可行性评估状态
     *
     * @param productAudit 产品准入检查对象
     */
    private void updateFillingPackagingFeasibility(ProductAudit productAudit) {
        // TODO 根据productAudit获取灌包可行性评估内容

        // 更新状态和风险提示
        productAudit.setFillingPackagingFeasibility(1);
        productAudit.setFillingPackagingFeasibilityHoverTip("风险提示内容");
    }

    /**
     * 更新灌装/包装SOP状态
     *
     * @param productAudit 产品准入检查对象
     */
    private void updateFillingPackagingSop(ProductAudit productAudit) {
        // TODO 根据productAudit获取灌装/包装SOP内容

        // 更新状态
        productAudit.setFillingPackagingSop(1);
    }

    /**
     * 更新成品检验标准状态
     *
     * @param productAudit 产品准入检查对象
     */
    private void updateFinishedProductStandard(ProductAudit productAudit) {
        // TODO 根据productAudit获取成品检验标准内容

        // 更新状态
        productAudit.setFinishedProductStandard(1);
    }

    /**
     * 更新质量协议状态
     *
     * @param productAudit 产品准入检查对象
     */
    private void updateQualityAgreement(ProductAudit productAudit) {
        // TODO 根据productAudit获取质量协议内容

        // 更新状态和合同类型提示
        productAudit.setQualityAgreement(1);
        productAudit.setQualityAgreementHoverTip("合同类型提示内容");
    }

    /**
     * 更新包材标准状态
     *
     * @param productAudit 产品准入检查对象
     */
    private void updatePackagingMaterialStandard(ProductAudit productAudit) {
        // TODO 根据productAudit获取包材标准内容

        // 更新状态
        productAudit.setPackagingMaterialStandard(1);
    }

    /**
     * 更新料体标样状态
     *
     * @param productAudit 产品准入检查对象
     */
    private void updateLiquidSample(ProductAudit productAudit) {
        // TODO 根据productAudit获取料体标样内容

        // 更新状态
        productAudit.setLiquidSample(1);
    }

    /**
     * 更新包材标准样状态
     *
     * @param productAudit 产品准入检查对象
     */
    private void updatePackagingMaterialSample(ProductAudit productAudit) {
        // TODO 根据productAudit获取包材标准样内容

        // 更新状态
        productAudit.setPackagingMaterialSample(1);
    }

    /**
     * 更新成品标样状态
     *
     * @param productAudit 产品准入检查对象
     */
    private void updateFinishedProductSample(ProductAudit productAudit) {
        // TODO 根据productAudit获取成品标样内容

        // 更新状态
        productAudit.setFinishedProductSample(1);
    }

    /**
     * 更新过度包装确认状态
     *
     * @param productAudit 产品准入检查对象
     */
    private void updateExcessivePackagingConfirmation(ProductAudit productAudit) {
        // TODO 根据productAudit获取过度包装确认内容

        // 更新状态和风险提示
        productAudit.setExcessivePackagingConfirmation(1);
        productAudit.setExcessivePackagingConfirmationHoverTip("风险提示内容");
    }

    /**
     * 更新注册备案是否完成状态
     *
     * @param productAudit 产品准入检查对象
     */
    private void updateRegistrationCompletion(ProductAudit productAudit) {
        // TODO 根据productAudit获取注册备案完成内容

        // 更新状态和备案号
        productAudit.setRegistrationCompletion(1);
        productAudit.setRegistrationCompletionHoverTip("备案号内容");
    }

    /**
     * 更新大货标准配方工艺单与注册/备案一致性状态
     *
     * @param productAudit 产品准入检查对象
     */
    private void updateFormulaProcessConsistency(ProductAudit productAudit) {
        // TODO 根据productAudit获取大货标准配方工艺单与注册/备案一致性内容

        // 更新状态和风险提示
        productAudit.setFormulaProcessConsistency(1);
        productAudit.setFormulaProcessConsistencyHoverTip("风险提示内容");
    }

    /**
     * 更新大货文案与备案资料一致性状态
     *
     * @param productAudit 产品准入检查对象
     */
    private void updateDocumentationConsistency(ProductAudit productAudit) {
        // TODO 根据productAudit获取大货文案与备案资料一致性内容

        // 更新状态和风险提示
        productAudit.setDocumentationConsistency(1);
        productAudit.setDocumentationConsistencyHoverTip("风险提示内容");
    }

    /**
     * 更新产品内控标准符合备案执行标准状态
     *
     * @param productAudit 产品准入检查对象
     */
    private void updateInternalStandardCompliance(ProductAudit productAudit) {
        // TODO 根据productAudit获取产品内控标准符合备案执行标准内容

        // 更新状态
        productAudit.setInternalStandardCompliance(1);
    }

}