package com.ruoyi.qc.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.qc.domain.ProductAudit;
import com.ruoyi.qc.mapper.ProductAuditMapper;
import com.ruoyi.qc.service.IProductAuditService;
import com.ruoyi.resource.domain.ResourceFinishedGoods;
import com.ruoyi.resource.mapper.ResourceFinishedGoodsMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 产品准入检查Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Service
public class ProductAuditServiceImpl implements IProductAuditService
{
    @Resource
    private ProductAuditMapper productAuditMapper;
    @Resource
    private ResourceFinishedGoodsMapper resourceFinishedGoodsMapper;

    @Override
    public void insertProductAudit(ProductAudit productAudit)
    {
        if (productAudit == null || productAudit.getProductCode() == null) {
            throw new IllegalArgumentException("产品准入检查信息不能为空");
        }
        // 设置产品准入检查的基本信息
        ResourceFinishedGoods resourceFinishedGoods = resourceFinishedGoodsMapper.selectResourceFinishedGoodsByProductCode(productAudit.getProductCode());
        // 实验室编号       laboratory_code
        productAudit.setLaboratoryCode(resourceFinishedGoods.getLabCode());
        // 产品名称        product_name
        productAudit.setProductName(resourceFinishedGoods.getProductName());
        // 项目号          project_no
        productAudit.setProjectNo(resourceFinishedGoods.getProjectNo());
        // 规格             spec
        productAudit.setSpec(resourceFinishedGoods.getSpec());

        // 生产企业        manufacturer
        // productAudit.setManufacturer("生产企业");
        // 产品类型        product_type
        // productAudit.setProductType("类型");

        // 订单数量        order_quantity
        // productAudit.setOrderQuantity(999L);
        // 预计生产日期       planned_production_date
        // productAudit.setPlannedProductionDate(new Date());
        // 产品交期        delivery_date
        // productAudit.setDeliveryDate(new Date());

        productAudit.setCreateTime(DateUtils.getNowDate());
        productAudit.setCreateBy(SecurityUtils.getUsername());
        productAuditMapper.insertProductAudit(productAudit);

        // 添加后获取对应的状态信息内容 todo

    }

    @Override
    public void updateProductAudit(ProductAudit productAudit)
    {
        productAudit.setUpdateTime(DateUtils.getNowDate());
        productAudit.setUpdateBy(SecurityUtils.getUsername());
        productAuditMapper.updateProductAudit(productAudit);
    }

    /**
     * 刷新产品准入检查状态
     *
     * @param id 产品准入检查ID
     * @param flag 状态标识字符串
     */
    @Override
    public void refreshStatus(Long id, String flag) {
        // 查询产品准入检查记录
        ProductAudit productAudit = productAuditMapper.selectProductAuditById(id);
        if (productAudit == null) {
            throw new IllegalArgumentException("产品准入检查记录不存在");
        }

//        formula_stability_report	配方稳定性报告状态
//        formula_feasibility_assessment	配制可行性评估状态
//        standard_formula_process	标准配制工艺单状态
//        mold_tool_confirmation	生产模具治具确认状态
//        filling_packaging_feasibility	灌包可行性评估状态
//        filling_packaging_sop	灌装/包装SOP状态
//        finished_product_standard	成品检验标准状态
//        quality_agreement	质量协议状态
//        packaging_material_standard	包材标准状态
//        liquid_sample	料体标样状态
//        packaging_material_sample	包材标准样状态
//        finished_product_sample	成品标样状态
//        excessive_packaging_confirmation	过度包装确认状态
//        registration_completion	注册备案是否完成状态
//        formula_process_consistency	大货标准配方工艺单与注册/备案一致性状态
//        documentation_consistency	大货文案与备案资料一致性状态
//        internal_standard_compliance	产品内控标准符合备案执行标准状态

        // 更新状态
        switch (flag) {
            case "formulaStabilityReport":
                // 配方稳定性报告状态
                System.out.println("根据productAudit获取稳定性报告内容");

                // 更新状态 和 风险提示
                // productAudit.setFormulaStabilityReport(1);
                // productAudit.setFormulaStabilityReportHoverTip("风险");
                break;
            case "formulaFeasibilityAssessment":

                break;
            case "11":

                break;
            case "22":

                break;
        }
    }


}
