<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.software.mapper.EngineerSampleOrderMapper">

    <sql id="selectEngineerSampleOrderVo">
        select
            eso.id,
            eso.user_id,
            eso.rank,
            sd.dept_name,
            su.nick_name,
            eso.sample_order_code,
            eso.project_order_id,
            eso.service_mode,
            eso.completion_status,
            eso.scheduled_date,
            eso.actual_start_time,
            eso.actual_finish_time,
            eso.actual_man_hours,
            eso.estimated_man_hours,
            eso.standard_man_hours,
            eso.assistant_name,
            eso.total_batches,
            eso.current_batch,
            eso.is_locked,
            eso.difficulty_level_id,
            eso.category_id,
            tc.category_name,
            eso.latest_start_time,
            eso.end_date,
            eso.check_type,
            eso.start_date,
            eso.sample_order_remark,
            eso.reason_for_no_sample,
            eso.solution,
            eso.reject_reason,
            eso.association_status,
            eso.adjust_work_schedule,
            eso.failure_reason,
            eso.del_flag,
            eso.create_by,
            eso.create_time,
            eso.update_by,
            eso.update_time,
            eso.remark,
            eso.laboratory_code,
            eso.expected_sample_time,
            pio.lab_no as laboratory,
            REPLACE(pio.fields -> '$.xqsl','"','') as sku,
            pio.create_by as applicant,
            c.customer_yw as sales,
            c.name as customerName,
            IF(pio.PROJECT_TYPE = 0, p.product_name,(select GROUP_CONCAT(bpp.product_name) from t_before_project_product bpp where FIND_IN_SET(bpp.id,pi.project_product_id))) as productName,
            c.customer_level as customerLevel,
            p.level as projectLevel,
            pio.item_id,
            pio.project_id,
            pio.fields,
            pi.status itemStatus
        from engineer_sample_order eso
        LEFT JOIN sys_user su on su.user_id = eso.user_id
        LEFT JOIN t_category tc ON tc.category_id = eso.category_id
        LEFT JOIN sys_dept sd on sd.dept_id = tc.dept_id
        LEFT JOIN t_project_item_order pio on pio.id = eso.PROJECT_ORDER_ID
        LEFT join t_project_item pi on pi.id = pio.item_id
        LEFT JOIN t_project p on pio.project_id  = p.id
        LEFT JOIN t_customer c on p.customer_id = c.id
    </sql>

    <select id="selectEngineerSampleOrderList" parameterType="EngineerSampleOrder" resultType="EngineerSampleOrder">
        <include refid="selectEngineerSampleOrderVo"/>
        <where>
            eso.del_flag = 0
            <if test="associationStatus != null "> and eso.association_status = #{associationStatus}</if>
            <if test="associationStatus != null and associationStatus == 0">
                AND eso.completion_status NOT IN (3, 4)
            </if>
            <if test="userId != null and userId != ''"> and eso.user_id like concat('%', #{userId}, '%')</if>
            <if test="nickName != null and nickName != '' "> and su.nick_name like concat('%', #{nickName}, '%')</if>
            <if test="deptId != null and deptId != ''">
                and (find_in_set( #{deptId}, sd.ancestors) or sd.dept_id = #{deptId})
            </if>
            <!-- 多个组别ID查询条件 -->
            <if test="deptIds != null and deptIds.length > 0">
                and (
                <foreach collection="deptIds" item="deptIdItem" separator=" or ">
                    (find_in_set(#{deptIdItem}, sd.ancestors) or sd.dept_id = #{deptIdItem})
                </foreach>
                )
            </if>
            <if test="sampleOrderCode != null and sampleOrderCode != ''"> and eso.sample_order_code like concat('%', #{sampleOrderCode}, '%')</if>
            <if test="completionStatus != null and completionStatus != ''"> and eso.completion_status = #{completionStatus}</if>
            <if test="isLocked != null "> and eso.is_locked = #{isLocked}</if>
            <if test="laboratory != null and laboratory!= ''" > and pio.lab_no = #{laboratory}</if>
            <if test="params.beginScheduledDate != null and params.beginScheduledDate != ''">
                AND date_format(eso.scheduled_date,'%y%m%d') &gt;= date_format(#{params.beginScheduledDate},'%y%m%d')
            </if>
            <if test="params.endScheduledDate != null and params.endScheduledDate != ''">
                AND date_format(eso.scheduled_date,'%y%m%d') &lt;= date_format(#{params.endScheduledDate},'%y%m%d')
            </if>
            <if test="params.beginStartDate != null and params.beginStartDate != ''">
                AND date_format(eso.start_date,'%y%m%d') &gt;= date_format(#{params.beginStartDate},'%y%m%d')
            </if>
            <if test="params.endStartDate != null and params.endStartDate != ''">
                AND date_format(eso.start_date,'%y%m%d') &lt;= date_format(#{params.endStartDate},'%y%m%d')
            </if>
            <if test="params.beginActualStartTime != null and params.beginActualStartTime != ''">
                AND date_format(eso.actual_start_time,'%y%m%d') &gt;= date_format(#{params.beginActualStartTime},'%y%m%d')
            </if>
            <if test="params.endActualStartTime != null and params.endActualStartTime != ''">
                AND date_format(eso.actual_start_time,'%y%m%d') &lt;= date_format(#{params.endActualStartTime},'%y%m%d')
            </if>
            <if test="params.beginActualFinishTime != null and params.beginActualFinishTime != ''">
                AND date_format(eso.actual_finish_time,'%y%m%d') &gt;= date_format(#{params.beginActualFinishTime},'%y%m%d')
            </if>
            <if test="params.endActualFinishTime != null and params.endActualFinishTime != ''">
                AND date_format(eso.actual_finish_time,'%y%m%d') &lt;= date_format(#{params.endActualFinishTime},'%y%m%d')
            </if>
            <if test="serviceMode != null and serviceMode != ''"> and eso.service_mode like concat('%', #{serviceMode}, '%') </if>
            <!-- 日期范围筛选条件 - 综合查询多个日期字段 -->
            <if test="beginDateRange != null and beginDateRange != '' and endDateRange != null and endDateRange != ''">
                AND (
                    (date_format(eso.scheduled_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.scheduled_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
                    OR (date_format(eso.end_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.end_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
                    OR (date_format(eso.start_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.start_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
                    OR (date_format(eso.latest_start_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.latest_start_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
                    OR (date_format(eso.actual_start_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.actual_start_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
                    OR (date_format(eso.actual_finish_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.actual_finish_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
                )
            </if>
            <!-- 逾期任务过滤条件 -->
            <if test="isOverdue != null and isOverdue == 1">
                AND (
                    -- 已完成且逾期：实际完成时间 > 截止日期
                    (eso.completion_status = 2 AND eso.end_date &lt; eso.actual_finish_time)
                    OR
                    -- 进行中且逾期：当前时间 > 截止日期
                    (eso.completion_status = 1 AND eso.end_date &lt; NOW())
                    OR
                    -- 未开始且逾期：未开始但当前时间 > 截止日期
                    (eso.completion_status = 0 AND eso.end_date &lt; NOW())
                )
            </if>
            <if test="confirmCode != null  and confirmCode != ''"> and  pio.confirm_code like  concat('%', #{confirmCode}, '%')</if>
            <if test="productName != null  and productName != ''">
                and  IF( P.PROJECT_TYPE = 0, p.product_name,(select GROUP_CONCAT(bpp.product_name) from t_before_project_product bpp where FIND_IN_SET(bpp.id,pi.project_product_id))) like concat('%', #{productName}, '%')
            </if>
            <if test="customerId != null and customerId != ''">
                and p.customer_id = #{customerId}
            </if>
            <!-- 权限控制：当前用户权限限制 -->
            <if test="currentUserDeptId != null or currentUserId != null">
                AND (
                    <!-- 部门权限：用户可查看的部门数据 -->
                    <if test="currentUserDeptId != null">
                        (find_in_set(#{currentUserDeptId}, sd.ancestors) or sd.dept_id = #{currentUserDeptId})
                    </if>
                    <!-- 个人权限：分配给当前用户的打样单 -->
                    <if test="currentUserId != null">
                        <if test="currentUserDeptId != null">OR</if>
                        eso.user_id = #{currentUserId}
                    </if>
                )
            </if>
            <if test="searchUserId!=null">
                and p.customer_id in (SELECT customer_id FROM t_customer_assist WHERE status = 0 and user_id = #{searchUserId})
            </if>
        </where>
        <!-- 动态排序规则 -->
        ORDER BY
        <choose>
            <!-- 当associationStatus=0时按 最晚开始日期 排序 -->
            <when test="associationStatus != null and associationStatus == 0">
                eso.latest_start_time ASC
            </when>
            <!-- 其他情况按 最晚截至日期 排序 -->
            <otherwise>
                eso.end_date ASC
            </otherwise>
        </choose>
    </select>

    <select id="selectEngineerSampleOrderById" parameterType="Long" resultType="EngineerSampleOrder" >
        <include refid="selectEngineerSampleOrderVo"/>
        where eso.id = #{id}
    </select>

    <insert id="insertEngineerSampleOrder" parameterType="EngineerSampleOrder" useGeneratedKeys="true" keyProperty="id">
        insert into engineer_sample_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="rank != null">rank,</if>
            <if test="sampleOrderCode != null">sample_order_code,</if>
            <if test="projectOrderId != null">project_order_id,</if>
            <if test="serviceMode != null">service_mode,</if>
            <if test="completionStatus != null">completion_status,</if>
            <if test="scheduledDate != null">scheduled_date,</if>
            <if test="actualStartTime != null">actual_start_time,</if>
            <if test="actualFinishTime != null">actual_finish_time,</if>
            <if test="actualManHours != null">actual_man_hours,</if>
            <if test="estimatedManHours != null">estimated_man_hours,</if>
            <if test="standardManHours != null">standard_man_hours,</if>
            <if test="assistantName != null">assistant_name,</if>
            <if test="expectedSampleTime != null">expected_sample_time,</if>
            <if test="totalBatches != null">total_batches,</if>
            <if test="currentBatch != null">current_batch,</if>
            <if test="isLocked != null">is_locked,</if>
            <if test="difficultyLevelId != null">difficulty_level_id,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="associationStatus != null">association_status,</if>
            <if test="failureReason != null">failure_reason,</if>
            <if test="startDate != null">start_date,</if>
            <if test="reasonForNoSample != null">reason_for_no_sample,</if>
            <if test="solution != null">solution,</if>
            <if test="rejectReason != null">reject_reason,</if>
            <if test="latestStartTime != null">latest_start_time,</if>
            <if test="endDate != null">end_date,</if>
            <if test="checkType != null">check_type,</if>
            <if test="sampleOrderRemark != null">sample_order_remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="laboratoryCode != null">laboratory_code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="rank != null">#{rank},</if>
            <if test="sampleOrderCode != null">#{sampleOrderCode},</if>
            <if test="projectOrderId != null">#{projectOrderId},</if>
            <if test="serviceMode != null">#{serviceMode},</if>
            <if test="completionStatus != null">#{completionStatus},</if>
            <if test="scheduledDate != null">#{scheduledDate},</if>
            <if test="actualStartTime != null">#{actualStartTime},</if>
            <if test="actualFinishTime != null">#{actualFinishTime},</if>
            <if test="actualManHours != null">#{actualManHours},</if>
            <if test="estimatedManHours != null">#{estimatedManHours},</if>
            <if test="standardManHours != null">#{standardManHours},</if>
            <if test="assistantName != null">#{assistantName},</if>
            <if test="expectedSampleTime != null">#{expectedSampleTime},</if>
            <if test="totalBatches != null">#{totalBatches},</if>
            <if test="currentBatch != null">#{currentBatch},</if>
            <if test="isLocked != null">#{isLocked},</if>
            <if test="difficultyLevelId != null">#{difficultyLevelId},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="associationStatus != null">#{associationStatus},</if>
            <if test="failureReason != null">#{failureReason},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="reasonForNoSample != null">#{reasonForNoSample},</if>
            <if test="rejectReason != null">#{rejectReason},</if>
            <if test="solution != null">#{solution},</if>
            <if test="latestStartTime != null">#{latestStartTime},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="checkType != null">#{checkType},</if>
            <if test="sampleOrderRemark != null">#{sampleOrderRemark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="laboratoryCode != null">#{laboratoryCode},</if>
        </trim>
    </insert>

    <update id="updateEngineerSampleOrder" parameterType="EngineerSampleOrder">
        update engineer_sample_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="rank != null">rank = #{rank},</if>
            <if test="sampleOrderCode != null">sample_order_code = #{sampleOrderCode},</if>
            <if test="projectOrderId != null">project_order_id = #{projectOrderId},</if>
            <if test="serviceMode != null">service_mode = #{serviceMode},</if>
            <if test="completionStatus != null">completion_status = #{completionStatus},</if>
            <if test="scheduledDate != null">scheduled_date = #{scheduledDate},</if>
            <if test="actualStartTime != null">actual_start_time = #{actualStartTime},</if>
            <if test="actualFinishTime != null">actual_finish_time = #{actualFinishTime},</if>
            <if test="actualManHours != null">actual_man_hours = #{actualManHours},</if>
            <if test="estimatedManHours != null">estimated_man_hours = #{estimatedManHours},</if>
            <if test="standardManHours != null">standard_man_hours = #{standardManHours},</if>
            <if test="assistantName != null">assistant_name = #{assistantName},</if>
            <if test="expectedSampleTime != null">expected_sample_time = #{expectedSampleTime},</if>
            <if test="totalBatches != null">total_batches = #{totalBatches},</if>
            <if test="currentBatch != null">current_batch = #{currentBatch},</if>
            <if test="isLocked != null">is_locked = #{isLocked},</if>
            <if test="difficultyLevelId != null">difficulty_level_id = #{difficultyLevelId},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="associationStatus != null">association_status = #{associationStatus},</if>
            <if test="failureReason != null">failure_reason = #{failureReason},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="reasonForNoSample != null">reason_for_no_sample = #{reasonForNoSample},</if>
            <if test="solution != null">solution = #{solution},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="latestStartTime != null">latest_start_time = #{latestStartTime},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="checkType != null">check_type = #{checkType},</if>
            <if test="sampleOrderRemark != null">sample_order_remark = #{sampleOrderRemark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="laboratoryCode != null">laboratory_code = #{laboratoryCode},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteEngineerSampleOrderByIds" parameterType="String">
        update  engineer_sample_order set del_flag = 2,update_time = now() where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectUnlockedSampleOrders" resultType="EngineerSampleOrder">
        <include refid="selectEngineerSampleOrderVo"/>
        WHERE eso.del_flag = 0
        AND eso.is_locked = 0 and eso.completion_status = 0
        <!-- 工程师过滤 -->
        <if test="userId != null">
            AND eso.user_id = #{userId}
        </if>
        <!-- 时间范围：当前日期+2 到 当前日期+2+days -->
        AND eso.scheduled_date BETWEEN
        DATE_ADD(CURDATE(), INTERVAL 2 DAY)
        AND
        DATE_ADD(CURDATE(), INTERVAL 2 +
        <choose>
            <when test="days != null">#{days}</when>
            <otherwise>7</otherwise>
        </choose>
        DAY)
        ORDER BY eso.scheduled_date ASC
        LIMIT
        <choose>
            <when test="limit != null">#{limit}</when>
            <otherwise>20</otherwise>
        </choose>
    </select>

    <!-- 查询指定工程师在指定日期以后的未锁定打样单列表（按排单日期排序） -->
    <select id="selectUnlockedSampleOrdersAfterDate" resultType="EngineerSampleOrder">
        <include refid="selectEngineerSampleOrderVo"/>
        WHERE eso.del_flag = 0
        AND eso.is_locked = 0 and eso.completion_status = 0
        AND eso.user_id = #{userId}
        AND eso.scheduled_date >= #{scheduledDate}
        ORDER BY eso.scheduled_date ASC
    </select>

    <!-- 查询指定工程师在指定日期范围内的未锁定打样单列表（按排单日期排序） -->
    <select id="selectUnlockedSampleOrdersInDateRange" resultType="EngineerSampleOrder">
        <include refid="selectEngineerSampleOrderVo"/>
        WHERE eso.del_flag = 0
        AND eso.is_locked = 0 and eso.completion_status = 0
        AND eso.user_id = #{userId}
        AND date_format(eso.scheduled_date,'%y%m%d') &gt;= date_format(#{startDate},'%y%m%d')
        AND date_format(eso.scheduled_date,'%y%m%d') &lt;= date_format(#{endDate},'%y%m%d')
        ORDER BY eso.scheduled_date ASC
    </select>

    <!-- 批量删除工程师打样单关联记录 -->
    <update id="batchDeleteEngineerSampleOrders">
        UPDATE engineer_sample_order
        SET del_flag = 2, update_time = NOW()
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="dashboardStats" parameterType="EngineerSampleOrder" resultType="com.ruoyi.software.domain.EngineerSampleOrderDashboardStatsVo">
        SELECT
        COUNT(*) AS total,
        SUM(CASE WHEN eso.completion_status = 2 THEN 1 ELSE 0 END) AS completed,
        SUM(CASE WHEN eso.completion_status = 1 THEN 1 ELSE 0 END) AS inProgress,
        SUM(
            CASE
            -- 已完成且逾期：实际完成时间 > 截止日期
            WHEN eso.completion_status = 2 AND eso.end_date &lt; eso.actual_finish_time THEN 1
            -- 进行中且逾期：当前时间 > 截止日期
            WHEN eso.completion_status = 1 AND eso.end_date &lt; NOW() THEN 1
            -- 未开始且逾期：未开始但当前时间 > 开始日期
            WHEN eso.completion_status = 0 AND eso.end_date &lt; NOW() THEN 1
            ELSE 0
            END
            ) AS overdue
        FROM engineer_sample_order eso
        LEFT JOIN sys_user su ON su.user_id = eso.user_id
        LEFT JOIN t_project_item_order pio on eso.project_order_id = pio.id
        LEFT JOIN t_category tc ON tc.category_id = eso.category_id
        LEFT JOIN sys_dept sd on sd.dept_id = tc.dept_id
        <where>
            eso.del_flag = 0 and eso.association_status = 1
            <if test="userId != null">
                and eso.user_id like concat('%', #{userId}, '%')
            </if>
            <if test="isLocked != null">
                AND eso.is_locked = #{isLocked}
            </if>
            <if test="nickName != null and nickName != ''">
                AND su.nick_name LIKE CONCAT('%', #{nickName}, '%')
            </if>
            <if test="searchUserId!=null">
                and pio.customer_id in (SELECT customer_id FROM t_customer_assist WHERE status = 0 and user_id = #{searchUserId})
            </if>
            <!-- 仅在 INNER JOIN 存在时使用 deptId -->
            <if test="deptId != null ">
                and (find_in_set( #{deptId}, sd.ancestors) or sd.dept_id = #{deptId})
            </if>
            <if test="params.beginScheduledDate != null and params.beginScheduledDate != ''">
                AND date_format(eso.scheduled_date,'%y%m%d') &gt;= date_format(#{params.beginScheduledDate},'%y%m%d')
            </if>
            <if test="params.endScheduledDate != null and params.endScheduledDate != ''">
                AND date_format(eso.scheduled_date,'%y%m%d') &lt;= date_format(#{params.endScheduledDate},'%y%m%d')
            </if>
            <if test="params.beginStartDate != null and params.beginStartDate != ''">
                AND date_format(eso.start_date,'%y%m%d') &gt;= date_format(#{params.beginStartDate},'%y%m%d')
            </if>
            <if test="params.endStartDate != null and params.endStartDate != ''">
                AND date_format(eso.start_date,'%y%m%d') &lt;= date_format(#{params.endStartDate},'%y%m%d')
            </if>
            <if test="params.beginActualStartTime != null and params.beginActualStartTime != ''">
                AND date_format(eso.actual_start_time,'%y%m%d') &gt;= date_format(#{params.beginActualStartTime},'%y%m%d')
            </if>
            <if test="params.endActualStartTime != null and params.endActualStartTime != ''">
                AND date_format(eso.actual_start_time,'%y%m%d') &lt;= date_format(#{params.endActualStartTime},'%y%m%d')
            </if>
            <if test="params.beginActualFinishTime != null and params.beginActualFinishTime != ''">
                AND date_format(eso.actual_finish_time,'%y%m%d') &gt;= date_format(#{params.beginActualFinishTime},'%y%m%d')
            </if>
            <if test="params.endActualFinishTime != null and params.endActualFinishTime != ''">
                AND date_format(eso.actual_finish_time,'%y%m%d') &lt;= date_format(#{params.endActualFinishTime},'%y%m%d')
            </if>
            <!-- 日期范围筛选条件 - 综合查询多个日期字段 -->
            <if test="beginDateRange != null and beginDateRange != '' and endDateRange != null and endDateRange != ''">
                AND (
                (date_format(eso.scheduled_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.scheduled_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
                OR (date_format(eso.end_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.end_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
                OR (date_format(eso.start_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.start_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
                OR (date_format(eso.latest_start_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.latest_start_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
                OR (date_format(eso.actual_start_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.actual_start_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
                OR (date_format(eso.actual_finish_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.actual_finish_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
                )
            </if>
            <!-- 多个组别ID查询条件 -->
            <if test="deptIds != null and deptIds.length > 0">
                and (
                <foreach collection="deptIds" item="deptIdItem" separator=" or ">
                    (find_in_set(#{deptIdItem}, sd.ancestors) or sd.dept_id = #{deptIdItem})
                </foreach>
                )
            </if>
            <!-- 权限控制：当前用户权限限制 -->
            <if test="currentUserDeptId != null or currentUserId != null">
                AND (
                <!-- 部门权限：用户可查看的部门数据 -->
                <if test="currentUserDeptId != null">
                    (find_in_set(#{currentUserDeptId}, sd.ancestors) or sd.dept_id = #{currentUserDeptId})
                </if>
                <!-- 个人权限：分配给当前用户的打样单 -->
                <if test="currentUserId != null">
                    <if test="currentUserDeptId != null">OR</if>
                    eso.user_id = #{currentUserId}
                </if>
                )
            </if>
        </where>
    </select>

    <!-- 获取组别项目汇总数据 -->
    <select id="selectGroupSummary" parameterType="DashboardQueryDTO" resultType="com.ruoyi.software.domain.vo.GroupSummaryVo">
        SELECT
        sd.dept_name as deptName,
        COUNT(DISTINCT eso.id) as orderCount,
        COUNT(DISTINCT su.user_id) as memberCount,
        SUM(CASE WHEN eso.completion_status IN (0, 1) THEN 1 ELSE 0 END) as pendingCount,
        SUM(CASE WHEN eso.completion_status IN (0, 1) AND eso.end_date &lt;= DATE_ADD(NOW(), INTERVAL 1 DAY) THEN 1 ELSE 0 END) as mustCompleteCount,
        SUM(CASE WHEN eso.completion_status = 2 THEN 1 ELSE 0 END) as completedCount,
        SUM(CASE
        -- 已完成且逾期：实际完成时间 > 截止日期
        WHEN eso.completion_status = 2 AND eso.end_date &lt; eso.actual_finish_time THEN 1
        -- 进行中且逾期：当前时间 > 截止日期
        WHEN eso.completion_status = 1 AND eso.end_date &lt; NOW() THEN 1
        -- 未开始且逾期：未开始但当前时间 > 开始日期
        WHEN eso.completion_status = 0 AND eso.end_date &lt; NOW() THEN 1
        ELSE 0
        END
        ) AS overdueCount,
        SUM(CASE WHEN eso.difficulty_level_id IN (0, 3, 6) THEN 1 ELSE 0 END) as difficultyA,
        SUM(CASE WHEN eso.difficulty_level_id IN (1, 4, 7) THEN 1 ELSE 0 END) as difficultyB,
        SUM(CASE WHEN eso.difficulty_level_id IN (2, 5, 8) THEN 1 ELSE 0 END) as difficultyC,
        SUM(CASE WHEN eso.service_mode = 'VVIP' THEN 1 ELSE 0 END) as vvipCount,
        SUM(CASE WHEN eso.service_mode = 'AVIP' THEN 1 ELSE 0 END) as avipCount,
        SUM(CASE WHEN eso.service_mode = 'SVIP' THEN 1 ELSE 0 END) as svipCount,
        SUM(CASE WHEN eso.service_mode = 'BVIP' THEN 1 ELSE 0 END) as bvipCount,
        SUM(CASE WHEN eso.failure_reason IS NOT NULL AND eso.failure_reason != '' THEN 1 ELSE 0 END) as exceptionCount, -- 异常举手
        ROUND(
            CASE
            WHEN available_hours.total_available_hours &gt; 0 THEN (standard_hours.total_standard_hours / available_hours.total_available_hours) * 100.0
            ELSE 0
            END,
            2
        ) AS workloadPercentage, -- 负荷 = (部门内所有打样单对应的标准工时 / 日期范围内工程师可用的工时) × 100%
        -- 完成率 = 已完成的数量/打样单总数数量*100%
        ROUND(
            CASE
            WHEN COUNT(DISTINCT eso.id) &gt; 0
            THEN (SUM(CASE WHEN eso.completion_status = 2 THEN 1 ELSE 0 END) * 100.0 / COUNT(DISTINCT eso.id))
            ELSE 0
            END, 2
        ) as completionRate,
        -- 及时率 = 在必完成日期(截至日期)当日之前完成的打样单数量/必完成打样单数量*100%
        -- 必完成打样单：截止日期在查询时间范围内的打样单
        ROUND(
            CASE
            WHEN SUM(CASE WHEN eso.end_date IS NOT NULL THEN 1 ELSE 0 END) > 0
            THEN (SUM(CASE
            WHEN eso.completion_status = 2 AND eso.actual_finish_time &lt;= eso.end_date THEN 1
            ELSE 0
            END) * 100.0 / SUM(CASE WHEN eso.end_date IS NOT NULL THEN 1 ELSE 0 END))
            ELSE 0
            END, 2
        ) as timelyRate

        -- 确认率 = 一定时间内被确认的打样单数量/该时间段内执行完成的打样单数量*100%
        -- 被确认的打样单：关联的项目订单审核状态为通过(sh_status1=1)的已完成打样单
        -- ROUND() as confirmationRate

        FROM
        sys_dept sd
        LEFT JOIN sys_user su ON su.dept_id = sd.dept_id AND su.del_flag = 0 AND su.is_user = 0 AND su.positive_status != 2
        LEFT JOIN engineer_sample_order eso ON eso.user_id = su.user_id AND eso.del_flag = 0
        LEFT JOIN (
            SELECT su.dept_id,
                SUM(
                    COALESCE(
                        CASE
                        WHEN eso.difficulty_level_id IN (8, 5, 2) THEN tc.sample_level_3
                        WHEN eso.difficulty_level_id IN (7, 4, 1) THEN tc.sample_level_2
                        WHEN eso.difficulty_level_id IN (6, 3, 0) THEN tc.sample_level_1
                        ELSE 0
                        END, 0
                    )
                ) AS total_standard_hours
            FROM
            sys_user su
            LEFT JOIN engineer_sample_order eso ON eso.user_id = su.user_id AND eso.del_flag = 0
            LEFT JOIN t_category tc ON tc.category_id = eso.category_id
            WHERE
            su.del_flag = 0 AND su.is_user = 0 AND su.positive_status != 2
                <if test="beginDateRange != null and beginDateRange != '' and endDateRange != null and endDateRange != ''">
                    AND eso.end_date BETWEEN #{beginDateRange} AND #{endDateRange}
                </if>
            GROUP BY
            su.dept_id
        ) standard_hours ON standard_hours.dept_id = sd.dept_id
        LEFT JOIN (
            SELECT su.dept_id, SUM(ewr.work_hours) AS total_available_hours
            FROM sys_user su
            LEFT JOIN engineer_work_record ewr ON ewr.user_id = su.user_id
            WHERE su.del_flag = 0 AND su.is_user = 0 AND su.positive_status != 2
            <if test="beginDateRange != null and beginDateRange != '' and endDateRange != null and endDateRange != ''">
                AND ewr.work_date BETWEEN #{beginDateRange} AND #{endDateRange}
            </if>
            GROUP BY
            su.dept_id
        ) available_hours ON available_hours.dept_id = sd.dept_id
        WHERE sd.del_flag = 0

        <choose>
            <!-- 当 deptId 不为空时 -->
            <when test="deptId != null">
                AND (FIND_IN_SET(#{deptId}, sd.ancestors) OR sd.dept_id = #{deptId})
            </when>
            <!-- 当 deptId 为空时 -->
            <otherwise>
                AND (sd.dept_id IN (464, 465) OR sd.ancestors REGEXP '(^|,)464(,|$)|(^|,)465(,|$)') -- 默认“护肤和彩妆研发部”
            </otherwise>
        </choose>
        <!-- 日期范围筛选条件 - 综合查询多个日期字段 -->
        <if test="beginDateRange != null and beginDateRange != '' and endDateRange != null and endDateRange != ''">
            AND (
                (date_format(eso.scheduled_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.scheduled_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
                OR (date_format(eso.end_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.end_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
                OR (date_format(eso.start_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.start_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
                OR (date_format(eso.latest_start_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.latest_start_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
                OR (date_format(eso.actual_start_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.actual_start_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
                OR (date_format(eso.actual_finish_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.actual_finish_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            )
        </if>
        <!-- 多个组别ID查询条件 -->
        <if test="deptIds != null and deptIds.length > 0">
            AND (
            <foreach collection="deptIds" item="deptIdItem" separator=" or ">
                (find_in_set(#{deptIdItem}, sd.ancestors) or sd.dept_id = #{deptIdItem})
            </foreach>
            )
        </if>
        <!-- 权限控制：当前用户权限限制 -->
        <if test="currentUserDeptId != null or currentUserId != null">
            AND (
            <!-- 部门权限：用户可查看的部门数据 -->
            <if test="currentUserDeptId != null">
                (find_in_set(#{currentUserDeptId}, sd.ancestors) or sd.dept_id = #{currentUserDeptId})
            </if>
            <!-- 个人权限：分配给当前用户的打样单 -->
            <if test="currentUserId != null">
                <if test="currentUserDeptId != null">OR</if>
                eso.user_id = #{currentUserId}
            </if>
            )
        </if>
        GROUP BY sd.dept_id, sd.dept_name
        HAVING COUNT(DISTINCT su.user_id) &gt; 0
        ORDER BY sd.dept_name
    </select>

    <!-- 获取工程师项目汇总数据 -->
    <select id="selectEngineerSummary" parameterType="DashboardQueryDTO" resultType="com.ruoyi.software.domain.vo.EngineerSummaryVo">
        SELECT
        sd.dept_name AS deptName,
        su.nick_name AS nickName,
        eso.rank,
        COUNT(DISTINCT eso.id) AS orderCount,
        SUM(CASE WHEN eso.completion_status IN (0, 1) THEN 1 ELSE 0 END) AS pendingCount,
        SUM(CASE WHEN eso.completion_status IN (0, 1) AND eso.end_date &lt;= DATE_ADD(NOW(), INTERVAL 1 DAY) THEN 1 ELSE 0 END) AS mustCompleteCount,
        SUM(CASE WHEN eso.completion_status = 2 THEN 1 ELSE 0 END) AS completedCount,
        SUM(
            CASE
            -- 已完成且逾期：实际完成时间 > 截止日期
            WHEN eso.completion_status = 2 AND eso.end_date &lt; eso.actual_finish_time THEN 1
            -- 进行中且逾期：当前时间 > 截止日期
            WHEN eso.completion_status = 1 AND eso.end_date &lt; NOW() THEN 1
            -- 未开始且逾期：未开始但当前时间 > 开始日期
            WHEN eso.completion_status = 0 AND eso.end_date &lt; NOW() THEN 1
            ELSE 0
            END
        ) AS overdueCount,
        SUM(CASE WHEN eso.difficulty_level_id IN (0, 3, 6) THEN 1 ELSE 0 END) AS difficultyA,
        SUM(CASE WHEN eso.difficulty_level_id IN (1, 4, 7) THEN 1 ELSE 0 END) AS difficultyB,
        SUM(CASE WHEN eso.difficulty_level_id IN (2, 5, 8) THEN 1 ELSE 0 END) AS difficultyC,
        SUM(CASE WHEN eso.service_mode = 'VVIP' THEN 1 ELSE 0 END) AS vvipCount,
        SUM(CASE WHEN eso.service_mode = 'AVIP' THEN 1 ELSE 0 END) AS avipCount,
        SUM(CASE WHEN eso.service_mode = 'SVIP' THEN 1 ELSE 0 END) AS svipCount,
        SUM(CASE WHEN eso.service_mode = 'BVIP' THEN 1 ELSE 0 END) AS bvipCount,
        SUM(CASE WHEN eso.failure_reason IS NOT NULL AND eso.failure_reason != '' THEN 1 ELSE 0 END) AS exceptionCount,
        ROUND(
            CASE
            WHEN ewr_agg.total_work_hours &gt; 0 THEN (ewr_agg.total_sample * 100.0 / ewr_agg.total_work_hours)
            ELSE 0
            END,2
        ) AS workloadPercentage
        FROM
        sys_user su
        LEFT JOIN engineer_sample_order eso ON eso.user_id = su.user_id AND eso.del_flag = 0
        LEFT JOIN t_category tc ON tc.category_id = eso.category_id
        LEFT JOIN sys_dept sd ON sd.dept_id = tc.dept_id
        LEFT JOIN (
            SELECT ewr.user_id, SUM(ewr.work_hours) AS total_work_hours,
            SUM(
                COALESCE(
                CASE
                    WHEN eso.difficulty_level_id IN (8, 5, 2) THEN tc.sample_level_3
                    WHEN eso.difficulty_level_id IN (7, 4, 1) THEN tc.sample_level_2
                    WHEN eso.difficulty_level_id IN (6, 3, 0) THEN tc.sample_level_1
                ELSE 0
                END, 0
                )
            ) AS total_sample
            FROM engineer_work_record ewr
            LEFT JOIN engineer_sample_order eso ON eso.user_id = ewr.user_id AND eso.del_flag = 0
            LEFT JOIN t_category tc ON tc.category_id = eso.category_id
            WHERE
            1 = 1
            <if test="beginDateRange != null and beginDateRange != '' and endDateRange != null and endDateRange != ''">
                AND ewr.work_date BETWEEN #{beginDateRange} AND #{endDateRange}
            </if>
            GROUP BY ewr.user_id
        ) ewr_agg ON ewr_agg.user_id = su.user_id

        WHERE su.del_flag = 0 AND su.is_user = 0 AND su.positive_status != 2

        <choose>
            <!-- 当 deptId 不为空时 -->
            <when test="deptId != null">
                AND (FIND_IN_SET(#{deptId}, sd.ancestors) OR sd.dept_id = #{deptId})
            </when>
            <!-- 当 deptId 为空时 -->
            <otherwise>
                AND (sd.dept_id IN (464, 465) OR sd.ancestors REGEXP '(^|,)464(,|$)|(^|,)465(,|$)') -- 默认“护肤和彩妆研发部”
            </otherwise>
        </choose>
        <!-- 日期范围筛选条件 - 综合查询多个日期字段 -->
        <if test="beginDateRange != null and beginDateRange != '' and endDateRange != null and endDateRange != ''">
            AND (
            (date_format(eso.scheduled_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.scheduled_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            OR (date_format(eso.end_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.end_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            OR (date_format(eso.start_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.start_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            OR (date_format(eso.latest_start_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.latest_start_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            OR (date_format(eso.actual_start_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.actual_start_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            OR (date_format(eso.actual_finish_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.actual_finish_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            )
        </if>
        <!-- 多个组别ID查询条件 -->
        <if test="deptIds != null and deptIds.length > 0">
            AND (
            <foreach collection="deptIds" item="deptIdItem" separator=" or ">
                (find_in_set(#{deptIdItem}, sd.ancestors) or sd.dept_id = #{deptIdItem})
            </foreach>
            )
        </if>
        <!-- 权限控制：当前用户权限限制 -->
        <if test="currentUserDeptId != null or currentUserId != null">
            AND (
            <!-- 部门权限：用户可查看的部门数据 -->
            <if test="currentUserDeptId != null">
                (find_in_set(#{currentUserDeptId}, sd.ancestors) or sd.dept_id = #{currentUserDeptId})
            </if>
            <!-- 个人权限：分配给当前用户的打样单 -->
            <if test="currentUserId != null">
                <if test="currentUserDeptId != null">OR</if>
                eso.user_id = #{currentUserId}
            </if>
            )
        </if>
        GROUP BY su.user_id, sd.dept_name, su.nick_name, eso.rank, ewr_agg.total_work_hours, ewr_agg.total_sample
        HAVING ewr_agg.total_work_hours > 0 AND COUNT(eso.id) > 0
        ORDER BY sd.dept_name, su.nick_name
    </select>

    <!-- 获取打样进度明细数据 -->
    <select id="selectSampleDetail" parameterType="DashboardQueryDTO" resultType="com.ruoyi.software.domain.vo.SampleDetailVo">
        SELECT
            su.nick_name as nickName,
            eso.rank as rank,
            eso.sample_order_code as sampleOrderCode,
            eso.laboratory_code as laboratoryCode,
            p.product_name as productName,
            tc.category_name as category,
            eso.completion_status completionStatus,
            eso.total_batches as totalBatches,
            eso.actual_start_time as actualStartTime,
            eso.actual_finish_time as actualFinishTime,
            eso.actual_man_hours as actualManHours,
            eso.sample_order_remark as remark,
            eso.reason_for_no_sample as reasonForNoSample,
            eso.solution as solution,
            -- CASE WHEN IFNULL(so.customer_follow, 0) = 1 THEN 1 ELSE 0 END as customerFollow, -- 客户是否跟进
            eso.difficulty_level_id as difficultyLevelId,
            eso.service_mode as serviceMode,
            eso.end_date as endDate
        FROM engineer_sample_order eso
        LEFT JOIN sys_user su ON su.user_id = eso.user_id AND su.del_flag = 0 AND su.is_user = 0 AND su.positive_status != 2
        LEFT JOIN t_project_item_order pio on pio.id = eso.PROJECT_ORDER_ID
        LEFT join t_project_item pi on pi.id = pio.item_id
        LEFT JOIN t_project p on pio.project_id  = p.id
        LEFT JOIN t_category tc ON tc.category_id = eso.category_id
        LEFT JOIN sys_dept sd on sd.dept_id = tc.dept_id
        WHERE eso.del_flag = 0 AND eso.association_status = 1
        <choose>
            <!-- 当 deptId 不为空时 -->
            <when test="deptId != null">
                AND (FIND_IN_SET(#{deptId}, sd.ancestors) OR sd.dept_id = #{deptId})
            </when>
            <!-- 当 deptId 为空时 -->
            <otherwise>
                AND (sd.dept_id IN (464, 465) OR sd.ancestors REGEXP '(^|,)464(,|$)|(^|,)465(,|$)') -- 默认“护肤和彩妆研发部”
            </otherwise>
        </choose>
        <!-- 日期范围筛选条件 - 综合查询多个日期字段 -->
        <if test="beginDateRange != null and beginDateRange != '' and endDateRange != null and endDateRange != ''">
            AND (
            (date_format(eso.scheduled_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.scheduled_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            OR (date_format(eso.end_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.end_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            OR (date_format(eso.start_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.start_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            OR (date_format(eso.latest_start_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.latest_start_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            OR (date_format(eso.actual_start_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.actual_start_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            OR (date_format(eso.actual_finish_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.actual_finish_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            )
        </if>
        <!-- 多个组别ID查询条件 -->
        <if test="deptIds != null and deptIds.length > 0">
            AND (
            <foreach collection="deptIds" item="deptIdItem" separator=" or ">
                (find_in_set(#{deptIdItem}, sd.ancestors) or sd.dept_id = #{deptIdItem})
            </foreach>
            )
        </if>
        <!-- 权限控制：当前用户权限限制 -->
        <if test="currentUserDeptId != null or currentUserId != null">
            AND (
            <!-- 部门权限：用户可查看的部门数据 -->
            <if test="currentUserDeptId != null">
                (find_in_set(#{currentUserDeptId}, sd.ancestors) or sd.dept_id = #{currentUserDeptId})
            </if>
            <!-- 个人权限：分配给当前用户的打样单 -->
            <if test="currentUserId != null">
                <if test="currentUserDeptId != null">OR</if>
                eso.user_id = #{currentUserId}
            </if>
            )
        </if>
        ORDER BY eso.scheduled_date DESC, su.nick_name
    </select>

    <!-- 根据项目订单ID查询关联记录 -->
    <select id="selectByProjectOrderId" resultType="EngineerSampleOrder">
        <include refid="selectEngineerSampleOrderVo"/>
        WHERE eso.del_flag = 0
        AND eso.project_order_id = #{projectOrderId}
        AND eso.completion_status NOT IN (3, 4)
        LIMIT 1
    </select>

    <update id="deleteEngineerRecordData">
        update engineer_work_record set del_flag = '2',update_time=now(),update_by = 'admin' where user_id = #{userId} AND WORK_DATE = #{workDate}
    </update>

    <select id="selectExecutionByOrderInfo" resultType="com.alibaba.fastjson.JSONObject" parameterType="java.lang.Long">
        SELECT id from t_project_execution
        where PROJECT_ORDER_ID = #{projectOrderId} and step = 3 order by id desc limit 1
    </select>

    <update id="updateProjectItemOrderDelayInfo" parameterType="ProjectItemOrder">
        UPDATE t_project_item_order set  delay_yj_time = #{delayYjTime} where id = #{id}
    </update>

    <!-- 获取报警信息数据 -->
    <select id="selectAlertInfo" parameterType="DashboardQueryDTO" resultType="com.ruoyi.software.domain.vo.AlertInfoVo">
        SELECT
            eso.id as sampleOrderId,
            eso.sample_order_code as sampleOrderCode,
            sd.dept_name as deptName,
            su.nick_name as nickName,
            eso.rank as rank,
            p.product_name as productName,
            eso.difficulty_level_id as difficultyLevelId,
            eso.category_id as categoryId,
            eso.service_mode as serviceMode,
            eso.failure_reason as failureReason,
            eso.latest_start_time as latestStartTime,
            eso.end_date as endDate,
            eso.standard_man_hours as standardManHours
        FROM engineer_sample_order eso
        LEFT JOIN t_project_item_order pio on pio.id = eso.PROJECT_ORDER_ID
        LEFT join t_project_item pi on pi.id = pio.item_id
        LEFT JOIN t_project p on pio.project_id  = p.id
        LEFT JOIN sys_user su ON su.user_id = eso.user_id AND su.del_flag = 0 AND su.is_user = 0 AND su.positive_status != 2
        LEFT JOIN t_category tc ON tc.category_id = eso.category_id
        LEFT JOIN sys_dept sd on sd.dept_id = tc.dept_id
        WHERE eso.del_flag = 0 AND eso.association_status = 0

        <choose>
            <!-- 当 deptId 不为空时 -->
            <when test="deptId != null">
                AND (FIND_IN_SET(#{deptId}, sd.ancestors) OR sd.dept_id = #{deptId})
            </when>
            <!-- 当 deptId 为空时 -->
            <otherwise>
                AND (sd.dept_id IN (464, 465) OR sd.ancestors REGEXP '(^|,)464(,|$)|(^|,)465(,|$)') -- 默认“护肤和彩妆研发部”
            </otherwise>
        </choose>
        <!-- 日期范围筛选条件 - 综合查询多个日期字段 -->
        <if test="beginDateRange != null and beginDateRange != '' and endDateRange != null and endDateRange != ''">
            AND (
            (date_format(eso.scheduled_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.scheduled_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            OR (date_format(eso.end_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.end_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            OR (date_format(eso.start_date,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.start_date,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            OR (date_format(eso.latest_start_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.latest_start_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            OR (date_format(eso.actual_start_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.actual_start_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            OR (date_format(eso.actual_finish_time,'%y%m%d') &gt;= date_format(#{beginDateRange},'%y%m%d') AND date_format(eso.actual_finish_time,'%y%m%d') &lt;= date_format(#{endDateRange},'%y%m%d'))
            )
        </if>
        <!-- 多个组别ID查询条件 -->
        <if test="deptIds != null and deptIds.length > 0">
            AND (
            <foreach collection="deptIds" item="deptIdItem" separator=" or ">
                (find_in_set(#{deptIdItem}, sd.ancestors) or sd.dept_id = #{deptIdItem})
            </foreach>
            )
        </if>
        <!-- 权限控制：当前用户权限限制 -->
        <if test="currentUserDeptId != null or currentUserId != null">
            AND (
            <!-- 部门权限：用户可查看的部门数据 -->
            <if test="currentUserDeptId != null">
                (find_in_set(#{currentUserDeptId}, sd.ancestors) or sd.dept_id = #{currentUserDeptId})
            </if>
            <!-- 个人权限：分配给当前用户的打样单 -->
            <if test="currentUserId != null">
                <if test="currentUserDeptId != null">OR</if>
                eso.user_id = #{currentUserId}
            </if>
            )
        </if>
        ORDER BY eso.latest_start_time ASC
    </select>

</mapper>
