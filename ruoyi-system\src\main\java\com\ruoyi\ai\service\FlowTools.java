package com.ruoyi.ai.service;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.hr.domain.TLegalHolidays;
import com.ruoyi.hr.mapper.TLeaveUserMapper;
import com.ruoyi.hr.service.ITLegalHolidaysService;
import com.ruoyi.system.service.ISysConfigService;
import io.github.imfangs.dify.client.DifyClient;
import io.github.imfangs.dify.client.DifyClientFactory;
import io.github.imfangs.dify.client.DifyCompletionClient;
import io.github.imfangs.dify.client.enums.ResponseMode;
import io.github.imfangs.dify.client.model.DifyConfig;
import io.github.imfangs.dify.client.model.file.FileUploadResponse;
import io.github.imfangs.dify.client.model.workflow.WorkflowRunRequest;
import io.github.imfangs.dify.client.model.workflow.WorkflowRunResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.*;

@Service
@Slf4j
public class FlowTools {

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private ITLegalHolidaysService tLegalHolidaysService;

    @Resource
    private TLeaveUserMapper leaveUserMapper;

    /**
     * 解析合同信息转json
     *
     * @param file
     * @return
     */
    public String convertContractFileToJson(MultipartFile file, String file_path, Long userId) throws Exception {

        String url = sysConfigService.selectConfigByKey("CONTRACT_FILE_TO_JSON_URL");
        String key = sysConfigService.selectConfigByKey("CONTRACT_FILE_TO_JSON_KEY");

        // 创建自定义配置
        DifyConfig config = DifyConfig.builder().baseUrl(url).apiKey(key).connectTimeout(120000)  // 连接超时（毫秒）
                .readTimeout(300000)    // 读取超时（毫秒）
                .writeTimeout(300000)   // 写入超时（毫秒）
                .build();

        // 使用自定义配置创建客户端
        DifyClient client = DifyClientFactory.createClient(config);

        // 创建工作流请求
        Map<String, Object> inputs = new HashMap<>();

        if (!StringUtils.isEmpty(file_path)) {
            // 构建参数
            inputs.put("file_path", file_path);

        } else if (file != null) {
            // 判断文件大小是否大于15MB
            long maxFileSize = 15 * 1024 * 1024; // 15MB
            if (file.getSize() > maxFileSize) {
                return "上传的文件大小不能超过15MB";
            } else if (file.isEmpty()) {
                return "文件为空，请选择一个文件上传";
            }

            FileUploadResponse fileUpload = FileUploadToDify(file, url, key, userId);
            log.info(fileUpload.toString());
            if (fileUpload == null) {
                return "上传文件失败";
            }
            // 创建 file 参数的 Map
            Map<String, Object> fileParams = new HashMap<>();
            fileParams.put("type", getFileType(fileUpload.getExtension()));
            fileParams.put("transfer_method", "remote_url");
            fileParams.put("url", "");
            fileParams.put("upload_file_id", fileUpload.getId());

            inputs.put("file", fileParams);
        }

        WorkflowRunRequest request = WorkflowRunRequest.builder().inputs(inputs).responseMode(ResponseMode.BLOCKING).user(userId.toString()).build();

        // 执行工作流并获取响应
        WorkflowRunResponse response = client.runWorkflow(request);
        System.out.println("工作流执行ID: " + response.getTaskId());

        // 输出结果
        if (response.getData() != null && response.getData().getOutputs() != null && response.getData().getOutputs().get("data") != null) {
            return response.getData().getOutputs().get("data").toString();
        }
        return null;
    }

    /**
     * 打样单自动排单
     *
     * @return 响应结果
     */
    public JSONObject autoScheduleSampleOrder(JSONObject dataObj) throws Exception {
        JSONObject returnObj = new JSONObject();
        returnObj.put("status","1");
        returnObj.put("msg","操作异常!");
        String url = sysConfigService.selectConfigByKey("RD_ORDER_URL");
        String key = sysConfigService.selectConfigByKey("RD_ORDER_KEY");

        // 创建自定义配置
        DifyConfig config = DifyConfig.builder().baseUrl(url).apiKey(key).connectTimeout(120000)  // 连接超时（毫秒）
                .readTimeout(300000)    // 读取超时（毫秒）
                .writeTimeout(300000)   // 写入超时（毫秒）
                .build();

        // 使用自定义配置创建客户端
        DifyClient client = DifyClientFactory.createClient(config);

        // 创建工作流请求参数
        Map<String, Object> inputs = new HashMap<>();
        inputs.put("projectOrderId",dataObj.getLong("projectOrderId")); // 打样单ID
        inputs.put("sampleOrderCode",dataObj.getString("code")); // 打样单编号
        inputs.put("difficultyLevelId",dataObj.getString("dylb")); // 0,1,2,3
        inputs.put("difficultyLevel",dataObj.getString("dylbLabel")); // A1,B1,A2等
        inputs.put("serviceMode",dataObj.getString("serviceMode")); // 客户模式,VIP/SVIP等等(可能为空)
        inputs.put("estimatedManHours",dataObj.getString("standardHours"));  // 标准工时
        inputs.put("checkType",dataObj.getString("checkType"));  // 1 根据客户等级    打样单难度
        inputs.put("categoryId",dataObj.getLong("categoryId")); // 类别id
        inputs.put("dylx",dataObj.getLong("dylx")); // 打样类别 0 打样 1 复样

        // 处理时间参数
        if (dataObj.containsKey("startDate") && dataObj.containsKey("endDate")) {
            // 1. 优先使用传入的开始时间和结束时间
            inputs.put("startDate", dataObj.getString("startDate"));
            inputs.put("endDate", dataObj.getString("endDate"));
        }
        else if (dataObj.containsKey("days")) {
            // 2. 如果有days参数，使用当前时间和days计算
            Date nowDate = DateUtils.getNowDate();
            String createTime = dataObj.getString("createTime");
            if(StringUtils.isNotNull(createTime)){
                inputs.put("startDate", createTime); // 可开始时间
            }else{
                inputs.put("startDate", DateUtils.dateTime(nowDate, DateUtils.YYYY_MM_DD_HH_MM_SS)); // 可开始时间
            }
            Date endDate = findTargetWorkday(nowDate,dataObj.getInteger("days"),1); // 计算最晚交付时间（去除非工作日）
            inputs.put("endDate", DateUtils.dateTime(endDate, DateUtils.YYYY_MM_DD_HH_MM_SS)); // 最晚交付时间
        } else {
            // 3. 默认情况：使用当前时间和7天后
            Date nowDate = DateUtils.getNowDate();
            inputs.put("startDate", DateUtils.dateTime(nowDate, DateUtils.YYYY_MM_DD_HH_MM_SS)); // 可开始时间
            Date endDate = DateUtils.addDays(nowDate, 7); // 默认7天(包含非工作日)
            inputs.put("endDate", DateUtils.dateTime(endDate, DateUtils.YYYY_MM_DD_HH_MM_SS)); // 最晚交付时间
        }

        inputs.put("sampleOrderRemark", dataObj.getString("remark"));// 打样单备注（非必填）

        WorkflowRunRequest request = WorkflowRunRequest.builder().inputs(inputs).responseMode(ResponseMode.BLOCKING).user(dataObj.getString("userId")).build();
        // 执行工作流并获取响应
        WorkflowRunResponse response = client.runWorkflow(request);
        // 输出结果
        if (response.getData() != null && response.getData().getOutputs() != null && response.getData().getOutputs().get("data") != null) {
            returnObj.put("status","2");
            returnObj.put("msg",response.getData().getOutputs().get("data").toString());
        }
        return returnObj;
    }

    /**
     * 上传文件
     */
    public static FileUploadResponse FileUploadToDify(MultipartFile file, String url, String key, Long userId) throws IOException {
        // 将 MultipartFile 转换为 File
        File tempFile = convertMultipartFileToFile(file);

        // 创建聊天客户端
        DifyCompletionClient completionClient = DifyClientFactory.createCompletionClient(url, key);

        // 上传文件
        FileUploadResponse uploadResponse = completionClient.uploadFile(tempFile, userId.toString());

        // 删除临时文件
        if (!tempFile.delete()) {
            System.err.println("警告：临时文件删除失败: " + tempFile.getAbsolutePath());
        }

        if (uploadResponse == null || uploadResponse.getId() == null) {
            System.out.println("文件上传失败");
            return null;
        }

        System.out.println("文件上传成功，文件ID: " + uploadResponse.getId());

        return uploadResponse;
    }

    private static File convertMultipartFileToFile(MultipartFile file) throws IOException {
        // 创建一个临时文件
        File tempFile = File.createTempFile("upload_", file.getOriginalFilename());
        tempFile.deleteOnExit(); // 在JVM退出时删除临时文件

        // 将 MultipartFile 的内容复制到临时文件
        Files.copy(file.getInputStream(), tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
        return tempFile;
    }


    /**
     * 根据文件类型返回对应的类型分类
     *
     * @param fileType 文件类型字符串
     * @return 对应的类型分类
     */
    public static String getFileType(String fileType) {
        if (fileType == null) {
            return "custom"; // 处理 null 输入
        }

        switch (fileType.toUpperCase()) {
            case "TXT":
            case "MD":
            case "MARKDOWN":
            case "PDF":
            case "HTML":
            case "XLSX":
            case "XLS":
            case "DOCX":
            case "CSV":
            case "EML":
            case "MSG":
            case "PPTX":
            case "PPT":
            case "XML":
            case "EPUB":
                return "document";

            case "JPG":
            case "JPEG":
            case "PNG":
            case "GIF":
            case "WEBP":
            case "SVG":
                return "image";

            case "MP3":
            case "M4A":
            case "WAV":
            case "WEBM":
            case "AMR":
                return "audio";

            case "MP4":
            case "MOV":
            case "MPEG":
            case "MPGA":
                return "video";

            default:
                return "custom"; // 处理其他类型
        }
    }


    /**
     * 返回0 工作日
     * @param time
     * @return
     */
    public int getAttendanceType(Date time){
        Calendar c = Calendar.getInstance();
        c.setTime(time);
        String weekday = String.valueOf((c.get(Calendar.DAY_OF_WEEK) - 1) == 0 ? 7 : (c.get(Calendar.DAY_OF_WEEK) - 1));
        int type = 1;//0工作日，1休息日，2节假日
        String [] weekdays = new String[]{"1","2","3","4","5"};
        for(String week : weekdays){
            if(weekday.equals(week)){
                type = 0;
            }
        }
        TLegalHolidays tLegalHolidays = new TLegalHolidays();
        tLegalHolidays.setYear(String.valueOf(c.get(Calendar.YEAR)));
        List<TLegalHolidays> holidaysList = tLegalHolidaysService.selectTLegalHolidaysList(tLegalHolidays);
        if(holidaysList != null && holidaysList.size() > 0){
            for(TLegalHolidays holidays : holidaysList){
                if(holidays.getDaysList() != null && holidays.getDaysList().size() > 0){
                    for(Map<String, Object> map : holidays.getDaysList()){
                        String day = (String) map.get("date");
                        if(DateUtils.dateTime(time).equals(day)){
                            if(holidays.getName().equals("补班")){
                                type = 0;
                            }else if(holidays.getName().equals("休息")){
                                type = 1;
                            }else{
                                type = 2;
                            }
                        }
                    }
                }
            }
        }
        return type;
    }

    /**
     * 计算目标工作日的方法
     *
     * @param startDate 起始日期字符串，例如 "2025-07-15 14:17:21"
     * @param days 需要移动的工作日天数
     * @param increment 时间跨度（-1：代表向前-1天数，1：代表向后1天查询）
     * @return 目标工作日的日期字符串，格式与输入相同
     */
    public Date findTargetWorkday(Date startDate, int days, int increment) {
        // 使用Calendar进行日期操作
        Calendar cal = Calendar.getInstance();
        cal.setTime(startDate);
        if (increment == 0) {
            return startDate;
        }
        int count = 0;
        while (count < days) {
            // 增加或减少 increment 天
            cal.add(Calendar.DATE, increment);
            // 获取当前日期
            Date currentDate = cal.getTime();
            // 判断是否为工作日
            int attendanceType = getAttendanceType(currentDate);
            if (attendanceType == 0) { // 工作日
                count++;
            }
        }
        // 返回目标工作日的字符串
        return cal.getTime();
    }

    /**
     * 是否请假
     * @param userId
     * @param time
     * @return
     */
    public boolean isLeave(Long userId,Date time){
         boolean isLeave = false;
         JSONObject params = new JSONObject();
         params.put("userId",userId);
         params.put("time",DateUtils.dateTime(time));
         Integer count =  leaveUserMapper.queryLeaveUserInfo(params);
         count = StringUtils.convertDefaultValue(count,0);
         if(count>0){
             isLeave = true;
         }
         return isLeave;
    }

    public static void main(String[] args) throws Exception {
        try {

            // 指定本地文件的路径
            File localFile = new File("C:\\Users\\<USER>\\Desktop\\公司\\提取合同信息\\华熙-瀛彩年度协议.pdf"); // 替换为实际文件路径

            // 创建 FileInputStream 以读取文件内容
            FileInputStream fileInputStream = new FileInputStream(localFile);

            // 创建 MockMultipartFile 实例
            MockMultipartFile mockFile = new MockMultipartFile("file", // 表单字段名称
                    localFile.getName(), // 文件名
                    "text/plain", // 文件类型，根据实际文件类型进行设置
                    fileInputStream // 文件内容的输入流
            );

            // 1. 测试整个工作流（上传文件+执行工作流）
//            String string = convertContractFileToJson(mockFile, 1L);
//            System.out.println("响应结果==" + string);

            // 2. 测试文件上传
//            // 创建一个 MockMultipartFile 实例
//            MockMultipartFile mockFile = new MockMultipartFile(
//                    "file", // 表单字段名称
//                    "test.txt", // 文件名
//                    "text/plain", // 文件类型
//                    "Hello, World!".getBytes() // 文件内容
//            );

            // 设置上传的 URL 和密钥
//            String url = "http://***********/v1";
//            String key = "app-ZnAcUzXcFIqK0AmMGURUgOkP";
//            Long userId = 1L; // 替换为实际的用户 ID
//
//            // 调用文件上传方法
//            FileUploadResponse response = FileUploadToDify(mockFile, url, key, userId);
//
//            // 输出结果
//            System.out.println("上传响应: " + response);
//            System.out.println("文件ID: " + response.getId());
//            System.out.println("文件类型: " + response.getExtension());

        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
