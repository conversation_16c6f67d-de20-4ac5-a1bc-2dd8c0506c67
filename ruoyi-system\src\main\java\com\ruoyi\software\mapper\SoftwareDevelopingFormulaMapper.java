package com.ruoyi.software.mapper;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.vo.AiDto;
import com.ruoyi.project.domain.ProjectItemOrder;
import com.ruoyi.qc.domain.QcBcpStandard;
import com.ruoyi.software.domain.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 研发配方Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-20
 */

@DataSource(DataSourceType.SLAVE)
public interface SoftwareDevelopingFormulaMapper
{

    public SoftwareDevelopingFormula selectSoftwareDevelopingFormulaById(Long id);

    public SoftwareDevelopingFormula selectSoftwareDevelopingProductSafetyAssessmentDataFormulaById(Long id);

    public List<SoftwareDevelopingFormula> selectSoftwareDevelopingFormulaList(SoftwareDevelopingFormula softwareDevelopingFormula);

    public List<SoftwareDevelopingFormula> selectSoftwareDevelopingMaterialFormulaList(SoftwareDevelopingFormula softwareDevelopingFormula);

    public int insertSoftwareDevelopingFormula(SoftwareDevelopingFormula softwareDevelopingFormula);

    public int updateSoftwareDevelopingFormula(SoftwareDevelopingFormula softwareDevelopingFormula);

    public int updateSoftwareDevelopingFormulaFiles(SoftwareDevelopingFormula softwareDevelopingFormula);

    public int deleteSoftwareDevelopingFormulaByIds(Long[] ids);

    void updateFormulaPriceData(Map<String, Object> params);
    //更新配方出口国家
    void updateFormulaImportInfo(Long id);
    //更新原料周期
    void updateFormulaCycleInfoInfo(Long id);
    //获取配方类别数据
    List<JSONObject> queryFormulaClassifyData();

    //获取是否存在
    Integer queryFormulaLabCount(SoftwareDevelopingFormula softwareDevelopingFormula);

    //获取配方数量
    Integer queryFormulaCount();

    //更新是否包含日本原料
    void updateFormulaMaterialData(Long id);

    //更新是否含有周期超过30天原料
    void updateFormulaMaterialCycleData(Long id);

    //查找配方信息
    SoftwareDevelopingFormula queryFormulaDataInfo(SoftwareMaterial softwareMaterial);

    List<JSONObject> queryAllFormulaData();

    //保存变更记录
    void batchMaterialFormulaRecordList(List<Map<String, Object>> mapData);

    //获取变更记录
    List<JSONObject> queryFormualMaterialRecipeChangeHistoryData(Long id);

    //获取执行标准内容
    List<JSONObject> queryFormulaZxbzDataList(Long id);

    //获取标准详细内容
    JSONObject queryFormulaZxbzDataDetail(Long id);

    //配方成分
    List<Map<String, Object>> queryIngredientExcelList(Long id);

    //获取配方成分为配方的 获取基础数据
    List<Map<String, Object>> queryIngredientNewExcelList(List<Map<String, Object>> formulaIdList);

    //获取特殊原料替换信息
    List<Map<String, Object>> queryMaterialFormulaSpecialInfo(Long id);

    void deleteTipsMaterialFormulaInfo(Long formulaId);

    void batchTipsMaterialFormulaInfo(@Param("list") List<Map<String, Object>> dataMaps);

    //安评报告使用目的
    List<Map<String, Object>> queryFormulaSymdInfo(Long id);

    void deleteFormulaSmdInfo(Long formulaId);

    void batchFormulaSymdInfo(@Param("list") List<Map<String, Object>> maps);

    Integer queryPFormulaCountInfo(Long formulaId);

    String queryMaxPFormulaInfo(Long formulaId);

    //获取P配方内容
    List<Map<String, Object>> queryPFormulaMaterialInfo(Long id);

    String queryMaterialCountInfo(Long formulaId);

    List<String> queryPFormulaDataList(Long formulaId);

    void updateBpNoteInfo(SoftwareDevelopingFormula formualInfo);

    Integer queryFormualBMaterialInfo(Long id);

    List<Map<String, Object>> queryFormulaBMaterialDataList(Long id);

    List<Long> queryPFormulaIdDataInfo(Long formulaId);

    List<Map<String, Object>> queryFormulaMaterialNewInfo(long formulaId);

    List<JSONObject> querySoftwareFormulaReleaseDataList(SoftwareDevelopingFormula softwareDevelopingFormula);

    List<JSONObject> querySoftwareFormulaReleaseByIdDataList(@Param("list") List<Long> formulaIdList);

    void updateSoftwareDevelopingFormulaCkcf(@Param("list") Set<Long> formulaIdSet,@Param("voucherCode") String voucherCode);

    void updateSoftwareDevelopingFormulaHuahe(@Param("list") Set<Long> formulaIdSet,@Param("voucherCode") String voucherCode);

    void updateSoftwareDevelopingFormulaFiling(@Param("list") List<Long> formulaIdSet,@Param("voucherCode") String voucherCodde,@Param("userName") String userName);

    void updateSoftwareDevelopingFormulaCancelFiling(Long formulaId);

    //保存配方组织关系
    void saveFormulaDeptDataInfo(Map<String, Object> params);

    //获取分类
    List<JSONObject> queryUserFormulaCategory(SoftwareFormulaRelease softwareFormulaRelease);

    List<JSONObject> queryUserUseFormulaCategory(SoftwareFormulaRelease softwareFormulaRelease);

    void addFormulaCategoryData(Map<String, Object> params);

    void deleteSoftwareFormulaCategoryUser(SoftwareFormulaRelease softwareFormulaRelease);

    List<Long> queryFormulaShareDeptDataDetail(Long id);

    void deleteFormulaDeptDataInfo(Long id);

    //获取价格
    List<Map<String, Object>> queryFormulaMaterialPriceInfo(Long id);

    List<SoftwareDevelopingFormula> selectSoftwareDevelopingFormulaPriceList(SoftwareDevelopingFormula softwareDevelopingFormula);

    Integer queryFormulaSpecZxbzCount(Long fid);

    void updateSoftwareDevelopingFormulaZs(@Param("formulaId") Long formulaId,@Param("voucherCode") String voucherCode);

    void insertFormulaBcpTemplateData(SoftwareDevelopingFormula softwareDevelopingFormula);

    List<TUnMaterialFormula> queryFormulaMaterialList(Long formulaId);

    void batchInsertInfo(List<TUnMaterialFormulaAudit> materialFormulaList);

    List<JSONObject> queryFormulaSpecDataList();

    void updateFormulaSpecData(Map<String, Object> params);

    void updateMaterialFormulaSymdInfo(Map<String, Object> materialFormulaParams);

    List<Long> queryFormulaDataList();

    List<JSONObject> queryFormulaProjectList(String projectNo);

    void updateFormulaProductNameData(Map<String, Object> params);

    JSONObject queryFormulaInfo(String labNo);

    List<JSONObject> queryFormulaMaterialCostDataList(Long formulaId);

    List<JSONObject> queryOrderFormulaDataList(String labNo);

    JSONObject queryFormulaProductName(ProjectItemOrder projectItemOrder);

    List<JSONObject> queryFormulaAppointMaterialDataList(Long id);

    List<SoftwareDevelopingFormula> selectSoftwareDevelopingFormulaListByErpCode(@Param("erpCode") String erpCode);

    //获取配方类别数据
    JSONObject queryFormulaCategoryDataInfo(QcBcpStandard qcBcpStandard);
    List<JSONObject> queryFormulaCategoryDataList(QcBcpStandard qcBcpStandard);

    JSONObject queryFormulaCategoryDetailData(JSONObject obj);

    void updateSoftwareDevelopingFormulaProductSafetyAssessmentData(SoftwareDevelopingFormula softwareDevelopingFormula);

    List<Long> querySoftwareFormulaDataList();

    List<Long> querySoftwareFormulaDataListByUpdateTime();

    void updateSoftwareDevelopingFormulaVersion(Long id);

    List<JSONObject> queryFormulaGxList();

    void updateZdyFormulaDesc(Map<String, Object> params);

    List<JSONObject> queryFormulaAiDataList(AiDto aiDto);

    Long selectFormulaIdByFormulaCode(String formulaCode);

    List<JSONObject> queryEnowFormulaDataList(String code);

    String selectLabCodeByErpCode(@Param("erpCode") String erpCode);

    List<SoftwareDevelopingFormula> selectRdFormulaList(SoftwareDevelopingFormula softwareDevelopingFormula);

    JSONObject queryMaterialFormulaSpecZxbzDataList(Long id);
}
