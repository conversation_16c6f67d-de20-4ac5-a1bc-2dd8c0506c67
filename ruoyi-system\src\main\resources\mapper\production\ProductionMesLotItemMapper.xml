<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.ProductionMesLotItemMapper">

    <sql id="selectProductionMesLotItemVo">
        SELECT
            li.id,
            li.lot_no,
            li.schedule_code,
            li.work_date,
            li.sailings,
            li.area_no,
            li.equipment_no,
            li.op_no,
            li.line_code,
            li.line_leader,
            li.product_nums,
            li.del_flag,
            li.create_by,
            li.create_time,
            li.update_by,
            li.update_time,
            li.remark,
            li.type
        FROM
            t_production_mes_lot_item li
    </sql>

    <select id="selectProductionMesLotItemList" parameterType="ProductionMesLotItem" resultType="ProductionMesLotItem">
        <include refid="selectProductionMesLotItemVo"/>
        <where>
            li.del_flag = 0
            <if test="lotNo != null  and lotNo != ''"> and li.lot_no = #{lotNo}</if>
            <if test="scheduleCode != null  and scheduleCode != ''"> and li.schedule_code = #{scheduleCode}</if>
            <if test="workDate != null "> and li.work_date = #{workDate}</if>
            <if test="sailings != null  and sailings != ''"> and li.sailings = #{sailings}</if>
            <if test="areaNo != null  and areaNo != ''"> and li.area_no = #{areaNo}</if>
            <if test="equipmentNo != null  and equipmentNo != ''"> and li.equipment_no = #{equipmentNo}</if>
            <if test="opNo != null  and opNo != ''"> and li.op_no = #{opNo}</if>
            <if test="lineCode != null  and lineCode != ''"> and li.line_code = #{lineCode}</if>
            <if test="lineLeader != null  and lineLeader != ''"> and li.line_leader = #{lineLeader}</if>
            <if test="type != null  and type != ''"> and li.type = #{type}</if>
        </where>
        order by li.id desc
    </select>

    <select id="selectProductionMesLotItemById" parameterType="Long" resultType="ProductionMesLotItem" >
        <include refid="selectProductionMesLotItemVo"/>
        where li.id = #{id}
    </select>

    <select id="selectProductionMesLotItemByParams" parameterType="ProductionMesLotItem" resultType="ProductionMesLotItem" >
        <include refid="selectProductionMesLotItemVo"/>
        <where>
            <if test="lotNo != null  and lotNo != ''"> and li.lot_no = #{lotNo}</if>
            <if test="workDate != null "> and li.work_date = #{workDate}</if>
            <if test="sailings != null  and sailings != ''"> and li.sailings = #{sailings}</if>
            <if test="areaNo != null  and areaNo != ''"> and li.area_no = #{areaNo}</if>
            <if test="equipmentNo != null  and equipmentNo != ''"> and li.equipment_no = #{equipmentNo}</if>
            <if test="opNo != null  and opNo != ''"> and li.op_no = #{opNo}</if>
        </where>
    </select>

    <insert id="insertProductionMesLotItem" parameterType="ProductionMesLotItem" useGeneratedKeys="true" keyProperty="id">
        insert into t_production_mes_lot_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="lotNo != null">lot_no,</if>
            <if test="scheduleCode != null">schedule_code,</if>
            <if test="workDate != null">work_date,</if>
            <if test="sailings != null">sailings,</if>
            <if test="areaNo != null">area_no,</if>
            <if test="equipmentNo != null">equipment_no,</if>
            <if test="opNo != null">op_no,</if>
            <if test="lineCode != null">line_code,</if>
            <if test="lineLeader != null">line_leader,</if>
            <if test="productNums != null">product_nums,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="type != null">type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="lotNo != null">#{lotNo},</if>
            <if test="scheduleCode != null">#{scheduleCode},</if>
            <if test="workDate != null">#{workDate},</if>
            <if test="sailings != null">#{sailings},</if>
            <if test="areaNo != null">#{areaNo},</if>
            <if test="equipmentNo != null">#{equipmentNo},</if>
            <if test="opNo != null">#{opNo},</if>
            <if test="lineCode != null">#{lineCode},</if>
            <if test="lineLeader != null">#{lineLeader},</if>
            <if test="productNums != null">#{productNums},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="type != null">#{type},</if>
         </trim>
    </insert>

    <update id="updateProductionMesLotItem" parameterType="ProductionMesLotItem">
        update t_production_mes_lot_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="lotNo != null">lot_no = #{lotNo},</if>
            <if test="scheduleCode != null">schedule_code = #{scheduleCode},</if>
            <if test="workDate != null">work_date = #{workDate},</if>
            <if test="sailings != null">sailings = #{sailings},</if>
            <if test="areaNo != null">area_no = #{areaNo},</if>
            <if test="equipmentNo != null">equipment_no = #{equipmentNo},</if>
            <if test="opNo != null">op_no = #{opNo},</if>
            <if test="lineCode != null">line_code = #{lineCode},</if>
            <if test="lineLeader != null">line_leader = #{lineLeader},</if>
            <if test="productNums != null">product_nums = #{productNums},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="type != null">type = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteProductionMesLotItemByIds" parameterType="String">
        update  t_production_mes_lot_item set del_flag = 2,update_time = now() where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
