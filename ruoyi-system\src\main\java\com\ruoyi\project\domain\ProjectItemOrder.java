package com.ruoyi.project.domain;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 子项目订单对象 t_project_item_order
 *
 * <AUTHOR>
 * @date 2021-09-23
 */
public class ProjectItemOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    private List<Integer> idArr;

    private Integer type;

    /** 子项目id */
    @Excel(name = "子项目id")
    private Long itemId;

    /** 报价 */
    private String samplePrice;

    private Long customerId;

    /** 系统生成编码 */
    private String dynCode;
    /** 子订单编码 */
    @Excel(name = "子订单编码")
    private String code;

    /** 订单状态(关联数据字典表) */
    @Excel(name = "订单状态(关联数据字典表)")
    private String status;

    private String itemStatus;

    private Long processUserId;
    //项目总二级状态
    private String projectItemstatus;
    /** 步骤 */
    @Excel(name = "步骤")
    private Integer step;

    @Excel(name = "步骤")
    private String currentStep;

    private Integer feedStatus;

    /** 审核状态（0待审核，1审核通过，2审核不通过） */
    @Excel(name = "审核状态", readConverterExp = "0=待审核，1审核通过，2审核不通过")
    private Integer shStatus;

    /** 审核状态（0待审核，1审核通过，2审核不通过） */
    @Excel(name = "审核状态", readConverterExp = "0=待审核，1审核通过，2审核不通过")
    private Integer shStatus1;

    private String auditFields;
    /** 确认编码 */
    @Excel(name = "确认编码")
    private String confirmCode;

    private String selectedCode;

    /** 期望完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "期望完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date qwTime;

    private Integer isAudit;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 预计完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预计完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date yjTime;

    private Integer fromType;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预计完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date delayYjTime;

    /** 第一次预计完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "第一次预计完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firstYjTime;

    /** 实际完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "实际完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sjTime;

    /** 及时率（%） */
    @Excel(name = "及时率", readConverterExp = "%=")
    private BigDecimal timelyRate;

    /** 修改意见 */
    @Excel(name = "修改意见")
    private String amendments;

    /** 修改意见 */
    @Excel(name = "修改意见")
    private String amendments1;

    private Integer isEntered;

    /** 差异化字段(json) */
    @Excel(name = "差异化字段(json)")
    private String fields;

    private String workstationDiagramDatas;

    private Integer isApply;

    private Integer delFlag;

    private String note;

    private String projectCode;

    private String projectItemCode;

    private String productName;

    private String customerName;
    private String customerShortName;

    private String brandName;

    private String customerYw;

    private Long projectOfferOrderId;

    private Long engineerId;
    /**
     * 是否撤销 0 否 1 是 默认0
     */
    private  Integer isRevoke;

    private String projectItemType;

    private Long projectId;

    private String itemFields;

    private String itemName;

    private String itemNames;

    private String imgs;

    private String files;

    private String projectItemName;

    private String preItemFields;

    private String orderStatus;

    private Integer projectItemOrderStatus;

    private List<JSONObject> offerDataList;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date orderYtTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedTime;

    private Integer orderNum;

    private Integer yjSpDdl;

    private Long userId;

    private String customerLevel;

    private String projectLevel;

    private String projectNo;

    private String customerAssist;

    private Integer isFeed;
    private String itemSqlx;

    private Integer isSync;


    private Integer isApplyOrder;
    private Integer isApplyPurchase;

    private List<Long> projectIds;

    private String mergeFields;

    private String laboratory;

    private String labNo;

    private String formulaCode;

    private Integer applyType;

    private Integer dataId;

    private Integer addStyle;

    private String bomChangeArray;
    private String bomChangeReplyArray;

    private String projectProductId;

    private Integer projectType;

    private String itemText;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bcReplyTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gongyiReplyTime;

    //商品类型
    private String productType;

    private String bcReplyStatus;
    private String archiveCode;

    private List<ProjectBc> bcList;
    private List<ProjectOrderBcReply> projectOrderBcReplyList;


    private BigDecimal contentPrice;
    private BigDecimal packagingMaterialPrice;
    private BigDecimal fillingPrice;
    private BigDecimal totalPrice;

    private String requestType;

    private Long executionId;

    private String auditRemark;
    private String itemRemark;
    private String filesFushen;

    private Integer isStep;

    private Integer executionStatus;

    private Integer isEdit;

    private String opType;

    private String projectOfferDatas;

    //包材价格信息
    private String packagingMaterialDatas;
    //包材价格信息类型
    private String packagingMaterialDatasType;

    private String gongyiReplyUser;
    private String bcReplyUser;

    private String productTabs;

    //列表数据
    private String previewExportProjectOfferDatas;
    //包装材料
    private String packagingTypeArrayData;
    //半成品主包材
    private String bcpPackagingTypeArrayData;
    //表单数据
    private String previewExportForm;

    private String workUserId;

    private String bomArray;

    private String categoryText;

    public String getProductTabs() {
        return productTabs;
    }

    public void setProductTabs(String productTabs) {
        this.productTabs = productTabs;
    }

    public String getOpType() {
        return opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }

    public String getBomChangeReplyArray() {
        return bomChangeReplyArray;
    }

    public void setBomChangeReplyArray(String bomChangeReplyArray) {
        this.bomChangeReplyArray = bomChangeReplyArray;
    }

    public String getBomChangeArray() {
        return bomChangeArray;
    }

    public void setBomChangeArray(String bomChangeArray) {
        this.bomChangeArray = bomChangeArray;
    }

    public String getBcReplyStatus() {
        return bcReplyStatus;
    }

    public void setBcReplyStatus(String bcReplyStatus) {
        this.bcReplyStatus = bcReplyStatus;
    }

    public Date getBcReplyTime() {
        return bcReplyTime;
    }

    public void setBcReplyTime(Date bcReplyTime) {
        this.bcReplyTime = bcReplyTime;
    }

    public List<ProjectOrderBcReply> getProjectOrderBcReplyList() {
        return projectOrderBcReplyList;
    }

    public void setProjectOrderBcReplyList(List<ProjectOrderBcReply> projectOrderBcReplyList) {
        this.projectOrderBcReplyList = projectOrderBcReplyList;
    }

    private List<String> orderTypes;

    public List<String> getOrderTypes() {
        return orderTypes;
    }

    public void setOrderTypes(List<String> orderTypes) {
        this.orderTypes = orderTypes;
    }

    public List<ProjectBc> getBcList() {
        return bcList;
    }

    public void setBcList(List<ProjectBc> bcList) {
        this.bcList = bcList;
    }

    public String getSelectedCode() {
        return selectedCode;
    }

    public void setSelectedCode(String selectedCode) {
        this.selectedCode = selectedCode;
    }

    public String getItemSqlx() {
        return itemSqlx;
    }

    public void setItemSqlx(String itemSqlx) {
        this.itemSqlx = itemSqlx;
    }

    public List<Long> getProjectIds() {
        return projectIds;
    }

    public void setProjectIds(List<Long> projectIds) {
        this.projectIds = projectIds;
    }

    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    public String getImgs() {
        return imgs;
    }

    public void setImgs(String imgs) {
        this.imgs = imgs;
    }

    public String getFiles() {
        return files;
    }

    public void setFiles(String files) {
        this.files = files;
    }

    public String getProjectItemstatus() {
        return projectItemstatus;
    }

    public void setProjectItemstatus(String projectItemstatus) {
        this.projectItemstatus = projectItemstatus;
    }

    public String getCustomerShortName() {
        return customerShortName;
    }

    public void setCustomerShortName(String customerShortName) {
        this.customerShortName = customerShortName;
    }

    public String getProjectLevel() {
        return projectLevel;
    }

    public void setProjectLevel(String projectLevel) {
        this.projectLevel = projectLevel;
    }

    public String getCustomerLevel() {
        return customerLevel;
    }

    public void setCustomerLevel(String customerLevel) {
        this.customerLevel = customerLevel;
    }

    public String getDynCode() {
        return dynCode;
    }

    public void setDynCode(String dynCode) {
        this.dynCode = dynCode;
    }

    private String[] ids;

    public String[] getIds() {
        return ids;
    }

    public void setIds(String[] ids) {
        this.ids = ids;
    }

    public String getProjectItemName() {
        return projectItemName;
    }

    public void setProjectItemName(String projectItemName) {
        this.projectItemName = projectItemName;
    }

    public String getProjectItemType() {
        return projectItemType;
    }

    public void setProjectItemType(String projectItemType) {
        this.projectItemType = projectItemType;
    }

    public String getProjectItemCode() {
        return projectItemCode;
    }

    public void setProjectItemCode(String projectItemCode) {
        this.projectItemCode = projectItemCode;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public String getLabNo() {
        return labNo;
    }

    public void setLabNo(String labNo) {
        this.labNo = labNo;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setItemId(Long itemId)
    {
        this.itemId = itemId;
    }

    public Long getItemId()
    {
        return itemId;
    }
    public void setCode(String code)
    {
        this.code = code;
    }

    public String getCode()
    {
        return code;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setStep(Integer step)
    {
        this.step = step;
    }

    public Integer getStep()
    {
        return step;
    }
    public void setCurrentStep(String currentStep)
    {
        this.currentStep = currentStep;
    }

    public String getCurrentStep()
    {
        return currentStep;
    }
    public void setShStatus(Integer shStatus)
    {
        this.shStatus = shStatus;
    }

    public Integer getShStatus()
    {
        return shStatus;
    }
    public void setShStatus1(Integer shStatus1)
    {
        this.shStatus1 = shStatus1;
    }

    public Integer getShStatus1()
    {
        return shStatus1;
    }
    public void setConfirmCode(String confirmCode)
    {
        this.confirmCode = confirmCode;
    }

    public String getConfirmCode()
    {
        return confirmCode;
    }
    public void setQwTime(Date qwTime)
    {
        this.qwTime = qwTime;
    }



    public Date getQwTime()
    {
        return qwTime;
    }
    public void setYjTime(Date yjTime)
    {
        this.yjTime = yjTime;
    }

    public Date getYjTime()
    {
        return yjTime;
    }
    public void setSjTime(Date sjTime)
    {
        this.sjTime = sjTime;
    }

    public Date getSjTime()
    {
        return sjTime;
    }
    public void setTimelyRate(BigDecimal timelyRate)
    {
        this.timelyRate = timelyRate;
    }

    public BigDecimal getTimelyRate()
    {
        return timelyRate;
    }
    public void setAmendments(String amendments)
    {
        this.amendments = amendments;
    }

    public String getAmendments()
    {
        return amendments;
    }
    public void setAmendments1(String amendments1)
    {
        this.amendments1 = amendments1;
    }

    public String getAmendments1()
    {
        return amendments1;
    }
    public void setFields(String fields)
    {
        this.fields = fields;
    }

    public String getFields()
    {
        return fields;
    }
    public void setDelFlag(Integer delFlag)
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag()
    {
        return delFlag;
    }

    public Integer getIsRevoke() {
        return isRevoke;
    }

    public void setIsRevoke(Integer isRevoke) {
        this.isRevoke = isRevoke;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }


    public String getWorkstationDiagramDatas() {
        return workstationDiagramDatas;
    }

    public void setWorkstationDiagramDatas(String workstationDiagramDatas) {
        this.workstationDiagramDatas = workstationDiagramDatas;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getAuditFields() {
        return auditFields;
    }

    public String getItemFields() {
        return itemFields;
    }

    public void setItemFields(String itemFields) {
        this.itemFields = itemFields;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public void setAuditFields(String auditFields) {
        this.auditFields = auditFields;
    }

    public Date getFirstYjTime() {
        return firstYjTime;
    }

    public void setFirstYjTime(Date firstYjTime) {
        this.firstYjTime = firstYjTime;
    }

    public String getItemStatus() {
        return itemStatus;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public void setItemStatus(String itemStatus) {
        this.itemStatus = itemStatus;
    }

    public String getPreItemFields() {
        return preItemFields;
    }

    public void setPreItemFields(String preItemFields) {
        this.preItemFields = preItemFields;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Date getOrderYtTime() {
        return orderYtTime;
    }

    public void setOrderYtTime(Date orderYtTime) {
        this.orderYtTime = orderYtTime;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getYjSpDdl() {
        return yjSpDdl;
    }

    public void setYjSpDdl(Integer yjSpDdl) {
        this.yjSpDdl = yjSpDdl;
    }

    public String getItemNames() {
        return itemNames;
    }

    public void setItemNames(String itemNames) {
        this.itemNames = itemNames;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getProjectItemOrderStatus() {
        return projectItemOrderStatus;
    }

    public Date getDelayYjTime() {
        return delayYjTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public void setDelayYjTime(Date delayYjTime) {
        this.delayYjTime = delayYjTime;
    }

    public void setProjectItemOrderStatus(Integer projectItemOrderStatus) {
        this.projectItemOrderStatus = projectItemOrderStatus;
    }

    public List<Integer> getIdArr() {
        return idArr;
    }

    public void setIdArr(List<Integer> idArr) {
        this.idArr = idArr;
    }

    public String getCustomerAssist() {
        return customerAssist;
    }

    public void setCustomerAssist(String customerAssist) {
        this.customerAssist = customerAssist;
    }

    public Integer getFeedStatus() {
        return feedStatus;
    }

    public void setFeedStatus(Integer feedStatus) {
        this.feedStatus = feedStatus;
    }

    public Date getFeedTime() {
        return feedTime;
    }

    public void setFeedTime(Date feedTime) {
        this.feedTime = feedTime;
    }

    public String getMergeFields() {
        return mergeFields;
    }

    public void setMergeFields(String mergeFields) {
        this.mergeFields = mergeFields;
    }

    public Integer getIsSync() {
        return isSync;
    }

    public void setIsSync(Integer isSync) {
        this.isSync = isSync;
    }

    public String getLaboratory() {
        return laboratory;
    }

    public void setLaboratory(String laboratory) {
        this.laboratory = laboratory;
    }

    public Integer getIsApply() {
        return isApply;
    }

    public void setIsApply(Integer isApply) {
        this.isApply = isApply;
    }

    public Integer getApplyType() {
        return applyType;
    }

    public void setApplyType(Integer applyType) {
        this.applyType = applyType;
    }


    public Integer getDataId() {
        return dataId;
    }

    public void setDataId(Integer dataId) {
        this.dataId = dataId;
    }


    public Integer getIsApplyOrder() {
        return isApplyOrder;
    }

    public void setIsApplyOrder(Integer isApplyOrder) {
        this.isApplyOrder = isApplyOrder;
    }

    public Integer getIsApplyPurchase() {
        return isApplyPurchase;
    }

    public void setIsApplyPurchase(Integer isApplyPurchase) {
        this.isApplyPurchase = isApplyPurchase;
    }


    public Integer getAddStyle() {
        return addStyle;
    }

    public void setAddStyle(Integer addStyle) {
        this.addStyle = addStyle;
    }


    public String getProjectProductId() {
        return projectProductId;
    }

    public void setProjectProductId(String projectProductId) {
        this.projectProductId = projectProductId;
    }

    public Integer getProjectType() {
        return projectType;
    }

    public void setProjectType(Integer projectType) {
        this.projectType = projectType;
    }


    public Date getGongyiReplyTime() {
        return gongyiReplyTime;
    }

    public void setGongyiReplyTime(Date gongyiReplyTime) {
        this.gongyiReplyTime = gongyiReplyTime;
    }

    public String getArchiveCode() {
        return archiveCode;
    }

    public void setArchiveCode(String archiveCode) {
        this.archiveCode = archiveCode;
    }

    public BigDecimal getContentPrice() {
        return contentPrice;
    }

    public void setContentPrice(BigDecimal contentPrice) {
        this.contentPrice = contentPrice;
    }

    public BigDecimal getPackagingMaterialPrice() {
        return packagingMaterialPrice;
    }

    public void setPackagingMaterialPrice(BigDecimal packagingMaterialPrice) {
        this.packagingMaterialPrice = packagingMaterialPrice;
    }

    public BigDecimal getFillingPrice() {
        return fillingPrice;
    }

    public void setFillingPrice(BigDecimal fillingPrice) {
        this.fillingPrice = fillingPrice;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getFormulaCode() {
        return formulaCode;
    }

    public void setFormulaCode(String formulaCode) {
        this.formulaCode = formulaCode;
    }

    public List<JSONObject> getOfferDataList() {
        return offerDataList;
    }

    public void setOfferDataList(List<JSONObject> offerDataList) {
        this.offerDataList = offerDataList;
    }

    public Long getExecutionId() {
        return executionId;
    }

    public void setExecutionId(Long executionId) {
        this.executionId = executionId;
    }

    public Integer getExecutionStatus() {
        return executionStatus;
    }

    public void setExecutionStatus(Integer executionStatus) {
        this.executionStatus = executionStatus;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("itemId", getItemId())
            .append("code", getCode())
            .append("status", getStatus())
            .append("step", getStep())
            .append("currentStep", getCurrentStep())
            .append("shStatus", getShStatus())
            .append("shStatus1", getShStatus1())
            .append("confirmCode", getConfirmCode())
            .append("qwTime", getQwTime())
            .append("yjTime", getYjTime())
            .append("sjTime", getSjTime())
            .append("timelyRate", getTimelyRate())
            .append("amendments", getAmendments())
            .append("amendments1", getAmendments1())
            .append("fields", getFields())
            .append("remark", getRemark())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public String getFilesFushen() {
        return filesFushen;
    }

    public void setFilesFushen(String filesFushen) {
        this.filesFushen = filesFushen;
    }

    public String getCustomerYw() {
        return customerYw;
    }

    public void setCustomerYw(String customerYw) {
        this.customerYw = customerYw;
    }

    public Integer getIsFeed() {
        return isFeed;
    }

    public void setIsFeed(Integer isFeed) {
        this.isFeed = isFeed;
    }

    public Integer getIsEdit() {
        return isEdit;
    }

    public void setIsEdit(Integer isEdit) {
        this.isEdit = isEdit;
    }

    public String getProjectOfferDatas() {
        return projectOfferDatas;
    }

    public String getPackagingMaterialDatas() {
        return packagingMaterialDatas;
    }

    public void setPackagingMaterialDatas(String packagingMaterialDatas) {
        this.packagingMaterialDatas = packagingMaterialDatas;
    }

    public String getGongyiReplyUser() {
        return gongyiReplyUser;
    }

    public void setGongyiReplyUser(String gongyiReplyUser) {
        this.gongyiReplyUser = gongyiReplyUser;
    }

    public String getBcReplyUser() {
        return bcReplyUser;
    }

    public void setBcReplyUser(String bcReplyUser) {
        this.bcReplyUser = bcReplyUser;
    }

    public void setProjectOfferDatas(String projectOfferDatas) {
        this.projectOfferDatas = projectOfferDatas;
    }

    public Long getProcessUserId() {
        return processUserId;
    }

    public void setProcessUserId(Long processUserId) {
        this.processUserId = processUserId;
    }

    public Integer getIsAudit() {
        return isAudit;
    }

    public void setIsAudit(Integer isAudit) {
        this.isAudit = isAudit;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public Long getProjectOfferOrderId() {
        return projectOfferOrderId;
    }

    public void setProjectOfferOrderId(Long projectOfferOrderId) {
        this.projectOfferOrderId = projectOfferOrderId;
    }


    public String getPreviewExportProjectOfferDatas() {
        return previewExportProjectOfferDatas;
    }

    public void setPreviewExportProjectOfferDatas(String previewExportProjectOfferDatas) {
        this.previewExportProjectOfferDatas = previewExportProjectOfferDatas;
    }

    public String getPackagingTypeArrayData() {
        return packagingTypeArrayData;
    }

    public void setPackagingTypeArrayData(String packagingTypeArrayData) {
        this.packagingTypeArrayData = packagingTypeArrayData;
    }

    public String getPreviewExportForm() {
        return previewExportForm;
    }

    public void setPreviewExportForm(String previewExportForm) {
        this.previewExportForm = previewExportForm;
    }

    public String getBomArray() {
        return bomArray;
    }

    public void setBomArray(String bomArray) {
        this.bomArray = bomArray;
    }

    public String getPackagingMaterialDatasType() {
        return packagingMaterialDatasType;
    }

    public void setPackagingMaterialDatasType(String packagingMaterialDatasType) {
        this.packagingMaterialDatasType = packagingMaterialDatasType;
    }

    public String getItemText() {
        return itemText;
    }

    public void setItemText(String itemText) {
        this.itemText = itemText;
    }

    public String getItemRemark() {
        return itemRemark;
    }

    public void setItemRemark(String itemRemark) {
        this.itemRemark = itemRemark;
    }

    public Integer getFromType() {
        return fromType;
    }

    public void setFromType(Integer fromType) {
        this.fromType = fromType;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getBcpPackagingTypeArrayData() {
        return bcpPackagingTypeArrayData;
    }

    public void setBcpPackagingTypeArrayData(String bcpPackagingTypeArrayData) {
        this.bcpPackagingTypeArrayData = bcpPackagingTypeArrayData;
    }

    public String getSamplePrice() {
        return samplePrice;
    }

    public void setSamplePrice(String samplePrice) {
        this.samplePrice = samplePrice;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Integer getIsStep() {
        return isStep;
    }

    public void setIsStep(Integer isStep) {
        this.isStep = isStep;
    }

    public String getWorkUserId() {
        return workUserId;
    }

    public void setWorkUserId(String workUserId) {
        this.workUserId = workUserId;
    }

    public Long getEngineerId() {
        return engineerId;
    }

    public void setEngineerId(Long engineerId) {
        this.engineerId = engineerId;
    }

    public Integer getIsEntered() {
        return isEntered;
    }

    public void setIsEntered(Integer isEntered) {
        this.isEntered = isEntered;
    }
    public String getCategoryText() {
        return categoryText;
    }
    public void setCategoryText(String categoryText) {
        this.categoryText = categoryText;
    }
}
