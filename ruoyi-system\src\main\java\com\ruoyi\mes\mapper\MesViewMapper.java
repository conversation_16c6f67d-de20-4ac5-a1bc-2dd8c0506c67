package com.ruoyi.mes.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.mes.domain.MesLotLog;
import com.ruoyi.mes.domain.WipLotAreaUser;
import com.ruoyi.mes.domain.WipLotUser;
import com.ruoyi.mes.domain.WipMaterialLog;
import com.ruoyi.mes.vo.MesLotAreaUserVo;
import com.ruoyi.mes.vo.MesLotEventVo;
import com.ruoyi.mes.vo.MesLotWaitVo;
import com.ruoyi.production.domain.ProductionMesLot;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@DataSource(DataSourceType.MES)
public interface MesViewMapper {

    List<WipMaterialLog> selectWipMaterialLogList(WipMaterialLog wipMaterialLog);

    List<MesLotLog> selectWipLotLogList(MesLotLog wipLotLog);

    /**
     *  出站或暂停
     */
    List<MesLotLog> selectWipLotOutList(MesLotLog wipLotLog);
    /**
     *  入站或继续生产
     */
    List<MesLotLog> selectWipLotInList(MesLotLog wipLotLog);

    /**
     * 查询出站人员名单
     */
    List<MesLotLog> selectWipLotOutUserList(MesLotLog wipLotLog);

    /**
     * 查询今日有操作记录的批次
     */
    Set<String> selectTodayWipLotList(MesLotLog mesLotLog);

    List<MesLotAreaUserVo> selectMesLotAreaUserVoList(MesLotAreaUserVo mesLotAreaUserVo);

    List<MesLotEventVo> selectMesLotEventVoList(MesLotEventVo mesLotEventVo);

    List<MesLotWaitVo> selectMesLotWaitVoList(MesLotWaitVo mesLotWaitVo);

    BigDecimal selectMesRkNums(ProductionMesLot productionMesLot);

    BigDecimal selectMesQyNums(MesLotEventVo eventVo);

    /**
     * 查询近期报工批次
     */
    Set<String> selectRecentWipLotList(MesLotLog mesLotLog);

    List<MesLotLog> selectGroupLotWorkDateNums(MesLotLog mesLotLog);

    /**
     * 查询出站报工的数量(如果有同批次同设备的,需要去除之前的产量)
     */
    List<MesLotLog> selectLotOutList(MesLotLog lotLogParams);

}
