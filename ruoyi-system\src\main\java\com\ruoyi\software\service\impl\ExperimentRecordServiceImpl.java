package com.ruoyi.software.service.impl;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.software.domain.ExperimentRecord;
import com.ruoyi.software.mapper.ExperimentRecordMapper;
import com.ruoyi.software.service.IExperimentRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 实验编号记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
public class ExperimentRecordServiceImpl implements IExperimentRecordService 
{
    private static final Logger log = LoggerFactory.getLogger(ExperimentRecordServiceImpl.class);

    @Autowired
    private ExperimentRecordMapper experimentRecordMapper;

    /**
     * 查询实验编号记录
     * 
     * @param id 实验编号记录主键
     * @return 实验编号记录
     */
    @Override
    public ExperimentRecord selectExperimentRecordById(Long id)
    {
        return experimentRecordMapper.selectExperimentRecordById(id);
    }

    /**
     * 查询实验编号记录列表
     * 
     * @param experimentRecord 实验编号记录
     * @return 实验编号记录
     */
    @Override
    public List<ExperimentRecord> selectExperimentRecordList(ExperimentRecord experimentRecord)
    {
        return experimentRecordMapper.selectExperimentRecordList(experimentRecord);
    }

    /**
     * 新增实验编号记录
     *
     * @param experimentRecord 实验编号记录
     * @return 结果
     */
    @Override
    public int insertExperimentRecord(ExperimentRecord experimentRecord)
    {
        experimentRecord.setCreateBy(SecurityUtils.getUsername());
        experimentRecord.setCreateTime(DateUtils.getNowDate());
        return experimentRecordMapper.insertExperimentRecord(experimentRecord);
    }

    /**
     * 修改实验编号记录
     * 
     * @param experimentRecord 实验编号记录
     * @return 结果
     */
    @Override
    public int updateExperimentRecord(ExperimentRecord experimentRecord)
    {
        experimentRecord.setUpdateTime(DateUtils.getNowDate());
        return experimentRecordMapper.updateExperimentRecord(experimentRecord);
    }

    /**
     * 批量删除实验编号记录
     * 
     * @param ids 需要删除的实验编号记录主键
     * @return 结果
     */
    @Override
    public int deleteExperimentRecordByIds(Long[] ids)
    {
        return experimentRecordMapper.deleteExperimentRecordByIds(ids);
    }

    /**
     * 删除实验编号记录信息
     * 
     * @param id 实验编号记录主键
     * @return 结果
     */
    @Override
    public int deleteExperimentRecordById(Long id)
    {
        return experimentRecordMapper.deleteExperimentRecordById(id);
    }

    /**
     * 根据批次ID查询实验记录列表
     *
     * @param batchId 批次ID
     * @return 实验记录列表
     */
    @Override
    public List<ExperimentRecord> selectExperimentRecordsByBatchId(Long batchId)
    {
        return experimentRecordMapper.selectExperimentRecordsByBatchId(batchId);
    }

    /**
     * 为指定批次添加实验记录
     *
     * @param experimentRecord 实验记录对象
     * @return 新创建的实验记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExperimentRecord addExperimentRecord(ExperimentRecord experimentRecord)
    {
        try {
            log.info("为批次{}添加实验记录，实验编号：{}", experimentRecord.getBatchId(), experimentRecord.getExperimentCode());

            experimentRecord.setDelFlag(0);
            experimentRecord.setCreateBy(SecurityUtils.getUsername());
            experimentRecord.setCreateTime(DateUtils.getNowDate());

            int result = experimentRecordMapper.insertExperimentRecord(experimentRecord);
            if (result > 0) {
                log.info("成功添加实验记录，实验编号：{}，记录ID：{}", experimentRecord.getExperimentCode(), experimentRecord.getId());
                return experimentRecord;
            } else {
                throw new RuntimeException("添加实验记录失败");
            }
        } catch (Exception e) {
            log.error("添加实验记录失败，批次ID：{}，实验编号：{}，错误：{}", experimentRecord.getBatchId(), experimentRecord.getExperimentCode(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 为指定批次添加实验记录（小程序）
     *
     * @param experimentRecord 实验记录对象
     * @param sysUser 小程序用户信息
     * @return 新创建的实验记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExperimentRecord addExperimentRecord(ExperimentRecord experimentRecord, SysUser sysUser)
    {
        try {
            log.info("为批次{}添加实验记录，实验编号：{}", experimentRecord.getBatchId(), experimentRecord.getExperimentCode());

            experimentRecord.setDelFlag(0);
            experimentRecord.setCreateBy(sysUser.getNickName());
            experimentRecord.setCreateTime(DateUtils.getNowDate());

            int result = experimentRecordMapper.insertExperimentRecord(experimentRecord);
            if (result > 0) {
                log.info("成功添加实验记录，实验编号：{}，记录ID：{}", experimentRecord.getExperimentCode(), experimentRecord.getId());
                return experimentRecord;
            } else {
                throw new RuntimeException("添加实验记录失败");
            }
        } catch (Exception e) {
            log.error("添加实验记录失败，批次ID：{}，实验编号：{}，错误：{}", experimentRecord.getBatchId(), experimentRecord.getExperimentCode(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据工程师打样单ID查询所有批次实验室编号
     *
     * @param engineerSampleOrderId 工程师打样单ID
     * @return 实验室编号列表
     */
    @Override
    public List<ExperimentRecord> getBatchExperimentCodeList(Long engineerSampleOrderId) {
        return experimentRecordMapper.getBatchExperimentCodeList(engineerSampleOrderId);
    }

    /**
     * 批量添加实验记录
     *
     * @param batchId 批次ID
     * @param experimentCodes 实验编号列表
     * @param experimentNote 实验备注
     * @param remark 备注信息
     * @return 成功添加的记录数
     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public int batchAddExperimentRecords(Long batchId, List<String> experimentCodes, String experimentNote, String remark)
//    {
//        try {
//            log.info("为批次{}批量添加实验记录，实验编号数量：{}", batchId, experimentCodes.size());
//
//            if (experimentCodes == null || experimentCodes.isEmpty()) {
//                return 0;
//            }
//
//            // 1. 创建实验记录列表
//            List<ExperimentRecord> experimentRecords = new ArrayList<>();
//            String currentUser = SecurityUtils.getUsername();
//            for (String code : experimentCodes) {
//                ExperimentRecord record = new ExperimentRecord();
//                record.setBatchId(batchId);
//                record.setExperimentCode(code);
//                record.setExperimentNote(experimentNote);
//                record.setRemark(remark);
//                record.setDelFlag(0);
//                record.setCreateBy(currentUser);
//                record.setCreateTime(DateUtils.getNowDate());
//                experimentRecords.add(record);
//            }
//
//            // 2. 批量插入
//            int result = experimentRecordMapper.batchInsertExperimentRecords(experimentRecords);
//            log.info("成功批量添加实验记录，批次ID：{}，添加数量：{}", batchId, result);
//            return result;
//        } catch (Exception e) {
//            log.error("批量添加实验记录失败，批次ID：{}，错误：{}", batchId, e.getMessage(), e);
//            throw e;
//        }
//    }

}
