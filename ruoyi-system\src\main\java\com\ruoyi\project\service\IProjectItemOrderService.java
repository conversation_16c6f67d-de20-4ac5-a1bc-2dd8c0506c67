package com.ruoyi.project.service;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.project.domain.ProjectBc;
import com.ruoyi.project.domain.ProjectItemOrder;
import com.ruoyi.project.domain.excel.NrwArrangement;
import com.ruoyi.project.domain.excel.ProjectItemOrderExcel;
import com.ruoyi.project.vo.ProjectOfferOrder;

import java.util.List;
import java.util.Map;

/**
 * 子项目订单Service接口
 *
 * <AUTHOR>
 * @date 2021-09-23
 */
public interface IProjectItemOrderService {

    public List<ProjectItemOrder> selectProjectItemOrderList(ProjectItemOrder projectItemOrder);

    public List<ProjectItemOrder> selectProjectItemOrderListBySamplePrice(ProjectItemOrder projectItemOrder);

    public int insertProjectItemOrder(ProjectItemOrder projectItemOrder);
    public int insertProjectItemOrderSoftware(ProjectItemOrder projectItemOrder);

    public int updateProjectItemOrder(ProjectItemOrder projectItemOrder);

    /**
     * 撤销子订单
     * @param projectItemOrder
     * @return
     */
    int revokeProjectItemOrder(ProjectItemOrder projectItemOrder);

    void saveProjectRemindInfo(Long projectOrderId,Integer type);

    /**
     * 获取未完成订单
     * @param id
     * @return
     */
    Integer selectItemIsComplete(Long id);

    void updateProjectItemOrderStatus(ProjectItemOrder projectItemOrder);

    List<Map<String,Object>> projectItemOfferDataList(ProjectItemOrder projectItemOrder);

    List<Map<String,Object>> projectItemBomDataList(ProjectItemOrder projectItemOrder);

    void batchSaveRemind();

    List<NrwArrangement> exportNrwArrangement(ProjectItemOrder projectItemOrder);

    List<ProjectItemOrder> projectItemOrderConfirmCodeByItemId(ProjectItemOrder projectItemOrder);

    List<ProjectItemOrderExcel> selectProjectItemOrderExcelList(ProjectItemOrder projectItemOrder);

    void updateProjectExecutionStatus(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> prjectItemOrderOfferNrwPriceInfo(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> projectItemOrderBcBomDataList(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> projectItemOfferDataListNew(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> projectItemOfferDataListFinidshNew(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> projectItemOfferBcDataList(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> projectItemOfferDataListArrNew(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> projectItemOrderSckxxGwtDataList(ProjectItemOrder projectItemOrder);

    List<Map<String, Object>> projectItemOfferJgfDataList(ProjectItemOrder projectItemOrder);

    void orderFeed();

    void offerFeed();

    void productionFeasibilityFeed();

    void mergeSjOrderData();

    void mergeBaOrderData();

    void updatePrjectSjbaProgress();

    //同步研发配方
    void syncProjectExecutionSoftLabNoInfo();

    //自动同步
    void passProjectSjbaExecutionData();

    List<ProjectItemOrder> selectProjectItemOrderReleaseList(ProjectItemOrder projectItemOrder);

    List<JSONObject> selectProjectItemOrderFeeList(ProjectItemOrder projectItemOrder);

    void saveBcReply(ProjectItemOrder projectItemOrder);

    void saveGongyiReply(ProjectItemOrder projectItemOrder);

    List<ProjectItemOrder> queryProjectItemOrderOfferDataList(ProjectItemOrder projectItemOrder);

    List<ProjectOfferOrder> queryProjectOfferFormulaDataList(Long id);
    List<ProjectOfferOrder> queryProjectOfferChangeRecordDataList(Long id);
    List<ProjectOfferOrder> queryMultiProjectOfferChangeRecordDataList(ProjectOfferOrder projectOfferOrder);
    List<ProjectOfferOrder> queryMultiProjectOfferFormulaDetailsDataList(ProjectOfferOrder projectOfferOrder);
    List<ProjectOfferOrder> queryProjectOfferFormulaDataDetailsList(Long id);
    List<ProjectBc> queryProjectOfferPackagingMaterialDataList(Long id);

    JSONObject queryFormulaProductName(ProjectItemOrder projectItemOrder);

    JSONObject getMultiPackagingMaterialDatas(ProjectOfferOrder projectOfferOrder);

    JSONObject processPackagingMaterialTypePriceInfo(ProjectOfferOrder projectOfferOrder);

    void deleteProjectItemOrderByIds(Long[] ids);

    JSONObject autoScheduleSampleOrder(Long projectOrderId,Integer projectType,Long userId);
    JSONObject autoScheduleSampleOrder(ProjectItemOrder projectItemOrder);

    /**
     * 更新项目订单样品报价
     * @param projectItemOrderId 项目订单ID
     * @param samplePrice 样品报价
     * @return 更新结果
     */
    int updateProjectItemOrderSamplePrice(Long projectItemOrderId, String samplePrice);
}
