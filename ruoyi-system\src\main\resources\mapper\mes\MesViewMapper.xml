<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mes.mapper.MesViewMapper">

    <!-- 批次中的物料领用记录 -->
    <select id="selectWipMaterialLogList" parameterType="WipMaterialLog" resultType="WipMaterialLog" >
        SELECT
            DISTINCT A.MaterialNo materialNo,
            c.MATERIALNAME materialName,
            c.MATERIALSPEC spec,
            isnull( A.<PERSON>ty, B.LotQty ) useQty,
            B.MaterialLotNo materialLotNo,
            A.UnitNo unit,
            A.LogGroupSerial AS logSerial
        FROM
            (
                SELECT
                    LOGGROUPSERIAL,
                    MATERIALNO,
                    MaterialLotNo,
                    SUM( LOTQTY ) LOTQTY
                FROM
                    tblWIPCont_MaterialLot
                WHERE
                    LOTQTY &gt; 0
                <if test="baseLotNo != null and baseLotNo !=''">
                    and LOGGROUPSERIAL like concat(#{baseLotNo},'%')
                </if>
                <if test="baseLotNos != null and baseLotNos.size() > 0" >
                    and
                    <foreach collection="baseLotNos" item="baseLotNo" separator="or" open="(" close=")" >
                        (LOGGROUPSERIAL like concat(#{baseLotNo},'%'))
                    </foreach>
                </if>
                GROUP BY
                    LOGGROUPSERIAL,
                    MATERIALNO,
                    MaterialLotNo
            ) B
            RIGHT OUTER JOIN (
                SELECT
                    LOGGROUPSERIAL,
                    MATERIALNO,
                    MATERIALTYPE,
                    UNITNO,
                    SUM( USEQTY ) USEQTY
                FROM
                    tblWIPCont_Material
                WHERE
                    USEQTY &gt; 0
                <if test="baseLotNo != null and baseLotNo !=''">
                    and LOGGROUPSERIAL like concat(#{baseLotNo},'%')
                </if>
                <if test="baseLotNos != null and baseLotNos.size() > 0" >
                    and
                    <foreach collection="baseLotNos" item="baseLotNo" separator="or" open="(" close=")" >
                        (LOGGROUPSERIAL like concat(#{baseLotNo},'%'))
                    </foreach>
                </if>
                GROUP BY
                    LOGGROUPSERIAL,
                    MATERIALNO,
                    MATERIALTYPE,
                    UNITNO
            ) A ON A.MaterialNo = B.MaterialNo
                AND A.LogGroupSerial = B.LogGroupSerial
                LEFT JOIN TBLMTLMATERIALBASIS c ON A.MATERIALNO = c.MATERIALNO
                JOIN V_Q05 ON V_Q05._LogGroupSerial= a.LogGroupSerial
    </select>

    <!-- 生产批操作记录 -->
    <select id="selectWipLotLogList" parameterType="MesLotLog" resultType="MesLotLog" >
        SELECT
            aa.LotNo,
            isnull( aa.OPNo, '' ) opNo,
            aa.AreaNo areaNo,
            REPLACE( REPLACE( aa.DTYPENAME, '[%Module_SYS.', '' ), '%]', '' ) AS opType,
            CASE
            WHEN aa.EQUIPMENTNO IS NULL OR aa.EQUIPMENTNO= '' THEN ''
            ELSE aa.EQUIPMENTNO
            END equipmentNo,
            ISNULL( aa.EquipmentName, '' ) equipmentName,
            TRY_CAST ( aa.EventTime AS DATETIME ) createTime,
            aa.InputQty qty,
            CASE
            WHEN aa.UserNo IS NULL OR aa.UserNo= '' THEN ''
            ELSE aa.UserNo
            END userNo,
            ISNULL( aa.USERNAME, '' ) userName
        FROM (
            /*1、生产批开立*/
            SELECT
                lot.BASELOTNO LotNo,
                'LOTCREATE' OPNO,
                '' AreaNo,
                LOT.CREATEDATE EventTime,
                '' EQUIPMENTNO,
                '' EquipmentName,
                LOT.CREATOR UserNo,
                us.USERNAME USERNAME,
                lot.INPUTQTY,
                N'[%Module_SYS.LOTCREATE%]' DTYPENAME --开批
            FROM
                TBLWIPLOTBASIS lot
                LEFT JOIN tblUSRUserBasis us ON us.USERNO= lot.CREATOR
             <where>
                 <if test="lotNo!=null and lotNo!=''">and lot.BASELOTNO = #{lotNo}</if>
                 <if test="areaNo != null and areaNo != ''">and '' = #{areaNo}</if>
                 <if test="equipmentNo!=null and equipmentNo!=''">and '' = #{equipmentNo}</if>
                 <if test="createTime!=null">and CONVERT(DATE, LOT.CREATEDATE) = CONVERT(DATE, #{createTime})</if>
                 <if test="scheduleCode != null and scheduleCode != ''" >and lot.BASELOTNO like concat(#{scheduleCode},'%')</if>
                 <if test="recentFlag != null and recentFlag == '1'.toString() ">
                     LOT.CREATEDATE &gt;= DATEADD(day, -7, GETDATE())
                 </if>
             </where>
            UNION ALL
            /*2、进站报工*/
            SELECT
                b.LotNo,
                b.OPNo,
                b.AreaNo,
                b.EventTime,
                b.EQUIPMENTNO,
                eq.EquipmentName,
                b.UserNo,
                us.USERNAME,
                b.InputQty,
                N'[%Module_SYS.CHECKIN%]' DTYPENAME --进站
            FROM
                tblWIPCont_Partialin b
                LEFT JOIN TBLEQPEQUIPMENTBASIS eq ON eq.EQUIPMENTNO= b.EQUIPMENTNO
                LEFT JOIN tblUSRUserBasis us ON us.USERNO= b.UserNo
            <where>
                <if test="lotNo!=null and lotNo!=''">and b.LotNo = #{lotNo}</if>
                <if test="areaNo != null and areaNo != ''">and b.AreaNo = #{areaNo}</if>
                <if test="equipmentNo!=null and equipmentNo!=''">and b.EQUIPMENTNO = #{equipmentNo}</if>
                <if test="createTime!=null">and CONVERT(DATE, b.EventTime) = CONVERT(DATE, #{createTime})</if>
                <if test="scheduleCode != null and scheduleCode != ''" >and b.LotNo like concat(#{scheduleCode},'%')</if>
                <if test="recentFlag != null and recentFlag == '1'.toString() ">
                    b.EventTime &gt;= DATEADD(day, -7, GETDATE())
                </if>
            </where>
            union all
            /*3、出站报工*/
            SELECT
                a.LotNo,
                a.OPNo,
                a.AreaNo,
                a.EventTime,
                a.EQUIPMENTNO,
                eq.EquipmentName,
                a.UserNo,
                us.USERNAME,
                a.InputQty,
                N'[%Module_SYS.CHECKOUT%]' DTYPENAME --出站
            FROM
                tblWIPCont_PartialOut a
                LEFT JOIN TBLEQPEQUIPMENTBASIS eq ON eq.EQUIPMENTNO= a.EQUIPMENTNO
                LEFT JOIN tblUSRUserBasis us ON us.USERNO= a.UserNo
            <where>
                <if test="lotNo!=null and lotNo!=''">and a.LotNo = #{lotNo}</if>
                <if test="areaNo != null and areaNo != ''">and a.AreaNo = #{areaNo}</if>
                <if test="equipmentNo!=null and equipmentNo!=''">and a.EQUIPMENTNO = #{equipmentNo}</if>
                <if test="createTime!=null">and CONVERT(DATE, a.EventTime) = CONVERT(DATE, #{createTime})</if>
                <if test="scheduleCode != null and scheduleCode != ''" >and a.LotNo like concat(#{scheduleCode},'%')</if>
                <if test="recentFlag != null and recentFlag == '1'.toString() ">
                    a.EventTime &gt;= DATEADD(day, -7, GETDATE())
                </if>
            </where>
            union all
            /*4、设备变更*/
            SELECT
                c.LOTNO,
                c.OPNO,
                c.AREANO,
                c.MOVEDATE,
                c.FROMEQUIPMENTNO EQUIPMENTNO,
                eq.EquipmentName,
                c.USERNO,
                us.USERNAME,
                c.MOVEQTY INPUTQTY,
                N'[%Module_SYS.EQPCHANGE%]' DTYPENAME --设备变更
            FROM
                tblWIPLotEQPChangeLog c
                LEFT JOIN TBLEQPEQUIPMENTBASIS eq ON eq.EQUIPMENTNO= c.FROMEQUIPMENTNO
                LEFT JOIN tblUSRUserBasis us ON us.USERNO= c.UserNo
            <where>
                <if test="lotNo!=null and lotNo!=''">and c.LOTNO = #{lotNo}</if>
                <if test="areaNo != null and areaNo != ''">and c.AREANO = #{areaNo}</if>
                <if test="equipmentNo!=null and equipmentNo!=''">and c.FROMEQUIPMENTNO = #{equipmentNo}</if>
                <if test="createTime!=null">and CONVERT(DATE, c.MOVEDATE) = CONVERT(DATE, #{createTime})</if>
                <if test="scheduleCode != null and scheduleCode != ''" >and c.LOTNO like concat(#{scheduleCode},'%')</if>
                <if test="recentFlag != null and recentFlag == '1'.toString() ">
                    c.MOVEDATE &gt;= DATEADD(day, -7, GETDATE())
                </if>
            </where>
            union all
            /*10、生产批暂停*/
            SELECT
                wt.LOTNO,
                wt.OPNO,
                '' AreaNo,
                wt.CREATEDATE,
                wt.EQUIPMENTNO,
                eq.EquipmentName,
                wt.CREATOR,
                us.USERNAME,
                wt.ABNORMAL_QTY,
                N'[%Module_SYS.WAITDISPOSITION%]' DTYPENAME --生产批暂停
            FROM
                tblWIPWaitBasis wt
                LEFT JOIN TBLEQPEQUIPMENTBASIS eq ON eq.EQUIPMENTNO= wt.EQUIPMENTNO
                LEFT JOIN tblUSRUserBasis us ON us.USERNO= wt.CREATOR
            <where>
                <if test="lotNo!=null and lotNo!=''">and  wt.LOTNO  = #{lotNo}</if>
                <if test="areaNo != null and areaNo != ''">and '' = #{areaNo}</if>
                <if test="equipmentNo!=null and equipmentNo!=''">and wt.EQUIPMENTNO = #{equipmentNo}</if>
                <if test="createTime!=null">and CONVERT(DATE, wt.CREATEDATE) = CONVERT(DATE, #{createTime})</if>
                <if test="scheduleCode != null and scheduleCode != ''" >and wt.LOTNO like concat(#{scheduleCode},'%')</if>
                <if test="recentFlag != null and recentFlag == '1'.toString() ">
                    wt.CREATEDATE &gt;= DATEADD(day, -7, GETDATE())
                </if>
            </where>
            union all
            /*11、生产批解除暂停*/
            SELECT
                wt.LOTNO,
                wt.OPNO,
                '' AreaNo,
                wtd.CREATEDATE,
                wt.EQUIPMENTNO,
                eq.EquipmentName,
                wtd.CREATOR,
                us.USERNAME,
                wt.CREATEQTY,
                CASE wtd.lotdisptype
                    WHEN 0 THEN N'[%Module_SYS.RELEASE-GO%]' --解除暂停-继续生产
                    WHEN 1 THEN N'[%Module_SYS.RELEASE-JumpOP%]' --解除暂停-跳站/重工/让步
                    WHEN 2 THEN N'[%Module_SYS.RELEASE-JumpProcess%]' --解除暂停-跳流程
                    WHEN 3 THEN N'[%Module_SYS.RELEASE-Inventory%]' --解除暂停-结束生产
                    WHEN 7 THEN N'[%Module_SYS.RELEASE-ScrapInvebtory%]' --解除暂停-整批报废
                END DTYPENAME
            FROM
                tblWIPWaitLotDisposition wtd
                JOIN tblWIPWaitBasis wt ON wt.WAITNO= wtd.WAITNO
                LEFT JOIN TBLEQPEQUIPMENTBASIS eq ON eq.EQUIPMENTNO= wt.EQUIPMENTNO
                LEFT JOIN tblUSRUserBasis us ON us.USERNO= wtd.CREATOR
            <where>
                <if test="lotNo!=null and lotNo!=''">and wt.LOTNO  = #{lotNo}</if>
                <if test="areaNo != null and areaNo != ''">and '' = #{areaNo}</if>
                <if test="equipmentNo!=null and equipmentNo!=''">and wt.EQUIPMENTNO = #{equipmentNo}</if>
                <if test="createTime!=null">and CONVERT(DATE, wtd.CREATEDATE) = CONVERT(DATE, #{createTime})</if>
                <if test="scheduleCode != null and scheduleCode != ''" >and wt.LOTNO like concat(#{scheduleCode},'%')</if>
                <if test="recentFlag != null and recentFlag == '1'.toString() ">
                    wtd.CREATEDATE &gt;= DATEADD(day, -7, GETDATE())
                </if>
            </where>
             ) aa
        order by aa.EventTime asc
    </select>

    <!--  出站 和 暂停, 结束生产 和 设备变更 暂时不算  -->
    <select id="selectWipLotOutList" parameterType="MesLotLog" resultType="MesLotLog" >
        select
        aa.LotNo,
        isnull(aa.OPNo,'') opNo,
        aa.AreaNo areaNo,
        REPLACE(REPLACE(aa.DTYPENAME, '[%Module_SYS.', ''), '%]', '') AS opType,
        case when
        aa.EQUIPMENTNO is null or aa.EQUIPMENTNO='' then ''
        else aa.EQUIPMENTNO
        end equipmentNo ,
        ISNULL(aa.EquipmentName,'') equipmentName,
        TRY_CAST(aa.EventTime as DATETIME) createTime,
        aa.InputQty qty,
        case when
        aa.UserNo is null or aa.UserNo='' then ''
        else aa.UserNo
        end userNo,
        ISNULL(aa.USERNAME,'') userName
        from (
        SELECT
        a.LotNo,
        a.OPNo,
        a.AreaNo,
        a.EventTime,
        a.EQUIPMENTNO,
        eq.EquipmentName,
        a.UserNo,
        us.USERNAME,
        a.InputQty,
        N'[%Module_SYS.CHECKOUT%]' DTYPENAME,
        '' reasonName
        FROM
        tblWIPCont_PartialOut a
        LEFT JOIN TBLEQPEQUIPMENTBASIS eq ON eq.EQUIPMENTNO = a.EQUIPMENTNO
        LEFT JOIN tblUSRUserBasis us ON us.USERNO = a.UserNo
        <where>
            <if test="lotNo!=null and lotNo!=''">and a.LotNo = #{lotNo}</if>
            <if test="areaNo != null and areaNo != ''">and a.AreaNo = #{areaNo}</if>
            <if test="equipmentNo!=null and equipmentNo!=''">and a.EQUIPMENTNO = #{equipmentNo}</if>
            <if test="createTime!=null">and CONVERT(DATE, a.EventTime) = CONVERT(DATE, #{createTime})</if>
            <if test="userNo != null and userNo != ''">and a.UserNo = #{userNo}</if>
            <if test="startTime != null" >and CONVERT(DATETIME, a.EventTime ) &gt;= CONVERT(DATETIME, #{startTime} ) </if>
            <if test="endTime != null" >and CONVERT(DATETIME, a.EventTime ) &lt;= CONVERT(DATETIME, #{endTime} )</if>
            <if test="eventDate != null" >and CONVERT(DATE, a.EventTime) = CONVERT(DATE, #{eventDate})</if>
            <if test="scheduleCode != null" >and SUBSTRING( a.LotNo, 1, CHARINDEX('-',  a.LotNo, CHARINDEX('-',  a.LotNo) + 1) - 1) = #{scheduleCode} </if>
            <if test="recentFlag != null and recentFlag == '1'.toString() ">
                a.EventTime &gt;= DATEADD(day, -7, GETDATE())
            </if>
        </where>
        union all
        SELECT
        wt.LOTNO,
        wt.OPNO,
        '' AreaNo,
        wt.CREATEDATE,
        wt.EQUIPMENTNO,
        eq.EquipmentName,
        wt.CREATOR userNo,
        us.USERNAME,
        wt.ABNORMAL_QTY,
        N'[%Module_SYS.WAITDISPOSITION%]' DTYPENAME,
        (
        SELECT CAST
        ( e.REASONNAME AS NVARCHAR ) + ' '
        FROM
        TBLWIPWAITREASON d
        JOIN TBLQCREASONBASIS e ON d.REASONNO = e.REASONNO
        WHERE
        d.WAITNO = wt.WAITNO FOR XML PATH ( '' )
        ) reasonName
        FROM
        tblWIPWaitBasis wt
        LEFT JOIN TBLEQPEQUIPMENTBASIS eq ON eq.EQUIPMENTNO= wt.EQUIPMENTNO
        LEFT JOIN tblUSRUserBasis us ON us.USERNO= wt.CREATOR
        <where>
            <if test="lotNo!=null and lotNo!=''">and wt.LOTNO = #{lotNo}</if>
            <if test="areaNo != null and areaNo != ''">and '' = #{areaNo}</if>
            <if test="equipmentNo!=null and equipmentNo!=''">and wt.EQUIPMENTNO = #{equipmentNo}</if>
            <if test="createTime!=null">and CONVERT(DATE, wt.CREATEDATE) = CONVERT(DATE, #{createTime})</if>
            <if test="userNo != null and userNo != ''">and wt.CREATOR = #{userNo}</if>
            <if test="startTime != null" >and CONVERT(DATETIME, wt.CREATEDATE ) &gt;= CONVERT(DATETIME, #{startTime} ) </if>
            <if test="endTime != null" >and CONVERT(DATETIME, wt.CREATEDATE ) &lt;= CONVERT(DATETIME, #{endTime} )</if>
            <if test="eventDate != null" >and CONVERT(DATE, wt.CREATEDATE) = CONVERT(DATE, #{eventDate})</if>
            <if test="scheduleCode != null" >and SUBSTRING( wt.LOTNO, 1, CHARINDEX('-',  wt.LOTNO, CHARINDEX('-',  wt.LOTNO) + 1) - 1) = #{scheduleCode} </if>
            <if test="recentFlag != null and recentFlag == '1'.toString() ">
                wt.CREATEDATE &gt;= DATEADD(day, -7, GETDATE())
            </if>
        </where>
        ) aa
        order by aa.EventTime asc
    </select>

    <!--  进站 和 继续生产  -->
    <select id="selectWipLotInList" parameterType="MesLotLog" resultType="MesLotLog" >
        select
        aa.LotNo,
        isnull(aa.OPNo,'') opNo,
        aa.AreaNo areaNo,
        REPLACE(REPLACE(aa.DTYPENAME, '[%Module_SYS.', ''), '%]', '') AS opType,
        case when
            aa.EQUIPMENTNO is null or aa.EQUIPMENTNO='' then ''
            else aa.EQUIPMENTNO
        end equipmentNo ,
        ISNULL(aa.EquipmentName,'') equipmentName,
        TRY_CAST(aa.EventTime as DATETIME) createTime,
        aa.InputQty qty,
        case when
            aa.UserNo is null or aa.UserNo='' then ''
            else aa.UserNo
        end userNo,
        ISNULL(aa.USERNAME,'') userName
        from (
        SELECT
        b.LotNo ,
        b.OPNo,
        b.AreaNo ,
        b.EventTime,
        b.EQUIPMENTNO,
        eq.EquipmentName,
        b.UserNo,
        us.USERNAME,
        b.InputQty,
        N'[%Module_SYS.CHECKIN%]' DTYPENAME --进站
        FROM
        tblWIPCont_Partialin b
        LEFT JOIN TBLEQPEQUIPMENTBASIS eq ON eq.EQUIPMENTNO= b.EQUIPMENTNO
        LEFT JOIN tblUSRUserBasis us ON us.USERNO= b.UserNo
        <where>
            <if test="lotNo!=null and lotNo!=''">and b.LotNo = #{lotNo}</if>
            <if test="areaNo != null and areaNo != ''">and b.AreaNo = #{areaNo}</if>
            <if test="equipmentNo!=null and equipmentNo!=''">and b.EQUIPMENTNO = #{equipmentNo}</if>
            <if test="createTime!=null">and CONVERT(DATE, b.EventTime) = CONVERT(DATE, #{createTime})</if>
            <if test="userNo != null and userNo != ''">and b.UserNo = #{userNo}</if>
            <if test="startTime != null" >and CONVERT(DATETIME, b.CREATEDATE ) &gt;= CONVERT(DATETIME, #{startTime} ) </if>
            <if test="endTime != null" >and CONVERT(DATETIME, b.CREATEDATE ) &lt;= CONVERT(DATETIME, #{endTime} )</if>
            <if test="eventDate != null" >and CONVERT(DATE, b.CREATEDATE) = CONVERT(DATE, #{eventDate})</if>
            <if test="scheduleCode != null" >and SUBSTRING( b.LotNo, 1, CHARINDEX('-',  b.LotNo, CHARINDEX('-',  b.LotNo) + 1) - 1) = #{scheduleCode} </if>
            <if test="recentFlag != null and recentFlag == '1'.toString() ">
                b.EventTime &gt;= DATEADD(day, -7, GETDATE())
            </if>
        </where>
        union all
            SELECT
                wt.LOTNO,
                wt.OPNO,
                '' AreaNo,
                wtd.CREATEDATE,
                wt.EQUIPMENTNO,
                eq.EquipmentName,
                wtd.CREATOR,
                us.USERNAME,
                wt.CREATEQTY,
                CASE wtd.lotdisptype
                    WHEN 0 THEN N'[%Module_SYS.RELEASE-GO%]' --解除暂停-继续生产
                    WHEN 1 THEN N'[%Module_SYS.RELEASE-JumpOP%]' --解除暂停-跳站/重工/让步
                    WHEN 2 THEN N'[%Module_SYS.RELEASE-JumpProcess%]' --解除暂停-跳流程
                    WHEN 3 THEN N'[%Module_SYS.RELEASE-Inventory%]' --解除暂停-结束生产
                    WHEN 7 THEN N'[%Module_SYS.RELEASE-ScrapInvebtory%]' --解除暂停-整批报废
                END DTYPENAME
            FROM
            tblWIPWaitLotDisposition wtd
            JOIN tblWIPWaitBasis wt ON wt.WAITNO= wtd.WAITNO
            LEFT JOIN TBLEQPEQUIPMENTBASIS eq ON eq.EQUIPMENTNO= wt.EQUIPMENTNO
            LEFT JOIN tblUSRUserBasis us ON us.USERNO= wtd.CREATOR
            <where>
                wtd.lotdisptype = 0 --只留继续生产
                <if test="lotNo!=null and lotNo!=''">and wt.LOTNO  = #{lotNo}</if>
                <if test="areaNo != null and areaNo != ''">and '' = #{areaNo}</if>
                <if test="equipmentNo!=null and equipmentNo!=''">and wt.EQUIPMENTNO = #{equipmentNo}</if>
                <if test="createTime!=null">and CONVERT(DATE, wtd.CREATEDATE) = CONVERT(DATE, #{createTime})</if>
                <if test="userNo != null and userNo != ''">and wtd.CREATOR = #{userNo}</if>
                <if test="startTime != null" >and CONVERT(DATETIME, wtd.CREATEDATE ) &gt;= CONVERT(DATETIME, #{startTime} ) </if>
                <if test="endTime != null" >and CONVERT(DATETIME,wtd.CREATEDATE ) &lt;= CONVERT(DATETIME, #{endTime} )</if>
                <if test="eventDate != null" >and CONVERT(DATE, wtd.CREATEDATE) = CONVERT(DATE, #{eventDate})</if>
                <if test="scheduleCode != null" >and SUBSTRING( wt.LOTNO, 1, CHARINDEX('-',  wt.LOTNO, CHARINDEX('-',  wt.LOTNO) + 1) - 1) = #{scheduleCode} </if>
                <if test="recentFlag != null and recentFlag == '1'.toString() ">
                    wtd.CREATEDATE &gt;= DATEADD(day, -7, GETDATE())
                </if>
            </where>
        ) aa
        order by aa.EventTime asc
    </select>

    <select id="selectWipLotOutUserList" parameterType="MesLotLog" resultType="MesLotLog" >
        SELECT
            CAST(LOTPART.EventTime AS DATETIME) createTime,
            EQTime.BASELOTNO lotNo,
            EQ.EquipmentNO equipmentNo,
            EQTime.USERNO userNo
        FROM
            TBLWIPCONT_PARTIALOUT LOTPART
            JOIN TBLWIPCONT_RESOURCE EQTime ON LOTPART.LOTNO = EQTime.LOTNO AND LOTPART.OPNO = EQTime.OPNO AND LOTPART.EVENTTIME = EQTime.EVENTTIME AND EQTime.RESCLASS IN ( 0, 1 )
            LEFT JOIN TBLWIPCONT_RESOURCE EMPEQPInfo ON EMPEQPInfo.LogGroupSerial = EQTime.LogGroupSerial AND EMPEQPInfo.EventTime = EQTime.EventTime AND EMPEQPInfo.ResClass = 1
            LEFT JOIN TBLEQPEQUIPMENTBASIS EQ ON
            (CASE
             WHEN EQTime.ResClass = 1 THEN EQTime.RESITEM ELSE EMPEQPInfo.ResItem
            END) = EQ.EQUIPMENTNO
        where EQTime.RESCLASS = '0'
            <if test="equipmentNo!=null and equipmentNo!=''">and EQ.EQUIPMENTNO = #{equipmentNo}</if>
            <if test="lotNo!=null and lotNo!=''">and EQTime.BASELOTNO = #{lotNo}</if>
            <if test="userNo!=null and userNo!=''">and EQTime.USERNO = #{userNo}</if>
            <if test="createTime!=null">and CONVERT(DATE, LOTPART.EventTime) = CONVERT(DATE, #{createTime})</if>
            <if test="recentFlag != null and recentFlag == '1'.toString() ">
                LOTPART.EventTime &gt;= DATEADD(day, -7, GETDATE())
            </if>
    </select>

    <select id="selectTodayWipLotList" parameterType="MesLotLog" resultType="String" >
        WITH tt AS (
            select DISTINCT t.lotNo from (
             select DISTINCT l.BASELOTNO lotNo from TBLWIPLOTBASIS l  where CONVERT(DATE, l.CREATEDATE) = CONVERT(DATE, #{eventDate})
             UNION
             select i.LotNo lotNo from tblWIPCont_Partialin i  where CONVERT(DATE, i.EventTime) = CONVERT(DATE, #{eventDate})
             UNION
             select l.LOTNO lotNo from tblWIPLotEQPChangeLog l where CONVERT(DATE, l.MOVEDATE) = CONVERT(DATE, #{eventDate})
             UNION
             select i.LOTNO lotNo from tblINVFGDInDetail i where CONVERT(DATE, i.CREATEDATE) = CONVERT(DATE, #{eventDate})
             UNION
             select s.FROMLOTNO lotNo from tblWIPSplitContent s where CONVERT(DATE, s.EVENTTIME) = CONVERT(DATE, #{eventDate})
             UNION
             select d.LOTNO lotNo
             from tblWIPOSBasis b
                      join tblWIPOSDetail d on b.osno=d.OSNO
             where CONVERT(DATE, b.CREATEDATE) = CONVERT(DATE, #{eventDate})
             UNION
             select w.LOTNO lotNo from tblWIPWaitBasis w where CONVERT(DATE, w.CREATEDATE) = CONVERT(DATE, #{eventDate})
             UNION
             SELECT l.LOTNO lotNo from tblWIPOSReturnLog l where CONVERT(DATE, l.ReturnDate) = CONVERT(DATE, #{eventDate})
             UNION
             select wt.LOTNO lotNo
             from tblWIPWaitLotDisposition wtd
                      join tblWIPWaitBasis wt on wt.WAITNO = wtd.WAITNO
             where CONVERT(DATE, wtd.CREATEDATE) = CONVERT(DATE, #{eventDate})
             UNION
             select r.LOTNO lotNo from TblWIPReworkReason r where CONVERT(DATE, r.EVENTTIME) = CONVERT(DATE, #{eventDate})
         ) t )
        select 
            tt.lotNo
        from tt 
            inner join TBLWIPLOTBASIS b on tt.lotNo = b.BASELOTNO
        <where>
            <if test="baseProcessNo != null  and baseProcessNo != ''">
                <choose>
                    <when test="baseProcessNo == 'PZ'.toString()">and b.BaseProcessNo LIKE 'PZ%'</when>
                    <otherwise>and b.BaseProcessNo not LIKE 'PZ%'</otherwise>
                </choose>
            </if>
        </where>
    </select>

    <select id="selectMesLotAreaUserVoList" parameterType="MesLotAreaUserVo" resultType="MesLotAreaUserVo" >
        SELECT
            DISTINCT a.USERNO userNo,
            b.USERNAME userName,
            CAST(a.EVENTTIME AS DATETIME) eventTime,
            ROUND( a.RESVALUE / 60, 2 ) realManTime,
            a.INPUTQTY inputQty
        FROM
            tblWIPCont_Resource a
            JOIN TBLUSRUSERBASIS b ON a.USERNO = b.USERNO AND a.RESCLASS = 0
            JOIN V_Q05 ON V_Q05.LOTNO= a.LOTNO AND V_Q05.OPNO= a.OPNO
        <where>
            <if test="lotNo != null and lotNo != '' ">and a.BASELOTNO = #{lotNo}</if>
        </where>
        GROUP BY
            a.USERNO ,
            b.USERNAME ,
            a.EVENTTIME,
            a.RESVALUE ,
            a.INPUTQTY
    </select>

    <select id="selectMesLotEventVoList" parameterType="MesLotEventVo" resultType="MesLotEventVo" >
        SELECT DISTINCT
            B.REASONNO reasonNo,
            ( CASE WHEN A.ERRORNO = 'iSPC_Scrap' THEN 'iSPC判定損壞' WHEN A.ERRORNO = 'iSPC_Return' THEN 'iSPC判定驗退' ELSE B.ReasonName END ) AS reasonName,
            CAST(A.EVENTTIME AS DATETIME) eventTime,
            A.ERRORQTY errorQty,
            A.DESCRIPTION description,
            A.LogGroupSerial _LogGroupSerial
        FROM
            tblQCReasonBasis B
            JOIN tblWIPCont_Error A ON A.ErrorNo = B.ReasonNo
            JOIN V_Q05 ON V_Q05._LogGroupSerial= A.LogGroupSerial
        <where>
            <if test="lotNo != null and lotNo != '' ">and V_Q05.BASELOTNO = #{lotNo}</if>
        </where>
    </select>

    <select id="selectMesLotWaitVoList" parameterType="MesLotWaitVo" resultType="MesLotWaitVo" >
        SELECT
            CAST(a.CREATEDATE AS DATETIME) AS waitDate,
            CAST(a.RELEASEDATE AS DATETIME) AS releaseDate,
            DATEDIFF( MINUTE, a.CREATEDATE, ISNULL( a.RELEASEDATE, GETDATE( ) ) ) waitPeriod,
            a.CREATEQTY createQty,
            (
               SELECT CAST
                      ( e.REASONNAME AS NVARCHAR ) + ' '
               FROM
                   TBLWIPWAITREASON d
                       JOIN TBLQCREASONBASIS e ON d.REASONNO = e.REASONNO
               WHERE
                   d.WAITNO = a.WAITNO FOR XML PATH ( '' )
            ) reasonName,
            c1.USERNO AS stopUserNo,
            c1.USERNAME AS stopUserName,
            c2.USERNO AS releaseUserNo,
            c2.USERNAME AS releaseUserName,
            CONTLOGGROUPSERIAL _LogGroupSerial,
            a.LOTNO lotNo,
            a.OPNO opNo
        FROM
            TBLWIPWAITBASIS a
            LEFT JOIN TBLUSRUSERBASIS c1 ON a.CREATOR = c1.USERNO
            LEFT JOIN TBLUSRUSERBASIS c2 ON a.RELEASER = c2.USERNO
            JOIN V_Q05 ON V_Q05.LOTNO= A.LOTNO AND V_Q05.OPNO= A.OPNO
            AND CONVERT ( CHAR ( 16 ), a.CREATEDATE, 120 ) = V_Q05.STARTTIME
        <where>
            <if test="lotNo != null and lotNo != '' ">and V_Q05.BASELOTNO = #{lotNo}</if>
            <if test="lotNos != null and lotNos.size() > 0" >
                and V_Q05.BASELOTNO in
                <foreach collection="lotNos" item="lotNo" open="(" separator="," close=")" >
                    #{lotNo}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectMesRkNums" parameterType="ProductionMesLot" resultType="java.math.BigDecimal" >
        select
            nullif(SUM(QTY),0) nums
        from
            TBLINVWIPINVENTORY_SEMI
        where
            BOOKINGFLAG = 0
        <if test="lotNo!=null and lotNo!=''">and LOTNO = #{lotNo}</if>
    </select>

    <select id="selectMesQyNums" parameterType="MesLotEventVo" resultType="java.math.BigDecimal" >
        SELECT
            nullif(SUM(A.ERRORQTY),0) nums
        FROM
            tblQCReasonBasis B
            JOIN tblWIPCont_Error A ON A.ErrorNo = B.ReasonNo
            JOIN V_Q05 ON V_Q05._LogGroupSerial= A.LogGroupSerial
        where B.REASONNO like 'QY-%'
        <if test="lotNo != null and lotNo != '' ">and V_Q05.BASELOTNO = #{lotNo}</if>
    </select>

    <select id="selectRecentWipLotList" parameterType="MesLotLog" resultType="String" >
        select out.lotNo from tblWIPCont_PartialOut out
        <where>
            <if test="recentFlag != null and recentFlag == '1'.toString() ">
                out.EventTime &gt;= DATEADD(day, -7, GETDATE())
            </if>
        </where>
        union all
        select wt.lotNo from tblWIPWaitBasis wt
        <where>
            <if test="recentFlag != null and recentFlag == '1'.toString() ">
                wt.CREATEDATE &gt;= DATEADD(day, -7, GETDATE())
            </if>
        </where>
    </select>

    <select id="selectGroupLotWorkDateNums" parameterType="MesLotLog" resultType="MesLotLog" >
        select
            aa.LotNo,
            REPLACE(REPLACE(aa.DTYPENAME, '[%Module_SYS.', ''), '%]', '') AS opType,
            aa.OPNo,
            aa.AreaNo,
            aa.EQUIPMENTNO equipmentNo,
            CASE
            WHEN CAST(aa.EventTime AS TIME) BETWEEN '06:00:00' AND '18:00:00' THEN '0'
            ELSE '1'
            END AS sailings,
            CASE
            WHEN CAST(aa.EventTime AS TIME) BETWEEN '06:00:00' AND '18:00:00'
            THEN TRY_CAST(aa.EventTime as DATE)
            ELSE DATEADD(DAY, -1, TRY_CAST(aa.EventTime as DATE))
            END AS createTime,
            STRING_AGG(aa.USERNAME, ',') lineLeader,
            sum(aa.InputQty) qty
        from (
                 SELECT
                     a.LotNo,
                     a.OPNo,
                     a.AreaNo,
                     a.EventTime,
                     a.EQUIPMENTNO,
                     eq.EquipmentName,
                     a.UserNo,
                     us.USERNAME,
                     a.InputQty,
                     N'[%Module_SYS.CHECKOUT%]' DTYPENAME
                 FROM
                     tblWIPCont_PartialOut a
                     LEFT JOIN TBLEQPEQUIPMENTBASIS eq ON eq.EQUIPMENTNO = a.EQUIPMENTNO
                     LEFT JOIN tblUSRUserBasis us ON us.USERNO = a.UserNo
                <where>
                    <if test="lotNos != null and lotNos.size() > 0" >
                        and a.LotNo in
                        <foreach collection="lotNos" item="lotNo" open="(" separator="," close=")" >
                            #{lotNo}
                        </foreach>
                    </if>
                </where>
                 union all
                 SELECT
                     wt.LOTNO,
                     wt.OPNO,
                     '' AreaNo,
                     wt.CREATEDATE,
                     wt.EQUIPMENTNO,
                     eq.EquipmentName,
                     wt.CREATOR userNo,
                     us.USERNAME,
                     wt.ABNORMAL_QTY,
                     N'[%Module_SYS.WAITDISPOSITION%]' DTYPENAME
                 FROM
                     tblWIPWaitBasis wt
                     LEFT JOIN TBLEQPEQUIPMENTBASIS eq ON eq.EQUIPMENTNO= wt.EQUIPMENTNO
                     LEFT JOIN tblUSRUserBasis us ON us.USERNO= wt.CREATOR
                <where>
                    <if test="lotNos != null and lotNos.size() > 0" >
                        and wt.LOTNO in
                        <foreach collection="lotNos" item="lotNo" open="(" separator="," close=")" >
                            #{lotNo}
                        </foreach>
                    </if>
                </where>
             ) aa where aa.InputQty > 0
        GROUP BY
            aa.LotNo,
            REPLACE(REPLACE(aa.DTYPENAME, '[%Module_SYS.', ''), '%]', ''),
            aa.OPNo,
            aa.AreaNo,
            aa.EQUIPMENTNO,
            CASE
                WHEN CAST(aa.EventTime AS TIME) BETWEEN '06:00:00' AND '18:00:00'
                THEN '0'
                ELSE '1'
            END,
            CASE
                WHEN CAST(aa.EventTime AS TIME) BETWEEN '06:00:00' AND '18:00:00'
                THEN TRY_CAST(aa.EventTime as DATE)
                ELSE DATEADD(DAY, -1, TRY_CAST(aa.EventTime as DATE))
            END
    </select>
    
    <select id="selectLotOutList" parameterType="MesLotLog" resultType="MesLotLog" >
        SELECT
            a.LotNo,
            a.OPNo,
            a.AreaNo,
            a.EQUIPMENTNO,
            CASE
            WHEN CAST(a.EventTime AS TIME) BETWEEN '06:00:00' AND '18:00:00' THEN '0'
            ELSE '1'
            END AS sailings,
            CASE
            WHEN CAST(a.EventTime AS TIME) BETWEEN '06:00:00' AND '18:00:00'
            THEN TRY_CAST(a.EventTime as DATE)
            ELSE DATEADD(DAY, -1, TRY_CAST(a.EventTime as DATE))
            END AS eventDate,
            a.UserNo,
            us.USERNAME,
            a.InputQty - (
                    SELECT
                        ISNULL(SUM(w.ABNORMAL_QTY),0)
                    FROM
                        tblWIPWaitBasis w
                    WHERE
                        w.LOTNO = a.LOTNO
                        AND w.EQUIPMENTNO = a.EQUIPMENTNO
                        AND w.CREATEDATE &gt; (
                            SELECT
                                MAX(o.EventTime)
                            FROM
                                tblWIPCont_PartialOut o
                            WHERE
                                o.LotNo = a.LotNo
                                AND o.EQUIPMENTNO = a.EQUIPMENTNO
                                AND o.EventTime &lt;= a.EventTime
                        )
            ) AS nums
        FROM
        tblWIPCont_PartialOut a
        LEFT JOIN tblUSRUserBasis us ON us.USERNO = a.UserNo
        <where>
            <if test="lotNos != null and lotNos.size() > 0" >
                and a.LOTNO in
                <foreach collection="lotNos" item="lotNo" open="(" separator="," close=")" >
                    #{lotNo}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
