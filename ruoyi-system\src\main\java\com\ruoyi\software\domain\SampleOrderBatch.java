package com.ruoyi.software.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 打样批次记录对象 sample_order_batch
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class SampleOrderBatch extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 批次ID */
    private Long id;

    /** 工程师打样单关联ID */
    @Excel(name = "工程师打样单关联ID")
    private Long engineerSampleOrderId;

    /** 打样批次序号(从1开始) */
    @Excel(name = "打样批次序号")
    private Integer batchIndex;

    /** 本次打样开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "本次打样开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 本次打样结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "本次打样结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 本次实际工时 */
    @Excel(name = "本次实际工时(小时)")
    private BigDecimal actualManHours;

    /** 本次质量评价 */
    @Excel(name = "本次质量评价")
    private String qualityEvaluation;

    /** 批次状态(0:未开始/1:进行中/2:已完成/3:已取消) */
    @Excel(name = "批次状态")
    private Integer batchStatus;

    /** 实验室编号(每个批次独立的实验室编号) */
    @Excel(name = "实验室编号")
    private String laboratoryCode;

    /** 删除标志（0存在，2删除） */
    private Integer delFlag;

    /** 关联的实验记录列表 */
    private List<ExperimentRecord> experimentRecords;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setEngineerSampleOrderId(Long engineerSampleOrderId) 
    {
        this.engineerSampleOrderId = engineerSampleOrderId;
    }

    public Long getEngineerSampleOrderId() 
    {
        return engineerSampleOrderId;
    }

    public void setBatchIndex(Integer batchIndex) 
    {
        this.batchIndex = batchIndex;
    }

    public Integer getBatchIndex() 
    {
        return batchIndex;
    }

    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }

    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }

    public void setActualManHours(BigDecimal actualManHours) 
    {
        this.actualManHours = actualManHours;
    }

    public BigDecimal getActualManHours() 
    {
        return actualManHours;
    }

    public void setQualityEvaluation(String qualityEvaluation) 
    {
        this.qualityEvaluation = qualityEvaluation;
    }

    public String getQualityEvaluation() 
    {
        return qualityEvaluation;
    }

    public void setBatchStatus(Integer batchStatus)
    {
        this.batchStatus = batchStatus;
    }

    public Integer getBatchStatus()
    {
        return batchStatus;
    }

    public void setLaboratoryCode(String laboratoryCode)
    {
        this.laboratoryCode = laboratoryCode;
    }

    public String getLaboratoryCode()
    {
        return laboratoryCode;
    }

    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    public List<ExperimentRecord> getExperimentRecords() 
    {
        return experimentRecords;
    }

    public void setExperimentRecords(List<ExperimentRecord> experimentRecords) 
    {
        this.experimentRecords = experimentRecords;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("engineerSampleOrderId", getEngineerSampleOrderId())
            .append("batchIndex", getBatchIndex())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("actualManHours", getActualManHours())
            .append("qualityEvaluation", getQualityEvaluation())
            .append("batchStatus", getBatchStatus())
            .append("laboratoryCode", getLaboratoryCode())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
