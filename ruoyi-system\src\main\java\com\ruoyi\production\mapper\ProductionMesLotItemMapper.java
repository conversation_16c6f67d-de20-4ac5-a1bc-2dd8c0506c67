package com.ruoyi.production.mapper;

import java.util.List;
import com.ruoyi.production.domain.ProductionMesLotItem;

/**
 * mes生产批明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-03
 */
public interface ProductionMesLotItemMapper
{

    public ProductionMesLotItem selectProductionMesLotItemById(Long id);

    public List<ProductionMesLotItem> selectProductionMesLotItemList(ProductionMesLotItem productionMesLotItem);

    public int insertProductionMesLotItem(ProductionMesLotItem productionMesLotItem);

    public int updateProductionMesLotItem(ProductionMesLotItem productionMesLotItem);

    public int deleteProductionMesLotItemByIds(Long[] ids);

    ProductionMesLotItem selectProductionMesLotItemByParams(ProductionMesLotItem lotItem);

}
