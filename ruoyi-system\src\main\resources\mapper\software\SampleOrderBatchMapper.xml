<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.software.mapper.SampleOrderBatchMapper">
    
    <resultMap type="SampleOrderBatch" id="SampleOrderBatchResult">
        <result property="id"    column="id"    />
        <result property="engineerSampleOrderId"    column="engineer_sample_order_id"    />
        <result property="batchIndex"    column="batch_index"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="actualManHours"    column="actual_man_hours"    />
        <result property="qualityEvaluation"    column="quality_evaluation"    />
        <result property="batchStatus"    column="batch_status"    />
        <result property="laboratoryCode"    column="laboratory_code"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSampleOrderBatchVo">
        select id, engineer_sample_order_id, batch_index, start_time, end_time, actual_man_hours, quality_evaluation, batch_status, laboratory_code, del_flag, create_by, create_time, update_by, update_time, remark from sample_order_batch
    </sql>

    <select id="selectSampleOrderBatchList" parameterType="SampleOrderBatch" resultMap="SampleOrderBatchResult">
        <include refid="selectSampleOrderBatchVo"/>
        <where>  
            del_flag = 0
            <if test="engineerSampleOrderId != null "> and engineer_sample_order_id = #{engineerSampleOrderId}</if>
            <if test="batchIndex != null "> and batch_index = #{batchIndex}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="actualManHours != null "> and actual_man_hours = #{actualManHours}</if>
            <if test="qualityEvaluation != null  and qualityEvaluation != ''"> and quality_evaluation = #{qualityEvaluation}</if>
            <if test="batchStatus != null "> and batch_status = #{batchStatus}</if>
            <if test="laboratoryCode != null and laboratoryCode != ''"> and laboratory_code = #{laboratoryCode}</if>
        </where>
        order by batch_index desc
    </select>
    
    <select id="selectSampleOrderBatchById" parameterType="Long" resultMap="SampleOrderBatchResult">
        <include refid="selectSampleOrderBatchVo"/>
        where id = #{id} and del_flag = 0
    </select>

    <select id="selectBatchesByOrderId" parameterType="Long" resultMap="SampleOrderBatchResult">
        <include refid="selectSampleOrderBatchVo"/>
        where engineer_sample_order_id = #{engineerSampleOrderId} and del_flag = 0
        order by batch_index asc
    </select>

    <select id="selectCurrentBatchByOrderId" parameterType="Long" resultMap="SampleOrderBatchResult">
        <include refid="selectSampleOrderBatchVo"/>
        where engineer_sample_order_id = #{engineerSampleOrderId} and batch_status = 1 and del_flag = 0
        order by batch_index desc
        limit 1
    </select>

    <!-- 查询指定打样单的所有进行中批次 -->
    <select id="selectActiveBatchesByOrderId" parameterType="Long" resultMap="SampleOrderBatchResult">
        <include refid="selectSampleOrderBatchVo"/>
        where engineer_sample_order_id = #{engineerSampleOrderId} and batch_status = 1 and del_flag = 0
        order by batch_index asc
    </select>

    <select id="selectNextBatchIndex" parameterType="Long" resultType="Integer">
        select IFNULL(MAX(batch_index), 0) + 1 from sample_order_batch 
        where engineer_sample_order_id = #{engineerSampleOrderId} and del_flag = 0
    </select>
        
    <insert id="insertSampleOrderBatch" parameterType="SampleOrderBatch" useGeneratedKeys="true" keyProperty="id">
        insert into sample_order_batch
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="engineerSampleOrderId != null">engineer_sample_order_id,</if>
            <if test="batchIndex != null">batch_index,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="actualManHours != null">actual_man_hours,</if>
            <if test="qualityEvaluation != null">quality_evaluation,</if>
            <if test="batchStatus != null">batch_status,</if>
            <if test="laboratoryCode != null">laboratory_code,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="engineerSampleOrderId != null">#{engineerSampleOrderId},</if>
            <if test="batchIndex != null">#{batchIndex},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="actualManHours != null">#{actualManHours},</if>
            <if test="qualityEvaluation != null">#{qualityEvaluation},</if>
            <if test="batchStatus != null">#{batchStatus},</if>
            <if test="laboratoryCode != null">#{laboratoryCode},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSampleOrderBatch" parameterType="SampleOrderBatch">
        update sample_order_batch
        <trim prefix="SET" suffixOverrides=",">
            <if test="engineerSampleOrderId != null">engineer_sample_order_id = #{engineerSampleOrderId},</if>
            <if test="batchIndex != null">batch_index = #{batchIndex},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="actualManHours != null">actual_man_hours = #{actualManHours},</if>
            <if test="qualityEvaluation != null">quality_evaluation = #{qualityEvaluation},</if>
            <if test="batchStatus != null">batch_status = #{batchStatus},</if>
            <if test="laboratoryCode != null">laboratory_code = #{laboratoryCode},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSampleOrderBatchById" parameterType="Long">
        update sample_order_batch set del_flag = 2 where id = #{id}
    </delete>

    <delete id="deleteSampleOrderBatchByIds" parameterType="String">
        update sample_order_batch set del_flag = 2 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
