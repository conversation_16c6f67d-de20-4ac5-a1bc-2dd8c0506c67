package com.ruoyi.system.service;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.customer.domain.Customer;
import com.ruoyi.hr.domain.SysHrUser;
import com.ruoyi.hr.domain.SysUserCvLog;
import com.ruoyi.hr.domain.SysUserImport;
import com.ruoyi.system.domain.excel.SysUserInfo;
import com.ruoyi.system.domain.excel.UserApplyExcel;
import com.ruoyi.system.domain.excel.UserExcel;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户 业务层
 *
 * <AUTHOR>
 */
public interface ISysUserService
{

    public List<SysUser> selectUserList(SysUser user);

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectAllocatedList(SysUser user);

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUnallocatedList(SysUser user);

    public SysUser selectUserByUserName(String userName);

    public SysUser selectUserById(Long userId);

    /**
     * 根据用户ID查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    public String selectUserRoleGroup(String userName);

    /**
     * 根据用户ID查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    public String selectUserPostGroup(String userName);

    /**
     * 校验用户编码是否唯一
     *
     * @param userCode 用户编码
     * @return 结果
     */
    public String checkUserCodeUnique(String userCode);

    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    public String checkUserNameUnique(String userName);

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public String checkPhoneUnique(SysUser user);

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public String checkEmailUnique(SysUser user);

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    public void checkUserAllowed(SysUser user);

    public void insertUser(SysUser user);

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean registerUser(SysUser user);

    public void updateUser(SysUser user);

    /**
     * 用户授权角色
     *
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    public void insertUserAuth(Long userId, Long[] roleIds);

    public int deleteUserRoleByUserId(Long userId, Long[] roleIds);

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUserStatus(SysUser user);

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUserProfile(SysUser user);

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    public boolean updateUserAvatar(String userName, String avatar);

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    public int resetPwd(SysUser user);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    public int resetUserPwd(String userName, String password);

    public void deleteUserByIds(Long[] userIds);

    /**
     * 导入用户数据
     *
     * @param userList 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importUser(List<SysUserImport> userList, Boolean isUpdateSupport, String operName);

    /**
     * 获取人员信息
     * @param params
     * @return
     */
    Map<String,Object> queryProcessUserInfo(Map<String, Object> params);

    /**
     * 获取所有用户
     * @param user
     * @return
     */
    List<SysUser> selectAllUserList(SysUser user);

    /**
     * 获取客户跟进人
     * @param tCustomer
     * @return
     */
    List<SysUser> queryCustomerUserList(Customer tCustomer);

    Set<Long> queryUserLeaderDataInfo(Long userId);
    List<Long> queryUserLeaderDataInfoNew(Long userId);

    List<Long> queryUserLeaderAndCustomerAssistDataInfo(Long userId,Long customerId);

    int assginDataUser(SysUser user);

    List<SysUserInfo> selectUserProccessList(SysUser user);

    List<SysUser> queryCustomerAllOrderUser(Customer tCustomer);

    Map<String,Object> queryUserFields(Long userId);

    List<SysUserCvLog> selectSysUserCvLogList(Long userId);

    List<UserExcel> selectUserProccessListNew(SysUser user);

    List<UserApplyExcel> selectUserApplyListNew(SysUser user);

    void updateUserPayslipPwd(SysHrUser sysHrUser);

    List<String> selectPermsByUserId(Long userId);

    List<String> selectPermsByUserId();

    /**
     * 根据产品类别id，获取对应研发可用的用户列表
     *
     * @param categoryId 产品类别id
     * @param projectOrderId 项目ID
     * @return 可用工程师列表
     */
    List<SysUser> selectEngineersByCategoryId(Long categoryId, Long projectOrderId);

    /**
     * 根据部门，获取对应的用户列表
     * @param DeptId 部门ID
     * @return 用户列表
     */
    List<SysUser> selectUsersByDeptId(Long DeptId);

}
