package com.ruoyi.mes.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.hr.domain.SysHrUser;
import com.ruoyi.hr.mapper.SysHrUserMapper;
import com.ruoyi.mes.domain.*;
import com.ruoyi.mes.mapper.*;
import com.ruoyi.order.domain.OrderProductionSchedule;
import com.ruoyi.order.domain.OrderScheduleStandard;
import com.ruoyi.order.mapper.OrderProductionScheduleMapper;
import com.ruoyi.order.mapper.OrderScheduleStandardMapper;
import com.ruoyi.production.domain.*;
import com.ruoyi.production.mapper.*;
import com.ruoyi.mes.service.IMesService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;

@Slf4j
@Service
public class MesServiceImpl implements IMesService {

    @Resource
    private OrderProductionScheduleMapper scheduleMapper;
    @Resource
    private ProductionMesLotMapper mesLotMapper;
    @Resource
    private MesWiplotBasisMapper mesWiplotBasisMapper;
    @Resource
    private MoBasisMapper moBasisMapper;
    @Resource
    private WipOperatorLogMapper wipOperatorLogMapper;
    @Resource
    private ProductionMesHoursMapper productionMesHoursMapper;
    @Resource
    private WipcontPartialoutMapper wipcontPartialoutMapper;
    @Resource
    private WipcontResourceMapper wipcontResourceMapper;
    @Resource
    private ProductionPlanAreaHoursMapper productionPlanAreaHoursMapper;
    @Resource
    private MesViewMapper mesViewMapper;
    @Resource
    private ProductionDayHoursMapper productionDayHoursMapper;
    @Resource
    private SysHrUserMapper userMapper;
    @Resource
    private ProductionMesLotDataMapper mesLotDataMapper;
    @Resource
    private OrderScheduleStandardMapper standardMapper;
    @Resource
    private ProductionMesLogMapper mesLogMapper;
    @Resource
    private ProductionMesLotItemMapper lotItemMapper;

    private static Set<String> ynSet;
    private static Set<String> ycSet;
    protected final static Logger logger = LoggerFactory.getLogger(MesServiceImpl.class);

    @PostConstruct
    private void init() {
        ynSet = CollUtil.newHashSet("5102","5103","51M2","5302","53M2","5402");
        ycSet = CollUtil.newHashSet("5101","5104","51M1","5301","53M1","5401");
    }

    @Override
    public void asyncMo() {
        List<String> moNos =moBasisMapper.selectRecentMoNoList();//查询最近一周的工单号
        if(CollUtil.isNotEmpty( moNos )){
            List<String> sapNos = scheduleMapper.selectCodeByMesMoNos(moNos);
            Set<String> newNos = new HashSet<>();//sap中还没有的计划编号
            for (String moNo : moNos) {
                if(!sapNos.contains(moNo)){
                    newNos.add(moNo);
                }
            }
            if(CollUtil.isNotEmpty(newNos)){
                MoBasis params = new MoBasis();
                params.setMoNos(new ArrayList<>(newNos));
                List<MoBasis> list = moBasisMapper.selectMoBasisList(params);
                for (MoBasis moBasis : list) {
                    OrderProductionSchedule schedule = new OrderProductionSchedule();
                    String[] strs = moBasis.getMono().split("-");
                    schedule.setSingleCategory(strs[0]);
                    schedule.setWorkOrderNo(strs[1]);
                    schedule.setCode(moBasis.getMono());
                    schedule.setNums(moBasis.getMoqty());
                    schedule.setProductNo(moBasis.getProductno());
                    schedule.setPlanStartTime(moBasis.getPlanstartdate());
                    schedule.setPlanFinishedTime(moBasis.getPlanfinishdate());
                    schedule.setUnit(moBasis.getMounitno());
                    schedule.setCreateBy(moBasis.getCreator());
                    schedule.setCreateTime(moBasis.getCreatedate());
                    schedule.setNatureType(1);
                    schedule.setIsPush(1);
                    schedule.setCompany(moBasis.getWarehouseNo());
                    String factory = "";
                    String prefix = moBasis.getMono().trim().substring(0, 4);
                    if(ynSet.contains(prefix)) {
                        factory = "COMPANY_YN";
                    }
                    if(ycSet.contains(prefix)) {
                        factory = "COMPANY_YC";
                    }
                    schedule.setFactory(factory);
                    schedule.setProductName(moBasis.getProductname());
                    schedule.setMb005(moBasis.getProducttype());
                    schedule.setScheduleTime(moBasis.getPlanstartdate());
                    schedule.setNatureType(1);
                    schedule.setIsPush(1);
                    schedule.setTa013("y");

                    scheduleMapper.insertOrderProductionSchedule(schedule);
                }
            }
        }

        List<String> scheduleCodes = scheduleMapper.selectNullNumsScheduleList();
        MoBasis moParams = new MoBasis();
        moParams.setMoNos(scheduleCodes);
        List<MoBasis> moList = moBasisMapper.selectMoBasisList(moParams);
        for (MoBasis moBasis : moList) {
            OrderProductionSchedule schedule = new OrderProductionSchedule();
            schedule.setCode(moBasis.getMono());
            schedule.setPlanNums(moBasis.getMoqty());
            scheduleMapper.updateOrderProductionScheduleByCode(schedule);
        }
    }

    @Override
    public void asyncMesLot() {
        List<MesWiplotBasis> list = mesWiplotBasisMapper.selectRecentMesWiplotBasisList();//查询批次基础信息
        for (MesWiplotBasis mesWiplotBasis : list) {
            ProductionMesLot l = mesLotMapper.selectProductionMesLotByLotNo(mesWiplotBasis.getBaselotno());
            if(l==null) {
                ProductionMesLot mesLot = new ProductionMesLot();
                String baseLotNo = mesWiplotBasis.getBaselotno();
                mesLot.setLotNo(baseLotNo);
                String mono = mesWiplotBasis.getMono();
                if(StringUtils.isNotEmpty(mono) && mono.contains("-")) {
                    String[] strs = mono.split("-");
                    OrderProductionSchedule orderProductionSchedule = scheduleMapper.selectOrderProductionSchedule(strs[0], strs[1]);
                    if(orderProductionSchedule != null) {
                        mesLot.setScheduleId(orderProductionSchedule.getId());
                        mesLot.setCreateBy(mesWiplotBasis.getCreator());
                        mesLot.setCreateTime(mesWiplotBasis.getCreatedate());
                        mesLot.setInNums(mesWiplotBasis.getInputqty());
                        mesLot.setState(mesWiplotBasis.getLotstate().toString());
                        mesLot.setWipTime(mesWiplotBasis.getWipdate());
                        mesLot.setProcessNo(mesWiplotBasis.getBaseprocessno());
                        mesLot.setRoNo(mesWiplotBasis.getRono());
                        mesLot.setProductNo(mesWiplotBasis.getProductno());
                        mesLot.setCustomerNo(mesWiplotBasis.getCustomerno());
                        mesLot.setUnit(mesWiplotBasis.getInputunitno());
                        mesLot.setPriority(mesWiplotBasis.getPriority());
                        mesLot.setHotLot(mesWiplotBasis.getHotlot().toString());
                        mesLot.setMoStartDate(mesWiplotBasis.getPlanstartdate());
                        mesLot.setMoEndDate(mesWiplotBasis.getPlanfinishdate());
                        String planType = "production";
                        if(mesLot.getProcessNo().startsWith("PZ")) {
                            planType = "makeUp";
                        }
                        mesLot.setType(planType);

                        mesLotMapper.insertProductionMesLot(mesLot);
                    }
                }
            }
        }
    }

    public static boolean isBetween0amAnd6pm(Date date) {//判断是否夜班
        LocalTime time = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalTime();

        LocalTime start = LocalTime.of(0, 0);
        LocalTime end = LocalTime.of(6, 0);

        return !time.isBefore(start) && !time.isAfter(end);
    }

    //是否是白班
    public static boolean isBetween6amAnd6pm(Date date) {
        LocalTime time = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalTime();

        LocalTime start = LocalTime.of(6, 0);  // 7:00 AM
        LocalTime end = LocalTime.of(18, 0);   // 19:00 PM

        return !time.isBefore(start) && !time.isAfter(end);
    }

    /**
     * 工段根据作业站编号判断
     */
    @Override
    public void asyncMesHours() {
        saveMesHours();
    }

    private void saveMesHours() {
        MesLotLog lotLogParams = new MesLotLog();
        lotLogParams.setRecentFlag("1");//查询最近一周的
        Set<String> lotNos = mesViewMapper.selectRecentWipLotList(lotLogParams);
        if(CollUtil.isNotEmpty( lotNos )) {
            WipcontResource resource = new WipcontResource();
            resource.setBaseNotNos(new ArrayList<>(lotNos));
            List<WipcontResource> wipcontResources = wipcontResourceMapper.selectWipcontResourceList(resource);
            Set<String> resItems = new HashSet<>();
            for (WipcontResource wipcontResource : wipcontResources) {
                resItems.add(wipcontResource.getResitem());
            }
            WipOperatorLog logParams = new WipOperatorLog();
            logParams.setLoginPlaceNos(resItems);
            List<WipOperatorLog> logs = wipOperatorLogMapper.selectWipOperatorLogList(logParams);//根据最近的批号查询所有的工时
            Set<String> mesIds = new HashSet<>();//mes中存在的Sid
            for (WipOperatorLog opLog : logs) {
                mesIds.add(opLog.getSid());
            }
            if(CollUtil.isNotEmpty(mesIds)) {
                List<String> existIds = productionMesHoursMapper.selectExistHourIdsByMesIds(mesIds);//查询sap已同步过的id
                for (WipOperatorLog opLog : logs) {
                    if(!existIds.contains(opLog.getSid())) {//如果没有同步过
                        ProductionMesHours hours = new ProductionMesHours();
                        hours.setMesId(opLog.getSid());
                        hours.setAreaNo(opLog.getAreaNo());
                        hours.setEquipmentNo(opLog.getEquipmentNo());
                        hours.setUserCode(opLog.getUserno());
                        String userType = "";
                        if(opLog.getUserno().startsWith("HR")) {
                            userType = "user";
                        } else if(opLog.getUserno().startsWith("LW-U-")) {
                            userType = "labor";
                        } else if(opLog.getUserno().startsWith("BG")) {
                            userType = "outer";
                        }
                        hours.setUserType(userType);
                        hours.setShiftno(opLog.getShiftno());
                        hours.setExceptiontime(opLog.getExceptiontime());
                        hours.setMinutes(opLog.getWorktime());
                        String sailings = "0";//默认是白班
                        Date startTime = opLog.getLogindate();
//                        if(opLog.getUserno().equals("HR20000610")) {
//                            log.info("HR20000610");
//                            if(DateUtils.dateTime(startTime).equals("2025-06-25")) {
//                                log.info("d");
//                            }
//                        }
                        Date topStartTime = getMesTopStartDate(opLog,logs);
                        Date workdate = DateUtils.parseDate(DateUtils.dateTime(topStartTime));
                        if(!isBetween6amAnd6pm(topStartTime)) {
                            sailings = "1";
                            if(isBetween0amAnd6pm(topStartTime)) {//如果最前面的夜班是0点之后开始的
                                workdate = DateUtils.parseDate(DateUtils.dateTime(DateUtils.addDays(topStartTime,-1)));
                            }
                        }
                        hours.setWorkdate(workdate);
                        hours.setSailings(sailings);
                        hours.setStartTime(startTime);
                        hours.setEndTime(opLog.getLogoutdate());
                        hours.setCreateTime(opLog.getCreatedate());
                        hours.setCreateBy(opLog.getCreateBy());
                        productionMesHoursMapper.insertProductionMesHours(hours);
                    }
                }
            }
        }
    }

    private static Date getMesTopStartDate(WipOperatorLog opLog, List<WipOperatorLog> logs) {
        Date topStartDate = opLog.getLogindate();//eventTime不知道干嘛的
        for (WipOperatorLog log : logs) {
//            if(opLog.getLoginplaceno().equals("A-04-08") && opLog.getUserno().equals("HR25006320")) {
//                System.out.println(opLog.getLoginplaceno());
//            }
            if(
                log.getUserno().equals(opLog.getUserno())
                && (isWithinOneHourPrecise(log.getLogoutdate(),opLog.getLogindate())//时间段之间相差不到一小时的
                        || (opLog.getLogindate().after(log.getLogindate()) && opLog.getLogindate().before(log.getLogoutdate())))//后一个时间段的开始时间包含在前一个时间段之内的
            ) { //如果这台设备上这个人一个小时之前有上工，就取前一个时间段的开始时间来判断日期归属和班次
                return getMesTopStartDate(log,logs);
            }
        }
        return topStartDate;
    }

    /**
     *  currentDate 当前日期,大的那个
     *  如果开始日期比前一个时间段的结束日期大不到一个小时的,理论上就是同一个班次的,可能是类似换线打卡
      */
    public static boolean isWithinOneHourPrecise(Date previousDate, Date currentDate) {
        if (previousDate == null || currentDate == null) {
            return false;
        }
        long diffInMillis = currentDate.getTime() - previousDate.getTime();

        final long ONE_HOUR_IN_MILLIS = 60 * 60 * 1000;

        return diffInMillis > 0 && diffInMillis <= ONE_HOUR_IN_MILLIS;
    }

    @Override
    public void asyncDayHours() {
        ProductionDayHours dayHourParams = new ProductionDayHours();
        dayHourParams.setRecentFlag("1");
        List<ProductionDayHours> dayHoursList = productionPlanAreaHoursMapper.selectRecentProductionDayHoursList(dayHourParams);
        for (ProductionDayHours productionDayHours : dayHoursList) {
            ProductionDayHours dayHours = productionDayHoursMapper.selectProductionDayHoursByFactoryAndDate(productionDayHours.getFactory(), productionDayHours.getWorkDate());
            if(dayHours == null) {
                productionDayHours.setCreateTime(DateUtils.getNowDate());
                productionDayHoursMapper.insertProductionDayHours(productionDayHours);
            }
        }
    }

    @Override
    public void asyncMesSapHours() {
        List<SysHrUser> userList = userMapper.selectUserListByDeptId(593L);
        List<ProductionMesHours> mesHourList = productionMesHoursMapper.selectProductionMesHoursNotInSapList();
        MesLotLog lotLogParams = new MesLotLog();
        lotLogParams.setRecentFlag("1");//查询最近一周的
        List<MesLotLog> outLogs = mesViewMapper.selectWipLotOutList(lotLogParams);//查询优化
        List<MesLotLog> inLogs = mesViewMapper.selectWipLotInList(lotLogParams);
//        List<MesLotLog> outUserLogs = mesViewMapper.selectWipLotOutUserList(lotLogParams);//查询出站人数(由于出站太迟了会跨好几天,没有办法单纯根据出站去判定是否参加过该批次)
        for (ProductionMesHours h : mesHourList) {
            String factory = null;
            for (SysHrUser user : userList) {
                if(user.getUserCode().equals(h.getUserCode())) {
                    factory = user.getCompanyCode();
                }
            }
//            if(h.getUserCode().equals("HR23002161")) {
//                logger.info(DateUtils.dateTime(h.getWorkdate()));
//                if( DateUtils.dateTime(h.getWorkdate()).equals("2025-07-08")) {
//                    logger.info(DateUtils.dateTime(h.getWorkdate()));
//                }
//            }
            if(factory != null) {
                List<MesLotLog> outList = getMesLotLogs(h, outLogs);
                List<MesLotLog> inList = getMesLotLogs(h, inLogs);//从小到大排的
//                if(h.getUserCode().equals("HR23002161") && DateUtils.dateTime(h.getWorkdate()).equals("2025-07-08")) {
//                    logger.info(DateUtils.dateTime(h.getWorkdate()));
//                }
                Date startTime = h.getStartTime();//暂时不好确定每个人的每个班次内，哪个时段是最先开始的,暂时不做半点向上取整
                int i = 0;
                for (MesLotLog out : outList) {
//                    if(out.getLotNo().equals("51M2-20250703014-001")) {
//                        logger.info("51M2-20250703014-001");
//                    }
                    ProductionPlanAreaHours hours = new ProductionPlanAreaHours();
                    hours.setUserCode(h.getUserCode());
                    String planType = "production";
                    if(out.getOpNo().startsWith("PZ")) {
                        planType = "makeUp";
                    }
                    hours.setPlanType(planType);
                    hours.setLotNo(out.getLotNo());
                    hours.setEquipmentNo(out.getEquipmentNo());
                    hours.setMesId(h.getMesId());
                    hours.setOpNo(out.getOpNo());
                    hours.setAreaNo(h.getAreaNo());
                    hours.setSailings(h.getSailings());
                    hours.setWorkDate(h.getWorkdate());
                    hours.setRemark("");

                    Date finalStartTime = startTime;
                    if(i == 0) {
                        finalStartTime = getFinalStartTime(startTime,out,inList);//如果开始时间和结束时间之间有暂停或开始,取时间段内的开始或暂停时间
                    }
                    hours.setStartTime(finalStartTime);//暂时不做段内截取了
                    hours.setEndTime(out.getCreateTime());
                    BigDecimal minutes = DateUtils.getExactDifferenceInMinutes(finalStartTime, out.getCreateTime());
                    if(out.getOpNo().startsWith("YCL"))  {//如果是预处理忽略 5分钟 以下的时段
                        if(minutes.compareTo(BigDecimal.valueOf(5)) < 0) {
                            continue;
                        }
                    } else {//其他作业站忽略 15分钟 以下的时段
                        if(minutes.compareTo(BigDecimal.valueOf(15)) < 0) {
                            continue;
                        }
                    }
                    hours.setMinutes(minutes);
                    String userType = "";
                    if(hours.getUserCode().startsWith("HR")) {
                        userType = "user";
                    } else if(hours.getUserCode().startsWith("LW-U-")) {
                        userType = "labor";
                    } else if(hours.getUserCode().startsWith("BG")) {
                        userType = "outer";
                    }
                    hours.setUserType(userType);

                    productionPlanAreaHoursMapper.insertProductionPlanAreaHours(hours);
                    startTime = out.getCreateTime();
                    i++;
                }
                if(startTime.before(h.getEndTime())) {//如果还有没用完的时间,全部给当前 startTime 之后的第一个
                    MesLotLog in = null;
                    if(CollUtil.isNotEmpty(inList)) {
                        if(CollUtil.isNotEmpty(outList)) {
                            for (MesLotLog lotLog : inList) {
                                if(lotLog.getCreateTime().after(startTime)) {//startTime 之后的第一个
                                    in = lotLog;
                                    break;
                                }
                            }
                        } else {//如果只有入站没有出站，就取入站最早的那个
                            in = inList.get(0);//第一个
                            startTime = in.getCreateTime();//开始时间别忘了
                        }
                    } else {
                        if(CollUtil.isEmpty(outList)) {//出入站都为空
                            in = getPreLotInLog(h, inLogs);//注意这里第二个参数要用全部了
                        }
                    }
                    if(in != null) {
                        ProductionPlanAreaHours hours = new ProductionPlanAreaHours();
                        hours.setUserCode(h.getUserCode());
                        String planType = "production";
                        if(in.getOpNo().startsWith("PZ")) {
                            planType = "makeUp";
                        }
                        hours.setPlanType(planType);
                        hours.setMesId(h.getMesId());
                        hours.setLotNo(in.getLotNo());
                        hours.setEquipmentNo(in.getEquipmentNo());
                        hours.setOpNo(in.getOpNo());
                        hours.setAreaNo(h.getAreaNo());
                        hours.setSailings(h.getSailings());
                        hours.setWorkDate(h.getWorkdate());
                        hours.setRemark("中途出站");
                        hours.setStartTime(startTime);
                        hours.setEndTime(h.getEndTime());//注意只取批次信息
                        BigDecimal minutes = DateUtils.getExactDifferenceInMinutes(startTime, h.getEndTime());
                        boolean flag = false;
                        if(in.getOpNo().startsWith("YCL"))  {//如果是预处理忽略 5分钟 以下的时段
                            if(minutes.compareTo(BigDecimal.valueOf(5)) > 0) {
                                flag = true;
                            }
                        } else {//其他作业站忽略 15分钟 以下的时段
                            if(minutes.compareTo(BigDecimal.valueOf(15)) > 0) {
                                flag = true;
                            }
                        }
                        if(flag) {
                            hours.setMinutes(minutes);
                            String userType = "";
                            if(hours.getUserCode().startsWith("HR")) {
                                userType = "user";
                            } else if(hours.getUserCode().startsWith("LW-U-")) {
                                userType = "labor";
                            } else if(hours.getUserCode().startsWith("BG")) {
                                userType = "outer";
                            }
                            hours.setUserType(userType);
                            productionPlanAreaHoursMapper.insertProductionPlanAreaHours(hours);
                        }
                    }
                }
            }
        }
    }

    private Date getFinalStartTime(Date startTime,MesLotLog out, List<MesLotLog> inList) {
        Date time = startTime;
        Date minTime = null;//取小于截取开始时间到本节出站时间段内最早的入站时间
        for (MesLotLog lotLog : inList) {
            Date t = lotLog.getCreateTime();
            if(t.after(startTime) && t.before(out.getCreateTime()) && lotLog.getLotNo().equals(out.getLotNo())) {
                if(minTime == null || minTime.after(t)) {
                    minTime = t;
                }
            }
        }
        if(minTime != null) {// minTime.after(time) 这个不用加了,上面已经判断过了,这里始终为true
            time = minTime;
        }
        return time;
    }

    private static MesLotLog getPreLotInLog(ProductionMesHours h, List<MesLotLog> logs) {
        MesLotLog row = null;
        for (MesLotLog log : logs) {
//            boolean flag = false;//当前人是否在这个生产批下的设备出站过(因为不清楚暂停和进站的人员,暂时不考虑时间)
//            for (MesLotLog mesLotLog : outUserLogs) {
//                if (log.getEquipmentNo().equals(mesLotLog.getEquipmentNo()) && log.getLotNo().equals(mesLotLog.getLotNo())) {
//                    flag = true;
//                    break;
//                }
//            }
//            if(flag) {//如果在这个批次设备上出站过
//
//            }
            if(log.getCreateTime().before(h.getStartTime())) {
                if(log.getEquipmentNo().equals(h.getEquipmentNo())) {//如果设备一致
                    row = log;
                }
            }
        }
        return row;
    }

    private static List<MesLotLog> getMesLotLogs(ProductionMesHours h, List<MesLotLog> logs) {
        List<MesLotLog> list = new ArrayList<>();//按出的时间切
        for (MesLotLog log : logs) {
//            if(log.getLotNo().equals("51M2-20250703014-001")
//                    && h.getEquipmentNo().equals("A-03-02")
//                    && log.getEquipmentNo().equals(h.getEquipmentNo())
//                    && DateUtils.dateTime(log.getCreateTime()).equals("2025-06-30")
//            ) {
//                logger.info(JSONObject.toJSONString(h));
//            }
//            boolean flag = false;//当前人是否在这个生产批下的设备出站过(因为不清楚暂停和进站的人员,暂时不考虑时间)
//            for (MesLotLog mesLotLog : outUserLogs) {
//                if (log.getEquipmentNo().equals(mesLotLog.getEquipmentNo()) && log.getLotNo().equals(mesLotLog.getLotNo())) {
//                    flag = true;
//                    break;
//                }
//            }
//            if(flag) {//如果在这个批次设备上出站过
//
//            }
            if(log.getCreateTime().after(h.getStartTime()) && log.getCreateTime().before(h.getEndTime()) ) {
                if(log.getEquipmentNo().equals(h.getEquipmentNo())) {//如果设备一致
                    list.add(log);
                }
            }
        }
        return list;
    }

    /**
     * 半点向上取整
     * @param date
     * @return
     */
    public static Date ceilToHalfHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        int minutes = calendar.get(Calendar.MINUTE);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);

        if (minutes == 0 && calendar.get(Calendar.SECOND) == 0
                && calendar.get(Calendar.MILLISECOND) == 0) {
            // 已经是整点，无需处理
            return date;
        }

        if (minutes < 30) {
            calendar.set(Calendar.MINUTE, 30);
        } else {
            // 超过30分钟，小时加1，分钟设为0
            calendar.set(Calendar.HOUR_OF_DAY, hour + 1);
            calendar.set(Calendar.MINUTE, 0);
        }

        // 清除秒和毫秒部分
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    @Override
    public void asyncMesLotItem() {
        MesLotLog lotLogParams = new MesLotLog();
        lotLogParams.setRecentFlag("1");//查询最近一周的
        Set<String> lotNos = mesViewMapper.selectRecentWipLotList(lotLogParams);
        if(CollUtil.isNotEmpty(lotNos)) {
            lotLogParams.setLotNos(lotNos);
            List<MesLotLog> mesLotLogs = mesViewMapper.selectGroupLotWorkDateNums(lotLogParams);
            List<MesLotLog> outLogs = mesViewMapper.selectLotOutList(lotLogParams);
            Set<MesLotDuplicate> lotSet = new HashSet<>();//除了操作类型、区域(暂停没有区域)和数量,都要放进去去重
            for (MesLotLog mesLotLog : mesLotLogs) {
                String opType = mesLotLog.getOpType();
                MesLotDuplicate d = new MesLotDuplicate();

                d.setLotNo(mesLotLog.getLotNo());
                d.setOpNo(mesLotLog.getOpNo());
                d.setEquipmentNo(mesLotLog.getEquipmentNo());
                d.setWorkDate(DateUtils.dateTime(mesLotLog.getCreateTime()));
                d.setSailings(mesLotLog.getSailings());
                d.setOpType(opType);
                if(opType.equals("WAITDISPOSITION")) {
                    d.setNums(mesLotLog.getQty());//出站的不要数量(从outLogs中匹配)
                } else {
                    d.setAreaNo(mesLotLog.getAreaNo());//暂停的没有区域编号
                }

                lotSet.add(d);
            }
            for (MesLotDuplicate duplicate : lotSet) {
                String opType = duplicate.getOpType();
                ProductionMesLotItem lotItem = new ProductionMesLotItem();

                lotItem.setLotNo(duplicate.getLotNo());
                String[] strs = duplicate.getLotNo().split("-");
                lotItem.setScheduleCode(strs[0] + "-" + strs[1]);
                lotItem.setOpNo(duplicate.getOpNo());
                lotItem.setEquipmentNo(duplicate.getEquipmentNo());
                lotItem.setWorkDate(DateUtils.parseDate(duplicate.getWorkDate()));
                lotItem.setSailings(duplicate.getSailings());

                if(opType.equals("WAITDISPOSITION")) {// 暂停的区域和线长用出站来替代(默认一个批次一个设备下的线长是同一个人)
                    MesLotLog current = null;
                    for (MesLotLog l : outLogs) {//暂停和出站和一般
                        if(l.getLotNo().equals(duplicate.getLotNo())
                                && l.getOpNo().equals(duplicate.getOpNo())
                                && l.getEquipmentNo().equals(duplicate.getEquipmentNo())
                        ) {
                            current = l;
                            break;
                        }
                    }
                    if(current!=null) {
                        lotItem.setAreaNo(current.getAreaNo());
                        lotItem.setLineCode(current.getUserNo());
                        lotItem.setLineLeader(current.getLineLeader());
                    } else {//如果没有匹配到出站跳过
                        continue;
                    }
                    lotItem.setProductNums(duplicate.getNums());
                } else {//出站严格匹配
                    MesLotLog current = null;
                    for (MesLotLog l : outLogs) {//暂停和出站和一般
                        if(l.getLotNo().equals(duplicate.getLotNo())
                                && l.getOpNo().equals(duplicate.getOpNo())
                                && l.getAreaNo().equals(duplicate.getAreaNo())
                                && l.getEquipmentNo().equals(duplicate.getEquipmentNo())
                                && DateUtils.dateTime(l.getEventDate()).equals(duplicate.getWorkDate())
                                && l.getSailings().equals(duplicate.getSailings())
                        ) {
                            current = l;
                            break;
                        }
                    }
                    if(current!=null) {
                        lotItem.setProductNums(current.getQty());//排除暂停后的数量,如果是负数就是填错了,当做0处理
                        lotItem.setLineCode(current.getUserNo());
                        lotItem.setLineLeader(current.getUserName());
                    } else {//如果没有匹配到出站跳过
                        continue;
                    }
                    lotItem.setAreaNo(duplicate.getAreaNo());
                }

                ProductionMesLotItem productionMesLotItem = lotItemMapper.selectProductionMesLotItemByParams(lotItem);
                if(productionMesLotItem==null) {
                    lotItem.setCreateTime(DateUtils.getNowDate());
                    if(lotItem.getProductNums().compareTo(BigDecimal.valueOf(1)) > 0) {
                        lotItemMapper.insertProductionMesLotItem(lotItem);
                    }
                }
            }
        }
    }

    /**
     * 方式一:用分摊的工时来拼接生产记录(要求分摊的工时精准)
     */
    @Override
    public void asyncSapLotItem() {
        MesLotLog lotLogParams = new MesLotLog();
        lotLogParams.setRecentFlag("1");//查询最近一周的
        List<MesLotLog> mesLotLogs = mesViewMapper.selectWipLotLogList(lotLogParams);//查询最近一周的
        List<ProductionPlanAreaHours> list = productionPlanAreaHoursMapper.selectGroupAreaAndWorkDateAndSailings();
        for (ProductionPlanAreaHours item : list) {
            List<MesLotLog> logs = new ArrayList<>();//根据工单、区域、设备筛选,且为最近两天的
            for (MesLotLog mesLotLog : mesLotLogs) {
                if(mesLotLog.getAreaNo().equals(item.getAreaNo())
                        && mesLotLog.getEquipmentNo().equals(item.getEquipmentNo())
                        && mesLotLog.getLotNo().startsWith(item.getScheduleCode())
                ) {
                    String workDate = DateUtils.dateTime(mesLotLog.getCreateTime());
                    if(workDate.equals(DateUtils.dateTime(item.getWorkDate()))) {//今天
                        logs.add(mesLotLog);
                    }
//                    if(workDate.equals(DateUtils.dateTime(DateUtils.addDays(item.getWorkDate(),-1)))) {//昨天
//                        logs.add(mesLotLog);
//                    }
                }
            }
            BigDecimal productionNums = BigDecimal.ZERO;
            String lineLeader = "";
            for (MesLotLog log : logs) {
                if(log.getOpType().equals("CHECKOUT")) {//出站
                    productionNums = log.getQty();
                    lineLeader = log.getUserName();
                }
                if(log.getOpType().equals("WAITDISPOSITION")) {//暂停

                }
            }
            ProductionMesLog mesLogParams = new ProductionMesLog();
            mesLogParams.setAreaNo(item.getAreaNo());
            mesLogParams.setWorkDate(item.getWorkDate());
            mesLogParams.setSailings(item.getSailings());
            mesLogParams.setScheduleCode(item.getScheduleCode());
            mesLogParams.setEquipmentNo(item.getEquipmentNo());
            mesLogParams.setOpNo(item.getOpNo());
            ProductionMesLog mesLog = mesLogMapper.selectProductionMesLogByAreaAndWorkDateAndSailings(mesLogParams);
            if(mesLog==null) {
                mesLogParams.setProductNums(productionNums);
                mesLogParams.setLineLeader(lineLeader);
                mesLogParams.setType(item.getOpNo().contains("PZ")?"makeUp":"production");
                mesLogParams.setCode(item.getScheduleCode() + "_" + item.getAreaNo() + "_" + DateUtils.dateTime(item.getWorkDate(),DateUtils.YYYYMMDD + "_" + (item.getSailings().equals("0") ? "白" : "晚")));
                mesLogMapper.insertProductionMesLog(mesLogParams);
            }
        }
    }

    @Override
    public void asyncMesLotData() {
        List<MesWiplotBasis> list = mesWiplotBasisMapper.selectRecentMesWiplotBasisList();
        for (MesWiplotBasis mesWiplotBasis : list) {
            if(!mesWiplotBasis.getBaseprocessno().startsWith("PZ")) {
                String lotNo = mesWiplotBasis.getBaselotno();
                ProductionMesLotData l = mesLotDataMapper.selectProductionMesLotDataByLotNo(lotNo);
                ProductionMesLotData mesLot = new ProductionMesLotData();
                BigDecimal outNums = wipcontPartialoutMapper.selectOutNumsByLotNo(lotNo);
                mesLot.setOutNum(outNums);
                mesLot.setRate(outNums.divide(mesWiplotBasis.getInputqty(),4,RoundingMode.HALF_UP));
                BigDecimal minutes = productionPlanAreaHoursMapper.selectMinutesByLotNo(lotNo);
                BigDecimal actualHours = minutes.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
                mesLot.setActualHours(actualHours);
                if(outNums.compareTo(BigDecimal.ZERO) > 0) {
                    OrderScheduleStandard orderScheduleStandard = standardMapper.selectOrderScheduleStandardByCode(mesWiplotBasis.getProductno());
                    BigDecimal standradHours = BigDecimal.ZERO;
                    if(orderScheduleStandard!= null) {
                        if(mesWiplotBasis.getBaseprocessno().equals("GB")) {//灌包一体 一阶取少的
                            standradHours = mesWiplotBasis.getInputqty().divide(orderScheduleStandard.getHourRate(),2,RoundingMode.HALF_UP);
                        } else {
                            standradHours = mesWiplotBasis.getInputqty().divide(orderScheduleStandard.getCostHoursRate(),2,RoundingMode.HALF_UP);
                        }
                    }
                    mesLot.setStandradHours(standradHours);
                    String hoursDesc;
                    if(actualHours.compareTo(BigDecimal.ZERO) > 0 && standradHours.compareTo(BigDecimal.ZERO) > 0) {
                        if(actualHours.compareTo(standradHours) > 0) {
                            hoursDesc = "超工时";
                        } else if(actualHours.compareTo(standradHours) == 0) {
                            hoursDesc = "按标准工时完成 ";
                        } else {
                            hoursDesc = "正常";
                        }
                    } else {
                        hoursDesc = "异常";
                    }
                    mesLot.setHoursDesc(hoursDesc);
                }
                if(l==null) {
                    mesLot.setLotNo(mesWiplotBasis.getBaselotno());
                    String mono = mesWiplotBasis.getMono();
                    if(StringUtils.isNotEmpty(mono) && mono.contains("-")) {
                        String[] strs = mono.split("-");
                        OrderProductionSchedule orderProductionSchedule = scheduleMapper.selectOrderProductionSchedule(strs[0], strs[1]);
                        if(orderProductionSchedule != null) {
                            mesLot.setScheduleId(orderProductionSchedule.getId());
                            mesLot.setCreateBy(mesWiplotBasis.getCreator());
                            mesLot.setCreateTime(mesWiplotBasis.getCreatedate());
                            mesLot.setInNums(mesWiplotBasis.getInputqty());
                            mesLot.setState(mesWiplotBasis.getLotstate().toString());
                            mesLot.setWipTime(mesWiplotBasis.getWipdate());
                            mesLot.setProcessNo(mesWiplotBasis.getBaseprocessno());
                            mesLot.setRoNo(mesWiplotBasis.getRono());
                            mesLot.setProductNo(mesWiplotBasis.getProductno());
                            mesLot.setCustomerNo(mesWiplotBasis.getCustomerno());
                            mesLot.setUnit(mesWiplotBasis.getInputunitno());
                            mesLot.setPriority(mesWiplotBasis.getPriority());
                            mesLot.setHotLot(mesWiplotBasis.getHotlot().toString());
                            mesLot.setMoStartDate(mesWiplotBasis.getPlanstartdate());
                            mesLot.setMoEndDate(mesWiplotBasis.getPlanfinishdate());
                            String planType = "production";
                            if(mesLot.getProcessNo().startsWith("PZ")) {
                                planType = "makeUp";
                            }
                            mesLot.setType(planType);

                            mesLotDataMapper.insertProductionMesLotData(mesLot);
                        }
                    }
                } else {
                    mesLot.setId(l.getId());
                    mesLotDataMapper.updateProductionMesLotData(mesLot);
                }
            }
        }
    }

}