package com.ruoyi.production.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * mes生产批明细对象 t_production_mes_lot_item
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public class ProductionMesLotItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 批次 */
    @Excel(name = "批次")
    private String lotNo;

    /** 工单编号 */
    @Excel(name = "工单编号")
    private String scheduleCode;

    /** 排班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "排班日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date workDate;

    /** 班别 */
    @Excel(name = "班别")
    private String sailings;

    /** 区域编号 */
    @Excel(name = "区域编号")
    private String areaNo;

    /** 设备编号 */
    @Excel(name = "设备编号")
    private String equipmentNo;

    /** 作业站编号 */
    @Excel(name = "作业站编号")
    private String opNo;

    /** 线长编号 */
    @Excel(name = "线长编号")
    private String lineCode;

    /** 线长 */
    @Excel(name = "线长")
    private String lineLeader;

    /** 生产数量 */
    @Excel(name = "生产数量")
    private BigDecimal productNums;

    /** 删除标志（0代表存在 2代表删除） */
    private Integer delFlag;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setLotNo(String lotNo) 
    {
        this.lotNo = lotNo;
    }

    public String getLotNo() 
    {
        return lotNo;
    }
    public void setScheduleCode(String scheduleCode) 
    {
        this.scheduleCode = scheduleCode;
    }

    public String getScheduleCode() 
    {
        return scheduleCode;
    }
    public void setWorkDate(Date workDate) 
    {
        this.workDate = workDate;
    }

    public Date getWorkDate() 
    {
        return workDate;
    }
    public void setSailings(String sailings) 
    {
        this.sailings = sailings;
    }

    public String getSailings() 
    {
        return sailings;
    }
    public void setAreaNo(String areaNo) 
    {
        this.areaNo = areaNo;
    }

    public String getAreaNo() 
    {
        return areaNo;
    }
    public void setEquipmentNo(String equipmentNo) 
    {
        this.equipmentNo = equipmentNo;
    }

    public String getEquipmentNo() 
    {
        return equipmentNo;
    }
    public void setOpNo(String opNo) 
    {
        this.opNo = opNo;
    }

    public String getOpNo() 
    {
        return opNo;
    }
    public void setLineCode(String lineCode) 
    {
        this.lineCode = lineCode;
    }

    public String getLineCode() 
    {
        return lineCode;
    }
    public void setLineLeader(String lineLeader) 
    {
        this.lineLeader = lineLeader;
    }

    public String getLineLeader() 
    {
        return lineLeader;
    }
    public void setProductNums(BigDecimal productNums) 
    {
        this.productNums = productNums;
    }

    public BigDecimal getProductNums() 
    {
        return productNums;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("lotNo", getLotNo())
            .append("scheduleCode", getScheduleCode())
            .append("workDate", getWorkDate())
            .append("sailings", getSailings())
            .append("areaNo", getAreaNo())
            .append("equipmentNo", getEquipmentNo())
            .append("opNo", getOpNo())
            .append("lineCode", getLineCode())
            .append("lineLeader", getLineLeader())
            .append("productNums", getProductNums())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("type", getType())
            .toString();
    }
}
