<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysUserMapper">

	<resultMap type="SysUser" id="SysUserResult">
		<id     property="userId"       column="user_id"      />
		<result property="deptId"       column="dept_id"      />
		<result property="workUserId"       column="work_user_id"      />
		<result property="userName"     column="user_name"    />
		<result property="userType" column="user_type"       />
		<result property="directSuperior"     column="direct_superior"    />
		<result property="companyCode"     column="company_code"    />
		<result property="nickName"     column="nick_name"    />
		<result property="email"        column="email"        />
		<result property="phonenumber"  column="phonenumber"  />
		<result property="sex"          column="sex"          />
		<result property="avatar"       column="avatar"       />
		<result property="password"     column="password"     />
		<result property="status"       column="status"       />
		<result property="type"       column="type"       />
		<result property="userCode"     column="user_code"       />
		<result property="delFlag"      column="del_flag"     />
		<result property="loginIp"      column="login_ip"     />
		<result property="loginDate"    column="login_date"   />
		<result property="createBy"     column="create_by"    />
		<result property="createTime"   column="create_time"  />
		<result property="updateBy"     column="update_by"    />
		<result property="updateTime"   column="update_time"  />
		<result property="remark"       column="remark"       />
		<result property="isMain"       column="is_main"       />
		<result property="groupName"       column="groupName"       />
		<result property="openUserName"       column="open_user_name"       />
		<result property="assistStatus"       column="assist_status"       />
		<result property="assistId"       column="assist_id"       />
		<result property="modifiedPwdTime"       column="modified_pwd_time"       />
		<result property="userStatus"       column="user_status"       />
		<result property="bgdd"       column="bgdd"       />
		<result property="bgcc"       column="bgcc"       />
		<result property="officePhone"       column="office_phone"       />
		<result property="seniority"       column="seniority"       />
		<result property="birthday"       column="birthday"       />
		<result property="positiveStatus"       column="positive_status"       />
		<result property="politicalFace"       column="political_face"       />
		<result property="rank"       column="rank"       />
		<result property="ethnic" column="ethnic"       />
		<result property="hukouXz"       column="hukou_xz"       />
		<result property="hukouJg"       column="hukou_jg"       />
		<result property="hukouHjdz"       column="hukou_hjdz"       />
		<result property="hukouXjjdz"       column="hukou_xjjdz"       />
		<result property="degree"       column="degree"       />
		<result property="hyzk"       column="hyzk"       />
		<result property="cardType"       column="card_type"       />
		<result property="cardNo"       column="card_no"       />
		<result property="bankName"       column="bank_name"       />
		<result property="bankNo"       column="bank_no"       />
		<result property="bankAccount"       column="bank_account"       />
		<result property="socialSecurityType"       column="social_security_type"       />
		<result property="socialSecurityNo"       column="social_security_no"       />
		<result property="socialSecurityAddress"       column="social_security_address"       />
		<result property="cpfNo"       column="cpf_no"       />
		<result property="cardBeforeImg"       column="card_before_img"       />
		<result property="cardAfterImg"       column="card_after_img"       />
		<result property="joinTime"		column="join_time" 		/>
		<result property="attendanceId"		column="attendance_id" 		/>
		<result property="schedulingId"		column="scheduling_id" 		/>
		<result property="clockId"		column="clock_id" 		/>
		<result property="isClock"		column="is_clock" 		/>
		<result property="isOvertime"		column="is_overtime" 		/>
		<result property="isPost"		column="is_post" 		/>
		<result property="qcCode"		column="qc_code" 		/>
		<result property="zzDate"		column="zz_date" 		/>
		<result property="zzType"		column="zz_type" 		/>
		<association property="expiresTimes" javaType="java.util.HashMap" column="{userId=user_id}" select="selectUserExpiresTimeMap" />
		<association property="wages" javaType="java.util.HashMap" column="{userId=user_id}" select="selectUserWagesMap" />
		<association property="dept"    column="dept_id" javaType="SysDept" resultMap="deptResult" />
		<collection  property="roles"   javaType="java.util.List"  column="{userId=user_id}" select="selectRoleList" />
		<collection property="posts" javaType="java.util.ArrayList" column="{userId=user_id}" select="selectPostList" />
	</resultMap>

	<resultMap type="SysUser" id="SysUserResultNew">
		<id     property="userId"       column="user_id"      />
		<result property="deptId"       column="dept_id"      />
		<result property="workUserId"       column="work_user_id"      />
		<result property="userName"     column="user_name"    />
		<result property="userType" column="user_type"       />
		<result property="directSuperior"     column="direct_superior"    />
		<result property="companyCode"     column="company_code"    />
		<result property="nickName"     column="nick_name"    />
		<result property="email"        column="email"        />
		<result property="phonenumber"  column="phonenumber"  />
		<result property="sex"          column="sex"          />
		<result property="avatar"       column="avatar"       />
		<result property="password"     column="password"     />
		<result property="status"       column="status"       />
		<result property="type"       column="type"       />
		<result property="userCode"     column="user_code"       />
		<result property="delFlag"      column="del_flag"     />
		<result property="loginIp"      column="login_ip"     />
		<result property="loginDate"    column="login_date"   />
		<result property="createBy"     column="create_by"    />
		<result property="createTime"   column="create_time"  />
		<result property="updateBy"     column="update_by"    />
		<result property="updateTime"   column="update_time"  />
		<result property="remark"       column="remark"       />
		<result property="isMain"       column="is_main"       />
		<result property="groupName"       column="groupName"       />
		<result property="openUserName"       column="open_user_name"       />
		<result property="assistStatus"       column="assist_status"       />
		<result property="assistId"       column="assist_id"       />
		<result property="modifiedPwdTime"       column="modified_pwd_time"       />
		<result property="userStatus"       column="user_status"       />
		<result property="bgdd"       column="bgdd"       />
		<result property="bgcc"       column="bgcc"       />
		<result property="officePhone"       column="office_phone"       />
		<result property="seniority"       column="seniority"       />
		<result property="birthday"       column="birthday"       />
		<result property="positiveStatus"       column="positive_status"       />
		<result property="politicalFace"       column="political_face"       />
		<result property="rank"       column="rank"       />
		<result property="ethnic" column="ethnic"       />
		<result property="hukouXz"       column="hukou_xz"       />
		<result property="hukouJg"       column="hukou_jg"       />
		<result property="hukouHjdz"       column="hukou_hjdz"       />
		<result property="hukouXjjdz"       column="hukou_xjjdz"       />
		<result property="degree"       column="degree"       />
		<result property="hyzk"       column="hyzk"       />
		<result property="cardType"       column="card_type"       />
		<result property="cardNo"       column="card_no"       />
		<result property="bankName"       column="bank_name"       />
		<result property="bankNo"       column="bank_no"       />
		<result property="bankAccount"       column="bank_account"       />
		<result property="socialSecurityType"       column="social_security_type"       />
		<result property="socialSecurityNo"       column="social_security_no"       />
		<result property="socialSecurityAddress"       column="social_security_address"       />
		<result property="cpfNo"       column="cpf_no"       />
		<result property="cardBeforeImg"       column="card_before_img"       />
		<result property="cardAfterImg"       column="card_after_img"       />
		<result property="joinTime"		column="join_time" 		/>
		<result property="attendanceId"		column="attendance_id" 		/>
		<result property="schedulingId"		column="scheduling_id" 		/>
		<result property="clockId"		column="clock_id" 		/>
		<result property="isClock"		column="is_clock" 		/>
		<result property="isOvertime"		column="is_overtime" 		/>
		<result property="qcCode"		column="qc_code" 		/>
		<result property="zzDate"		column="zz_date" 		/>
		<result property="zzType"		column="zz_type" 		/>
 		<association property="dept"    column="dept_id" javaType="SysDept" resultMap="deptResult" />
 	</resultMap>

	<select id="selectRoleList" resultMap="RoleResult">
		select
			r.role_id,
			r.role_name,
			r.role_key,
			r.role_sort,
			r.data_scope,
			r. STATUS AS role_status
		FROM
			sys_user_role ur
			LEFT JOIN sys_role r ON r.role_id = ur.role_id
			where ur.`user_id` = #{userId} and r.is_workflow = 0
	</select>

	<select id="selectPostList" resultType="Map">
		select p.post_id `postId`,p.post_name `postName`
		from sys_post p
		left join sys_user_post up on up.post_id = p.post_id
		where up.user_id = #{userId}
	</select>

	<select id="selectUserWagesMap" resultType="Map">
		select wages_base `wagesBase`, wages_post `wagesPost`,
		       wages_assess `wagesAssess`, wages_seniority `wagesSeniority`,
		       wages_bgjbf `wagesBgjbf`,
			   wages_meal `wagesMeal`,
			   wages_cart `wagesCart`,
		       wages_id `wagesId`,
		       social_security_id `socialSecurityId`,
		       allowance_id `allowanceId`
		from sys_user_wages
		where user_id = #{userId}
	</select>

	<select id="selectUserExpiresTimeMap" resultType="Map">
		SELECT
		(SELECT t.`contract_expires_time` FROM (
		SELECT date_format(c.`contract_expires_time`,'%Y-%m-%d %H:%i:%s') `contract_expires_time` FROM sys_user_contract c where c.user_id = #{userId} ORDER BY c.`ID` DESC LIMIT 1
		) t WHERE timestampdiff(day,NOW(),t.`contract_expires_time`) &lt; 30) `contractExpiresTime`,
		(SELECT t.`health_expire_time` FROM (
		SELECT date_format(c.`health_expire_time`,'%Y-%m-%d %H:%i:%s') `health_expire_time` FROM sys_user_health c where c.user_id = #{userId} ORDER BY c.`ID` DESC LIMIT 1
		) t WHERE timestampdiff(day,NOW(),t.`health_expire_time`) &lt; 30) `healthExpireTime`
	</select>

	<resultMap id="deptResult" type="SysDept">
		<id     property="deptId"   column="dept_id"     />
		<result property="parentId" column="parent_id"   />
		<result property="deptName" column="dept_name"   />
		<result property="orderNum" column="order_num"   />
		<result property="leader"   column="leader"      />
		<result property="status"   column="dept_status" />
	</resultMap>

	<resultMap id="RoleResult" type="SysRole">
		<id     property="roleId"       column="role_id"        />
		<result property="roleName"     column="role_name"      />
		<result property="roleKey"      column="role_key"       />
		<result property="roleSort"     column="role_sort"      />
		<result property="dataScope"     column="data_scope"    />
		<result property="status"       column="role_status"    />
	</resultMap>

	<sql id="selectUserVo">
		SELECT
			u.user_id,
			u.dept_id,
			u.work_user_id,
			u.user_code,
			u.user_name,
			u.nick_name,
			u.user_type,
			u.email,
			u.avatar,
			AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') `phonenumber`,
			u. PASSWORD,
			u.sex,
			u. STATUS,
			u.del_flag,
			u.login_ip,
			u.login_date,
			u.create_by,
			u.create_time,
			u.modified_pwd_time,
			u.remark,
			u.user_status,
			u.bgdd,
			u.bgcc,
			u.office_phone,
			u.seniority,
			u.birthday,
			u.positive_status,
			u.ethnic,
			u.political_face,
			u.rank,
			u.hukou_xz,
			u.hukou_jg,
			u.hukou_hjdz,
			u.hukou_xjjdz,
			u.degree,
			u.hyzk,
			u.card_type,
			AES_DECRYPT(FROM_BASE64(u.card_no),'ym3JB46gfD') card_no,
			u.bank_name,
			u.bank_no,
			u.bank_account,
			u.social_security_type,
			u.social_security_no,
			u.social_security_address,
			u.cpf_no,
			u.card_before_img,
			u.card_after_img,
			u.join_time,
			u.company_code,
			u.zz_date,
			u.zz_type,
			d.dept_id,
			d.parent_id,
			d.dept_name,
			d.order_num,
			d.leader,
			d.ancestors,
			d.STATUS AS                        dept_status,
			u.direct_superior,
			u.attendance_id,
			u.scheduling_id,
			u.clock_id,
			u.is_clock,
			u.is_overtime,
			u.qc_code,
			ifnull(u2.nick_name, u2.user_name) open_user_name
		FROM sys_user u
			LEFT JOIN sys_user u2 ON u.direct_superior = u2.user_id
			LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
    </sql>

    <select id="selectUserList" parameterType="SysUser" resultMap="SysUserResultNew">
		<include refid="selectUserVo"/>
		WHERE
		u.del_flag = '0' and u.is_user = 0
		<if test="postId != null" >
			and u.user_id in (select p.user_id from sys_user_post p where p.post_id = #{postId})
		</if>
		<if test="workUserId != null and workUserId != ''">
			and u.work_user_id = #{workUserId}
		</if>
		<if test="userCode != null and userCode != ''"><!-- 员工编号 -->
			AND u.user_code like concat('%', #{userCode}, '%')
		</if>
		<if test="userName != null and userName != ''"><!-- 员工编号 -->
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="nickName != null and nickName != ''"><!-- 姓名 -->
			AND u.nick_name like concat('%', #{nickName}, '%')
		</if>
		<if test="companyCode != null and companyCode != ''"><!-- 所属公司 -->
			AND u.company_code = #{companyCode}
		</if>
		<if test="userType != null and userType != ''"><!-- 聘用形式 -->
			AND u.user_type = #{userType}
		</if>
		<if test="month != null and month != ''"><!-- 生日 -->
			AND MONTH(u.birthday) = #{month}
		</if>
		<if test="positiveStatus != null and positiveStatus != ''"><!-- 状态 -->
			AND u.positive_status = #{positiveStatus}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD')  like concat('%', #{phonenumber}, '%')
		</if>
		<if test="isPurchase != null and isPurchase == 1">
			AND u.is_purchase = 1
		</if>
		<if test="deptId != null and deptId != ''"><!-- 部门 -->
			AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
		</if>
		<if test="deptIds != null and deptIds.size() > 0">
			and (
				u.dept_id in
				<foreach collection="deptIds" item="deptId" separator="," open="(" close=")" >
					#{deptId}
				</foreach>
				or
				<foreach collection="deptIds" item="deptId" separator="or" >
					(u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
				</foreach>
			)
		</if>
		<if test="rank != null and rank != ''"><!-- 岗位 -->
			AND u.rank = #{rank}
		</if>
		<if test="contractType != null and contractType != ''"><!-- 合同类型 -->
			AND u.user_id IN (SELECT user_id FROM sys_user_contract WHERE contract_type = #{contractType})
		</if>
		<if test="contractExpiresTime != null and contractExpiresTime != ''"><!-- 合同到期月份 -->
			AND u.user_id IN (SELECT user_id FROM sys_user_contract WHERE `contract_expires_time` = #{contractExpiresTime})
		</if>
		<if test="healthExpireTime != null and healthExpireTime != ''"><!-- 健康证到期月份 -->
			AND u.user_id IN (SELECT user_id FROM sys_user_health WHERE `health_expire_time` = #{healthExpireTime})
		</if>
		<if test="wagesId != null and wagesId != ''"><!-- 工资方案 -->
			AND u.user_id IN (SELECT user_id FROM sys_user_wages WHERE `wages_id` = #{wagesId})
		</if>
		<if test="socialSecurityId != null and socialSecurityId != ''"><!-- 社保方案 -->
			AND u.user_id IN (SELECT user_id FROM sys_user_wages WHERE `social_security_id` = #{socialSecurityId})
		</if>
		<if test="zzType != null and zzType != null" >and u.zz_type = #{zzType}</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
		order by u.user_id desc
	</select>

	<select id="selectUserAll" parameterType="SysUser" resultType="SysUser">
		SELECT
		u.user_id,
		u.dept_id,
		u.work_user_id,
		u.user_code,
		u.user_name,
		u.nick_name,
		AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') `phonenumber`,
		u.sex,
		u.avatar,
		u.user_type,
		s.parent_id,
		s.ancestors,
		s.dept_name
		FROM
		sys_user u
		inner join sys_dept s on s.dept_id = u.dept_id
		<where>
            u.`del_flag` = 0 and u.`positive_status` != 2 and u.is_user = 0
            <if test="userType!=null">
				and u.user_type != #{userType}
			</if>
            <if test="userName != null and userName != ''">
				AND (
					u.nick_name like concat('%', #{userName}, '%')
					or
					u.user_code like concat('%', #{userName}, '%')
					or
					AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') like concat('%', #{userName}, '%')
				)
			</if>
			<if test="deptId != null" >and (find_in_set(#{deptId}, s.ancestors) or s.dept_id = #{deptId}) </if>
			<if test="deptIds != null and deptIds.size() > 0">
				and (
				u.dept_id in
				<foreach collection="deptIds" item="deptId" separator="," open="(" close=")" >
					#{deptId}
				</foreach>
				or
				<foreach collection="deptIds" item="deptId" separator="or" >
					(u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
				</foreach>
				)
			</if>

			<if test="companyCodes != null and companyCodes.size() > 0">
				and u.company_code in
				<foreach collection="companyCodes" item="companyCode" separator="," open="(" close=")" >
					#{companyCode}
				</foreach>
			</if>
		</where>
	</select>

	<select id="selectIncumbencyList" parameterType="SysUser" resultType="SysUser">
		SELECT
		u.user_id,
		u.dept_id,
		u.work_user_id,
		u.user_code,
		u.user_name,
		u.nick_name,
		AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') `phonenumber`,
		u.sex,
		u.avatar,
		u.user_type,
		s.parent_id,
		s.ancestors,
		s.dept_name
		FROM
		sys_user u
		inner join sys_dept s on s.dept_id = u.dept_id
		<where>
			u.`del_flag` = 0 and u.`positive_status` != 2 and u.is_user = 0 and u.work_user_id is not null
			<if test="nameList != null and nameList.size() > 0" >
				and (
					u.nick_name in
					<foreach collection="nameList" item="name" separator="," open="(" close=")" >
						#{name}
					</foreach>
				)
			</if>
			<if test="nickName != null and nickName != ''" >u.nick_name like concat('%', #{userName}, '%')</if>
			<if test="deptId != null" >and (find_in_set(#{deptId}, s.ancestors) or s.dept_id = #{deptId}) </if>
			<if test="deptIds != null and deptIds.size() > 0">
				and (
				u.dept_id in
				<foreach collection="deptIds" item="deptId" separator="," open="(" close=")" >
					#{deptId}
				</foreach>
				or
				<foreach collection="deptIds" item="deptId" separator="or" >
					(u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
				</foreach>
				)
			</if>
			<if test="companyCodes != null and companyCodes.size() > 0">
				and u.company_code in
				<foreach collection="companyCodes" item="companyCode" separator="," open="(" close=")" >
					#{companyCode}
				</foreach>
			</if>
		</where>
	</select>

	<select id="selectUserListByUserIds" resultType="SysUser">
		SELECT
			u.user_id,
			u.dept_id,
			u.work_user_id,
			u.user_code,
			u.user_name,
			u.nick_name,
			AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') `phonenumber`,
			u.sex,
			u.avatar,
			u.user_type,
			s.parent_id,
			s.ancestors,
			s.dept_name
		FROM
			sys_user u
			inner join sys_dept s on s.dept_id = u.dept_id
		<where>
			and u.user_id in
			<foreach collection="userIds" item="userId" separator="," open="(" close=")" >
				#{userId}
			</foreach>
		</where>
	</select>

	<select id="queryUserDataInfo" resultType="java.lang.String">
		SELECT
			group_concat(u.nick_name)
		FROM
			sys_user u
		where u.user_id in
			<foreach collection="userIds" item="userId" separator="," open="(" close=")" >
				#{userId}
			</foreach>
	</select>

	<select id="selectUserTreeAll" parameterType="SysUser" resultType="SysUser">
		SELECT
		u.user_id,
		u.dept_id,
		u.work_user_id,
		u.user_code,
		u.user_name,
		u.nick_name,
		AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') `phonenumber`,
		u.sex,
		u.avatar,
		s.parent_id,
		s.dept_name,
		(
			SELECT GROUP_CONCAT(r.role_key)
			FROM sys_user_role ur
			LEFT JOIN sys_role r ON r.role_id = ur.role_id
			WHERE ur.user_id = u.user_id
		) `roleName`
		FROM
		sys_user u
		inner join sys_dept s on s.dept_id = u.dept_id
		left join (
			SELECT
			d.user_id,
			d.positive_status,
			d.take_effect_time
			FROM (
				SELECT
				MAX(sucl1.id) `id`
				FROM sys_user_cv_log sucl1
				GROUP BY sucl1.user_id
			) sucl
			LEFT JOIN sys_user_cv_log d ON d.id = sucl.`id`
		) l on l.user_id = u.user_id
		<where>
			u.`del_flag` = 0 and u.is_user = 0
			and (
				(u.`positive_status` != 2)
				or
				(l.positive_status = 2 and date_add(l.take_effect_time,INTERVAL 1 MONTH) &gt; NOW())
			)
			<if test="userName != null and userName != ''">
				AND (
				u.nick_name like concat('%', #{userName}, '%')
				or
				u.user_code like concat('%', #{userName}, '%')
				or
				AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') like concat('%', #{userName}, '%')
				)
			</if>
			<if test="deptId != null" >and (find_in_set(#{deptId}, s.ancestors) or s.dept_id = #{deptId}) </if>
			<if test="deptIds != null and deptIds.size() > 0">
				and (
				u.dept_id in
				<foreach collection="deptIds" item="deptId" separator="," open="(" close=")" >
					#{deptId}
				</foreach>
				or
				<foreach collection="deptIds" item="deptId" separator="or" >
					(u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
				</foreach>
				)
			</if>
			<if test="roleKeys != null and roleKeys.size() > 0">
				and u.user_id in (
					SELECT ur.user_id
					FROM sys_user_role ur
					LEFT JOIN sys_role r ON r.role_id = ur.role_id
					WHERE r.role_key IN
					<foreach collection="roleKeys" item="roleKey" separator="," open="(" close=")" >
						#{roleKey}
					</foreach>
				)
			</if>
			<if test="companyCodes != null and companyCodes.size() > 0">
				and u.company_code in
				<foreach collection="companyCodes" item="companyCode" separator="," open="(" close=")" >
					#{companyCode}
				</foreach>
			</if>
		</where>
	</select>

	<select id="selectUserDeptIds" parameterType="SysUser" resultType="Long" >
		SELECT
		distinct u.dept_id
		FROM
		sys_user u
		inner join sys_dept s on s.dept_id = u.dept_id
		<where>
			<if test="deptId != null" >and find_in_set(#{deptId}, s.ancestors) or s.dept_id = #{deptId} </if>
		</where>
	</select>

	<select id="selectAllocatedList" parameterType="SysUser" resultMap="SysUserResultOld">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') `phonenumber`, u.status, u.create_time
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and u.is_user = 0 and r.role_id = #{roleId}
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') `phonenumber`, like concat('%', #{phonenumber}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>

	<select id="selectUnallocatedList" parameterType="SysUser" resultMap="SysUserResultOld">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, AES_DECRYPT(FROM_BASE64(phonenumber),'ym3JB46gfD') phonenumber, u.status, u.create_time
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and u.is_user = 0 and (r.role_id != #{roleId} or r.role_id IS NULL)
	    and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and ur.role_id = #{roleId})
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND AES_DECRYPT(FROM_BASE64(phonenumber),'ym3JB46gfD') like concat('%', #{phonenumber}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>

	<select id="selectUserByUserName" parameterType="String" resultMap="SysUserResultOld">
	    <include refid="selectUserVo"/>
		where u.user_name = #{userName}
	</select>

	<select id="selectUserByUserCode" resultType="SysUser">
		<include refid="selectUserVo"/>
		where u.user_code = #{userCode}
	</select>

	<select id="selectUserByMobileNo" resultType="SysUser" >
		<include refid="selectUserVo"/>
		where (u.user_name = #{mobileNo} or AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') = #{mobileNo})
		and u.positive_status != '2'
		ORDER BY USER_ID DESC LIMIT 1
	</select>

	<select id="selectUserByPhoneNumber" resultType="SysUser" >
		<include refid="selectUserVo"/>
		where AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') = #{phoneNumber}
	</select>

	<select id="selectUserByNickName" parameterType="String" resultType="SysUser">
		<include refid="selectUserVo"/>
		where u.nick_name = #{nickName}
	</select>

	<select id="selectUserIdByNikeName" resultType="Long" parameterType="String">
		select user_id from sys_user where nick_name = #{nickName} limit 1
	</select>

	<select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId}
	</select>

	<select id="selectUserLiteById" parameterType="Long" resultType="SysUser">
		SELECT
			u.user_id,
			u.work_user_id,
			u.nick_name,
			AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') `phonenumber`,
			(select tryout_time from sys_user_contract uc where uc.user_id = u.user_id order by  uc.id desc limit 1) tryoutTime
		FROM sys_user u
		where u.user_id = #{userId}
	</select>

	<select id="checkUserCodeUnique" parameterType="String" resultType="int">
		select count(1) from sys_user where user_code = #{userCode} limit 1
	</select>

	<select id="checkUserNameUnique" parameterType="String" resultType="int">
		select count(1) from sys_user where user_name = #{userName} limit 1
	</select>

	<select id="checkPhoneUnique" parameterType="String" resultType="SysUser">
		select user_id, AES_DECRYPT(FROM_BASE64(phonenumber),'ym3JB46gfD') `phonenumber` from sys_user where AES_DECRYPT(FROM_BASE64(phonenumber),'ym3JB46gfD') = #{phonenumber} limit 1
	</select>

	<select id="checkEmailUnique" parameterType="String" resultMap="SysUserResultOld">
		select user_id, email from sys_user where email = #{email} limit 1
	</select>


	<insert id="insertUser" parameterType="SysUser" useGeneratedKeys="true" keyProperty="userId">
		insert into sys_user
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="deptId != null">dept_id,</if>
			<if test="userCode != null">user_code,</if>
			<if test="userName != null and userName != ''">user_name,</if>
			<if test="nickName != null and nickName != ''">nick_name,</if>
			<if test="userType != null">user_type,</if>
			<if test="email != null">email,</if>
			<if test="phonenumber != null">phonenumber,</if>
			<if test="sex != null">sex,</if>
			<if test="avatar != null">avatar,</if>
			<if test="password != null">password,</if>
			<if test="status != null">status,</if>
			<if test="directSuperior != null">direct_superior,</if>
			<if test="companyCode != null">company_code,</if>
			<if test="delFlag != null">del_flag,</if>
			<if test="loginIp != null">login_ip,</if>
			<if test="loginDate != null">login_date,</if>
			<if test="createBy != null">create_by,</if>
			<if test="createTime != null">create_time,</if>
			<if test="updateBy != null">update_by,</if>
			<if test="updateTime != null">update_time,</if>
			<if test="remark != null">remark,</if>
			<if test="userStatus != null">user_status,</if>
			<if test="bgdd != null">bgdd,</if>
			<if test="bgcc != null">bgcc,</if>
			<if test="officePhone != null">office_phone,</if>
			<if test="seniority != null">seniority,</if>
			<if test="birthday != null">birthday,</if>
			<if test="positiveStatus != null">positive_status,</if>
			<if test="rank != null">rank,</if>
			<if test="hukouXz != null">hukou_xz,</if>
			<if test="degree != null">degree,</if>
			<if test="hukouJg != null">hukou_jg,</if>
			<if test="hukouHjdz != null">hukou_hjdz,</if>
			<if test="hukouXjjdz != null">hukou_xjjdz,</if>
			<if test="cardType != null">card_type,</if>
			<if test="cardNo != null">card_no,</if>
			<if test="hyzk != null">hyzk,</if>
			<if test="bankName != null">bank_name,</if>
			<if test="modifiedPwdTime != null">modified_pwd_time,</if>
			<if test="bankNo != null">bank_no,</if>
			<if test="ethnic != null">ethnic,</if>
			<if test="bankAccount != null">bank_account,</if>
			<if test="politicalFace != null">political_face,</if>
			<if test="socialSecurityType != null">social_security_type,</if>
			<if test="joinTime != null">join_time,</if>
			<if test="socialSecurityNo != null">social_security_no,</if>
			<if test="socialSecurityAddress != null">social_security_address,</if>
			<if test="cpfNo != null">cpf_no,</if>
			<if test="cardBeforeImg != null">card_before_img,</if>
			<if test="cardAfterImg != null">card_after_img,</if>
			<if test="attendanceId != null">attendance_id,</if>
			<if test="schedulingId != null">scheduling_id,</if>
			<if test="clockId != null">clock_id,</if>
			<if test="isClock != null">is_clock,</if>
			<if test="isOvertime != null">is_overtime,</if>
			<if test="isPost != null">is_post,</if>
			<if test="qcCode != null">qc_code,</if>
			<if test="workUserId != null">work_user_id,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="deptId != null">#{deptId},</if>
			<if test="userCode != null">#{userCode},</if>
			<if test="userName != null and userName != ''">#{userName},</if>
			<if test="nickName != null and nickName != ''">#{nickName},</if>
			<if test="userType != null">#{userType},</if>
			<if test="email != null">#{email},</if>
			<if test="phonenumber != null">TO_BASE64(AES_ENCRYPT(#{phonenumber},'ym3JB46gfD')),</if>
			<if test="sex != null">#{sex},</if>
			<if test="avatar != null">#{avatar},</if>
			<if test="password != null">#{password},</if>
			<if test="status != null">#{status},</if>
			<if test="directSuperior != null">#{directSuperior},</if>
			<if test="companyCode != null">#{companyCode},</if>
			<if test="delFlag != null">#{delFlag},</if>
			<if test="loginIp != null">#{loginIp},</if>
			<if test="loginDate != null">#{loginDate},</if>
			<if test="createBy != null">#{createBy},</if>
			<if test="createTime != null">#{createTime},</if>
			<if test="updateBy != null">#{updateBy},</if>
			<if test="updateTime != null">#{updateTime},</if>
			<if test="remark != null">#{remark},</if>
			<if test="userStatus != null">#{userStatus},</if>
			<if test="bgdd != null">#{bgdd},</if>
			<if test="bgcc != null">#{bgcc},</if>
			<if test="officePhone != null">#{officePhone},</if>
			<if test="seniority != null">#{seniority},</if>
			<if test="birthday != null">#{birthday},</if>
			<if test="positiveStatus != null">#{positiveStatus},</if>
			<if test="rank != null">#{rank},</if>
			<if test="hukouXz != null">#{hukouXz},</if>
			<if test="degree != null">#{degree},</if>
			<if test="hukouJg != null">#{hukouJg},</if>
			<if test="hukouHjdz != null">#{hukouHjdz},</if>
			<if test="hukouXjjdz != null">#{hukouXjjdz},</if>
			<if test="cardType != null">#{cardType},</if>
			<if test="cardNo != null">#{cardNo},</if>
			<if test="hyzk != null">#{hyzk},</if>
			<if test="bankName != null">#{bankName},</if>
			<if test="modifiedPwdTime != null">#{modifiedPwdTime},</if>
			<if test="bankNo != null">#{bankNo},</if>
			<if test="ethnic != null">#{ethnic},</if>
			<if test="bankAccount != null">#{bankAccount},</if>
			<if test="politicalFace != null">#{politicalFace},</if>
			<if test="socialSecurityType != null">#{socialSecurityType},</if>
			<if test="joinTime != null">#{joinTime},</if>
			<if test="socialSecurityNo != null">#{socialSecurityNo},</if>
			<if test="socialSecurityAddress != null">#{socialSecurityAddress},</if>
			<if test="cpfNo != null">#{cpfNo},</if>
			<if test="cardBeforeImg != null">#{cardBeforeImg},</if>
			<if test="cardAfterImg != null">#{cardAfterImg},</if>
			<if test="attendanceId != null">#{attendanceId},</if>
			<if test="schedulingId != null">#{schedulingId},</if>
			<if test="clockId != null">#{clockId},</if>
			<if test="isClock != null">#{isClock},</if>
			<if test="isOvertime != null">#{isOvertime},</if>
			<if test="isPost != null">#{isPost},</if>
			<if test="qcCode != null">#{qcCode},</if>
			<if test="workUserId != null">#{workUserId},</if>
		</trim>
	</insert>

	<update id="updateUserWorkUserInfo" parameterType="SysUser">
		update sys_user set work_user_id = #{workUserId},user_name = AES_DECRYPT(FROM_BASE64(phonenumber),'ym3JB46gfD'),status = 0 where user_id = #{userId}
	</update>

	<update id="updateUser" parameterType="SysUser">
		update sys_user
		<trim prefix="SET" suffixOverrides=",">
			<if test="deptId != null">dept_id = #{deptId},</if>
			<if test="userCode != null">user_code = #{userCode},</if>
			<if test="userName != null and userName != ''">user_name = #{userName},</if>
			<if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
			<if test="userType != null">user_type = #{userType},</if>
			<if test="email != null">email = #{email},</if>
			<if test="phonenumber != null">phonenumber = TO_BASE64(AES_ENCRYPT(#{phonenumber},'ym3JB46gfD')),</if>
			<if test="sex != null">sex = #{sex},</if>
			<if test="avatar != null">avatar = #{avatar},</if>
			<if test="password != null">password = #{password},</if>
			<if test="status != null">status = #{status},</if>
			<if test="directSuperior != null">direct_superior = #{directSuperior},</if>
			<if test="companyCode != null">company_code = #{companyCode},</if>
			<if test="delFlag != null">del_flag = #{delFlag},</if>
			<if test="loginIp != null">login_ip = #{loginIp},</if>
			<if test="loginDate != null">login_date = #{loginDate},</if>
			<if test="createBy != null">create_by = #{createBy},</if>
			<if test="createTime != null">create_time = #{createTime},</if>
			<if test="updateBy != null">update_by = #{updateBy},</if>
			<if test="updateTime != null">update_time = #{updateTime},</if>
			<if test="remark != null">remark = #{remark},</if>
			<if test="userStatus != null">user_status = #{userStatus},</if>
			<if test="bgdd != null">bgdd = #{bgdd},</if>
			<if test="bgcc != null">bgcc = #{bgcc},</if>
			<if test="officePhone != null">office_phone = #{officePhone},</if>
			<if test="seniority != null">seniority = #{seniority},</if>
			<if test="birthday != null">birthday = #{birthday},</if>
			<if test="positiveStatus != null">positive_status = #{positiveStatus},</if>
			<if test="rank != null">rank = #{rank},</if>
			<if test="hukouXz != null">hukou_xz = #{hukouXz},</if>
			<if test="degree != null">degree = #{degree},</if>
			<if test="hukouJg != null">hukou_jg = #{hukouJg},</if>
			<if test="hukouHjdz != null">hukou_hjdz = #{hukouHjdz},</if>
			<if test="hukouXjjdz != null">hukou_xjjdz = #{hukouXjjdz},</if>
			<if test="cardType != null">card_type = #{cardType},</if>
			<if test="hyzk != null">hyzk = #{hyzk},</if>
			<if test="bankName != null">bank_name = #{bankName},</if>
			<if test="modifiedPwdTime != null">modified_pwd_time = #{modifiedPwdTime},</if>
			<if test="bankNo != null">bank_no = #{bankNo},</if>
			<if test="ethnic != null">ethnic = #{ethnic},</if>
			<if test="bankAccount != null">bank_account = #{bankAccount},</if>
			<if test="politicalFace != null">political_face = #{politicalFace},</if>
			<if test="socialSecurityType != null">social_security_type = #{socialSecurityType},</if>
			<if test="joinTime != null">join_time = #{joinTime},</if>
			<if test="socialSecurityNo != null">social_security_no = #{socialSecurityNo},</if>
			<if test="socialSecurityAddress != null">social_security_address = #{socialSecurityAddress},</if>
			<if test="cpfNo != null">cpf_no = #{cpfNo},</if>
			<if test="cardBeforeImg != null">card_before_img = #{cardBeforeImg},</if>
			<if test="cardAfterImg != null">card_after_img = #{cardAfterImg},</if>
			<if test="attendanceId != null">attendance_id = #{attendanceId},</if>
			<if test="schedulingId != null">scheduling_id = #{schedulingId},</if>
			<if test="clockId != null">clock_id = #{clockId},</if>
			<if test="isClock != null">is_clock = #{isClock},</if>
			<if test="isOvertime != null">is_overtime = #{isOvertime},</if>
			<if test="isPost != null">is_post = #{isPost},</if>
			<if test="qcCode != null">qc_code = #{qcCode},</if>
			<if test="workUserId != null">work_user_id = #{workUserId},</if>
		</trim>
		where user_id = #{userId}
	</update>

	<select id="selectUserNamesByUserIds" resultType="String" >
		SELECT
		GROUP_CONCAT(DISTINCT u.user_name)
		FROM
		sys_user u
		where u.user_name is not null
		and u.status = 0
		and u.user_id in
		<foreach collection="userIds" item="userId" separator="," open="(" close=")" >
			#{userId}
		</foreach>
	</select>

	<select id="selectNickNamesByUserIds" resultType="String" >
		SELECT
		GROUP_CONCAT(DISTINCT u.nick_name)
		FROM
		sys_user u
		where u.user_id in
		<foreach collection="userIds" item="userId" separator="," open="(" close=")" >
			#{userId}
		</foreach>
	</select>

	<select id="selectUserNamesByRoleIds" resultType="String" >
		SELECT
		GROUP_CONCAT(DISTINCT u.user_name)
		FROM
		sys_user u
		INNER JOIN sys_user_role ur ON ur.user_id = u.user_id
		INNER JOIN sys_role r on r.role_id = ur.role_id
		where u.user_name is not null and r.role_id in
		<foreach collection="roleIds" item="roleId" separator="," open="(" close=")" >
			#{roleId}
		</foreach>
	</select>

	<select id="selectUserNamesByDeptId" resultType="String" >
		SELECT
			GROUP_CONCAT(DISTINCT u.user_name)
		FROM
			sys_user u
		where u.user_name is not null
		  and (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
	</select>

	<select id="selectUserNamesByPostIds" resultType="String" >
		SELECT
		GROUP_CONCAT(DISTINCT u.user_name)
		FROM
		sys_user u
		inner join sys_user_post up on up.user_id = u.user_id
		inner join sys_post p on p.post_id = up.post_id
		WHERE
		u.user_name IS NOT NULL and p.post_id in
		<foreach collection="postIds" item="postId" separator="," open="(" close=")" >
			#{postId}
		</foreach>
	</select>

	<select id="selectAssigneeByUserIds" resultType="SysActTaskNodeAssignee" >
		SELECT
		u.user_name `assigneeId`,
		u.nick_name `assigneeName`
		FROM
		sys_user u
		where u.user_name is not null
		and u.status = 0
		and u.user_id in
		<foreach collection="userIds" item="userId" separator="," open="(" close=")" >
			#{userId}
		</foreach>
		GROUP BY u.user_id
	</select>

	<select id="selectAssigneeByRoleIds" resultType="SysActTaskNodeAssignee" >
		SELECT
		u.user_name `assigneeId`,
		u.nick_name `assigneeName`
		FROM
		sys_user u
		INNER JOIN sys_user_role ur ON ur.user_id = u.user_id
		INNER JOIN sys_role r on r.role_id = ur.role_id
		where u.user_name is not null
		and ifnull(r.role_code,'') != ''
		and r.role_code in
		<foreach collection="roleIds" item="roleId" separator="," open="(" close=")" >
			#{roleId}
		</foreach>
		GROUP BY u.user_id
	</select>
	<select id="selectAssigneeByDeptId" resultType="SysActTaskNodeAssignee" >
		SELECT
			u.user_name `assigneeId`,
			u.nick_name `assigneeName`
		FROM
			sys_user u
		where u.user_name is not null
		  and (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
		GROUP BY u.user_id
	</select>
	<select id="selectAssigneeByPostIds" resultType="SysActTaskNodeAssignee" >
		SELECT
		u.user_name `assigneeId`,
		u.nick_name `assigneeName`
		FROM
		sys_user u
		inner join sys_user_post up on up.user_id = u.user_id
		inner join sys_post p on p.post_id = up.post_id
		WHERE
		u.user_name IS NOT NULL and p.post_id in
		<foreach collection="postIds" item="postId" separator="," open="(" close=")" >
			#{postId}
		</foreach>
		GROUP BY u.user_id
	</select>

	<select id="selectUserRoleByRoleCodes" resultType="String" >
		SELECT
			r.role_code
		FROM
			sys_user_role ur
				INNER JOIN sys_role r on r.role_id = ur.role_id
		where ur.user_id = #{userId} and r.role_code is not null
		order by r.`role_id` asc
	</select>
	<select id="selectUserRoleByRoleCodesByType" resultType="String" >
		SELECT
			r.role_code
		FROM
			sys_user_role ur
				INNER JOIN sys_role r on r.role_id = ur.role_id
		where ur.user_id = #{userId}
		and r.role_type_id IN
		<foreach item="typeId" collection="typeIds" open="(" separator="," close=")">
			#{typeId}
		</foreach>

		  and r.role_code is not null
		order by r.`role_id` asc
	</select>

	<update id="updateUserStatus" parameterType="SysUser">
 		update sys_user set status = #{status} where user_id = #{userId}
	</update>

	<update id="updateUserAvatar" parameterType="SysUser">
 		update sys_user set avatar = #{avatar} where user_name = #{userName}
	</update>

	<update id="resetUserPwd" parameterType="SysUser">
 		update sys_user set password = #{password},modified_pwd_time = date_add(now(),interval 2 month) where user_name = #{userName}
	</update>

 	<delete id="deleteUserByIds" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach>
 	</delete>

	<!-- 查询直接上级 -->
	<select id="queryDirectSuperiorInfo" parameterType="java.lang.Long" resultType="java.lang.Integer">
		select u1.direct_superior from sys_user u1  where u1.user_id = #{userId}
	</select>

	<!--查询部门总监 -->
	<select id="queryDepartmentDirectorInfo" parameterType="java.util.Map" resultType="java.lang.Long">
		select u.user_id from sys_user u
		<if test="processUserPost!=null and processUserPost!=''">
		where  u.user_id in (
				select user_id from sys_user_post sup
										left join sys_post p on sup.post_id = p.post_id
				where p.POST_code = #{processUserPost}
			)
			<if test="parentId!=null">and u.dept_id = #{parentId}</if>
		</if>
		<if test="rolesList!=null and rolesList.size()>0">
			where  u.user_id in (
				select user_id from sys_user_role  sur
				left JOIN sys_role sr on sur.role_id = sr.role_id
				where sr.role_key in
				<foreach collection="rolesList" close=")" open="(" separator="," item="roleKey">
					#{roleKey}
				</foreach>
			)
		</if>
	</select>

	<!--查询部门总监 -->
	<select id="queryDepartmentDirectorInfoByAncestors" parameterType="java.util.Map" resultType="java.lang.Long">
		select u.user_id from sys_user u
		<if test="processUserPost!=null and processUserPost!=''">
			where  u.user_id in (
				select user_id from sys_user_post sup
				left join sys_post p on sup.post_id = p.post_id
				where p.POST_code = #{processUserPost}
			)  and u.is_user = 0
			<if test="deptIds!=null">
			    and u.dept_id in
                <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
					#{deptId}
				</foreach>
			</if>
		</if>
        limit 1
	</select>

	<!--查询人事总监 -->
	<select id="queryHRDirectorInfo" parameterType="java.util.Map" resultType="java.lang.Integer">
		select u.user_id from  sys_user u
		where u.user_id in  (
			select user_id from sys_user_post sup
			 left join sys_post p on sup.post_id = p.post_id
			where p.POST_code = #{processUserPost}
		) and u.is_user = 0 limit 1
	</select>

	<!--查询上级部门-->
	<select id="queryUserParentDeptInfo" parameterType="java.util.Map" resultType="java.util.Map">
		select d.parent_id parentId,d.parent_first_id parentFirstId,parent_dept_id parentDeptId,is_dept isDept,d.ancestors,u.dept_id userDeptId,d.dept_name deptName from sys_user u
	    LEFT JOIN sys_dept d on u.dept_id = d.dept_id
		where u.user_id = #{userId}
	</select>

	<select id="selectPasswordByUserName" resultType="String" >
		select password from sys_user where user_name = #{userName}
	</select>

	<select id="selectAllUserList" parameterType="SysUser" resultMap="SysUserResultOld">
		select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email,AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') phonenumber,
		       u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time,
		       u.remark, d.dept_name, d.leader
		from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		where u.del_flag = '0'
		and u.status = 0 and u.is_user = 0
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="nickName != null and nickName != ''">
			AND u.nick_name like concat('%', #{nickName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') like concat('%', #{phonenumber}, '%')
		</if>
		<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
		</if>
		<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
		</if>
		<if test="deptId != null and deptId != 0">
			AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
		</if>
        order by u.user_id
	</select>

	<select id="queryCustomerUserList" parameterType="Customer" resultMap="SysUserResultOld">
		SELECT
			u.user_id,
			u.dept_id,
			u.user_name,
			u.nick_name,
			u.email,
			u.avatar,
			AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') phonenumber,
			u.PASSWORD,
			u.sex,
			u.STATUS,
			u.del_flag,
			u.login_ip,
			u.login_date,
			u.create_by,
			u.create_time,
			u.remark,
			d.dept_id,
			d.parent_id,
			d.dept_name,
			d.order_num,
			d.leader,
			d.STATUS AS dept_status,
			u.join_time,
			u.direct_superior,
			u.company_code,
		    ca.is_main,
		    ca.is_project_main,
		    ca.type,
		    ca.status assist_status,
			ca.id assist_id
		FROM
			sys_user u
	    left join sys_dept d on u.dept_id = d.dept_id
		inner join t_customer_assist ca on (ca.user_id = u.user_id and ca.status = 0)
        where u.status = 0 and u.del_flag = 0 and u.is_user = 0
		<if test="userName != null and userName != ''">
			AND (u.user_name like concat('%', #{userName}, '%') or u.nick_name like   concat('%', #{userName}, '%'))
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') like concat('%', #{phonenumber}, '%')
		</if>
		<if test="deptName != null and deptName != ''">
			AND d.dept_name like concat('%', #{deptName}, '%')
		</if>
        and ca.customer_id = #{id}
        and ca.user_id not in (315,3305)
		and ca.is_del = 0
	</select>


	<select id="queryCustomerAllOrderUser" parameterType="Customer" resultMap="SysUserResultOld">
		SELECT
			u.user_id,
			u.dept_id,
			u.user_name,
			u.nick_name,
			u.email,
			u.avatar,
			AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') phonenumber,
			u.PASSWORD,
			u.sex,
			u.STATUS,
			u.del_flag,
			u.login_ip,
			u.login_date,
			u.create_by,
			u.create_time,
			u.remark,
			d.dept_id,
			d.parent_id,
			d.dept_name,
			d.order_num,
			d.leader,
			d.STATUS AS dept_status,
			u.join_time,
			u.direct_superior,
			u.company_code,
		    ca.type,
		    ca.status assist_status,
			ca.id assist_id
		FROM
			sys_user u
	    left join sys_dept d on u.dept_id = d.dept_id
		inner join t_order_customer_follow_order ca on ca.user_id = u.user_id
        where u.status = 0 and u.del_flag = 0 and u.is_user = 0
		<if test="userName != null and userName != ''">
			AND (u.user_name like concat('%', #{userName}, '%') or u.nick_name like   concat('%', #{userName}, '%'))
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') like concat('%', #{phonenumber}, '%')
		</if>
		<if test="deptName != null and deptName != ''">
			AND d.dept_name like concat('%', #{deptName}, '%')
		</if>
        and ca.customer_id = #{id} and ca.type = 0
	</select>

	<select id="queryCustomerAssistUserList" parameterType="java.lang.Long" resultType="java.lang.Long">
		SELECT
		 ca.user_id
		FROM
		t_customer_assist  ca
		where
	    ca.customer_id = #{id}
		and ca.status = 0
		and ca.user_id not in (1126,315,3305,260)
		and ca.is_del = 0
	</select>

	<select id="selectLowerList" resultType="SysUser" >
		select
		    u.user_id,
		    u.nick_name,
		    u.user_code,
		    u.user_name,
			AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') `phonenumber`
		from
		     sys_user u
		where
			u.direct_superior = #{userId}
		   and u.status = 0 and u.is_user = 0
		   and u.`positive_status` != 2
		order by u.user_id desc
	</select>

	<!-- 获取用户信息 -->
	<select id="queryUserIdList" parameterType="java.util.List" resultType="java.lang.Long">
		select user_id from sys_user  where user_name in
		<foreach collection="list" item="mobile" open="(" separator="," close=")">
			#{mobile}
		</foreach>
	</select>

	<select id="queryProjectExecutionDataList" parameterType="java.util.List" resultType="TProjectExecutionUser">
		select id,execution_id,process_user_id,is_audit from t_project_execution_user
		where process_user_id in
		<foreach collection="list" item="userId" open="(" separator="," close=")">
			#{userId}
		</foreach>
	</select>

	<select id="queryMyExecutionDataList" parameterType="java.lang.Long" resultType="java.lang.Long">
		select execution_id from t_project_execution_user
		where process_user_id  = #{userId}
	</select>

	<insert id="batchExecutionUserDataList" parameterType="java.util.List">
		insert into t_project_execution_user
		             (execution_id, process_user_id, is_audit, create_by,create_time,remark)
		             values
		           <foreach collection ="list" item="reddemCode" index= "index" separator =",">
		                 (
		                 #{reddemCode.executionId}, #{reddemCode.processUserId},
		                  #{reddemCode.isAudit},
		                 #{reddemCode.createBy},
	                      NOW(), #{reddemCode.remark}
		                )
		             </foreach >
	</insert>

	<select id="selectAllProcessUserList" parameterType="java.util.List" resultType="SysUser">
		select   u.user_id, u.dept_id,d.dept_name, u.user_name, u.nick_name, u.email, AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') `phonenumber`, u.status, u.create_time
		from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
        where u.user_id in
        <foreach collection="allUserId" open="(" close=")" separator="," item="id">
			#{id}
		</foreach>
	</select>

	<!--查询用户角色-->
	<select id="selectAllProcessUserRoleList" parameterType="java.util.List" resultType="SysUser">
		select   u.user_id, u.dept_id,d.dept_name, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time,
				 (SELECT GROUP_CONCAT(r.role_name) from sys_role r
															INNER JOIN sys_user_role ur on r.role_id = ur.role_id
															INNER JOIN sys_user u1 on u1.user_id = ur.USER_ID
				  where u1.user_id = u.user_id and r.remark = '项目执行') remark
		from sys_user u
				 left join sys_dept d on u.dept_id = d.dept_id
		where u.status = 0 and u.user_id in (
			select user_id from sys_user_role where role_id = 105
		)
	</select>

	<select id="selectUserApplyListNew" resultType="com.ruoyi.system.domain.excel.UserApplyExcel">
		select u.nick_name,AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD'),d.dept_name from sys_user u
	   left join sys_dept d on u.dept_id = d.dept_id
		where u.user_id in (
			select user_id from sys_user_role where role_id = 104
		) and u.status = 0
	</select>

	<select id="selectIncludeGroupNameList" parameterType="SysUser" resultMap="SysUserResultOld" >
		SELECT
		u.user_id,
		u.dept_id,
		u.nick_name,
		u.user_name,
		u.email,
		u.avatar,
		AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') `phonenumber`,
		u. PASSWORD,
		u.sex,
		u. STATUS,
		u.del_flag,
		u.login_ip,
		u.login_date,
		u.create_by,
		u.create_time,
		u.remark,
		d.dept_name,
		d.leader,
		(
		SELECT
		GROUP_CONCAT(`name`)
		FROM
		sys_group
		WHERE
		id IN (
		SELECT
		group_id
		FROM
		sys_user_group
		WHERE
		user_id = u.user_id
		)
		) groupName
		FROM
		sys_user u
		LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
		WHERE
		u.del_flag = '0' and u.is_user = 0
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="nickName != null and nickName != ''">
			AND u.nick_name like concat('%', #{nickName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') like concat('%', #{phonenumber}, '%')
		</if>
		<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
		</if>
		<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
		</if>
		<if test="deptId != null and deptId != 0">
			AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
		</if>
		order by u.user_id desc
	</select>

	<select id="selectUserByAssist" parameterType="TCustomerAssist" resultType="SysUser" >
		SELECT
		u.user_id,
		u.dept_id,
		u.nick_name,
		u.user_name,
		u.email,
		u.avatar,
		AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD'),
		u.sex,
		u. STATUS,
		u.del_flag,
		u.login_ip,
		u.login_date,
		u.create_by,
		u.create_time,
		u.remark
		FROM
		sys_user u
		where u.user_id = (
			select user_id from t_customer_assist where status = 0 and  customer_id = #{customerId} and is_main = 1 limit 1
		)
	</select>

	<!--获取人员岗位内容-->
	<select id="queryUserPostDataList" parameterType="java.lang.Long" resultType="java.lang.String">
		select p.post_code from sys_user u
		inner join sys_user_post up on u.user_id = up.user_id
		inner join sys_post p on up.post_id = p.post_id
		where u.user_id = #{userId}
	</select>

	<select id="selectUserIdByRoleNames" resultType="Long" >
		SELECT DISTINCT
			u.user_id
		FROM
			sys_user u
			INNER JOIN sys_user_role ur ON ur.user_id = u.user_id
			INNER JOIN sys_role r on r.role_id = ur.role_id
		where role_name in
		<foreach collection="roleNames" item="roleName" separator="," open="(" close=")" >
			#{roleName}
		</foreach>
	</select>

	<resultMap type="SysUser" id="SysUserResultOld">
		<id     property="userId"       column="user_id"      />
		<result property="deptId"       column="dept_id"      />
		<result property="userName"     column="user_name"    />
		<result property="directSuperior"     column="direct_superior"    />
		<result property="joinTime"     column="join_time"    />
		<result property="companyCode"     column="company_code"    />
		<result property="nickName"     column="nick_name"    />
		<result property="email"        column="email"        />
		<result property="phonenumber"  column="phonenumber"  />
		<result property="sex"          column="sex"          />
		<result property="avatar"       column="avatar"       />
		<result property="password"     column="password"     />
		<result property="status"       column="status"       />
		<result property="type"       column="type"       />
		<result property="userCode"     column="user_code"       />
		<result property="delFlag"      column="del_flag"     />
		<result property="loginIp"      column="login_ip"     />
		<result property="loginDate"    column="login_date"   />
		<result property="createBy" column="create_by"/>
		<result property="createTime" column="create_time"/>
		<result property="updateBy" column="update_by"/>
		<result property="updateTime" column="update_time"/>
		<result property="remark" column="remark"/>
		<result property="isMain" column="is_main"/>
        <result property="groupName" column="groupName"/>
        <result property="openUserName" column="open_user_name"/>
        <result property="assistStatus" column="assist_status"/>
        <result property="assistId" column="assist_id"/>
        <result property="isProjectMain" column="is_project_main"/>
        <result property="ancestors" column="ancestors"/>
        <result property="modifiedPwdTime" column="modified_pwd_time"/>
        <association property="dept" column="dept_id" javaType="SysDept" resultMap="deptResult"/>
        <collection property="roles" javaType="java.util.List" resultMap="RoleResult"/>
    </resultMap>

    <select id="selectUserBaseList" parameterType="SysUser" resultType="SysUser">
		SELECT
			u.user_id,
			u.nick_name,
			u.user_name,
			u.user_code,
			AES_DECRYPT( FROM_BASE64 ( u.phonenumber ), 'ym3JB46gfD' ) phonenumber,
			u.del_flag,
			u.dept_id,
			u.user_type,
			d.ancestors,
			d.dept_name
		FROM
			sys_user u
			LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
		where
		u.del_flag = 0 and u.is_user = 0
		<if test="deptId != null" >and (find_in_set(#{deptId}, d.ancestors) or d.dept_id = #{deptId}) </if>
		order by u.user_id desc
    </select>

	<select id="selectWorkUserList" parameterType="SysUser" resultType="SysUser">
		SELECT
		u.user_id,
		u.nick_name,
		u.user_name,
		u.user_code,
		u.work_user_id,
		AES_DECRYPT( FROM_BASE64 ( u.phonenumber ), 'ym3JB46gfD' ) phonenumber,
		u.dept_id,
		u.user_type,
		d.ancestors,
		d.dept_name
		FROM
		sys_user u
		LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
		where
		u.del_flag = 0
		and u.work_user_id is not null
		<if test="deptId != null" >and (find_in_set(#{deptId}, d.ancestors) or d.dept_id = #{deptId}) </if>
		order by u.user_id desc
	</select>

	<select id="selectWorkUserByUserId" parameterType="SysUser" resultType="SysUser">
		SELECT
		u.user_id,
		u.nick_name,
		u.user_name,
		u.user_code,
		u.work_user_id,
		AES_DECRYPT( FROM_BASE64 ( u.phonenumber ), 'ym3JB46gfD' ) phonenumber,
		u.dept_id,
		u.user_type,
		d.ancestors,
		d.dept_name
		FROM
		sys_user u
		LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
		where u.user_id = #{userId}
	</select>

	<select id="selectUserBaseListNew" parameterType="SysUser" resultType="SysUser">
		SELECT
			u.user_id,
			u.nick_name,
			u.user_name,
			u.user_code,
			AES_DECRYPT( FROM_BASE64 ( u.phonenumber ), 'ym3JB46gfD' ) phonenumber,
			u.del_flag,
			u.dept_id,
			u.user_type,
			d.ancestors,
			d.dept_name
		FROM
			sys_user u
			LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
		where
		u.del_flag = 0 	and u.`positive_status` != 2 and u.is_user = 0
		<if test="deptId != null" >and (find_in_set(#{deptId}, d.ancestors) or d.dept_id = #{deptId}) </if>
		order by u.user_id desc
    </select>

	<select id="selectUserIdsByDeptId" resultType="Long">
		SELECT
			u.user_id
		FROM
			sys_user u
			inner join sys_dept s on s.dept_id = u.dept_id
		where
		    u.del_flag = 0
		  	and u.`positive_status` != 2
		<if test="deptId != null" >and (find_in_set(#{deptId}, s.ancestors) or s.dept_id = #{deptId}) </if>
		order by u.user_id desc
	</select>

	<select id="selectWorkUserIdsByDeptId" resultType="string">
		SELECT
		u.work_user_id
		FROM
		sys_user u
		inner join sys_dept s on s.dept_id = u.dept_id
		where
		u.del_flag = 0
		and u.`positive_status` != 2
		and u.work_user_id is not null and u.work_user_id != ''
		<if test="deptId != null" >and (find_in_set(#{deptId}, s.ancestors) or s.dept_id = #{deptId}) </if>
		order by u.user_id desc
	</select>

	<update id="updateUserPayslipPwd" parameterType="SysHrUser">
		update sys_user set payslip_pwd = #{payslipPwd} where user_id = #{userId}
	</update>

	<update id="updateAttendanceDeptInfo">
		update t_attendance a
			INNER JOIN sys_user u on a.user_id = u.user_id
			set a.dept_id = u.dept_id
		where a.dept_id is null
	</update>

	<update id="updateUserDDuserIdInfo">
		update sys_user u
			left join (
			SELECT
			user_id,
			MAX(id) `id`
			FROM sys_user_cv_log WHERE positive_status = 2
			GROUP BY user_id
			) sucl1 on  sucl1.user_id = u.user_id
			left join sys_user_cv_log l on l.id = sucl1.id
			set u.dd_user_id  = null
		WHERE u.del_flag = 0
		  AND u.is_user = 0
		  AND u.positive_status = 2 and l.take_effect_time >= now()
	</update>

	<select id="queryUserRoleCount" resultType="java.lang.Integer" parameterType="java.lang.Long">
		SELECT count(*) from  sys_user_role ur
		INNER JOIN sys_role r on ur.role_id = r.role_id
		where r.statistics  = 1 and ur.user_id = #{userId}
	</select>

	<!-- 查询今日离职用户 -->
	<select id="selectLeaveUserDay" resultType="SysUser" >
		SELECT
			u.user_id,
			d.ancestors
		FROM
			sys_user u
			inner join sys_dept d on d.dept_id = u.dept_id
			LEFT JOIN (
				SELECT MAX( sucl_.id ) `id`, sucl_.user_id
				FROM sys_user_cv_log sucl_
				WHERE sucl_.positive_status = 2
				GROUP BY sucl_.user_id
			) sucl2 ON sucl2.user_id = u.user_id
			LEFT JOIN sys_user_cv_log l2 ON l2.id = sucl2.id
		WHERE DATE_FORMAT(l2.take_effect_time,'%Y-%m-%d') = DATE_FORMAT(NOW(),'%Y-%m-%d')
	</select>

    <!--获取一级部门信息 -->
	<select id="queryUserParentDeptIdInfo" parameterType="java.lang.Long" resultType="java.lang.Long">
		SELECT  if(d1.parent_id = 0,d.dept_id,substring_index(substring_index(d.ancestors,',',3),',',-1)) `deptId`
		from sys_user u
		LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
		LEFT JOIN sys_dept d1 ON d1.dept_id = d.parent_id
		where u.USER_ID = #{userId}
    </select>

    <!--获取一级部门信息 -->
	<select id="queryUserParentDeptIdInfoNew" parameterType="java.lang.Long" resultType="com.alibaba.fastjson.JSONObject">
		SELECT  if(d1.parent_id = 0,d.dept_id,substring_index(substring_index(d.ancestors,',',3),',',-1)) `deptId`, u.dept_id  userDeptId
		from sys_user u
		LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
		LEFT JOIN sys_dept d1 ON d1.dept_id = d.parent_id
		where u.USER_ID = #{userId}
    </select>

	<select id="selectAsyncWorkUserList" resultType="UserParams">
		SELECT
			u.work_user_id,
			u.nick_name,
			u.email,
			u.dept_id,
			AES_DECRYPT(FROM_BASE64(u.phonenumber),'ym3JB46gfD') `phonenumber`,
			u.sex,
			d.work_dept_id,
			s.work_user_id superiorWorkUserId
		FROM
			sys_user u
			left join sys_user s on s.user_id = u.direct_superior
			INNER JOIN sys_dept d on d.dept_id = u.dept_id
		WHERE
			u.del_flag = 0
		  	AND u.positive_status != '2'
		AND u.user_type != '2'
		AND u.is_user = 0
	</select>

	<select id="queryResignataionsDataList" resultType="com.alibaba.fastjson.JSONObject">
		SELECT u.user_id userId,u.work_user_id workUserId,u.dd_user_id ddUserId from sys_user_cv_log cl
		INNER JOIN SYS_USER U on cl.user_id = u.user_id
		where  u.positive_status = '2'  and cl.positive_status = '2' and u.`status` = '0'
		and DATE(cl.take_effect_time) &lt;= CURDATE()
		order by u.USER_ID desc
	</select>

	<update id="updateUserResignataionsDataInfo" parameterType="java.lang.Long">
		update sys_user set status = '1',dd_user_id = null,work_user_id=null,agreement=null,early_conversion =null,delay_conversion=null  where user_id = #{userId}
	</update>

	<select id="selectUserIdByNickNames" resultType="String" >
		 select user_name from sys_user where nick_name in
		 <foreach collection="nickNames" item="nickName" separator="," open="(" close=")" >
			 #{nickName}
		 </foreach>
	</select>

	<select id="queryExpenseDataList" resultType="com.alibaba.fastjson.JSONObject">
		SELECT id,instance_id instanceId from t_expense_user where audit_status = 0 and instance_id is not NULL
	</select>

	<select id="queryAttendanceExceptionCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		select count(*) from t_attendance a where a.user_id = #{userId} and a.user_status != 2 and  a.date like concat(#{date},'%') and a.`result` = 1 and  a.date &lt; #{dayDate}
	</select>

	<select id="queryFirstDeptUserInfo" resultType="java.lang.Long" parameterType="java.lang.Long">
		SELECT user_id FROM sys_user u where (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
												   and positive_status != 2 and IFNULL(work_user_id,'') != ''
	</select>

	<select id="selectUserByEntryUserId" resultType="SysUser" >
		<include refid="selectUserVo"/>
		where u.entry_user_id = #{entryUserId}
	</select>

	<select id="selectPermsByUserId" resultType="String" >
		SELECT
			perms
		FROM
			sys_menu m
			inner join sys_role_menu rm on rm.menu_id = m.menu_id
			inner join sys_user_role ur on ur.role_id = rm.role_id
			inner join sys_user u on u.user_id = ur.user_id
		where u.user_id = #{userId}
			and perms is not null
			and perms != ''
	</select>

	<select id="getWorkUserIdByRoleKey" resultType="string" parameterType="string">
		SELECT work_user_id FROM sys_user
		WHERE
		user_id IN ( SELECT user_id FROM sys_user_role WHERE role_id = ( SELECT role_id FROM sys_role WHERE role_key = #{roleKey} LIMIT 1 ) )
		AND work_user_id IS NOT NULL AND work_user_id !=''
	</select>

	<select id="selectUserByUserCodes" resultType="SysUser" >
		select
			user_id,
		    user_code,
		    user_type
		from sys_user
		where user_code in
		<foreach collection="userCodes" item="userCode" separator="," open="(" close=")" >
			#{userCode}
		</foreach>
	</select>

	<select id="selectEngineersByCategoryId" resultType="SysUser" >
		SELECT su.user_id, su.rank, su.nick_name
		FROM sys_user su
			 LEFT JOIN t_category tc ON tc.dept_id = su.dept_id
		<if test="projectOrderId != null and projectOrderId!= ''" >
			LEFT JOIN t_project_item_order pio ON pio.id = #{projectOrderId}
			LEFT JOIN sys_dept sd ON sd.dept_id = su.dept_id
		</if>
		WHERE
			su.del_flag = 0 AND tc.del_flag = 0
		  and su.status = 0
		  AND su.positive_status &lt;&gt; 2
		  and tc.category_id = #{categoryId}
		<if test="projectOrderId != null and projectOrderId!= ''" >
		  -- 新增：部门判断逻辑
		  AND (
			-- 条件一：如果 lab_no 是 0(宜侬)，则匹配部门 464(宜侬) 及其子部门
			(pio.lab_no = 0 AND (su.dept_id = 464 OR FIND_IN_SET(464, sd.ancestors)))
			OR
			-- 条件二：如果 lab_no 是 1(瀛彩)，则匹配部门 465(瀛彩) 及其子部门
			(pio.lab_no = 1 AND (su.dept_id = 465 OR FIND_IN_SET(465, sd.ancestors)))
		   )
		</if>
	</select>

	<select id="selectUsersByDeptId" resultType="SysUser" >
		SELECT u.user_id, u.nick_name, u.user_code, u.company_code, p.post_name
		FROM
			sys_user u
				INNER JOIN sys_dept d ON d.dept_id = u.dept_id
				INNER JOIN sys_user_post up ON up.user_id = u.USER_ID
				inner join sys_post p on p.post_id = up.post_id
		WHERE
			(u.dept_id = #{DeptId}
		   OR FIND_IN_SET( #{DeptId}, d.ancestors ))
			AND u.del_flag = 0
			AND u.is_user = 0
			AND u.positive_status != 2
	</select>

</mapper>
