<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.resource.mapper.ResourceFinishedGoodsMapper">

    <sql id="selectResourceFinishedGoodsVo">
        SELECT
            fg.id,
            fg.category_finished_product_id,
            fg.category_finished_product_ids,
            fg.product_name,
            fg.CODE,
            fg.img,
            fg.swiper_imgs,
            fg.description,
            fg.del_flag,
            fg.create_by,
            fg.create_time,
            fg.update_by,
            fg.remark,
            fg.brand,
            fg.market,
            fg.update_time,
            fg.sh_type,
            fg.publish_date,
            fg.bar_code,
            fg.efficacy,
            fg.other_special_claims,
            fg.flavor,
            fg.texture,
            fg.ingredients,
            fg.num,
            fg.spec,
            fg.unit,
            fg.ba_customer_id,
            fg.customer_id,
            fg.project_id,
            fg.bom_id,
            fg.gf_id,
            fg.bom_json,
            fg.gf_json,
            fg.recorder,
            fg.icpn,
            fg.factory,
            fg.enow_erp_code,
            fg.yc_erp_code,
            fg.product_code,
            fg.lab_code,
            fg.nrw_json,
            fg.bcp_json,
            fg.lp_json,
            fg.cp_json,
            fg.lh_json,
            fg.bc_json,
            fg.resource_types,
            fg.draft_flag,
            fg.user_id,
            fg.cpsj,
            fg.sjxs,
            fg.syrq,
            fg.syff,
            fg.files,
            fg.videos,
            fg.icp_fields,
            fg.legal_icp_ids,
            fg.legal_box_icp_ids,
            fg.project_archive_id,
            fg.STATUS,
            fg.icp_type,
            fg.product_type,
            fg.new_material,
            c.`name` customerName,
            c.customer_level customerLevel,
            p.project_no,
            fg.other_product_code,
            fg.erp_first_ck_time,
            fg.documentary_ck_time
        FROM
            t_resource_finished_goods fg
            INNER JOIN t_customer c ON c.id = fg.customer_id
            LEFT JOIN t_project p on p.id = fg.project_id
    </sql>

    <sql id="selectResourceFinishedGoodsListVo">
        SELECT
            fg.id,
            fg.category_finished_product_id,
            fg.category_finished_product_ids,
            fg.product_name,
            fg.CODE,
            fg.img,
            fg.description,
            fg.del_flag,
            fg.create_by,
            fg.create_time,
            fg.update_by,
            fg.remark,
            fg.brand,
            fg.market,
            fg.update_time,
            fg.sh_type,
            fg.publish_date,
            fg.bar_code,
            fg.efficacy,
            fg.other_special_claims,
            fg.flavor,
            fg.texture,
            fg.ingredients,
            fg.num,
            fg.spec,
            fg.unit,
            fg.customer_id,
            fg.ba_customer_id,
            fg.project_id,
            fg.bom_id,
            fg.gf_id,
            fg.recorder,
            fg.icpn,
            fg.factory,
            fg.enow_erp_code,
            fg.yc_erp_code,
            fg.product_code,
            fg.lab_code,
            fg.resource_types,
            fg.draft_flag,
            fg.user_id,
            fg.cpsj,
            fg.sjxs,
            fg.syrq,
            fg.syff,
            fg.legal_icp_ids,
            fg.legal_box_icp_ids,
            fg.project_archive_id,
            fg.STATUS,
            fg.icp_type,
            fg.product_type,
            fg.nrw_json,
            fg.bcp_json,
            fg.lp_json,
            fg.cp_json,
            fg.lh_json,
            fg.bc_json,
            fg.new_material,
            fg.other_product_code,
            c.`name` customerName,
            c.customer_level customerLevel,
            p.project_no,
            fg.documentary_ck_time,
            s.hour_costs
        FROM
            t_resource_finished_goods fg
            INNER JOIN t_customer c ON c.id = fg.customer_id
            LEFT JOIN t_project p ON p.id = fg.project_id
            LEFT JOIN t_order_schedule_standard_selected ss ON  ss.`code` = fg.product_code
            LEFT JOIN t_order_schedule_standard s ON  ss.schedule_standard_id = s.id
    </sql>

    <select id="selectResourceFinishedGoodsList" parameterType="ResourceFinishedGoods" resultType="ResourceFinishedGoods">
        <include refid="selectResourceFinishedGoodsListVo"/>
        <where>
            fg.del_flag = 0
            <if test="productName != null  and productName != ''"> and fg.product_name like concat('%', #{productName}, '%')</if>
            <if test="productCode != null  and productCode != ''">
               and
                (fg.product_code like concat('%', #{productCode}, '%')
                 or
                fg.enow_erp_code like concat('%', #{productCode}, '%')
                or
                fg.yc_erp_code like concat('%', #{productCode}, '%'))
            </if>
            <if test="status != null  and status != ''"> and fg.status = #{status} </if>
            <if test="productType != null  and productType != ''"> and fg.product_type = #{productType} </if>
            <if test="code != null  and code != ''"> and fg.code like concat('%', #{code}, '%')</if>
            <if test="labCode != null  and labCode != ''"> and fg.lab_code like concat('%', #{labCode}, '%')</if>
            <if test="customerId != null"> and fg.customer_id = #{customerId}</if>
            <if test="baCustomerId != null"> and fg.ba_customer_id = #{baCustomerId}</if>
            <if test="icpType != null"> and fg.icp_type = #{icpType}</if>
            <if test="projectArchiveId != null"> and fg.project_archive_id = #{projectArchiveId}</if>
            <if test="factory != null"> and fg.factory = #{factory}</if>
            <if test="brand != null  and brand != ''"> and fg.brand like concat('%', #{brand}, '%')</if>
            <if test="market != null  and market != ''"> and fg.market like concat('%', #{market}, '%')</if>
            <if test="ingredients != null  and ingredients != ''"> and fg.ingredients like concat('%', #{ingredients}, '%')</if>
            <if test="shType != null  and shType != ''"> and fg.sh_type = #{shType}</if>
            <if test="projectId != null "> and fg.project_id = #{projectId}</if>
            <if test="bomId != null "> and fg.bom_id = #{bomId}</if>
            <if test="gfId != null "> and fg.gf_id = #{gfId}</if>
            <if test="draftFlag != null "> and fg.draft_flag = #{draftFlag}</if>
            <if test="userId != null "> and fg.user_id = #{userId}</if>
            <if test="categoryFinishedProductId != null "> and fg.category_finished_product_id = #{categoryFinishedProductId}</if>
            <if test="startDate != null and startDate != ''">
                and fg.publish_date &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != '' ">
                and fg.publish_date &lt;= #{endDate}
            </if>
            <if test="sopStatus != null">
                <choose>
                    <when test="sopStatus == 0">and s.hour_costs is not null</when>
                    <when test="sopStatus == 1">and s.hour_costs is null</when>
                </choose>
            </if>
        </where>
        order by fg.id desc
    </select>

    <select id="selectResourceFinishedGoodsById" parameterType="Long" resultType="ResourceFinishedGoods">
        <include refid="selectResourceFinishedGoodsVo"/>
        where fg.id = #{id}
    </select>

    <select id="selectOtherIcpnByProductCode" resultType="String">
        SELECT
            icpn
        FROM t_resource_finished_goods
        where other_product_code like concat('%',#{productCode},'%')
        and del_flag = 0
        order by id desc limit 1
    </select>

    <insert id="insertResourceFinishedGoods" parameterType="ResourceFinishedGoods" useGeneratedKeys="true" keyProperty="id">
        insert into t_resource_finished_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryFinishedProductId != null">category_finished_product_id,</if>
            <if test="categoryFinishedProductIds != null">category_finished_product_ids,</if>
            <if test="productName != null">product_name,</if>
            <if test="img != null">img,</if>
            <if test="files != null">FILES,</if>
            <if test="videos != null">VIDEOS,</if>
            <if test="swiperImgs != null">swiper_imgs,</if>
            <if test="description != null">description,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="brand != null">brand,</if>
            <if test="market != null">market,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="shType != null">sh_type,</if>
            <if test="publishDate != null">publish_date,</if>
            <if test="barCode != null">bar_code,</if>
            <if test="efficacy != null">efficacy,</if>
            <if test="flavor != null">flavor,</if>
            <if test="texture != null">texture,</if>
            <if test="ingredients != null">ingredients,</if>
            <if test="num != null">num,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="projectId != null">project_id,</if>
            <if test="bomId != null">bom_id,</if>
            <if test="gfId != null">gf_id,</if>
            <if test="bomJson != null">bom_json,</if>
            <if test="gfJson != null">gf_json,</if>
            <if test="spec != null">spec,</if>
            <if test="unit != null">unit,</if>
            <if test="recorder != null">recorder,</if>
            <if test="icpn != null">icpn,</if>
            <if test="enowErpCode != null">enow_erp_code,</if>
            <if test="ycErpCode != null">yc_erp_code,</if>
            <if test="productCode != null">product_code,</if>
            <if test="labCode != null">lab_code,</if>
            <if test="factory != null">factory,</if>
            <if test="nrwJson != null">nrw_json,</if>
            <if test="bcpJson != null">bcp_json,</if>
            <if test="lpJson != null">lp_json,</if>
            <if test="cpJson != null">cp_json,</if>
            <if test="lhJson != null">lh_json,</if>
            <if test="bcJson != null">bc_json,</if>
            <if test="resourceTypes != null">resource_types,</if>
            <if test="draftFlag != null">draft_flag,</if>
            <if test="userId != null">user_id,</if>
            <if test="cpsj != null">cpsj,</if>
            <if test="sjxs != null">sjxs,</if>
            <if test="syrq != null">syrq,</if>
            <if test="syff != null">syff,</if>
            <if test="icpFields != null">icp_fields,</if>
            <if test="code != null">code,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="projectArchiveId != null">project_archive_id,</if>
            <if test="legalIcpIds != null">legal_icp_ids,</if>
            <if test="legalBoxIcpIds != null">legal_box_icp_ids,</if>
            <if test="icpType != null">icp_type,</if>
            <if test="otherSpecialClaims != null">other_special_claims,</if>
            <if test="productType != null">product_type,</if>
            <if test="baCustomerId != null">ba_customer_id,</if>
            <if test="newMaterial != null">new_material,</if>
            <if test="documentaryCkTime != null">documentary_ck_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryFinishedProductId != null">#{categoryFinishedProductId},</if>
            <if test="categoryFinishedProductIds != null">#{categoryFinishedProductIds},</if>
            <if test="productName != null">#{productName},</if>
            <if test="img != null">#{img},</if>
            <if test="files != null">#{files},</if>
            <if test="videos != null">#{videos},</if>
            <if test="swiperImgs != null">#{swiperImgs},</if>
            <if test="description != null">#{description},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="brand != null">#{brand},</if>
            <if test="market != null">#{market},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="shType != null">#{shType},</if>
            <if test="publishDate != null">#{publishDate},</if>
            <if test="barCode != null">#{barCode},</if>
            <if test="efficacy != null">#{efficacy},</if>
            <if test="flavor != null">#{flavor},</if>
            <if test="texture != null">#{texture},</if>
            <if test="ingredients != null">#{ingredients},</if>
            <if test="num != null">#{num},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="bomId != null">#{bomId},</if>
            <if test="gfId != null">#{gfId},</if>
            <if test="bomJson != null">#{bomJson},</if>
            <if test="gfJson != null">#{gfJson},</if>
            <if test="spec != null">#{spec},</if>
            <if test="unit != null">#{unit},</if>
            <if test="recorder != null">#{recorder},</if>
            <if test="icpn != null">#{icpn},</if>
            <if test="enowErpCode != null">#{enowErpCode},</if>
            <if test="ycErpCode != null">#{ycErpCode},</if>
            <if test="productCode != null">#{productCode},</if>
            <if test="labCode != null">#{labCode},</if>
            <if test="factory != null">#{factory},</if>
            <if test="nrwJson != null">#{nrwJson},</if>
            <if test="bcpJson != null">#{bcpJson},</if>
            <if test="lpJson != null">#{lpJson},</if>
            <if test="cpJson != null">#{cpJson},</if>
            <if test="lhJson != null">#{lhJson},</if>
            <if test="bcJson != null">#{bcJson},</if>
            <if test="resourceTypes != null">#{resourceTypes},</if>
            <if test="draftFlag != null">#{draftFlag},</if>
            <if test="userId != null">#{userId},</if>
            <if test="cpsj != null">#{cpsj},</if>
            <if test="sjxs != null">#{sjxs},</if>
            <if test="syrq != null">#{syrq},</if>
            <if test="syff != null">#{syff},</if>
            <if test="icpFields != null">#{icpFields},</if>
            <if test="code != null">#{code},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="projectArchiveId != null">#{projectArchiveId},</if>
            <if test="legalIcpIds != null">#{legalIcpIds},</if>
            <if test="legalBoxIcpIds != null">#{legalBoxIcpIds},</if>
            <if test="icpType != null">#{icpType},</if>
            <if test="otherSpecialClaims != null">#{otherSpecialClaims},</if>
            <if test="productType != null">#{productType},</if>
            <if test="baCustomerId != null">#{baCustomerId},</if>
            <if test="newMaterial != null">#{newMaterial},</if>
            <if test="documentaryCkTime != null">#{documentaryCkTime},</if>

        </trim>
    </insert>

    <update id="updateResourceFinishedGoods" parameterType="ResourceFinishedGoods">
        update t_resource_finished_goods
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryFinishedProductId != null">category_finished_product_id = #{categoryFinishedProductId},</if>
            <if test="categoryFinishedProductIds != null">category_finished_product_ids = #{categoryFinishedProductIds},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="img != null">img = #{img},</if>
            <if test="files != null">FILES = #{files},</if>
            <if test="videos != null">VIDEOS = #{videos},</if>
            <if test="swiperImgs != null">swiper_imgs = #{swiperImgs},</if>
            <if test="description != null">description = #{description},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="brand != null">brand = #{brand},</if>
            <if test="market != null">market = #{market},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="shType != null">sh_type = #{shType},</if>
            <if test="publishDate != null">publish_date = #{publishDate},</if>
            <if test="barCode != null">bar_code = #{barCode},</if>
            <if test="efficacy != null">efficacy = #{efficacy},</if>
            <if test="flavor != null">flavor = #{flavor},</if>
            <if test="texture != null">texture = #{texture},</if>
            <if test="ingredients != null">ingredients = #{ingredients},</if>
            <if test="num != null">num = #{num},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="bomId != null">bom_id = #{bomId},</if>
            <if test="gfId != null">gf_id = #{gfId},</if>
            <if test="bomJson != null">bom_json = #{bomJson},</if>
            <if test="gfJson != null">gf_json = #{gfJson},</if>
            <if test="spec != null">spec = #{spec},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="recorder != null">recorder = #{recorder},</if>
            <if test="icpn != null">icpn = #{icpn},</if>
            <if test="enowErpCode != null">enow_erp_code = #{enowErpCode},</if>
            <if test="ycErpCode != null">yc_erp_code = #{ycErpCode},</if>
            <if test="productCode != null">product_code = #{productCode},</if>
            <if test="labCode != null">lab_code = #{labCode},</if>
            <if test="factory != null">factory = #{factory},</if>
            <if test="nrwJson != null">nrw_json = #{nrwJson},</if>
            <if test="bcpJson != null">bcp_json = #{bcpJson},</if>
            <if test="lpJson != null">lp_json = #{lpJson},</if>
            <if test="cpJson != null">cp_json = #{cpJson},</if>
            <if test="lhJson != null">lh_json = #{lhJson},</if>
            <if test="bcJson != null">bc_json = #{bcJson},</if>
            <if test="resourceTypes != null">resource_types = #{resourceTypes},</if>
            <if test="draftFlag != null">draft_flag = #{draftFlag},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="cpsj != null">cpsj = #{cpsj},</if>
            <if test="sjxs != null">sjxs = #{sjxs},</if>
            <if test="syrq != null">syrq = #{syrq},</if>
            <if test="syff != null">syff = #{syff},</if>
            <if test="icpFields != null">icp_fields = #{icpFields},</if>
            <if test="code != null">code = #{code},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="projectArchiveId != null">project_archive_id = #{projectArchiveId},</if>
            <if test="legalIcpIds != null">legal_icp_ids = #{legalIcpIds},</if>
            <if test="legalBoxIcpIds != null">legal_box_icp_ids = #{legalBoxIcpIds},</if>
            <if test="icpType != null">icp_type = #{icpType},</if>
            <if test="otherSpecialClaims != null">other_special_claims = #{otherSpecialClaims},</if>
            <if test="productType != null">product_type = #{productType},</if>
            <if test="baCustomerId != null">ba_customer_id = #{baCustomerId},</if>
            <if test="newMaterial != null">new_material = #{newMaterial},</if>
            <if test="documentaryCkTime != null">documentary_ck_time = #{documentaryCkTime},</if>
            <if test="otherProductCode != null and otherProductCode!=''">other_product_code = #{otherProductCode},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteResourceFinishedGoodsByIds" parameterType="String">
        update t_resource_finished_goods set del_flag = 2,update_time = now() where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="addNum" parameterType="ResourceFinishedGoods">
        update t_resource_finished_goods set num = num + #{num} where id = #{id}
    </update>

    <update id="delNum" parameterType="ResourceFinishedGoods">
        update t_resource_finished_goods set num = num - #{num} where id = #{id}
    </update>

    <update id="clearDraft" >
        update t_resource_finished_goods set draft_flag = 0 where user_id = #{userId}
    </update>

    <select id="selectResourceFinishedGoodsDraft" parameterType="ResourceFinishedGoods" resultType="ResourceFinishedGoods" >
        <include refid="selectResourceFinishedGoodsVo"/>
        where draft_flag = 1 and user_id = #{userId}
    </select>

    <update id="updateBatchPriceJson" >
        <foreach collection="itemList" item="item" separator=";">
            update t_resource_finished_goods
            <set>
                <if test="item.nrwJson!= null">nrw_json = #{item.nrwJson},</if>
                <if test="item.bcpJson!= null">bcp_json = #{item.bcpJson},</if>
                <if test="item.lpJson!= null">lp_json = #{item.lpJson},</if>
                <if test="item.cpJson!= null">cp_json = #{item.cpJson},</if>
                <if test="item.lhJson!= null">lh_json = #{item.lhJson},</if>
                <if test="item.bcJson!= null">bc_json = #{item.bcJson},</if>
                update_time = current_timestamp
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <select id="selectSerialNumber" resultType="String" >
        SELECT
            CONCAT('FG',lpad(count(1)+1,7,0)) code
        FROM
            t_resource_finished_goods
    </select>

    <select id="selectResourceFinishedGoodsBaseList" parameterType="ResourceFinishedGoods" resultType="ResourceFinishedGoods">
        SELECT
            fg.id,
            fg.product_name,
            fg.customer_id,
            fg.product_code
        FROM
            t_resource_finished_goods fg
        <where>
            fg.del_flag = 0
            <if test="productName != null and productName != ''"> and fg.product_name like concat('%', #{productName}, '%')</if>
            <if test="productCode != null and productCode != ''">
                and
                (fg.product_code like concat('%', #{productCode}, '%')
                or
                fg.enow_erp_code like concat('%', #{productCode}, '%')
                or
                fg.yc_erp_code like concat('%', #{productCode}, '%'))
            </if>
            <if test="code != null  and code != ''"> and fg.code like concat('%', #{code}, '%')</if>
            <if test="labCode != null  and labCode != ''"> and fg.lab_code like concat('%', #{labCode}, '%')</if>
            <if test="customerId != null"> and fg.customer_id = #{customerId}</if>
            <if test="factory != null"> and fg.factory = #{factory}</if>
            <if test="brand != null  and brand != ''"> and fg.brand like concat('%', #{brand}, '%')</if>
            <if test="market != null  and market != ''"> and fg.market like concat('%', #{market}, '%')</if>
            <if test="ingredients != null  and ingredients != ''"> and fg.ingredients like concat('%', #{ingredients}, '%')</if>
            <if test="shType != null  and shType != ''"> and fg.sh_type = #{shType}</if>
            <if test="projectId != null "> and fg.project_id = #{projectId}</if>
            <if test="bomId != null "> and fg.bom_id = #{bomId}</if>
            <if test="gfId != null "> and fg.gf_id = #{gfId}</if>
            <if test="draftFlag != null "> and fg.draft_flag = #{draftFlag}</if>
            <if test="userId != null "> and fg.user_id = #{userId}</if>
            <if test="categoryFinishedProductId != null "> and fg.category_finished_product_id = #{categoryFinishedProductId}</if>
            <if test="startDate != null and startDate != ''">
                and fg.publish_date &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != '' ">
                and fg.publish_date &lt;= #{endDate}
            </if>
        </where>
        order by fg.id desc
    </select>

    <select id="selectSimilarListByProductName" resultType="ResourceFinishedGoods" parameterType="ResourceFinishedGoods" >
        SELECT
            fg.id,
            fg.product_name,
            fg.customer_id,
            fg.product_code
        FROM
            t_resource_finished_goods fg
        where
            MATCH (product_name) AGAINST (#{productName}) > 0
            and fg.product_code is not null
    </select>

    <update id="updateBomEnByProductCode" >
        update t_resource_finished_goods set is_bom_en = '0' where product_code = #{productCode}
    </update>

    <select id="queryResourceFinishedCount" parameterType="ResourceFinishedGoods" resultType="java.lang.Integer">
        select count(*) from t_resource_finished_goods where product_code = #{productCode} and status not in ('3','4','5') and del_flag = 0
        <if test="id!=null">
            and id != #{id}
        </if>
    </select>

    <select id="selectResourceFinishedGoodsByProductCode" resultType="ResourceFinishedGoods">
        SELECT
            fg.product_name, -- 产品名称
            fg.lab_code, -- 实验室编号
            p.project_no, -- 项目号
            fg.spec -- 规格
        FROM
            t_resource_finished_goods fg
            LEFT JOIN t_project p on p.id = fg.project_id
        WHERE fg.product_code = #{productCode}
    </select>

</mapper>
