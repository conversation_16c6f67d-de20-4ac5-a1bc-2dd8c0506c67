package com.ruoyi.mes.domain;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 临时用来去重的类
 */
public class MesLotDuplicate {

    private String lotNo;
    private String opNo;
    private String areaNo;
    private String equipmentNo;
    private String workDate;
    private String sailings;
    private String opType;
    private BigDecimal nums;

    public String getOpType() {
        return opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }

    public BigDecimal getNums() {
        return nums;
    }

    public void setNums(BigDecimal nums) {
        this.nums = nums;
    }

    public String getLotNo() {
        return lotNo;
    }

    public void setLotNo(String lotNo) {
        this.lotNo = lotNo;
    }

    public String getOpNo() {
        return opNo;
    }

    public void setOpNo(String opNo) {
        this.opNo = opNo;
    }

    public String getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(String areaNo) {
        this.areaNo = areaNo;
    }

    public String getEquipmentNo() {
        return equipmentNo;
    }

    public void setEquipmentNo(String equipmentNo) {
        this.equipmentNo = equipmentNo;
    }

    public String getWorkDate() {
        return workDate;
    }

    public void setWorkDate(String workDate) {
        this.workDate = workDate;
    }

    public String getSailings() {
        return sailings;
    }

    public void setSailings(String sailings) {
        this.sailings = sailings;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        MesLotDuplicate that = (MesLotDuplicate) o;
        return Objects.equals(lotNo, that.lotNo) && Objects.equals(opNo, that.opNo) && Objects.equals(equipmentNo, that.equipmentNo) && Objects.equals(workDate, that.workDate) && Objects.equals(sailings, that.sailings);
    }

    @Override
    public int hashCode() {
        return Objects.hash(lotNo, opNo, equipmentNo, workDate, sailings);
    }

}
