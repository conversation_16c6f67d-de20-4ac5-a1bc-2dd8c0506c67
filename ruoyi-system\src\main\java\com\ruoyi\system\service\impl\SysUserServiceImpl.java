package com.ruoyi.system.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.constant.UserID;
import com.ruoyi.common.constant.WorkflowContants;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.service.ICodeInfoService;
import com.ruoyi.hr.domain.SysUserImport;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.*;
import com.ruoyi.customer.domain.Customer;
import com.ruoyi.hr.domain.*;
import com.ruoyi.hr.mapper.*;
import com.ruoyi.mes.domain.MesUser;
import com.ruoyi.mes.mapper.MesUserMapper;
import com.ruoyi.project.domain.TProjectExecutionUser;
import com.ruoyi.project.domain.TProjectWorkflowDefinition;
import com.ruoyi.project.mapper.TProjectWorkflowDefinitionMapper;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.domain.excel.SysUserInfo;
import com.ruoyi.system.domain.excel.UserApplyExcel;
import com.ruoyi.system.domain.excel.UserExcel;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl implements ISysUserService
{
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysPostMapper postMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysUserPostMapper userPostMapper;

    @Autowired
    private SysUserDegreeMapper userDegreeMapper;

    @Autowired
    private SysUserFamilyMapper userFamilyMapper;

    @Autowired
    private SysUserContactMapper userContactMapper;

    @Autowired
    private SysUserJobMapper userJobMapper;

    @Autowired
    private SysUserTrainingMapper userTrainingMapper;

    @Autowired
    private SysUserCertificateMapper userCertificateMapper;

    @Autowired
    private SysUserContractMapper userContractMapper;

    @Autowired
    private SysUserContractFileMapper userContractFileMapper;
    @Autowired
    private SysUserHealthMapper userHealthMapper;
    @Autowired
    private SysUserWagesMapper userWagesMapper;
    @Autowired
    private SysDeptMapper deptMapper;
    @Autowired
    private SysUserCvLogMapper userCvLogMapper;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private TProjectWorkflowDefinitionMapper projectWorkflowDefinitionMapper;
    @Autowired
    private ICodeInfoService codeInfoService;
    @Resource
    private MesUserMapper mesUserMapper;

    private void updateMesUser(SysUser user) {
        MesUser mesUser = new MesUser();
        mesUser.setUserno(user.getUserCode());
        mesUser.setUsername(user.getNickName());
        mesUser.setComeday(user.getJoinTime());
        mesUser.setMobileno(user.getPhonenumber());
        mesUser.setEmailaddress(user.getEmail());
        mesUser.setIccard(user.getUserCode());
        mesUser.setEditor(user.getUpdateBy());
        mesUser.setEditdate(user.getUpdateTime());
        if(user.getDeptId() != null) {
            mesUser.setDepartmentno(user.getDeptId().toString());
        }

        mesUserMapper.updateMesUser(mesUser);
    }

    private void addMesUser(SysUser user) {
        MesUser mesUser = new MesUser();
        mesUser.setUserno(user.getUserCode());
        mesUser.setUsername(user.getNickName());
        mesUser.setComeday(user.getJoinTime());
        mesUser.setMobileno(user.getPhonenumber());
        mesUser.setEmailaddress(user.getEmail());
        mesUser.setIccard(user.getUserCode());
        if(user.getDeptId() != null) {
            mesUser.setDepartmentno(user.getDeptId().toString());
        }

        mesUser.setUserlevel(BigDecimal.valueOf(2));//一般用户
        mesUser.setPassword("=MFW4NzYrhTQ");//默认密码 123456
        mesUser.setResetpassword(BigDecimal.ZERO);//不变更密码
        mesUser.setUsertype(BigDecimal.valueOf(0));//员工
        mesUser.setIssuestate(BigDecimal.valueOf(2));//已签核
        mesUser.setCreator(user.getCreateBy());
        mesUser.setCreatedate(user.getCreateTime());
        mesUser.setShiftno("BB01");
        mesUserMapper.insertMesUser(mesUser);
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUserList(SysUser user)
    {
        return userMapper.selectUserList(user);
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectAllocatedList(SysUser user)
    {
        return userMapper.selectAllocatedList(user);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUnallocatedList(SysUser user)
    {
        return userMapper.selectUnallocatedList(user);
    }

    @Override
    public SysUser selectUserByUserName(String userName)
    {
        return userMapper.selectUserByUserName(userName);
    }

    @Override
    public SysUser selectUserById(Long userId)
    {
        return userMapper.selectUserById(userId);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName)
    {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        StringBuffer idsStr = new StringBuffer();
        for (SysRole role : list)
        {
            idsStr.append(role.getRoleName()).append(",");
        }
        if (StringUtils.isNotEmpty(idsStr.toString()))
        {
            return idsStr.substring(0, idsStr.length() - 1);
        }
        return idsStr.toString();
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName)
    {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        StringBuffer idsStr = new StringBuffer();
        for (SysPost post : list)
        {
            idsStr.append(post.getPostName()).append(",");
        }
        if (StringUtils.isNotEmpty(idsStr.toString()))
        {
            return idsStr.substring(0, idsStr.length() - 1);
        }
        return idsStr.toString();
    }
    /**
     * 校验用户编码是否唯一
     *
     * @param userCode 用户编码
     * @return 结果
     */
    @Override
    public String checkUserCodeUnique(String userCode)
    {
        int count = userMapper.checkUserCodeUnique(userCode);
        if (count > 0)
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }
    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    @Override
    public String checkUserNameUnique(String userName)
    {
        int count = userMapper.checkUserNameUnique(userName);
        if (count > 0)
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkPhoneUnique(SysUser user)
    {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkPhoneUnique(user.getPhonenumber());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkEmailUnique(SysUser user)
    {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkEmailUnique(user.getEmail());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user)
    {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin())
        {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public void insertUser(SysUser user)
    {
        Date joinTime = user.getJoinTime() != null ? user.getJoinTime() : new Date();
        String password = configService.selectConfigByKey("sys.user.initPassword");
        user.setPassword(SecurityUtils.encryptPassword(password));
        user.setUserCode(codeInfoService.userCode(DateUtils.parseDateToStr(DateUtils.YY,joinTime)));
        // 新增用户信息
        int rows = userMapper.insertUser(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        // 新增用户学历信息
        insertUserDegree(user);
        // 新增用户家庭成员
        insertUserFamily(user);
        // 新增用户紧急联系人
        insertUserContact(user);
        // 新增用户工作经历
        insertUserJob(user);
        // 新增用户培训经历
        insertUserTraining(user);
        // 新增用户合同
        insertUserContract(user);
        // 新增用户证书
        inertUserCertificate(user);
        // 新增职业证书
        inertUserHealth(user);
        // 新增薪资福利
        insertUserWages(user);
        // 新增公司履历记录
        insertUserCvLog(new SysUser(),user);

        new Thread(() -> {
            addMesUser(user);
        }).start();

    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user)
    {
        return userMapper.insertUser(user) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public void updateUser(SysUser user)
    {
        SysUser oldUser = userMapper.selectUserById(user.getUserId());
        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId,null);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPostByUserId(userId);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        // 新增用户学历信息
        insertUserDegree(user);
        // 新增用户家庭成员
        insertUserFamily(user);
        // 新增用户紧急联系人
        insertUserContact(user);
        // 新增用户工作经历
        insertUserJob(user);
        // 新增用户培训经历
        insertUserTraining(user);
        // 新增用户合同
        insertUserContract(user);
        // 新增用户证书
        inertUserCertificate(user);
        // 新增职业证书
        inertUserHealth(user);
        // 新增薪资福利
        insertUserWages(user);
        // 新增公司履历记录
        insertUserCvLog(oldUser,user);
        user.setPassword(null);
        userMapper.updateUser(user);

        new Thread(() -> {
            MesUser mesUser = mesUserMapper.selectMesUserByUserno(user.getUserCode());
            if(mesUser != null) {
                updateMesUser(user);
            } else {
                addMesUser(user);
            }
        }).start();
    }

    /**
     * 用户授权角色
     *
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional
    public void insertUserAuth(Long userId, Long[] roleIds)
    {
        insertUserRole(userId, roleIds);
    }

    @Override
    public int deleteUserRoleByUserId(Long userId, Long[] roleIds) {
        return userRoleMapper.deleteUserRoleByUserId(userId,roleIds);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user)
    {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user)
    {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar)
    {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user)
    {
        return userMapper.updateUser(user);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password)
    {
        return userMapper.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user)
    {
        Long[] roles = user.getRoleIds();
        if (StringUtils.isNotNull(roles))
        {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (Long roleId : roles)
            {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(user.getUserId());
                ur.setRoleId(roleId);
                list.add(ur);
            }
            if (list.size() > 0)
            {
                userRoleMapper.batchUserRole(list);
            }
        }
    }

    public void insertUserDegree(SysUser user){
        String KEY = "xyjlDatas";
        if(!StringUtils.isEmpty(user.getFieldsJson())){
            userDegreeMapper.deleteUserDegree(user.getUserId());
            JSONObject obj = JSONObject.parseObject(user.getFieldsJson());
            if(obj.containsKey(KEY) && !StringUtils.isEmpty(obj.getString(KEY))){
                JSONArray array = obj.getJSONArray(KEY);
                if(array != null && array.size() > 0){
                    List<SysUserDegree> list = JSONUtils.parseList(array.toJSONString(),JSONUtils.getCollectionType(List.class, SysUserDegree.class));
                    if(list != null && list.size() > 0){
                        for(int i=0;i<list.size();i++){
                            list.get(i).setUserId(user.getUserId());
                        }
                        userDegreeMapper.batchUserDegree(list);
                    }
                }
            }
        }
    }

    public void insertUserFamily(SysUser user){
        String KEY = "jtcyDatas";
        if(!StringUtils.isEmpty(user.getFieldsJson())){
            userFamilyMapper.deleteUserFamily(user.getUserId());
            JSONObject obj = JSONObject.parseObject(user.getFieldsJson());
            if(obj.containsKey(KEY) && !StringUtils.isEmpty(obj.getString(KEY))){
                JSONArray array = obj.getJSONArray(KEY);
                if(array != null && array.size() > 0){
                    List<SysUserFamily> list = JSONUtils.parseList(array.toJSONString(),JSONUtils.getCollectionType(List.class, SysUserFamily.class));
                    if(list != null && list.size() > 0){
                        for(int i=0;i<list.size();i++){
                            list.get(i).setUserId(user.getUserId());
                        }
                        userFamilyMapper.batchUserFamily(list);
                    }
                }
            }
        }
    }

    public void insertUserContact(SysUser user){
        String KEY = "jjlxrDatas";
        if(!StringUtils.isEmpty(user.getFieldsJson())){
            userContactMapper.deleteUserContact(user.getUserId());
            JSONObject obj = JSONObject.parseObject(user.getFieldsJson());
            if(obj.containsKey(KEY) && !StringUtils.isEmpty(obj.getString(KEY))){
                JSONArray array = obj.getJSONArray(KEY);
                if(array != null && array.size() > 0){
                    List<SysUserContact> list = JSONUtils.parseList(array.toJSONString(),JSONUtils.getCollectionType(List.class, SysUserContact.class));
                    if(list != null && list.size() > 0){
                        for(int i=0;i<list.size();i++){
                            list.get(i).setUserId(user.getUserId());
                        }
                        userContactMapper.batchUserContact(list);
                    }
                }
            }
        }
    }

    public void insertUserJob(SysUser user){
        String KEY = "gzjlDatas";
        if(!StringUtils.isEmpty(user.getFieldsJson())){
            userJobMapper.deleteSysUserJob(user.getUserId());
            JSONObject obj = JSONObject.parseObject(user.getFieldsJson());
            if(obj.containsKey(KEY) && !StringUtils.isEmpty(obj.getString(KEY))){
                JSONArray array = obj.getJSONArray(KEY);
                if(array != null && array.size() > 0){
                    List<SysUserJob> list = JSONUtils.parseList(array.toJSONString(),JSONUtils.getCollectionType(List.class, SysUserJob.class));
                    if(list != null && list.size() > 0){
                        for(int i=0;i<list.size();i++){
                            list.get(i).setUserId(user.getUserId());
                        }
                        userJobMapper.insertSysUserJob(list);
                    }
                }
            }
        }
    }

    public void insertUserTraining(SysUser user){
        String KEY = "pxjlDatas";
        if(!StringUtils.isEmpty(user.getFieldsJson())){
            userTrainingMapper.deleteSysUserTraining(user.getUserId());
            JSONObject obj = JSONObject.parseObject(user.getFieldsJson());
            if(obj.containsKey(KEY) && !StringUtils.isEmpty(obj.getString(KEY))){
                JSONArray array = obj.getJSONArray(KEY);
                if(array != null && array.size() > 0){
                    List<SysUserTraining> list = JSONUtils.parseList(array.toJSONString(),JSONUtils.getCollectionType(List.class, SysUserTraining.class));
                    if(list != null && list.size() > 0){
                        for(int i=0;i<list.size();i++){
                            list.get(i).setUserId(user.getUserId());
                        }
                        userTrainingMapper.insertSysUserTraining(list);
                    }
                }
            }
        }
    }

    public void insertUserContract(SysUser user){
        String KEY = "htDatas";
        if(!StringUtils.isEmpty(user.getFieldsJson())){
            userContractFileMapper.deleteSysUserContractFile(user.getUserId());
            userContractMapper.deleteSysUserContract(user.getUserId());
            JSONObject obj = JSONObject.parseObject(user.getFieldsJson());
            if(obj.containsKey(KEY) && !StringUtils.isEmpty(obj.getString(KEY))){
                JSONArray array = obj.getJSONArray(KEY);
                if(array != null && array.size() > 0){
                    List<SysUserContract> list = JSONUtils.parseList(array.toJSONString(),JSONUtils.getCollectionType(List.class, SysUserContract.class));
                    if(list != null && list.size() > 0){
                        for(int i=0;i<list.size();i++){
                            SysUserContract contract = list.get(i);
                            contract.setUserId(user.getUserId());
                            userContractMapper.insertSysUserContract(contract);
                            if(contract.getFiles() != null && contract.getFiles().size() > 0){
                                for(int j=0;j<contract.getFiles().size();j++){
                                    contract.getFiles().get(j).setUserContractId(contract.getId());
                                }
                            }
                            userContractFileMapper.insertSysUserContractFile(contract.getFiles());
                        }

                    }
                }
            }
        }

    }

    public void inertUserCertificate(SysUser user){
        String KEY = "zsDatas";
        if(!StringUtils.isEmpty(user.getFieldsJson())){
            userCertificateMapper.deleteSysUserCertificate(user.getUserId());
            JSONObject obj = JSONObject.parseObject(user.getFieldsJson());
            if(obj.containsKey(KEY) && !StringUtils.isEmpty(obj.getString(KEY))){
                JSONArray array = obj.getJSONArray(KEY);
                if(array != null && array.size() > 0){
                    List<SysUserCertificate> list = JSONUtils.parseList(array.toJSONString(),JSONUtils.getCollectionType(List.class, SysUserCertificate.class));
                    if(list != null && list.size() > 0){
                        for(int i=0;i<list.size();i++){
                            list.get(i).setUserId(user.getUserId());
                        }
                        userCertificateMapper.insertSysUserCertificate(list);
                    }
                }
            }
        }

    }

    public void inertUserHealth(SysUser user){
        String KEY = "jkzDatas";
        if(!StringUtils.isEmpty(user.getFieldsJson())){
            userHealthMapper.deleteSysUserHealth(user.getUserId());
            JSONObject obj = JSONObject.parseObject(user.getFieldsJson());
            if(obj.containsKey(KEY) && !StringUtils.isEmpty(obj.getString(KEY))){
                JSONArray array = obj.getJSONArray(KEY);
                if(array != null && array.size() > 0){
                    List<SysUserHealth> list = JSONUtils.parseList(array.toJSONString(),JSONUtils.getCollectionType(List.class, SysUserHealth.class));
                    if(list != null && list.size() > 0){
                        for(int i=0;i<list.size();i++){
                            list.get(i).setUserId(user.getUserId());
                        }
                        userHealthMapper.insertSysUserHealth(list);
                    }
                }
            }
        }

    }

    public void insertUserWages(SysUser user){
        String KEY = "xzflDatas";
        if(!StringUtils.isEmpty(user.getFieldsJson())){
            userWagesMapper.deleteSysUserWages(user.getUserId());
            JSONObject obj = JSONObject.parseObject(user.getFieldsJson());
            if(obj.containsKey(KEY) && !StringUtils.isEmpty(obj.getString(KEY))){
                SysUserWages wages = JSONObject.parseObject(obj.getString(KEY),SysUserWages.class);
                if(wages != null){
                    wages.setUserId(user.getUserId());
                    userWagesMapper.insertSysUserWages(wages);
                }
            }
        }
    }

    public void insertUserPost(SysUser user)
    {
        Long[] posts = user.getPostIds();
        if (StringUtils.isNotNull(posts))
        {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<SysUserPost>();
            for (Long postId : posts)
            {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            if (list.size() > 0)
            {
                userPostMapper.batchUserPost(list);
            }
        }
    }

    public void insertUserRole(Long userId, Long[] roleIds)
    {
        if (StringUtils.isNotNull(roleIds))
        {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (Long roleId : roleIds)
            {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            if (list.size() > 0)
            {
                userRoleMapper.batchUserRole(list);
            }
        }
    }

    /**
     * 新增用户履历记录
     *
     * @param oldUser 修改前信息
     * @param newUser 修改后信息
     */
    public void insertUserCvLog(SysUser oldUser,SysUser newUser)
    {
        boolean isPost = true;
        List<Map<String,Object>> oldList = oldUser.getPosts();
        // 新增用户与岗位管理
        Long[] posts = newUser.getPostIds();
        if((oldList == null && posts != null) || (oldList != null && posts == null)){
            isPost = false;
        }else if(oldList != null && posts != null){
            if(oldList.size() == posts.length){
                for(Map<String,Object> o : oldList){
                    boolean isOldNew = false;
                    long oldPostId = (long) o.get("postId");
                    for(long newPostId : posts){
                        if(oldPostId == newPostId){
                            isOldNew = true;
                        }
                    }
                    if(!isOldNew){
                        isPost = false;
                    }
                }
            }else{
                isPost = false;
            }
        }
        oldUser.setRank(oldUser.getRank() != null?oldUser.getRank():"");
        newUser.setRank(newUser.getRank() != null?newUser.getRank():"");
        if(oldUser == null
         || oldUser.getPositiveStatus() == null
         || !oldUser.getPositiveStatus().equals(newUser.getPositiveStatus())
         || oldUser.getDeptId() == null
         || oldUser.getDeptId().longValue() != newUser.getDeptId().longValue()
         || oldUser.getRank() == null
         || !oldUser.getRank().equals(newUser.getRank())
         || !isPost){
            SysUserCvLog userCvLog = new SysUserCvLog();
            userCvLog.setUserId(newUser.getUserId());
            userCvLog.setDeptId(newUser.getDeptId());
            userCvLog.setPositiveStatus(newUser.getPositiveStatus());
            userCvLog.setRank(newUser.getRank());
            userCvLog.setTakeEffectTime(newUser.getTakeEffectTime());
            userCvLog.setCreateBy(SecurityUtils.getUsername());
            userCvLog.setCreateTime(DateUtils.getNowDate());
            userCvLogMapper.insertSysUserCvLog(userCvLog);
            if (StringUtils.isNotNull(posts) && posts.length > 0)
            {
                userCvLogMapper.batchUserCvLogPost(userCvLog.getId(),posts);
            }
        }
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public void deleteUserByIds(Long[] userIds)
    {
        for (Long userId : userIds)
        {
            checkUserAllowed(new SysUser(userId));

            new Thread(() -> {
                SysUser user = userMapper.selectUserById(userId);
                if(StringUtils.isNotEmpty(user.getUserCode())) {
                    mesUserMapper.deleteMesUserByUserNo(user.getUserCode());
                }
            }).start();
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        userMapper.deleteUserByIds(userIds);
    }

    /**
     * 导入用户数据
     *
     * @param userList 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysUserImport> userList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(userList) || userList.size() == 0)
        {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String password = configService.selectConfigByKey("sys.user.initPassword");
        for (SysUserImport u : userList)
        {
            try
            {
                // 验证是否存在这个用户
                if (UserConstants.UNIQUE.equals(checkUserCodeUnique(u.getUserCode())))
                {
                    SysUser user = new SysUser();
                    user.setUserCode(u.getUserCode());
                    user.setCompanyCode(DictUtils.getImportValue(u.getCompanyCode()));
                    user.setNickName(u.getNickName());
                    user.setPhonenumber(u.getPhonenumber());
                    user.setUserType(DictUtils.getImportValue(u.getUserType()));
                    user.setJoinTime(DateUtils.dateTime(DateUtils.YYYY_MM_DD,u.getJoinTime()));
                    user.setEmail(u.getEmail());
                    user.setHyzk(DictUtils.getImportValue(u.getHyzk()));
                    user.setEthnic(u.getEthnic());
                    user.setPoliticalFace(DictUtils.getImportValue(u.getPoliticalFace()));
                    user.setSex(DictUtils.getImportValue(u.getSex()));
                    user.setDegree(DictUtils.getImportValue(u.getDegree()));
                    user.setCardNo(u.getCardNo());
                    if(!StringUtils.isEmpty(u.getCardNo()) && u.getCardNo().length() > 15){
                        String date_y = u.getCardNo().substring(6,10);
                        String date_m = u.getCardNo().substring(10,12);
                        String date_d = u.getCardNo().substring(12,14);
                        user.setBirthday(DateUtils.dateTime(DateUtils.YYYY_MM_DD,date_y+"-"+date_m+"-"+date_d));
                    }

                    user.setHukouXz(DictUtils.getImportValue(u.getHukouXz()));
                    user.setHukouJg(u.getHukouJg());
                    user.setHukouHjdz(u.getHukouHjdz());
                    user.setBankName(u.getBankName());
                    user.setBankNo(u.getBankNo());
                    user.setBankAccount(u.getBankAccount());
                    user.setSocialSecurityType(DictUtils.getImportValue(u.getSocialSecurityType()));
                    user.setSocialSecurityNo(u.getSocialSecurityNo());
                    user.setSocialSecurityAddress(u.getSocialSecurityAddress());
                    user.setCpfNo(u.getCpfNo());
                    user.setPositiveStatus(DictUtils.getImportValue(u.getPositiveStatus()));
                    if(!StringUtils.isEmpty(u.getDept())){
                        user.setDeptId(deptMapper.queryDeptIdByDeptName(u.getDept()));
                    }
                    if(!StringUtils.isEmpty(u.getDirectSuperior())){
                        Long directSuperior = userMapper.selectUserIdByNikeName(u.getDirectSuperior());
                        if(directSuperior != null){
                            user.setDirectSuperior(directSuperior.intValue());
                        }
                    }

                    //user.setRank(DictUtils.getImportValue(u.getRank()));
                    user.setBgcc(u.getBgcc());
                    user.setBgdd(u.getBgdd());
                    user.setOfficePhone(u.getOfficePhone());
                    //takeEffectTime
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(operName);
                    user.setCreateTime(DateUtils.getNowDate());
                    this.insertUser(user);
                    if(!StringUtils.isEmpty(u.getPost())){
                        List<SysUserPost> userPostList = new ArrayList<SysUserPost>();
                        SysUserPost post = new SysUserPost();
                        Long postId = postMapper.queryPostIdByPostName(u.getPost());
                        if(postId != null){
                            post.setUserId(user.getUserId());
                            post.setPostId(postId);
                            userPostList.add(post);
                            userPostMapper.batchUserPost(userPostList);
                        }
                    }
                    if(!StringUtils.isEmpty(u.getContactName())){
                        List<SysUserContact> userContactList = new ArrayList<SysUserContact>();
                        SysUserContact contact = new SysUserContact();
                        contact.setUserId(user.getUserId());
                        contact.setName(u.getContactName());
                        contact.setMobileNo(u.getContactMobileNo());
                        contact.setGx(u.getContactGx());
                        contact.setPro(u.getContactPro());
                        userContactList.add(contact);
                        userContactMapper.batchUserContact(userContactList);
                    }
                    if(!StringUtils.isEmpty(u.getWagesBase())){
                        SysUserWages wages = new SysUserWages();
                        wages.setUserId(user.getUserId());
                        wages.setWagesBase(new BigDecimal(u.getWagesBase()));
                        wages.setWagesPost(new BigDecimal(u.getWagesPost()));
                        wages.setWagesSeniority(new BigDecimal(u.getWagesSeniority()));
                        wages.setWagesAssess(new BigDecimal(u.getWagesAssess()));
                        wages.setWagesBgjbf(new BigDecimal(u.getWagesBgjbf()));
                        userWagesMapper.insertSysUserWages(wages);
                    }

                    successNum++;
                    successMsg.append("<br/>" + successNum + "、员工编号 " + u.getUserCode() + " 导入成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、员工编号 " + u.getUserCode() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、员工编号 " + u.getUserCode()+ " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public Map<String, Object> queryProcessUserInfo(Map<String, Object> params) {
        Map<String,Object> map = new HashMap<String,Object>();
        String currentStep = "";
        List<Long> processUserIdList = new ArrayList<Long>();
        Integer directSuperiorUserId = WorkflowContants.WORKFLOW_PROCESS_DEFAULT_USER_ID;
        Object userId = params.get("userId");
        Object processUserPost = params.get("processUserPost");
        Object processUserRole = params.get("processUserRole");
        if(StringUtils.isNotNull(userId) && StringUtils.isNotNull(processUserPost)){
            Map<String,Object> userParams = new HashMap<String,Object>();
            userParams.put("userId",Long.valueOf(String.valueOf(userId)));
            userParams.put("processUserPost",processUserPost);
            Map<String,Object> parentMap = userMapper.queryUserParentDeptInfo(userParams);
            Object processUserId = params.get("processUserId");
            Object isDept  = parentMap.get("parentMap");
            Object parentDeptId = parentMap.get("parentDeptId");
            Object parentFirstId = parentMap.get("parentFirstId");
            Object parentId = parentMap.get("parentId");
            if(WorkflowContants.WORKFLOW_BMZJ.equals(processUserPost)){
                parentId = parentFirstId;
            }
            if(StringUtils.isNotNull(parentDeptId)){
                parentId = parentDeptId;
            }
            userParams.put("parentId",parentId);
            if(WorkflowContants.WORKFLOW_ZJSJ.equals(processUserPost)){  //直接上级
                //获取本人的直接上级
                directSuperiorUserId = userMapper.queryDirectSuperiorInfo(Long.valueOf(String.valueOf(userId)));
                currentStep = "直接上级审批";
                if(StringUtils.isNotNull(directSuperiorUserId)){
                    processUserIdList.add(directSuperiorUserId.longValue());
                }
            }else if(WorkflowContants.WORKFLOW_BMJL.equals(processUserPost)){  //部门经理
                //获取本人的部门主管
                processUserIdList = userMapper.queryDepartmentDirectorInfo(userParams);
                currentStep = "部门经理审批";
            }else if(WorkflowContants.WORKFLOW_BMZJ.equals(processUserPost)){  //部门总监
                //获取本人的部门总监
                processUserIdList = userMapper.queryDepartmentDirectorInfo(userParams);
                currentStep = "部门总监审批";
            }else if(WorkflowContants.WORKFLOW_RSZJ.equals(processUserPost)){  //人事总监
                //获取人事总监
                directSuperiorUserId = userMapper.queryHRDirectorInfo(userParams);
                currentStep = "人事总监审批";
                if(StringUtils.isNotNull(directSuperiorUserId)){
                    processUserIdList.add(directSuperiorUserId.longValue());
                }
            }else if(WorkflowContants.WORKFLOW_ZJL.equals(processUserPost)){  //总经理
                //获取总经理
                directSuperiorUserId = userMapper.queryHRDirectorInfo(userParams);
                currentStep = "总经理审批";
                if(StringUtils.isNotNull(directSuperiorUserId)){
                    processUserIdList.add(directSuperiorUserId.longValue());
                }
            }
        }else if(StringUtils.isNotNull(userId) && StringUtils.isNotNull(processUserRole)){
            Map<String,Object> userParams = new HashMap<String,Object>();
            userParams.put("userId",Long.valueOf(String.valueOf(userId)));
            List<String> rolesList = StringUtils.str2List(String.valueOf(processUserRole),",",true,true);
            //userParams.put("processUserRole",processUserRole);
            userParams.put("rolesList",rolesList);
            //获取ROLE标题
            SysRole roleInfo = roleMapper.selectRoleInfoByRoleKey(String.valueOf(rolesList.get(0)));
            //品质部总监审批
            processUserIdList = userMapper.queryDepartmentDirectorInfo(userParams);
            if(roleInfo!=null){
                currentStep = roleInfo.getRoleName()+"审批";
            }else{
                currentStep = "角色查询失败";
            }
        }else{
            //获取本人的直接上级
            directSuperiorUserId = userMapper.queryDirectSuperiorInfo(Long.valueOf(String.valueOf(userId)));
            currentStep = "直接上级审批";
            if(StringUtils.isNotNull(directSuperiorUserId)){
                processUserIdList.add(directSuperiorUserId.longValue());
            }
        }
        if(processUserIdList!=null && processUserIdList.size()<=0){
            processUserIdList.add(WorkflowContants.WORKFLOW_PROCESS_DEFAULT_USER_ID.longValue());
        }
        currentStep = StringUtils.convertDefaultValue(currentStep,"指派失败,请联系管理员");
        map.put("processUserIdList",processUserIdList);
        map.put("currentStep",currentStep);
        return map;
    }

    @Override
    public List<SysUser> selectAllUserList(SysUser user) {
        return userMapper.selectAllUserList(user);
    }

    @Override
    public List<SysUser> queryCustomerUserList(Customer tCustomer) {
        return userMapper.queryCustomerUserList(tCustomer);
    }

    @Override
    public List<SysUser> queryCustomerAllOrderUser(Customer tCustomer) {
        return userMapper.queryCustomerAllOrderUser(tCustomer);
    }

    @Override
    public Map<String, Object> queryUserFields(Long userId) {
        Map<String,Object> fields = new HashMap<String,Object>();
        fields.put("xyjlDatas",userDegreeMapper.selectSysUserDegreeList(userId));
        fields.put("jtcyDatas",userFamilyMapper.selectSysUserFamilyList(userId));
        fields.put("jjlxrDatas",userContactMapper.selectSysUserContactList(userId));
        fields.put("pxjlDatas",userTrainingMapper.selectSysUserTrainingList(userId));
        fields.put("gzjlDatas",userJobMapper.selectSysUserJobList(userId));
        fields.put("htDatas",userContractMapper.selectSysUserContractList(userId));
        fields.put("zsDatas",userCertificateMapper.selectSysUserCertificateList(userId));
        fields.put("jkzDatas",userHealthMapper.selectSysUserHealthList(userId));
        fields.put("xzflDatas",userWagesMapper.selectSysUserWagesById(userId));
        return fields;
    }

    @Override
    public List<SysUserCvLog> selectSysUserCvLogList(Long userId) {
        return userCvLogMapper.selectSysUserCvLogList(userId);
    }

    @Override
    public List<UserExcel> selectUserProccessListNew(SysUser user) {
        List<UserExcel> userInfoList = new ArrayList<UserExcel>();
        List<SysUser> userAllList = userMapper.selectAllProcessUserRoleList();
        for(SysUser userAllInfo : userAllList){
            UserExcel userInfo = new UserExcel();
            userInfo.setNickName(userAllInfo.getNickName());
            userInfo.setDeptName(userAllInfo.getDeptName());
            userInfo.setProcessContext(userAllInfo.getRemark());
            userInfoList.add(userInfo);
        }
        return userInfoList;
    }

    @Override
    public List<UserApplyExcel> selectUserApplyListNew(SysUser user) {
        return userMapper.selectUserApplyListNew();
    }

    @Override
    public void updateUserPayslipPwd(SysHrUser sysHrUser) {
        userMapper.updateUserPayslipPwd(sysHrUser);
    }

    @Override
    public Set<Long> queryUserLeaderDataInfo(Long userId) {
        Set<Long> userIdList = new HashSet<>();
        userIdList.add(userId);
        List<Long> processUserIdList = new ArrayList<Long>();
        //获取直接上级  部门总监 部门主管 总经理
        //获取本人的直接上级
        Integer directSuperiorUserId = userMapper.queryDirectSuperiorInfo(userId);
        if(StringUtils.isNotNull(directSuperiorUserId)){
            userIdList.add(directSuperiorUserId.longValue());
        }
        Map<String,Object> userParams = new HashMap<String,Object>();
        userParams.put("userId",userId);
        Map<String,Object> parentMap = userMapper.queryUserParentDeptInfo(userParams);
        if(parentMap != null) {
            Object parentDeptId = parentMap.get("parentDeptId");
            Object parentFirstId = parentMap.get("parentFirstId");  //部门总监
            Object parentId = parentMap.get("parentId");
            Object userDeptId = parentMap.get("userDeptId");
            Object deptName = parentMap.get("deptName");
            //20240118 更改  卢小平 上海市场开发一部
            if(StringUtils.isNotNull(deptName) && (String.valueOf(deptName).indexOf("上海市场开发一部")>=0)){
                //userIdList.add(UserID.LUXIAOPING);
            }
            if(StringUtils.isNotNull(parentDeptId)){
                parentId = parentDeptId;
            }
            Object ancestors = parentMap.get("ancestors");

            if(StringUtils.isNotNull(ancestors)){
                List<String> deptIds = StringUtils.str2List(String.valueOf(ancestors),",",true,true);
                if(StringUtils.isNotNull(userDeptId)){
                    deptIds.add(String.valueOf(userDeptId));
                }
                userParams.put("deptIds",deptIds);
            }
            //部门总监
            userParams.put("parentId",parentFirstId);
            userParams.put("processUserPost","WORKFLOW_BMZJ");
            processUserIdList = userMapper.queryDepartmentDirectorInfoByAncestors(userParams);
            userIdList.addAll(processUserIdList);
            //部门经理
            userParams.put("parentId",parentId);
            userParams.put("processUserPost","WORKFLOW_BMJL");
            processUserIdList = userMapper.queryDepartmentDirectorInfoByAncestors(userParams);
            userIdList.addAll(processUserIdList);
        }

        //总经理
        userParams.put("processUserPost","WORKFLOW_ZJL");
        directSuperiorUserId = userMapper.queryHRDirectorInfo(userParams);
        if(StringUtils.isNotNull(directSuperiorUserId)){
            userIdList.add(directSuperiorUserId.longValue());
        }
        //userIdList.add(UserID.YAN);
        return userIdList;
    }

    @Override
    public List<Long> queryUserLeaderDataInfoNew(Long userId) {
        List<Long> userIdList = new ArrayList<Long>();
        userIdList.add(userId);
        List<Long> processUserIdList = new ArrayList<Long>();
        //获取直接上级  部门总监 部门主管 总经理
        Integer directSuperiorUserId = userMapper.queryDirectSuperiorInfo(userId);
        if(StringUtils.isNotNull(directSuperiorUserId)){
            userIdList.add(directSuperiorUserId.longValue());
        }
        userIdList.add(UserID.MIAO);
        userIdList.add(UserID.WANG);
        //userIdList.add(UserID.YAN);
        return userIdList;
    }

    @Override
    public List<Long> queryUserLeaderAndCustomerAssistDataInfo(Long userId, Long customerId) {
        List<Long> userIdList = new ArrayList<Long>();
        userIdList.add(userId);
        List<Long> processUserIdList = new ArrayList<Long>();
        //获取直接上级  部门总监 部门主管 总经理
        //获取本人的直接上级
        Integer directSuperiorUserId = userMapper.queryDirectSuperiorInfo(userId);
        if(StringUtils.isNotNull(directSuperiorUserId)){
            userIdList.add(directSuperiorUserId.longValue());
        }
        Map<String,Object> userParams = new HashMap<String,Object>();
        userParams.put("userId",userId);
        Map<String,Object> parentMap = userMapper.queryUserParentDeptInfo(userParams);
        if(parentMap != null) {
            Object parentDeptId = parentMap.get("parentDeptId");
            Object parentFirstId = parentMap.get("parentFirstId");  //部门总监
            Object parentId = parentMap.get("parentId");
            Object userDeptId = parentMap.get("userDeptId");
            if(StringUtils.isNotNull(parentDeptId)){
                parentId = parentDeptId;
            }
            Object ancestors = parentMap.get("ancestors");

            if(StringUtils.isNotNull(ancestors)){
                List<String> deptIds = StringUtils.str2List(String.valueOf(ancestors),",",true,true);
                if(StringUtils.isNotNull(userDeptId)){
                    deptIds.add(String.valueOf(userDeptId));
                }
                userParams.put("deptIds",deptIds);
            }
            //部门总监
            userParams.put("parentId",parentFirstId);
            userParams.put("processUserPost","WORKFLOW_BMZJ");
            processUserIdList = userMapper.queryDepartmentDirectorInfoByAncestors(userParams);
            userIdList.addAll(processUserIdList);
            //部门经理
            userParams.put("parentId",parentId);
            userParams.put("processUserPost","WORKFLOW_BMJL");
            processUserIdList = userMapper.queryDepartmentDirectorInfoByAncestors(userParams);
            userIdList.addAll(processUserIdList);
        }

        //查询客户协助人
        List<Long> customerUserIdList = userMapper.queryCustomerAssistUserList(customerId);
        if(customerUserIdList!=null && customerUserIdList.size()>0){
            userIdList.addAll(customerUserIdList);
        }

        return userIdList;
    }

    @Override
    public int assginDataUser(SysUser user) {
        List<String> phonenumber = StringUtils.str2List(user.getPhonenumber(),",",true,true);
        List<Long> userIdList = userMapper.queryUserIdList(phonenumber);
        if(userIdList==null || userIdList.size()<=0){
            return 0;
        }
        //获取拥有数据
        List<TProjectExecutionUser> executionUserDataList = userMapper.queryProjectExecutionDataList(userIdList);
        //获取本人拥有数据
        Long userId = user.getUserId();
        List<Long> myExecutionDataList = userMapper.queryMyExecutionDataList(userId);
        List<TProjectExecutionUser> insertExecutionUserDataList = new ArrayList<TProjectExecutionUser>();
        for(TProjectExecutionUser executionUser : executionUserDataList){
            if(StringUtils.isNotNull(executionUser.getExecutionId()) && !myExecutionDataList.contains(executionUser.getExecutionId())){
                executionUser.setIsAudit(1);
                executionUser.setProcessUserId(userId);
                executionUser.setCreateBy(SecurityUtils.getUsername());
                executionUser.setRemark("后台设置用户数据");
                insertExecutionUserDataList.add(executionUser);
            }
        }
        if(insertExecutionUserDataList!=null && insertExecutionUserDataList.size()>0){
            userMapper.batchExecutionUserDataList(insertExecutionUserDataList);
        }else{
            return 0;
        }
        return 1;
    }

    @Override
    public List<SysUserInfo> selectUserProccessList(SysUser user) {
        List<TProjectWorkflowDefinition> list = projectWorkflowDefinitionMapper.selectTProjectWorkflowDefinitionList(new TProjectWorkflowDefinition());
        Set<Long> processUserList = new HashSet<Long>();
        Set<Long> processYnUserList = new HashSet<Long>();
        Set<Long> processYcUserList = new HashSet<Long>();
        Set<Long> ccUserList = new HashSet<Long>();
        Set<Long> ccYnUserList = new HashSet<Long>();
        Set<Long> ccYcUserList = new HashSet<Long>();
        Set<Long> allUserId = new HashSet<Long>();
        for(TProjectWorkflowDefinition workflowDefinition : list){
            String userId = workflowDefinition.getUserId();
            String ccId = workflowDefinition.getCcUserId();
            String ynUserId = workflowDefinition.getYnUserId();
            String ycUserId = workflowDefinition.getYcUserId();
            String ynCcUserId = workflowDefinition.getYnCcUserId();
            String ycCcUserId = workflowDefinition.getYcCcUserId();
            String postName = workflowDefinition.getPostName();
            String companyPostName = workflowDefinition.getCompanyPostName();
            String ccPostName = workflowDefinition.getCcPostName();
            String companyCcPostName = workflowDefinition.getCompanyCcPostName();
            if(StringUtils.isNotNull(ynUserId)){
                List<String> ynUserIdList = StringUtils.str2List(ynUserId,",",true,true);
                Set<Long> processYnUserListNew = StringUtils.dataList(ynUserIdList);
                if(processYnUserListNew!=null && processYnUserListNew.size()>0){
                    processYnUserList.addAll(processYnUserListNew);
                }
            }else{
                List<String> userIdList = StringUtils.str2List(userId,",",true,true);
                Set<Long> processYnUserListNew  = StringUtils.dataList(userIdList);
                if(processYnUserListNew!=null && processYnUserListNew.size()>0){
                    processYnUserList.addAll(processYnUserListNew);
                }
            }
            if(StringUtils.isNotNull(ycUserId)){
                List<String> ycUserIdList = StringUtils.str2List(ycUserId,",",true,true);
                Set<Long> processYnUserListNew  = StringUtils.dataList(ycUserIdList);
                if(processYnUserListNew!=null && processYnUserListNew.size()>0){
                    processYnUserList.addAll(processYnUserListNew);
                }
            }else{
                List<String> userIdList = StringUtils.str2List(userId,",",true,true);
                Set<Long> processYnUserListNew  = StringUtils.dataList(userIdList);
                if(processYnUserListNew!=null && processYnUserListNew.size()>0){
                    processYnUserList.addAll(processYnUserListNew);
                }
            }

            if(StringUtils.isNotNull(ynCcUserId)){
                List<String> ynUserIdList = StringUtils.str2List(ynCcUserId,",",true,true);
                Set<Long> processYnUserListNew  = StringUtils.dataList(ynUserIdList);
                if(processYnUserListNew!=null && processYnUserListNew.size()>0){
                    processYnUserList.addAll(processYnUserListNew);
                }
            }else{
                List<String> ccIdList = StringUtils.str2List(ccId,",",true,true);
                Set<Long> processYnUserListNew  = StringUtils.dataList(ccIdList);
                if(processYnUserListNew!=null && processYnUserListNew.size()>0){
                    processYnUserList.addAll(processYnUserListNew);
                }
            }
            if(StringUtils.isNotNull(ycCcUserId)){
                List<String> ycCcUserIdList = StringUtils.str2List(ycCcUserId,",",true,true);
                Set<Long> processYnUserListNew  = StringUtils.dataList(ycCcUserIdList);
                if(processYnUserListNew!=null && processYnUserListNew.size()>0){
                    processYnUserList.addAll(processYnUserListNew);
                }
            }else{
                List<String> ccIdList = StringUtils.str2List(ccId,",",true,true);
                Set<Long> processYnUserListNew = StringUtils.dataList(ccIdList);
                if(processYnUserListNew!=null && processYnUserListNew.size()>0){
                    processYnUserList.addAll(processYnUserListNew);
                }
            }
        }
        if(processUserList!=null && processUserList.size()>0){
            allUserId.addAll(processUserList);
        }
        if(processYnUserList!=null && processYnUserList.size()>0){
            allUserId.addAll(processYnUserList);
        }
        if(processYcUserList!=null && processYcUserList.size()>0){
            allUserId.addAll(processYcUserList);
        }
        if(ccUserList!=null && ccUserList.size()>0){
            allUserId.addAll(ccUserList);
        }
        if(ccYnUserList!=null && ccYnUserList.size()>0){
            allUserId.addAll(ccYnUserList);
        }
        if(ccYcUserList!=null && ccYcUserList.size()>0){
            allUserId.addAll(ccYcUserList);
        }
        List<SysUserInfo> userInfoList = new ArrayList<SysUserInfo>();
        List<SysUser> userAllList = userMapper.selectAllProcessUserList(allUserId);
        for(SysUser userAllInfo : userAllList){
            SysUserInfo userInfo = new SysUserInfo();
            userInfo.setNickName(userAllInfo.getNickName());
            userInfo.setDeptName(userAllInfo.getDeptName());
            Map<String,String> processInfo = queryProcessInfo(userAllInfo.getUserId(),list);
            userInfo.setProcessContext(processInfo.get("sb"));
            //userInfo.setCcContext(processInfo.get("ccBuff"));
            userInfoList.add(userInfo);
        }
        return userInfoList;
    }

    private Map<String,String> queryProcessInfo(Long currUserId,List<TProjectWorkflowDefinition> list){
        Map<String,String> map = new HashMap<String,String>();
        StringBuffer sb = new StringBuffer();
        StringBuffer ccBuff = new StringBuffer();
        for(TProjectWorkflowDefinition workflowDefinition : list) {
            String code = workflowDefinition.getWorkflowCode();
            String currentStep = workflowDefinition.getCurrentStep();
            String userId = workflowDefinition.getUserId();
            String ccId = workflowDefinition.getCcUserId();
            String ynUserId = workflowDefinition.getYnUserId();
            String ycUserId = workflowDefinition.getYcUserId();
            String ynCcUserId = workflowDefinition.getYnCcUserId();
            String ycCcUserId = workflowDefinition.getYcCcUserId();
            if(StringUtils.isNotNull(ynUserId)){
                List<String> ynUserIdList = StringUtils.str2List(ynUserId,",",true,true);
                if(ynUserIdList.contains(String.valueOf(currUserId))){
                    String dictVal = DictUtils.getDictLabel("PROJECT_ITEM",code);
                    sb.append(dictVal+currentStep);
                    sb.append(",");
                }
            }else{
                List<String> userIdList = StringUtils.str2List(userId,",",true,true);
                if(userIdList.contains(String.valueOf(currUserId))){
                    String dictVal = DictUtils.getDictLabel("PROJECT_ITEM",code);
                    sb.append(dictVal+currentStep);
                    sb.append(",");
                }
            }
            if(StringUtils.isNotNull(ycUserId)){
                List<String> ycUserIdList = StringUtils.str2List(ycUserId,",",true,true);
                if(ycUserIdList.contains(String.valueOf(currUserId))){
                    String dictVal = DictUtils.getDictLabel("PROJECT_ITEM",code);
                    sb.append(dictVal+currentStep);
                    sb.append(",");
                }
            }else{
                List<String> userIdList = StringUtils.str2List(userId,",",true,true);
                if(userIdList.contains(String.valueOf(currUserId))){
                    String dictVal = DictUtils.getDictLabel("PROJECT_ITEM",code);
                    sb.append(dictVal+currentStep);
                    sb.append(",");
                }
            }

            if(StringUtils.isNotNull(ynCcUserId)){
                List<String> ynUserIdList = StringUtils.str2List(ynCcUserId,",",true,true);
                if(ynUserIdList.contains(String.valueOf(currUserId))){
                    String dictVal = DictUtils.getDictLabel("PROJECT_ITEM",code);
                    ccBuff.append(dictVal+currentStep+"抄送");
                    ccBuff.append(",");
                }
            }else{
                List<String> ccIdList = StringUtils.str2List(ccId,",",true,true);
                if(ccIdList.contains(String.valueOf(currUserId))){
                    String dictVal = DictUtils.getDictLabel("PROJECT_ITEM",code);
                    ccBuff.append(dictVal+currentStep+"抄送");
                    ccBuff.append(",");
                }
            }
            if(StringUtils.isNotNull(ycCcUserId)){
                List<String> ycCcUserIdList = StringUtils.str2List(ycCcUserId,",",true,true);
                if(ycCcUserIdList.contains(String.valueOf(currUserId))){
                    String dictVal = DictUtils.getDictLabel("PROJECT_ITEM",code);
                    ccBuff.append(dictVal+currentStep+"抄送");
                    ccBuff.append(",");
                }
            }else{
                List<String> ccIdList = StringUtils.str2List(ccId,",",true,true);
                if(ccIdList.contains(String.valueOf(currUserId))){
                    String dictVal = DictUtils.getDictLabel("PROJECT_ITEM",code);
                    ccBuff.append(dictVal+currentStep+"抄送");
                    ccBuff.append(",");
                }
            }
        }
        map.put("sb",sb.toString());
        map.put("ccBuff",ccBuff.toString());
        return map;
    }

    @Override
    public List<String> selectPermsByUserId(Long userId) {
        return userMapper.selectPermsByUserId(userId);
    }

    @Override
    public List<String> selectPermsByUserId() {
        return userMapper.selectPermsByUserId(SecurityUtils.getUserId());
    }

    /**
     * 根据产品类别id，获取对应研发可用的用户列表
     *
     * @param categoryId 产品类别id
     * @param projectOrderId 项目ID
     * @return 可用工程师列表
     */
    @Override
    public List<SysUser> selectEngineersByCategoryId(Long categoryId, Long projectOrderId) {
        return userMapper.selectEngineersByCategoryId(categoryId, projectOrderId);
    }

    /**
     * 根据部门，获取对应的用户列表
     * @param DeptId 部门ID
     * @return 用户列表
     */
    @Override
    public List<SysUser> selectUsersByDeptId(Long DeptId) {
        return userMapper.selectUsersByDeptId(DeptId);
    }

}
