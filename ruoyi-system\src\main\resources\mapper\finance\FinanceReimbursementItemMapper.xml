<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.finance.mapper.FinanceReimbursementItemMapper">

    <sql id="selectFinanceReimbursementItemVo">
        select i.ID, REIMBURSEMENT_CHILD_ID, DEPT_ID,USER_ID, ACTUAL_USER_ID, REIMBUREMENT_DATE, INVOIC_SUPPLIER, AMOUNT_TOTAL, AMOUNT_REIMBUR,expense_id,state, i.del_flag, i.create_by, i.create_time, i.update_by, i.update_time, i.remark
        ,child.type,t.PAY_WAY,t.APPLY_USER_ID,t.ENTERPRISE_CODE
        ,(SELECT nick_name FROM sys_user WHERE user_id = i.USER_ID ) as USER_NAME
        ,(SELECT nick_name FROM sys_user WHERE user_id = i.ACTUAL_USER_ID ) as ACTUAL_NAME
        ,(SELECT nick_name FROM sys_user WHERE user_id = t.APPLY_USER_ID ) as APPLY_NAME
        from t_finance_reimbursement_item i
        LEFT JOIN t_finance_reimbursement_child child on i.REIMBURSEMENT_CHILD_ID = child.ID
        LEFT JOIN t_finance_reimbursement t on child.REIMBURSEMENT_ID = t.ID
    </sql>

    <select id="selectFinanceReimbursementItemList" parameterType="FinanceReimbursementItem" resultType="FinanceReimbursementItem">
        <include refid="selectFinanceReimbursementItemVo"/>
        <where>
            i.del_flag = 0
            <if test="reimbursementChildId != null "> and REIMBURSEMENT_CHILD_ID = #{reimbursementChildId}</if>
            <if test="userId != null "> and i.USER_ID = #{userId}</if>
            <if test="deptId != null "> and DEPT_ID = #{deptId}</if>
            <if test="actualUserId != null "> and ACTUAL_USER_ID = #{actualUserId}</if>
            <if test="applyUserId != null "> and t.APPLY_USER_ID = #{applyUserId}</if>
            <if test="payWay != null "> and t.PAY_WAY = #{payWay}</if>
            <if test="reimburementDate != null "> and REIMBUREMENT_DATE = #{reimburementDate}</if>
            <if test="invoicSupplier != null  and invoicSupplier != ''"> and INVOIC_SUPPLIER like concat('%', #{invoicSupplier}, '%')</if>
            <if test="amountTotal != null "> and AMOUNT_TOTAL = #{amountTotal}</if>
            <if test="amountReimbur != null "> and AMOUNT_REIMBUR = #{amountReimbur}</if>
            <if test="expenseId != null "> and expense_id = #{expenseId}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="enterpriseCode != null "> and t.enterprise_code = #{enterpriseCode}</if>
            <if test="states != null ">
                and state in
                <foreach collection="states" item="state" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
            <if test="reimburementDateGt != null "> and REIMBUREMENT_DATE <![CDATA[ >= ]]> #{reimburementDateGt}</if>
            <if test="reimburementDateLt != null "> and REIMBUREMENT_DATE <![CDATA[ <  ]]> #{reimburementDateLt}</if>
            <if test="deptIds != null and !deptIds.isEmpty()"><!-- 部门 -->
                AND
                <foreach collection="deptIds" item="dept" separator="or" open="(" close=")">
                    (i.dept_id = #{dept} OR i.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{dept}, ancestors) ))
                </foreach>
            </if>
        </where>
        order by i.id desc
    </select>

    <select id="selectFinanceReimbursementItemById" parameterType="Long" resultType="FinanceReimbursementItem" >
        <include refid="selectFinanceReimbursementItemVo"/>
        where i.ID = #{id}
    </select>

    <insert id="insertFinanceReimbursementItem" parameterType="FinanceReimbursementItem" useGeneratedKeys="true" keyProperty="id">
        insert into t_finance_reimbursement_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reimbursementChildId != null">REIMBURSEMENT_CHILD_ID,</if>
            <if test="userId != null">USER_ID,</if>
            <if test="deptId != null">DEPT_ID,</if>
            <if test="actualUserId != null">ACTUAL_USER_ID,</if>
            <if test="reimburementDate != null">REIMBUREMENT_DATE,</if>
            <if test="invoicSupplier != null">INVOIC_SUPPLIER,</if>
            <if test="amountTotal != null">AMOUNT_TOTAL,</if>
            <if test="amountReimbur != null">AMOUNT_REIMBUR,</if>
            <if test="expenseId != null">expense_id,</if>
            <if test="state != null">state,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reimbursementChildId != null">#{reimbursementChildId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="actualUserId != null">#{actualUserId},</if>
            <if test="reimburementDate != null">#{reimburementDate},</if>
            <if test="invoicSupplier != null">#{invoicSupplier},</if>
            <if test="amountTotal != null">#{amountTotal},</if>
            <if test="amountReimbur != null">#{amountReimbur},</if>
            <if test="expenseId != null">#{expenseId},</if>
            <if test="state != null">#{state},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateFinanceReimbursementItem" parameterType="FinanceReimbursementItem">
        update t_finance_reimbursement_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="reimbursementChildId != null">REIMBURSEMENT_CHILD_ID = #{reimbursementChildId},</if>
            <if test="userId != null">USER_ID = #{userId},</if>
            <if test="deptId != null">DEPT_ID = #{deptId},</if>
            <if test="actualUserId != null">ACTUAL_USER_ID = #{actualUserId},</if>
            <if test="reimburementDate != null">REIMBUREMENT_DATE = #{reimburementDate},</if>
            <if test="invoicSupplier != null">INVOIC_SUPPLIER = #{invoicSupplier},</if>
            <if test="amountTotal != null">AMOUNT_TOTAL = #{amountTotal},</if>
            <if test="amountReimbur != null">AMOUNT_REIMBUR = #{amountReimbur},</if>
            <if test="expenseId != null">expense_id = #{expenseId},</if>
            <if test="state != null">state = #{state},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where ID = #{id}
    </update>

    <update id="deleteFinanceReimbursementItemByIds" parameterType="String">
        update  t_finance_reimbursement_item set del_flag = 2,update_time = now() where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="deleteItem" parameterType="String">
        update  t_finance_reimbursement_item set del_flag = 2,update_time = now() where
        state = 0
        and REIMBURSEMENT_CHILD_ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getReimbursementItem" parameterType="map" resultType="FinanceReimbursementItem">
        select item.id,item.REIMBURSEMENT_CHILD_ID,item.USER_ID,re.APPLY_USER_ID ,item.ACTUAL_USER_ID,item.REIMBUREMENT_DATE,item.INVOIC_SUPPLIER,item.AMOUNT_TOTAL,item.AMOUNT_REIMBUR,item.state,u.nick_name actualName from t_finance_reimbursement_item item
        LEFT JOIN t_finance_reimbursement_child child on child.id = item.REIMBURSEMENT_CHILD_ID
        LEFT JOIN t_finance_reimbursement re on re.id = child.REIMBURSEMENT_ID
        LEFT JOIN sys_user u on  u.user_id = re.APPLY_USER_ID
        where child.type = #{type}
        and (item.state = 0 or item.state = 3)
        and re.ENTERPRISE_CODE = #{enterpriseCode} and re.PAY_WAY = #{payWay}
        and (re.APPLY_USER_ID = #{actualUserId} or item.ACTUAL_USER_ID = #{actualUserId})
        <if test="payWay == 1">
            <!-- REIMBUREMENT_DATE 小于上月最后一天 -->
            and item.REIMBUREMENT_DATE <![CDATA[ <= ]]> LAST_DAY(DATE_SUB(CURDATE(), INTERVAL 1 MONTH))
        </if>

    </select>

    <update id="updateItemStateByIds" parameterType="String">
        update  t_finance_reimbursement_item set state = 0,expense_id = null ,update_time = now() where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getItemExcel" resultType="com.ruoyi.finance.excel.FinanceReimbursementItemExcel">
        select MONTH(item.REIMBUREMENT_DATE) month,t.PAY_WAY,u.nick_name ACTUAL_NAME,d.dept_name,child.type,t.ENTERPRISE_CODE,item.INVOIC_SUPPLIER,item.AMOUNT_REIMBUR,item.AMOUNT_TOTAL,DATE_FORMAT(item.REIMBUREMENT_DATE, '%Y-%m-%d') REIMBUREMENT_DATE,t.SUMMARY,item.state
        ,CASE
        WHEN IFNULL(d1.dept_name,'') = '' THEN d3.dept_name
        WHEN IFNULL(d1.dept_name,'') = '宜侬科技' THEN d2.dept_name
        ELSE d1.dept_name
        END `dept_name1`,
        CASE
        WHEN IFNULL(d1.dept_name,'') = '' THEN ''
        WHEN IFNULL(d1.dept_name,'') = '宜侬科技' THEN d3.dept_name
        ELSE d2.dept_name
        END `dept_name2`,
        CASE
        WHEN IFNULL(d1.dept_name,'') = '' THEN ''
        WHEN IFNULL(d1.dept_name,'') = '宜侬科技' THEN ''
        ELSE d3.dept_name
        END `dept_name3`
        from t_finance_reimbursement_item item
        LEFT JOIN t_finance_reimbursement_child child on child.ID = item.REIMBURSEMENT_CHILD_ID
        LEFT JOIN t_finance_reimbursement t on t.ID = child.REIMBURSEMENT_ID
        LEFT JOIN sys_user u on u.user_id = item.ACTUAL_USER_ID
        LEFT JOIN sys_dept d on u.dept_id = d.dept_id
        LEFT JOIN sys_dept d3 ON d3.dept_id = d.dept_id
        LEFT JOIN sys_dept d2 ON d2.dept_id = d3.parent_id
        LEFT JOIN sys_dept d1 ON d1.dept_id = d2.parent_id
        where item.del_flag = 0
        <if test="applyUserId != null "> and t.apply_user_id = #{applyUserId}</if>
        <if test="payWay != null "> and t.PAY_WAY = #{payWay}</if>
        <if test="companys != null and !companys.isEmpty()">
            and t.ENTERPRISE_CODE in
            <foreach collection="companys" item="company" separator="," open="(" close=")">
                #{company}
            </foreach>
        </if>
        <if test="deptIds != null and !deptIds.isEmpty()"><!-- 部门 -->
            AND
            <foreach collection="deptIds" item="dept" separator="or" open="(" close=")">
                (item.dept_id = #{dept} OR item.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{dept}, ancestors) ))
            </foreach>
        </if>
    </select>

    <select id="getItemExcelByDept" parameterType="FinanceReimbursementItem" resultType="com.ruoyi.finance.excel.FinanceReimbursementItemExcel">
        select MONTH(item.REIMBUREMENT_DATE) month,t.PAY_WAY,u.nick_name ACTUAL_NAME,d.dept_name,child.type,t.ENTERPRISE_CODE,item.INVOIC_SUPPLIER,item.AMOUNT_REIMBUR,item.AMOUNT_TOTAL,DATE_FORMAT(item.REIMBUREMENT_DATE, '%Y-%m-%d') REIMBUREMENT_DATE,t.SUMMARY,item.state
        ,CASE
        WHEN IFNULL(d1.dept_name,'') = '' THEN d3.dept_name
        WHEN IFNULL(d1.dept_name,'') = '宜侬科技' THEN d2.dept_name
        ELSE d1.dept_name
        END `dept_name1`,
        CASE
        WHEN IFNULL(d1.dept_name,'') = '' THEN ''
        WHEN IFNULL(d1.dept_name,'') = '宜侬科技' THEN d3.dept_name
        ELSE d2.dept_name
        END `dept_name2`,
        CASE
        WHEN IFNULL(d1.dept_name,'') = '' THEN ''
        WHEN IFNULL(d1.dept_name,'') = '宜侬科技' THEN ''
        ELSE d3.dept_name
        END `dept_name3`
        from t_finance_reimbursement_item item
        LEFT JOIN t_finance_reimbursement_child child on child.ID = item.REIMBURSEMENT_CHILD_ID
        LEFT JOIN t_finance_reimbursement t on t.ID = child.REIMBURSEMENT_ID
        LEFT JOIN sys_user u on u.user_id = item.ACTUAL_USER_ID
        LEFT JOIN sys_dept d on u.dept_id = d.dept_id
        LEFT JOIN sys_dept d3 ON d3.dept_id = d.dept_id
        LEFT JOIN sys_dept d2 ON d2.dept_id = d3.parent_id
        LEFT JOIN sys_dept d1 ON d1.dept_id = d2.parent_id
        where item.del_flag = 0
        <if test="applyUserId != null "> and t.apply_user_id = #{applyUserId}</if>
        <if test="payWay != null "> and t.PAY_WAY = #{payWay}</if>
        <if test="companys != null and !companys.isEmpty()">
            and t.ENTERPRISE_CODE in
            <foreach collection="companys" item="company" separator="," open="(" close=")">
                #{company}
            </foreach>
        </if>
        <if test="deptIds != null and !deptIds.isEmpty()"><!-- 部门 -->
            AND
            <foreach collection="deptIds" item="dept" separator="or" open="(" close=")">
                (item.dept_id = #{dept} OR item.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{dept}, ancestors) ))
            </foreach>
        </if>
    </select>




    <!--  汇总-部门数据  -->
    <select id="getTopDept" parameterType="Long" resultType="SysDept">
        select * from sys_dept_back where parent_id = #{parentId} and  del_flag = 0
    </select>

    <!--  汇总-费用类别数据  -->
    <insert id="getSummaryType" parameterType="string">
        INSERT INTO t_finance_summary_type (type, type_name,CREATE_TIME)
        SELECT dict_value,dict_label,STR_TO_DATE(#{date}, '%Y-%m-%d %H:%i:%s')
        from sys_dict_data where dict_type = 'expense_type_list'
    </insert>

    <!--  汇总-金额统计  -->
    <select id="getSummaryDate" parameterType="map" resultType="map">
        select YEAR(item.REIMBUREMENT_DATE)year,month(item.REIMBUREMENT_DATE)month,t.pay_way payWay,item.dept_id deptId,d.dept_no deptNo,d.ancestors,child.type
        ,IFNULL(sum((select AMOUNT_REIMBUR from t_finance_reimbursement_item where id = item.id and del_flag = 0)),0)amountUnpaid
        ,IFNULL(sum((select AMOUNT_REIMBUR from t_finance_reimbursement_item where id = item.id and del_flag = 0 and expense_id = e.id and e.`status` = 1)),0)amountReimbur
        from t_finance_reimbursement_item item
        LEFT JOIN t_finance_reimbursement_child child on child.ID = item.REIMBURSEMENT_CHILD_ID and child.del_flag = 0
        LEFT JOIN t_finance_reimbursement t on t.ID = child.REIMBURSEMENT_ID and t.del_flag = 0
        LEFT JOIN sys_user u on u.user_id = item.ACTUAL_USER_ID
        LEFT JOIN sys_dept_back d on item.dept_id = d.dept_id
        LEFT JOIN t_expense_user e on e.id = item.expense_id and e.del_flag = 0
        <where>
            item.del_flag = 0
            <if test="date != null and date != ''">
                and DATE_FORMAT(item.REIMBUREMENT_DATE,'%Y-%m') = DATE_FORMAT(#{date},'%Y-%m')
            </if>
            <if test="companys != null and !companys.isEmpty()">
                and t.ENTERPRISE_CODE in
                <foreach collection="companys" item="company" separator="," open="(" close=")">
                    #{company}
                </foreach>
            </if>
            <if test="invoicSupplierNot != null and !invoicSupplierNot.isEmpty()">
                and  (t.PAY_WAY = 1 or (
                    t.PAY_WAY = 2 and
                    <foreach collection="invoicSupplierNot" item="invoicSupplier" separator="and" open="(" close=")">
                        (INSTR(#{invoicSupplier}, item.INVOIC_SUPPLIER)=0)
                    </foreach>
                    )
                )
            </if>
        </where>
        GROUP BY child.type,item.dept_id,t.pay_way,month(item.REIMBUREMENT_DATE) HAVING item.dept_id is not null and t.pay_way is not null
        order by month,type,deptId,payWay
    </select>

    <!-- 默认上个月 -->
    <select id="getPickerOptions" resultType="string">
        select  date_format(IFNULL(create_time,DATE_SUB(CURDATE(), INTERVAL 1 MONTH)), '%Y-%m') from t_finance_summary_type
        ORDER BY create_time desc limit 1
    </select>

</mapper>
