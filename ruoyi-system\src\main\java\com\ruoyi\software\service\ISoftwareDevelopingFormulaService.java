package com.ruoyi.software.service;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.vo.AiDto;
import com.ruoyi.software.domain.SoftwareDevelopingFormula;
import com.ruoyi.software.util.ComponentUtil;

import java.util.List;
import java.util.Map;

/**
 * 研发配方Service接口
 *
 * <AUTHOR>
 * @date 2024-01-20
 */
public interface ISoftwareDevelopingFormulaService
{

    public void insertSoftwareDevelopingFormula(SoftwareDevelopingFormula softwareDevelopingFormula);

    public void updateSoftwareDevelopingFormula(SoftwareDevelopingFormula softwareDevelopingFormula);

    public int insertOrUpdateSoftwareDevelopingFormula(SoftwareDevelopingFormula softwareDevelopingFormula);

    //获取配方原料数据
    List<JSONObject> queryFormulaMaterialData(Long formulaId);

    JSONObject queryFormulaMaterialObjData(Long id);

    SoftwareDevelopingFormula selectSoftwareDevelopingFormulaDetailById(Long id);
    SoftwareDevelopingFormula getSoftwareDevelopingFormulaProductSafetyAssessmentData(SoftwareDevelopingFormula softwareDevelopingFormula);
    List<ComponentUtil> getSoftwareDevelopingFormulaProductSafetyAssessmentData2(SoftwareDevelopingFormula softwareDevelopingFormula);
    SoftwareDevelopingFormula processSoftwareDevelopingFormulaDetailById(Long id);
    SoftwareDevelopingFormula selectSoftwareDevelopingFormulaSimpleById(Long id);
    SoftwareDevelopingFormula selectSoftwareDevelopingFormulaById(Long id);


    int addFormulaSpecZxbz(JSONObject specObj);
    int addUserFormulaSpecZxbz(JSONObject specObj);

    JSONObject addFormulaSpecPage(Long id);

    //添加特殊原料
    int addFormulaSpecMaterialData(SoftwareDevelopingFormula softwareDevelopingFormula);

    //添加配方使用目的
    int addFormulaSymdForm(SoftwareDevelopingFormula softwareDevelopingFormula);

    //生成P配方
    Map<String, Object> generatePFormulaInfo(SoftwareDevelopingFormula softwareDevelopingFormula);

    //生成B代码
    Map<String, Object> generateBMaterialInfo(SoftwareDevelopingFormula softwareDevelopingFormula);

    //生成B代码配方
    Map<String, Object> genNewformulaInfo(SoftwareDevelopingFormula softwareDevelopingFormula);

    //获取配方信息
    List<JSONObject> querySoftwareFormulaReleaseDataList(SoftwareDevelopingFormula softwareDevelopingFormula);

    List<JSONObject> querySoftwareFormulaReleaseByIdDataList(SoftwareDevelopingFormula softwareDevelopingFormula);

    List<JSONObject> queryMaterialFormulaSpecDataList(Long id);
    JSONObject queryFormulaStabilityRecordDataList(Long id);

    JSONObject queryMaterialFormulaSpecDataDetail(Long id);

    JSONObject queryFormulaLegalGy(SoftwareDevelopingFormula softwareDevelopingFormula);

    void addFormulaShareDataInfo(SoftwareDevelopingFormula softwareDevelopingFormula);

    void rdBomDiff() throws Exception;

    List<Long> querySoftwareFormulaDataListByUpdateTime();

    AjaxResult operatorAiData(AiDto aiDto);

    AjaxResult operatorEnowInnerAiData(AiDto aiDto);

    void processRdSchedulingPersonnerlWorkingHours();

    JSONObject queryMaterialFormulaSpecZxbzDataList(Long id);
}
