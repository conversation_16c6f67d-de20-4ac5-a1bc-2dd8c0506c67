package com.ruoyi.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.activiti.domain.ActProcessDto;
import com.ruoyi.activiti.domain.ActProcessParamDto;
import com.ruoyi.activiti.service.IProcessTaskService;
import com.ruoyi.common.constant.ProcessStatus;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.service.ICodeInfoService;
import com.ruoyi.common.util.CodeInfoUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.finance.domain.*;
import com.ruoyi.finance.mapper.*;
import com.ruoyi.finance.service.ITExpenseUserService;
import com.ruoyi.hr.mapper.TWagesMonthMapper;
import com.ruoyi.resource.domain.ResourceOfficeDz;
import com.ruoyi.resource.mapper.ResourceOfficeDzMapper;
import com.ruoyi.system.domain.SysActTaskNode;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysActTaskService;
import com.ruoyi.work.domain.MessageParams;
import com.ruoyi.work.service.MessageService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.repository.ProcessDefinition;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报销流程Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-09
 */
@Service
public class TExpenseUserServiceImpl implements ITExpenseUserService {
    @Resource
    private TExpenseUserMapper tExpenseUserMapper;
    @Resource
    private ICodeInfoService codeInfoService;
    @Resource
    private ISysActTaskService actTaskService;
    @Resource
    private RuntimeService runtimeService;
    @Resource
    private RepositoryService repositoryService;
    @Resource
    private IProcessTaskService processTaskService;
    @Resource
    private TWagesMonthMapper wagesMonthMapper;
    @Resource
    private MessageService messageService;
    @Resource
    private ResourceOfficeDzMapper officeDzMapper;
    @Resource
    private SysUserMapper sysUserMapper;

    @Override
    public List<Map<String,Object>> selectVerifyExpenseDate(TExpenseUser tExpenseUser) {
        List<TExpenseUserItemFrom> froms = new ArrayList<TExpenseUserItemFrom>();
        for(TExpenseUserItem item : tExpenseUser.getItems()){
            if(item.getFroms() != null && item.getFroms().size() > 0){
                froms.addAll(item.getFroms());
            }
        }
        List<Map<String,Object>> results = new ArrayList<Map<String,Object>>();
        if(froms != null && froms.size() > 0){
            List<TExpenseUserItemFrom> list = tExpenseUserMapper.selectVerifyExpenseDate(froms,tExpenseUser.getType());
            if(list != null && list.size() > 0){
                for(TExpenseUserItemFrom item : list){
                    if(tExpenseUser.getId() == null || item.getExpenseUserId().longValue() != tExpenseUser.getId().longValue()){
                        Map<String,Object> result = new HashMap<String,Object>();
                        result.put("name",item.getFromName());
                        results.add(result);
                    }
                }
            }
        }

        return results;
    }

    @Override
    public List<TExpenseUser> selectExpenseUserList(TExpenseUser tExpenseUser) {
        return tExpenseUserMapper.selectExpenseUserList(tExpenseUser);
    }
    @Override
    public List<TExpenseUser> selectExpenseUserFeeList(TExpenseUser tExpenseUser) {
        return tExpenseUserMapper.selectExpenseUserFeeList(tExpenseUser);
    }

    @Override
    public TExpenseUser selectExpenseUserById(Long id) {
        return tExpenseUserMapper.selectExpenseUserById(id);
    }

    @Override
    public List<TExpenseUser> selectExpenseUserChooseList(TExpenseUser tExpenseUser) {
        return tExpenseUserMapper.selectExpenseUserChooseList(tExpenseUser);
    }

    @Override
    public List<TExpenseUser> selectTExpenseUserAuditList(TExpenseUser tExpenseUser) {
        if(tExpenseUser.getList() != null && tExpenseUser.getList().size() > 0){
            return tExpenseUserMapper.selectTExpenseUserAuditList(tExpenseUser);
        }else{
            return new ArrayList<TExpenseUser>();
        }
    }

    @Override
    public List<TExpenseUser> selectTExpenseUserHistoryList(TExpenseUser tExpenseUser) {
        return tExpenseUserMapper.selectTExpenseUserHistoryList(tExpenseUser);
    }

    @Override
    public List<TExpenseUser> selectTExpenseUserLogs(TExpenseUser tExpenseUser) {
        return tExpenseUserMapper.selectTExpenseUserLogs(tExpenseUser);
    }

    @Override
    @Transactional
    public Long insertTExpenseUser(TExpenseUser tExpenseUser) {
        int row = 0;
        List<String> invoiceList = new ArrayList<String>();
        if(tExpenseUser.getType1().equals("2")){
            tExpenseUser.setIsFinish(0);
        }else{
            tExpenseUser.setIsFinish(1);
        }
        if(tExpenseUser.getItems() != null && tExpenseUser.getItems().size() > 0){
            for(TExpenseUserItem item : tExpenseUser.getItems()){
                if(item.getTypes() != null && item.getTypes().size() > 0){
                    for(TExpenseUserType type : item.getTypes()){
                        if(type.getInvoices() != null && type.getInvoices().size() > 0){
                            for(TExpenseUserInvoice tExpenseUserInvoice : type.getInvoices()){
                                String invoiceNumber = tExpenseUserInvoice.getInvoiceNumber();
                                if(StringUtils.isNotNull(invoiceNumber)){
                                    invoiceList.add(invoiceNumber);
                                }
                            }
                        }
                    }
                }
            }
        }
        if(invoiceList!=null && invoiceList.size()>0){
            String result = tExpenseUserMapper.queryExpenseDataList(invoiceList,tExpenseUser.getId());
            if(StringUtils.isNotNull(result)){
                throw new RuntimeException("存在发票["+result+"]信息重复上传,请核实!");
            }
        }
        if(tExpenseUser.getId() != null){
            tExpenseUser.setUpdateTime(DateUtils.getNowDate());
            tExpenseUser.setUpdateBy(SecurityUtils.getUsername());
            row = tExpenseUserMapper.updateTExpenseUser(tExpenseUser);
            tExpenseUserMapper.deleteTMaterialPurchaseUserProjectData(tExpenseUser.getId());
        }else{
            if(StringUtils.isEmpty(tExpenseUser.getVoucherCode())){
                if(tExpenseUser.getType().equals("0")){
                    if(tExpenseUser.getSource().intValue() == 0){
                        tExpenseUser.setVoucherCode(codeInfoService.voucherCode(CodeInfoUtil.FB));
                    }else{
                        tExpenseUser.setVoucherCode(codeInfoService.voucherCode(CodeInfoUtil.FBD));
                    }
                }else if(tExpenseUser.getType().equals("1")){
                    if(tExpenseUser.getSource().intValue() == 0){
                        tExpenseUser.setVoucherCode(codeInfoService.voucherCode(CodeInfoUtil.FP));
                    }else{
                        tExpenseUser.setVoucherCode(codeInfoService.voucherCode(CodeInfoUtil.FPD));
                    }
                }

            }
            tExpenseUser.setCreateTime(DateUtils.getNowDate());
            tExpenseUser.setCreateBy(SecurityUtils.getUsername());
            tExpenseUser.setUserId(SecurityUtils.getUserId());
            row = tExpenseUserMapper.insertTExpenseUser(tExpenseUser);
        }
        if(row > 0) {
            Long userId = null;
            tExpenseUserMapper.deleteTExpenseUserItemFrom(tExpenseUser.getId());
            tExpenseUserMapper.deleteTExpenseUserInvoiceItem(tExpenseUser.getId());
            tExpenseUserMapper.deleteTExpenseUserInvoice(tExpenseUser.getId());
            tExpenseUserMapper.deleteTExpenseUserFile(tExpenseUser.getId());
            tExpenseUserMapper.deleteTExpenseUserType(tExpenseUser.getId());
            tExpenseUserMapper.deleteTExpenseUserItem(tExpenseUser.getId());
            String projectDatas = tExpenseUser.getProjectDatas();
            if(StringUtils.isNotNull(projectDatas)){
                JSONArray array = JSONArray.parseArray(projectDatas);
                for(int i = 0;i<array.size();i++){
                    JSONObject obj = array.getJSONObject(i);
                    obj.put("userName",SecurityUtils.getUsername());
                    obj.put("dataId",tExpenseUser.getId());
                }
                if(array!=null && array.size()>0){
                    tExpenseUserMapper.batchUserApplyProjectDatas(array);
                }
            }
            if(tExpenseUser.getItems() != null && tExpenseUser.getItems().size() > 0){
                for(TExpenseUserItem item : tExpenseUser.getItems()){
                    userId = item.getUserId();
                    if(userId!=null){
                        SysUser sysUser = sysUserMapper.selectUserById(userId);
                        if(sysUser!=null){
                            item.setDeptId(sysUser.getDeptId());
                        }
                    }
                    item.setExpenseUserId(tExpenseUser.getId());
                    tExpenseUserMapper.insertTExpenseUserItem(item);

                    List<TExpenseUserItemFrom> froms = item.getFroms();
                    if(CollUtil.isNotEmpty(froms)){
                        for (TExpenseUserItemFrom from : froms) {
                            from.setExpenseUserId(item.getExpenseUserId());
                            from.setExpenseUserItemId(item.getId());

                            if(from.getFromType().equals("8")) {
                                ResourceOfficeDz dz = new ResourceOfficeDz();
                                dz.setId(from.getFromId());
                                dz.setFkId(tExpenseUser.getId());
                                officeDzMapper.updateResourceOfficeDz(dz);
                            }
                        }
                        tExpenseUserMapper.insertTExpenseUserItemFrom(item.getFroms());
                    }
                    if(item.getTypes() != null && item.getTypes().size() > 0){
                        for(TExpenseUserType type : item.getTypes()){
                            type.setExpenseUserId(item.getExpenseUserId());
                            type.setExpenseUserItemId(item.getId());
                            tExpenseUserMapper.insertTExpenseUserType(type);

                            if(type.getFiles() != null && type.getFiles().size() > 0){
                                for(int i=0;i<type.getFiles().size();i++){
                                    type.getFiles().get(i).setExpenseUserId(type.getExpenseUserId());
                                    type.getFiles().get(i).setExpenseUserTypeId(type.getId());
                                }
                                tExpenseUserMapper.insertTExpenseUserFile(type.getFiles());
                            }
                            if(type.getInvoices() != null && type.getInvoices().size() > 0){
                                for(TExpenseUserInvoice tExpenseUserInvoice : type.getInvoices()){
                                    tExpenseUserInvoice.setExpenseUserId(type.getExpenseUserId());
                                    tExpenseUserInvoice.setExpenseUserTypeId(type.getId());
                                    String invoiceNumber = tExpenseUserInvoice.getInvoiceNumber();
                                    if(StringUtils.isNotNull(invoiceNumber)){
                                        invoiceList.add(invoiceNumber);
                                    }
                                    tExpenseUserMapper.insertTExpenseUserInvoice(tExpenseUserInvoice);
                                    if(tExpenseUserInvoice.getItems() != null && tExpenseUserInvoice.getItems().size() > 0){
                                        for(int i=0;i<tExpenseUserInvoice.getItems().size();i++){
                                            tExpenseUserInvoice.getItems().get(i).setExpenseUserId(type.getExpenseUserId());
                                            tExpenseUserInvoice.getItems().get(i).setExpenseUserInvoiceId(tExpenseUserInvoice.getId());
                                        }
                                        tExpenseUserMapper.insertTExpenseUserInvoiceItem(tExpenseUserInvoice.getItems());
                                    }
                                }
                            }
                        }
                    }
                }
            }
            //直接申请
            if(tExpenseUser.getAuditStatus().equals(ProcessStatus.SHZ)){
                tExpenseUser.setUserId(userId);
                this.submitAudit(tExpenseUser);
            }
        }
        return tExpenseUser.getId();
    }

    @Override
    public int updateTExpenseUser(TExpenseUser tExpenseUser) {
        tExpenseUserMapper.deleteTExpenseUserFile(tExpenseUser.getId());
        if(tExpenseUser.getItems() != null && tExpenseUser.getItems().size() > 0){
            for(TExpenseUserItem item : tExpenseUser.getItems()){
                tExpenseUserMapper.deleteTExpenseUserTypes(item.getId(),item.getTypes());
                if(item.getTypes() != null && item.getTypes().size() > 0){
                    for(TExpenseUserType type : item.getTypes()) {
                        if(type.getId() == null){
                            type.setExpenseUserId(item.getExpenseUserId());
                            type.setExpenseUserItemId(item.getId());
                            tExpenseUserMapper.insertTExpenseUserType(type);
                        }else{
                            tExpenseUserMapper.updateTExpenseUserType1(type);
                        }


                        if(type.getInvoices() != null && type.getInvoices().size() > 0){
                            for(TExpenseUserInvoice invoice : type.getInvoices()){
                                invoice.setExpenseUserTypeId(type.getId());
                                tExpenseUserMapper.updateTExpenseUserInvoice(invoice);
                            }
                        }

                        if(type.getFiles() != null && type.getFiles().size() > 0){
                            for(int i=0;i<type.getFiles().size();i++){
                                type.getFiles().get(i).setExpenseUserTypeId(type.getId());
                            }
                            tExpenseUserMapper.insertTExpenseUserFile(type.getFiles());
                        }
                    }

                }
            }
        }
        return 1;
    }

    @Override
    public void supplement(TExpenseUser tExpenseUser) {
        TExpenseUser expense = new TExpenseUser();
        expense.setId(tExpenseUser.getId());
        expense.setIsFinish(1);
        tExpenseUserMapper.updateTExpenseUser(expense);
        tExpenseUserMapper.deleteTExpenseUserFile(tExpenseUser.getId());
        if(tExpenseUser.getItems() != null && tExpenseUser.getItems().size() > 0) {
            for (TExpenseUserItem item : tExpenseUser.getItems()) {
                if (item.getTypes() != null && item.getTypes().size() > 0) {
                    for (TExpenseUserType type : item.getTypes()) {
                        tExpenseUserMapper.updateTExpenseUserType(type);
                        if(type.getFiles() != null && type.getFiles().size() > 0){
                            for(int i=0;i<type.getFiles().size();i++){
                                type.getFiles().get(i).setExpenseUserId(tExpenseUser.getId());
                                type.getFiles().get(i).setExpenseUserTypeId(type.getId());
                            }
                            tExpenseUserMapper.insertTExpenseUserFile(type.getFiles());
                        }
                    }
                }
            }
        }

        TExpenseUser expenseUser = tExpenseUserMapper.selectExpenseUserById(tExpenseUser.getId());
        //1 是预付款
        if("1".equals(expenseUser.getType())) {
            String workUserId = "jinchunfeng";
            if(expenseUser.getCompanyCode().equals("CCOMPANY_YN") || expenseUser.getCompanyCode().equals("COMPANY_YS")) {
                workUserId = "jixinyi";
            } else if(expenseUser.getCompanyCode().equals("COMPANY_YC")) {
                workUserId = "tangjia";
            }
            MessageParams msg = new MessageParams();
            Set<String> workUserIds = new HashSet<>();
            workUserIds.add(workUserId);
            msg.setWorkUserIds(workUserIds);
            msg.setMsgType("textcard");
            msg.setTitle("预付款发票上传提醒");
            msg.setDescription("预付款(" + tExpenseUser.getVoucherCode() + ") 材料补充。");
            msg.setUrl("https://enow.enowmill.com");

            messageService.sendWorkMessage(msg);
        }
    }

    @Override
    public void updateTExpenseUserInvoice(TExpenseUserInvoice tExpenseUserInvoice) {
        tExpenseUserMapper.updateTExpenseUserInvoice(tExpenseUserInvoice);
    }

    @Override
    public void pigeonhole(List<Long> ids, String finishUser) {
        tExpenseUserMapper.updateTExpenseUserByIds(ids,finishUser);
    }

    @Override
    public void updateTExpenseUserItem(TExpenseUserItem tExpenseUserItem) {
        tExpenseUserMapper.updateTExpenseUserItem(tExpenseUserItem);
    }

    @Override
    public void submitAudit(TExpenseUser expenseUser) {
        List<Long> userIds = tExpenseUserMapper.selectTExpenseUserItemUserId(expenseUser.getId());
        try{
            ActProcessParamDto param = new ActProcessParamDto();
            param.setUserId(userIds.get(0));
            param.setCompanyCode(tExpenseUserMapper.selectTExpenseUserCompanyCode(expenseUser.getId()));
            ActProcessDto dto = new ActProcessDto();
            dto.setProcessDefinitionKey(expenseUser.getProcessDefinitionKey());
            dto.setProcessInstanceId(expenseUser.getInstanceId());
            dto.setBusinessKey(expenseUser.getId().toString());
            dto.setParam(param);
            dto.setIsRebootElement(true);
            String processInstanceId = processTaskService.submitProcessTask(dto);
            if(!StringUtils.isEmpty(processInstanceId)){
                TExpenseUser params = new TExpenseUser();
                params.setId(expenseUser.getId());
                params.setInstanceId(processInstanceId);
                params.setAuditStatus(ProcessStatus.SHZ);
                tExpenseUserMapper.updateTExpenseUser(params);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void cancelAudit(TExpenseUser expenseUser) {
        TExpenseUser expense = tExpenseUserMapper.selectExpenseUserById(expenseUser.getId());
        expenseUser.setAuditStatus(ProcessStatus.DSH);
        tExpenseUserMapper.updateTExpenseUser(expenseUser);

        if(!expense.getAuditStatus().equals(ProcessStatus.PASS)){
            //删除申请实例
            runtimeService.deleteProcessInstance(expense.getInstanceId(),"主动撤销");
        }
    }

    @Override
    public List<SysActTaskNode> selectTaskNode(TExpenseUser expenseUser) {
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionKey(expenseUser.getProcessDefinitionKey()).singleResult();
        ActProcessParamDto param = new ActProcessParamDto();
        param.setUserId(expenseUser.getUserId());
        param.setCompanyCode(expenseUser.getCompanyCode());
        List<SysActTaskNode> taskNodes = actTaskService.selectSysActTaskNodeList(processDefinition.getId(),param);
        return taskNodes;
    }

    @Override
    public void updateExpenseUserPrintNum(Long id) {
        tExpenseUserMapper.updateExpenseUserPrintNum(id);
    }

    @Override
    public List<JSONObject> getExpenseUserApplyType(TExpenseUser tExpenseUser) {
        List<JSONObject> list = tExpenseUserMapper.getExpenseUserApplyType(tExpenseUser);
        Collections.sort(list, (o1, o2) -> {
            Integer s1 =  o1.getInteger("strLength");
            Integer s2 = o2.getInteger("strLength");
            if(s1>s2){
                return 1;
            }else if(s1<s2){
                return -1;
            }else{
                String t1 = o1.getString("firstChar");
                String t2 = o2.getString("firstChar");
                return t1.compareTo(t2);
            }
        });
        return list;
    }

    public void pass(Long id) {
        TExpenseUser params = new TExpenseUser();
        params.setId(id);
        params.setAuditStatus(ProcessStatus.PASS);
        params.setPassTime(DateUtils.getNowDate());
        tExpenseUserMapper.updateTExpenseUser(params);

        TExpenseUser tExpenseUser = tExpenseUserMapper.selectExpenseUserById(id);

        if("2".equals(tExpenseUser.getType1()) && StringUtils.isNotEmpty(tExpenseUser.getWorkUserId())) {
            MessageParams msg = new MessageParams();
            Set<String> workUserIds = new HashSet<>();
            workUserIds.add(tExpenseUser.getWorkUserId());
            msg.setWorkUserIds(workUserIds);
            msg.setMsgType("textcard");
            msg.setTitle("预付款发票上传提醒-来自财务部门");
            msg.setDescription("预付款(" + tExpenseUser.getVoucherCode() + ")已审核通过，请尽快回传发票，感谢您的配合。");
            msg.setUrl("https://enow.enowmill.com");

            messageService.sendWorkMessage(msg);
        }
        wagesMonthMapper.updateWagesMonthTrend3(id);
    }

    public void reject(Long id,String msg) {
        TExpenseUser params = new TExpenseUser();
        params.setId(id);
        params.setAuditStatus(ProcessStatus.REJECT);
        tExpenseUserMapper.updateTExpenseUser(params);
    }

    @Resource
    private FinancePaysumDeptMapper financePaysumDeptMapper;
    @Resource
    private FinancePaysumTypeMapper financePaysumTypeMapper;
    @Resource
    private FinancePaysumAmountMapper financePaysumAmountMapper;
    @Resource
    private SysDeptMapper sysDeptMapper;

    @Override
    public void queryPaysumData(Map<String, Object> map) {
        String date = DateUtils.dateTimeNow("yyyy-MM-dd");
        if (map.get("date")!= null){
            date = map.get("date").toString();
        }
        // 清理数据
        financePaysumDeptMapper.clearDept(date);
        financePaysumTypeMapper.clearType(date);
        financePaysumAmountMapper.clearAmount(date);

        // ****************************************初始化数据 start**********************************************
        SysDept dept = new SysDept();
        dept.setDeptNo("YN");
        Long deptId = sysDeptMapper.selectDeptListFy(dept).get(0).getDeptId();

        List<SysDept> topDepts = tExpenseUserMapper.getTopDept(deptId);
        for (SysDept topDept : topDepts) {
            FinancePaysumDept summaryDept = new FinancePaysumDept();
            BeanUtils.copyProperties(topDept,summaryDept,"Id","createTime");
            summaryDept.setCreateTime(DateUtils.parseDate(date));
            financePaysumDeptMapper.insertFinancePaysumDept(summaryDept);
            List<SysDept> depts = tExpenseUserMapper.getTopDept(topDept.getDeptId()); // 二级
            topDept.setParentId(topDept.getDeptId());
            depts.add(topDept); // 添加一级部门作为二级部门
            for (SysDept deptTow : depts){
                FinancePaysumDept summaryDeptTow = new FinancePaysumDept();
                BeanUtils.copyProperties(deptTow,summaryDeptTow,"Id","createTime");
                summaryDeptTow.setParentId(summaryDept.getDeptId());
                summaryDeptTow.setCreateTime(DateUtils.parseDate(date));
                financePaysumDeptMapper.insertFinancePaysumDept(summaryDeptTow);
            }
        }
        tExpenseUserMapper.getSummaryType(date); // 获取费用类型

        FinancePaysumDept summaryDept = new FinancePaysumDept();
        summaryDept.setDate(date);
        summaryDept.setParentId(deptId);
        List<FinancePaysumDept> topDept = financePaysumDeptMapper.selectFinancePaysumDeptList(summaryDept);
        summaryDept.setParentId(topDept.get(topDept.size()-1).getDeptId()); // 总经办
        List<FinancePaysumDept> topDept2 = financePaysumDeptMapper.selectFinancePaysumDeptList(summaryDept);
        Long topDeptId = topDept2.get(0).getId();

        List<Map<String,Object>> queryData = tExpenseUserMapper.queryPaysumData(map);
        // 按单号分组
        Map<String, List<Map<String, Object>>> voucherGroup = queryData.stream().collect(Collectors.groupingBy(e -> e.get("voucherCode").toString()));
        for (Map<String, Object> obj : queryData) {
            FinancePaysumAmount summaryAmount = new FinancePaysumAmount();
            summaryAmount.setYear(obj.get("year").toString());
            summaryAmount.setMonth(obj.get("month").toString());
            summaryAmount.setPayWay(Integer.valueOf(obj.get("payWay").toString()));
            summaryAmount.setTypeId(financePaysumTypeMapper.selectFinancePaysumTypeByType(Long.valueOf(obj.get("type").toString()),date).getId());

            summaryAmount.setAmountUnpaid(new BigDecimal(obj.get("applyAmount").toString()));
            summaryAmount.setAmountReimbur(new BigDecimal(obj.get("payAmount").toString()));
            String voucherCode = obj.get("voucherCode").toString();
            summaryAmount.setVoucherCode(voucherCode);
            BigDecimal applyAmount = new BigDecimal(obj.get("applyAmount").toString()); // 申请金额
            BigDecimal sumAmount = new BigDecimal(obj.get("sumAmount").toString()); // item统计的金额
            BigDecimal payAmount = new BigDecimal(obj.get("payAmount").toString()); // 实际支付金额
            //计算金额： applyAmount/ sum(applyAmount) = 支付比例
            //sumAmount * 支付比例 = 实付金额
            if(sumAmount.compareTo(payAmount)!=0){
                List<Map<String, Object>> voucherMaps = voucherGroup.get(voucherCode);
                BigDecimal totalAmount = voucherMaps.stream().map(p ->  new BigDecimal(p.get("applyAmount").toString())).reduce(BigDecimal.ZERO, BigDecimal::add); // 使用 reduce 求和
                BigDecimal multiply = sumAmount.multiply((applyAmount.divide(totalAmount, 2, RoundingMode.HALF_UP)));  // 计算支付金额 保留小数点后两位
                summaryAmount.setAmountReimbur(multiply);
            }

            // 获取部门id
            if("YN".equals(obj.get("deptNo").toString())){
                summaryAmount.setDeptId(topDeptId);
            }else{
                FinancePaysumDept splitDept = new FinancePaysumDept();
                splitDept.setDate(date);
                String[] split = obj.get("ancestors").toString().split(",");
                String deptID = obj.get("deptId").toString();
                String dept_split;
                if(split.length>3){
                    dept_split = split[3];
                }else if(split.length == 3){
                    dept_split = deptID;
                }else{
                    dept_split = deptID;
                    splitDept.setParentId(Long.valueOf(dept_split)); // 本身作为二级部门
                }
                splitDept.setDeptId(Long.valueOf(dept_split));
                List<FinancePaysumDept> summaryDepts = financePaysumDeptMapper.selectFinancePaysumDeptList(splitDept);
                if(!summaryDepts.isEmpty()){
                    summaryAmount.setDeptId(summaryDepts.get(0).getId());
                }
            }

            summaryAmount.setCreateTime(DateUtils.parseDate(date));
            financePaysumAmountMapper.insertFinancePaysumAmount(summaryAmount);
        }

    }

}
