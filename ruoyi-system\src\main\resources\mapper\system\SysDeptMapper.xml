<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysDeptMapper">

	<sql id="selectDeptVo">
		SELECT
			d.dept_id,
			d.parent_id,
			d.ancestors,
			d.dept_name,
			d.dept_no,
			d.dept_code,
			d.order_num,
			d.leader,
			d.phone,
			d.email,
			d.STATUS,
			d.work_dept_id,
			d.parent_work_dept_id,
			d.del_flag,
			d.create_by,
			d.create_time,
			CASE
				WHEN IFNULL(d1.dept_name,'') = '' THEN d.dept_name
				WHEN IFNULL(d1.dept_name,'') = '宜侬科技' THEN d2.dept_name
				ELSE d1.dept_name
					END `deptName1`,
			CASE
				WHEN IFNULL(d1.dept_name,'') = '' THEN ''
				WHEN IFNULL(d1.dept_name,'') = '宜侬科技' THEN d.dept_name
				ELSE d2.dept_name
				END `deptName2`,

			CASE
				WHEN IFNULL(d1.dept_name,'') = '' THEN ''
				WHEN IFNULL(d1.dept_name,'') = '宜侬科技' THEN ''
				ELSE d.dept_name
				END `deptName3`,
			(SELECT COUNT(u.`user_id`) FROM sys_user u WHERE u.dept_id = d.dept_id AND u.`status` = 0) `inUserNum`,
			(SELECT COUNT(u.`user_id`) FROM sys_user u WHERE u.dept_id = d.dept_id AND u.`status` = 0 AND timestampdiff(day,u.create_time,NOW()) &lt; 30) `newUserNum`,
			(SELECT COUNT(u.`user_id`) FROM sys_user u WHERE u.dept_id = d.dept_id AND u.`status` = 1 AND timestampdiff(day,u.create_time,NOW()) &lt; 30) `outUserNum`
		FROM
			sys_dept d
			LEFT JOIN sys_dept d2 ON d2.dept_id = d.parent_id
			LEFT JOIN sys_dept d1 ON d1.dept_id = d2.parent_id
    </sql>

	<select id="selectDeptList" parameterType="SysDept" resultType="SysDept">
        <include refid="selectDeptVo"/>
        where d.del_flag = '0'
        <if test="parentId != null and parentId != 0">
			AND d.parent_id = #{parentId}
		</if>
		<if test="deptName != null and deptName != ''">
			AND d.dept_name like concat('%', #{deptName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND d.status = #{status}
		</if>
<!--		&lt;!&ndash; 数据范围过滤 &ndash;&gt;-->
<!--		${params.dataScope}-->
		order by d.parent_id, d.order_num
    </select>

	<select id="selectDeptListFy" parameterType="SysDept" resultType="SysDept">
         select 	d.dept_id,
				   d.parent_id,
				   d.ancestors,
				   d.dept_name,
				   d.dept_no,
				   d.dept_code,
				   d.order_num,
				   d.leader from sys_dept_back d where d.dept_no = #{deptNo} and d.del_flag = '0' limit 1
	</select>

	<select id="selectDeptByNo" resultType="SysDept" parameterType="string">
		select dept_id, dept_name from sys_dept where del_flag='0' and dept_no  = #{deptNo}
	</select>

    <select id="selectDeptListByRoleId" resultType="Integer">
		select d.dept_id
		from sys_dept d
            left join sys_role_dept rd on d.dept_id = rd.dept_id
        where rd.role_id = #{roleId}
            <if test="deptCheckStrictly">
              and d.dept_id not in (select d.parent_id from sys_dept d inner join sys_role_dept rd on d.dept_id = rd.dept_id and rd.role_id = #{roleId})
            </if>
		order by d.parent_id, d.order_num
	</select>

    <select id="selectDeptById" parameterType="Long" resultType="SysDept">
		<include refid="selectDeptVo"/>
		where d.dept_id = #{deptId}
	</select>

    <select id="checkDeptExistUser" parameterType="Long" resultType="int">
		select count(1) from sys_user where dept_id = #{deptId} and del_flag = '0'
	</select>

    <select id="hasChildByDeptId" parameterType="Long" resultType="int">
		select count(1) from sys_dept
		where del_flag = '0' and parent_id = #{deptId} limit 1
	</select>

    <select id="selectChildrenDeptById" parameterType="Long" resultType="SysDept">
		select * from sys_dept where find_in_set(#{deptId}, ancestors)
	</select>

	<select id="selectChildrenDeptByIds" parameterType="Long" resultType="SysDept">
		SELECT d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.dept_no
		FROM sys_dept d
		WHERE d.status = 0
		AND d.del_flag = 0
		AND (
		<foreach item="deptId" collection="array" open="(" separator=" OR " close=")">
			FIND_IN_SET(#{deptId}, d.ancestors) > 0
		</foreach>
		)
		ORDER BY d.dept_id DESC
	</select>

    <select id="selectNormalChildrenDeptById" parameterType="Long" resultType="int">
		select count(*) from sys_dept where status = 0 and del_flag = '0' and find_in_set(#{deptId}, ancestors)
	</select>

    <select id="checkDeptNameUnique" resultType="SysDept">
	    <include refid="selectDeptVo"/>
		where d.dept_name=#{deptName} and d.parent_id = #{parentId} limit 1
	</select>

    <insert id="insertDept" parameterType="SysDept" useGeneratedKeys="true" keyProperty="deptId" >
 		insert into sys_dept(
 			<if test="deptId != null and deptId != 0">dept_id,</if>
 			<if test="parentId != null and parentId != 0">parent_id,</if>
 			<if test="deptName != null and deptName != ''">dept_name,</if>
		    <if test="deptNo != null and deptNo != ''">dept_no,</if>
			<if test="deptCode != null and deptCode != ''">dept_code,</if>
 			<if test="ancestors != null and ancestors != ''">ancestors,</if>
 			<if test="orderNum != null and orderNum != ''">order_num,</if>
 			<if test="leader != null and leader != ''">leader,</if>
 			<if test="phone != null and phone != ''">phone,</if>
 			<if test="email != null and email != ''">email,</if>
 			<if test="status != null">status,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			create_time
 		)values(
 			<if test="deptId != null and deptId != 0">#{deptId},</if>
 			<if test="parentId != null and parentId != 0">#{parentId},</if>
 			<if test="deptName != null and deptName != ''">#{deptName},</if>
			<if test="deptNo != null and deptNo != ''">#{deptNo},</if>
			<if test="deptCode != null and deptCode != ''">#{deptCode},</if>
 			<if test="ancestors != null and ancestors != ''">#{ancestors},</if>
 			<if test="orderNum != null and orderNum != ''">#{orderNum},</if>
 			<if test="leader != null and leader != ''">#{leader},</if>
 			<if test="phone != null and phone != ''">#{phone},</if>
 			<if test="email != null and email != ''">#{email},</if>
 			<if test="status != null">#{status},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			sysdate()
 		)
	</insert>

    <update id="updateDept" parameterType="SysDept">
 		update sys_dept
 		<set>
 			<if test="parentId != null and parentId != 0">parent_id = #{parentId},</if>
 			<if test="deptName != null and deptName != ''">dept_name = #{deptName},</if>
			<if test="deptNo != null and deptNo != ''">dept_no = #{deptNo},</if>
			<if test="deptCode != null and deptCode != ''">dept_code = #{deptCode},</if>
 			<if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
 			<if test="orderNum != null and orderNum != ''">order_num = #{orderNum},</if>
 			<if test="leader != null">leader = #{leader},</if>
 			<if test="phone != null">phone = #{phone},</if>
 			<if test="email != null">email = #{email},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			update_time = sysdate()
 		</set>
 		where dept_id = #{deptId}
	</update>

    <update id="updateDeptChildren" parameterType="List">
	    update sys_dept set ancestors =
	    <foreach collection="depts" item="item" index="index"
	        separator=" " open="case dept_id" close="end">
	        when #{item.deptId} then #{item.ancestors}
	    </foreach>
	    where dept_id in
	    <foreach collection="depts" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.deptId}
        </foreach>
    </update>

    <update id="updateDeptStatusNormal" parameterType="Long">
        update sys_dept set status = '0' where dept_id in
        <foreach collection="array" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </update>

    <select id="selectDeptByParentId" parameterType="Long" resultType="SysDept">
        SELECT dept_id,
               parent_id,
               dept_name
        FROM sys_dept
		where `del_flag` = 0 and `status` = 0
		    and parent_id = #{parentId}
	</select>

	<select id="selectDeptListByIds" resultType="SysDept" >
		SELECT
			d.dept_id,
			d.parent_id,
			d.ancestors,
			d.dept_name,
			d.dept_no
		from
			sys_dept d
		<where>
			d.status = 0 and d.del_flag = 0
			<if test="deptIds != null and deptIds.size() > 0" >
				and d.dept_id in
				<foreach collection="deptIds" item="deptId" separator="," open="(" close=")" >
					#{deptId}
				</foreach>
			</if>
		</where>
		order by d.dept_id desc
	</select>

    <delete id="deleteDeptById" parameterType="Long">
		update sys_dept set del_flag = '2' where dept_id = #{deptId}
	</delete>

	<select id="queryProjectItem" resultType="String" parameterType="Long">
		select PROJECT_ITEM_TYPE from t_project_item_dept where DEPT_ID = #{deptId} and del_flag = 0
	</select>

	<delete id="deleteDeptProjectItemInfo" parameterType="Long">
		delete from t_project_item_dept where DEPT_ID = #{deptId}
	</delete>

	<insert id="batchInsertDeptProjectItemInfo" parameterType="Map">
        insert into t_project_item_dept(DEPT_ID,PROJECT_ITEM_TYPE,create_by,create_time)
        values
        <foreach collection="list" separator="," item="dept">
            (#{dept.deptId},#{dept.type},#{dept.createBy},now())
        </foreach>
    </insert>

    <select id="queryDeptIdByDeptName" resultType="Long" parameterType="String">
        select dept_id
        from sys_dept
        where dept_name = #{deptName}
          and del_flag = 0 limit 1
    </select>

	<select id="selectTopDeptIds" resultType="Long" >
		SELECT
			dept_id
		FROM
			sys_dept
		WHERE
			parent_id = ( SELECT dept_id FROM sys_dept WHERE parent_id = 0 AND del_flag = 0 )
	</select>

	<select id="selectNewWorkDept" resultType="SysDept" >
		SELECT
			d.dept_id,
			d.parent_id,
			d.order_num,
			d.dept_name,
			d.ancestors,
			d.work_dept_id,
			d.parent_work_dept_id
		FROM
			sys_dept d
		WHERE
			d.del_flag = 0 and work_dept_id is null
		order by d.parent_id asc
	</select>

	<select id="selectUserDeptInfo" resultType="com.alibaba.fastjson.JSONObject" parameterType="java.lang.Long">
		SELECT u.nick_name nickName,CASE
					WHEN IFNULL(d1.dept_name,'') = '' THEN d3.dept_id
					WHEN IFNULL(d1.dept_name,'') = '宜侬科技' THEN d2.dept_id
					ELSE d1.dept_id
			END `deptId`,
		    CASE
							  WHEN IFNULL(d1.dept_name,'') = '' THEN d3.dept_no
							  WHEN IFNULL(d1.dept_name,'') = '宜侬科技' THEN d2.dept_no
							  ELSE d1.dept_no
				   END `deptNo`,
			   u.dept_id userDeptId
		from  sys_user u
				  LEFT JOIN sys_dept d3 ON d3.dept_id = u.dept_id
				  LEFT JOIN sys_dept d2 ON d2.dept_id = d3.parent_id
				  LEFT JOIN sys_dept d1 ON d1.dept_id = d2.parent_id
		where  u.user_id = #{userId}
	</select>

</mapper>
