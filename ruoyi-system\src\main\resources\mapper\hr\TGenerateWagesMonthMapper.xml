<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.hr.mapper.TGenerateWagesMonthMapper">

    <!--u.company_code `companyCode`,  2023-12-08 更改为    IFNULL(ams.company_code,u.company_code) `companyCode`,
						IFNULL(ams.labor_companies_id,u.labor_companies_id) laborCompaniesId,-->
    <sql id="selectTWagesMonthCalculateVo">
        SELECT
            u.user_id,
            u.positive_status,
            ifnull(uw.wages_type,'0') `baseType`,
            ifnull(ams.user_type,u.user_type) `userType`,
            IFNULL(uw.wages_type,0) `wagesType`,
            IFNULL(u.`is_overtime`,-1) `isOvertime`,
            if(d1.parent_id = 0,d.dept_id,substring_index(substring_index(d.ancestors,',',3),',',-1)) `deptId`,
            ams.dept_id `userDeptId`,
            IFNULL(ams.company_code,u.company_code) `companyCode`,
            ams.labor_companies_id `laborCompaniesId`,
            IFNULL(ams.attendance_day_formal,0) `actual_attendance_day`,
            IFNULL(ams.month_weekday,0) `ahould_attendance_day`,
            IFNULL(ams.incumbency_weekday,0) `incumbency_weekday`,
            IFNULL(ams.attendance_day_hour,0) `actual_attendance_hour`,
            IFNULL(AES_DECRYPT(FROM_BASE64(uw.wages_base),'ym3JB46gfD'),0) `base_fee`,
            IFNULL(AES_DECRYPT(FROM_BASE64(uw.wages_post),'ym3JB46gfD'),0) `post_fee`,
            IFNULL(AES_DECRYPT(FROM_BASE64(uw.wages_assess),'ym3JB46gfD'),0) `assess_fee`,
            IFNULL(AES_DECRYPT(FROM_BASE64(uw.wages_bgjbf),'ym3JB46gfD'),0) `bgjbf_fee`,
            IFNULL(AES_DECRYPT(FROM_BASE64(uw.wages_cart),'ym3JB46gfD'),0) `jtbt_fee`,
            IFNULL(AES_DECRYPT(FROM_BASE64(uw.wages_rent),'ym3JB46gfD'),0) `zfbt_fee`,
            IFNULL(AES_DECRYPT(FROM_BASE64(uw.wages_heat),'ym3JB46gfD'),0) `heat_fee`,
            IFNULL(AES_DECRYPT(FROM_BASE64(uw.wages_powder),'ym3JB46gfD'),0) `powder_fee`,
            IFNULL(AES_DECRYPT(FROM_BASE64(uw.job_subsidies),'ym3JB46gfD'),0) `job_subsidies_fee`,
            IFNULL(AES_DECRYPT(FROM_BASE64(uw.wages_meal),'ym3JB46gfD'),0) `meal_fee`,

            ifnull(ams.late_work_num,0) `lateWorkNum`,
            ifnull(ams.is_full_attendance,0) `isFullAttendance`,

            ifnull(ams.weekdays_overtime_hour,0) `weekdays_overtime_hour`,
            ifnull(ams.restdays_overtime_hour,0) `restdays_overtime_hour`,
            ifnull(ams.holidays_overtime_hour,0) `holidays_overtime_hour`,
            ifnull(ads.sick_leave_pay_time,0) `sick_leave_hour`,
            ifnull(ads.things_leave_pay_time,0) `things_leave_hour`,
            ifnull(ams.leave1,0) `leave1`,
            ifnull(ams.`leave`,0) `leave`,
            ifnull(ams.leave0,0) `leave0`,
            ifnull(ads.late_pay_time,0) `lateTime`,
            ifnull(ads.early_pay_time,0) `earlyTime`,
            ifnull(ads.absenteeism_hour,0) `absenteeismHour`,
            IFNULL(FLOOR(TIMESTAMPDIFF(DAY,IFNULL(u.join_time,last_day(CONCAT(ams.date,'-01'))) ,last_day(CONCAT(ams.date,'-01'))) / 366),0) `siAge`,
            DATE_FORMAT(u.join_time,'%Y-%m-%d') joinTime,
            last_day(CONCAT(ams.date,'-01')) currTime,
            u.attendance_id `attendancePlanId`
    </sql>
    <select id="selectTAttendanceMonthUserList" resultType="TWagesMonthCalculate">
        <include refid="selectTWagesMonthCalculateVo"/>
        FROM sys_user u
         LEFT JOIN t_attendance_month_stats ams ON ams.user_id = u.user_id AND ams.date = #{date}
         LEFT JOIN t_attendance_deductions_stats ads ON ads.user_id = ams.user_id AND ads.date = ams.date and ads.user_type = ams.user_type
         LEFT JOIN sys_user_wages uw ON uw.user_id = u.user_id
         LEFT JOIN sys_dept d ON d.dept_id = ams.dept_id
         LEFT JOIN sys_dept d1 ON d1.dept_id = d.parent_id
        WHERE u.user_id in
        <foreach collection="userIds" item="userId" separator="," open="(" close=")" >
            #{userId}
        </foreach>
        GROUP BY u.user_id
        ORDER BY u.user_id ASC
    </select>

    <select id="selectTAttendanceMonthStatList" resultType="TWagesMonthCalculate" parameterType="String">
        <include refid="selectTWagesMonthCalculateVo"/>
        FROM t_attendance_month_stats ams
        LEFT JOIN t_attendance_deductions_stats ads ON ads.user_id = ams.user_id AND ads.date = ams.date and ads.user_type = ams.user_type
        LEFT JOIN sys_user u on u.user_id = ams.user_id
        LEFT JOIN sys_user_wages uw ON uw.user_id = u.user_id
        LEFT JOIN sys_dept d ON d.dept_id = ams.dept_id
        LEFT JOIN sys_dept d1 ON d1.dept_id = d.parent_id
        WHERE ams.date = #{date}
        <if test="userIds != null and userIds.length > 0" >
            and ams.user_id in
            <foreach collection="userIds" item="userId" separator="," open="(" close=")" >
                #{userId}
            </foreach>
        </if>
        GROUP BY u.user_id
        ORDER BY u.user_id ASC
    </select>
    <select id="selectUserSocialSecurity" resultType="Map" parameterType="Long">
        SELECT
            ifnull(ssi.base_prop,0) `baseFee`,
            ssi.item_id `itemId`,
            dd.dict_label `itemName`,
            ifnull(ssi.company_prop,0) `companyProp`,
            ifnull(ssi.employees_prop,0) `employeesProp`,
            ss.remark
        FROM sys_user_wages uw
                 LEFT JOIN t_social_security_plan ss ON ss.id = uw.social_security_id
                 LEFT JOIN t_social_security_plan_item ssi ON ssi.social_security_plan_id = ss.id
                 LEFT JOIN sys_dict_data dd ON dd.dict_type = 'social_security_item' AND dd.dict_value = ssi.item_id
        WHERE uw.user_id = #{userId}
        ORDER BY ssi.`item_id` ASC
    </select>
    <select id="selectUserAccumulationFund" resultType="Map" parameterType="Long">
        SELECT
            ifnull(ssi.base_prop,0) `baseFee`,
            ssi.item_id `itemId`,
            dd.dict_label `itemName`,
            ifnull(ssi.company_prop,0) `companyProp`,
            ifnull(ssi.employees_prop,0) `employeesProp`,
            ss.`remark`
        FROM sys_user_wages uw
                 LEFT JOIN t_accumulation_fund_plan ss ON ss.id = uw.accumulation_fund_id
                 LEFT JOIN t_accumulation_fund_plan_item ssi ON ssi.accumulation_fund_plan_id = ss.id
                 LEFT JOIN sys_dict_data dd ON dd.dict_type = 'accumulation_fund_item' AND dd.dict_value = ssi.item_id
        WHERE uw.user_id = #{userId}
        ORDER BY ssi.`item_id` ASC
    </select>
    <select id="selectWagesMonth" resultType="TWagesMonth" parameterType="TWagesMonth">
        select wm.id, wm.user_id, wm.`date`,
               wm.costcenter_code, wm.voucher_code,
               wm.finish_time, wm.finish_user,
               wm.images, wm.audit_status,wm.trend,
               wm.status, wm.instance_id,
               wm.initiated_time,
               wm.create_time,
               wm.remark
        from t_wages_month wm
        where wm.`date` = #{date}
    </select>
    <select id="selectDeptList" resultType="SysDept">
        SELECT
            d.dept_id,
            d.dept_name
        FROM sys_dept d
        WHERE d.del_flag = 0
          AND d.`status` = 0
          AND d.parent_id IN
              (SELECT dept_id
               FROM sys_dept
               WHERE del_flag = 0
                 AND `status` = 0
                 AND parent_id = 0)
        ORDER BY d.`order_num` ASC
    </select>
    <select id="selectTWagesMonthCompanyByWagesId" resultType="TWagesMonthCompany">
        SELECT
            wmc.id,
            wmc.wages_month_id `wagesMonthId`,
            dd.dict_value `companyCode`,
            dd.remark `companyName`
        FROM t_wages_month_company wmc
                 LEFT JOIN sys_dict_data dd ON dd.dict_type = 'COMPANY_NAME'
            AND dd.dict_value = wmc.company_code
        WHERE wmc.wages_month_id = #{wagesMonthId}
          AND wmc.company_code = #{companyCode}
    </select>
    <select id="selectTWagesMonthDeptByWagesId" resultType="TWagesMonthDept">
        SELECT
            wmd.id,
            wmd.wages_month_id `wagesMonthId`,
            d.dept_id `deptId`,
            d.dept_name `deptName`
        FROM sys_dept d
         LEFT JOIN t_wages_month_dept wmd ON wmd.dept_id = d.dept_id AND wmd.wages_month_id = #{wagesMonthId}
        WHERE d.dept_id = #{deptId}
    </select>
    <insert id="insertTWagesMonth" parameterType="TWagesMonth" useGeneratedKeys="true" keyProperty="id">
        insert into t_wages_month
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="date != null">date,</if>
            <if test="userId != null">user_id,</if>
            <if test="costcenterCode != null">costcenter_code,</if>
            <if test="voucherCode != null">voucher_code,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="date != null">#{date},</if>
            <if test="userId != null">#{userId},</if>
            <if test="costcenterCode != null">#{costcenterCode},</if>
            <if test="voucherCode != null">#{voucherCode},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>
    <delete id="deleteTWagesMonthDept">
        delete from t_wages_month_dept where wages_month_id = #{id}
    </delete>
    <insert id="insertTWagesMonthDept" parameterType="TWagesMonthDept" useGeneratedKeys="true" keyProperty="id">
        insert into t_wages_month_dept
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="wagesMonthId != null">wages_month_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="wagesMonthId != null">#{wagesMonthId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>
    <delete id="deleteTWagesMonthCompany">
        delete from t_wages_month_company where wages_month_id = #{id}
    </delete>
    <insert id="insertTWagesMonthCompany" parameterType="TWagesMonthCompany" useGeneratedKeys="true" keyProperty="id">
        insert into t_wages_month_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="wagesMonthId != null">wages_month_id,</if>
            <if test="companyCode != null">company_code,</if>
            <if test="companyName != null">company_name,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="wagesMonthId != null">#{wagesMonthId},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>
    <delete id="deleteTWagesMonthCalculate">
        DELETE FROM t_wages_month_calculate WHERE wages_month_id = #{id}
    </delete>
    <delete id="deleteTWagesMonthSocialSecuritye">
        DELETE FROM t_wages_month_social_security WHERE wages_month_id = #{id}
    </delete>
    <delete id="deleteTWagesMonthCalculateUserId">
        DELETE FROM t_wages_month_calculate WHERE wages_month_id = #{id}
        and user_id IN
        <foreach item="userId" collection="userIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
    <delete id="deleteTWagesMonthSocialSecurityeUserId">
        DELETE wmss
        FROM t_wages_month_social_security wmss
        INNER JOIN t_wages_month_calculate wmc ON wmc.id = wmss.wages_month_calculate_id
        WHERE wmss.wages_month_id = #{id}
        and wmc.user_id IN
        <foreach item="userId" collection="userIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <!--获取月工资公司列表-->
    <select id="queryMonthCompanyDataList" parameterType="java.lang.Long" resultType="com.alibaba.fastjson.JSONObject">
        SELECT id,company_code companyCode,company_name companyName from t_wages_month_company where wages_month_id = #{monthId}
    </select>

    <!--获取月工资部门列表-->
    <select id="queryMonthDeptDataList" parameterType="java.lang.Long" resultType="com.alibaba.fastjson.JSONObject">
        SELECT id,dept_id deptId,dept_name deptName from t_wages_month_dept where wages_month_id = #{monthId}
    </select>

    <!--获取月工资部门列表-->
    <select id="queryWagesMonthCalculateDataList" parameterType="java.lang.Long" resultType="TWagesMonthCalculate">
        SELECT
                wmc.id,
                wmc.user_id,
                wmc.wages_month_id,
                wmc.wages_month_dept_id,
                wmc.wages_month_company_id,
                wmc.wages_month_user_dept_id,
                wmc.user_type,
                wmc.company_pay_fee,
                IFNULL(AES_DECRYPT(FROM_BASE64(wmc.SHOULD_PAY),'ym3JB46gfD'),0) shouldPay,
                IFNULL(AES_DECRYPT(FROM_BASE64(wmc.real_pay),'ym3JB46gfD'),0)  realPay,
                IFNULL(AES_DECRYPT(FROM_BASE64(wmss.ssb_gs_fee),'ym3JB46gfD'),0)  ssbGsFee,
                IFNULL(AES_DECRYPT(FROM_BASE64(wmss.gjj_gs_fee),'ym3JB46gfD'),0)  gjjGsFee,
                (SELECT GROUP_CONCAT(p.POST_NAME) from sys_user_post up
                     INNER JOIN sys_post p on up.post_id = p.post_id
                     where up.user_id = wmc.user_id) postName,
               CASE
                    WHEN IFNULL(d1.dept_name,'') = '' THEN d3.dept_id
                    WHEN IFNULL(d1.dept_name,'') = '宜侬科技' THEN d2.dept_id
                    ELSE d1.dept_id
                    END `dept_id1`,
                CASE
                    WHEN IFNULL(d1.dept_name,'') = '' THEN ''
                    WHEN IFNULL(d1.dept_name,'') = '宜侬科技' THEN d3.dept_id
                    ELSE d2.dept_id
                    END `dept_id2`,
                CASE
                    WHEN IFNULL(d1.dept_name,'') = '' THEN ''
                    WHEN IFNULL(d1.dept_name,'') = '宜侬科技' THEN ''
                    ELSE d3.dept_id
                    END `dept_id3`
        from t_wages_month_calculate wmc
        left join t_wages_month_social_security wmss on wmss.wages_month_calculate_id = wmc.id
        LEFT JOIN sys_dept_back d3 ON d3.dept_id = wmc.wages_month_user_dept_id
        LEFT JOIN sys_dept_back d2 ON d2.dept_id = d3.parent_id
        LEFT JOIN sys_dept_back d1 ON d1.dept_id = d2.parent_id
        where wmc.wages_month_id = #{monthId}
    </select>
    <!--获取二级部门列表-->
    <select id="querySecondDeptDataList" parameterType="java.lang.Long" resultType="com.alibaba.fastjson.JSONObject">
        SELECT dept_id deptId,dept_name deptName from sys_dept_back where PARENT_ID = #{deptId} and DEL_FLAG = 0
    </select>

    <select id="queryWagesDetailInfo" parameterType="java.util.Map" resultType="com.alibaba.fastjson.JSONObject">
        SELECT count(wmc.id) userCount,
           SUM(IFNULL(AES_DECRYPT(FROM_BASE64(wmc.SHOULD_PAY),'ym3JB46gfD'),0)) shouldPay,
           SUM(IFNULL(AES_DECRYPT(FROM_BASE64(wmc.real_pay),'ym3JB46gfD'),0))  realPay,
           SUM(IFNULL(AES_DECRYPT(FROM_BASE64(wmss.ssb_gs_fee),'ym3JB46gfD'),0))  ssbGsFee,
           SUM(IFNULL(AES_DECRYPT(FROM_BASE64(wmss.gjj_gs_fee),'ym3JB46gfD'),0))  gjjGsFee
        from t_wages_month_calculate wmc
        left join t_wages_month_social_security wmss on wmss.wages_month_calculate_id = wmc.id
        where wmc.wages_month_id = #{monthId}
          and wmc.wages_month_company_id = #{wagesCompnayId}
          AND wmc.wages_month_user_dept_id = #{firstDeptId}
          AND wmc.user_type = 0
    </select>

    <select id="querySecondWagesDetailInfo" parameterType="java.util.Map" resultType="com.alibaba.fastjson.JSONObject">
        SELECT count(wmc.id) userCount,
           SUM(IFNULL(AES_DECRYPT(FROM_BASE64(wmc.SHOULD_PAY),'ym3JB46gfD'),0)) shouldPay,
           SUM(IFNULL(AES_DECRYPT(FROM_BASE64(wmc.real_pay),'ym3JB46gfD'),0))  realPay,
           SUM(IFNULL(AES_DECRYPT(FROM_BASE64(wmss.ssb_gs_fee),'ym3JB46gfD'),0))  ssbGsFee,
           SUM(IFNULL(AES_DECRYPT(FROM_BASE64(wmss.gjj_gs_fee),'ym3JB46gfD'),0))  gjjGsFee
        from t_wages_month_calculate wmc
        left join t_wages_month_social_security wmss on wmss.wages_month_calculate_id = wmc.id
        where wmc.wages_month_id = #{monthId}
          and wmc.wages_month_company_id = #{wagesCompnayId}
          AND wmc.wages_month_user_dept_id = #{secondDeptId}
          AND wmc.user_type = 0
    </select>


    <insert id="batchInsertWegesData">
        INSERT INTO  `t_company_personnel_month_cost` (
            `MONTH_ID`,
            `COST_TYPE`,
            `COMPANY_CODE`,
            `COMPANY_NAME`,
            `FIRST_DEPT_ID`,
            `SECOND_DEPT_ID`,
            `FIRST_DEPT_NAME`,
            `SECOND_DEPT_NAME`,
            `POST_NAME`,
            `USER_COUNT`,
            `SHOULD_PAY`,
            `GJJ_PAY`,
            `SHB_PAY`,
            `REAL_PAY`,
            `create_by`,
            `create_time`
        )
        VALUES
        <foreach item="item" index="index" collection="list" separator=",">
          (
            #{item.monthId},
            #{item.costType},
            #{item.companyCode},
            #{item.companyName},
            #{item.firstDeptId},
            #{item.secondDeptId},
            #{item.firstDeptName},
            #{item.secondDeptName},
            #{item.postName},
            #{item.userCount},
            #{item.shouldPay},
            #{item.gjjPay},
            #{item.shbPay},
            #{item.realPay},
            #{item.createBy},
            now()
            )
       </foreach>
    </insert>

    <delete id="deleteCompanyCostCenterData" parameterType="java.lang.Long">
        delete from t_company_costs_center where MONTH_ID = #{monthId}
    </delete>
    <delete id="deleteCompanyCostCenterHzData" parameterType="java.lang.Long">
        delete from t_company_costs_center_hz where MONTH_ID = #{monthId}
    </delete>
    <delete id="deleteCompanyCostDeptMonthData" parameterType="java.lang.Long">
        delete from t_company_personnel_month_cost where MONTH_ID = #{monthId}
    </delete>
    <delete id="deleteCompanyCostDeptMonthPostData" parameterType="java.lang.Long">
        delete from t_company_personnel_month_post_cost where MONTH_ID = #{monthId}
    </delete>
    
    <insert id="batchSaveCompanyCostCenterHzData">
        insert into t_company_costs_center_hz(`MONTH_ID`,
                                                `YEAR`,
                                                `MONTH`,
                                                `COST_MONTH`,`FORMAL_SHOULD_MONEY`,`LABOR_SHOULD_MONEY`,`COMPANY_PAY_SHB`,`COMPANY_PAY_GJJ`,
                                                `create_by`,
                                                `create_time`)
        values
            ( #{monthId},
              #{year},
              #{month},
              #{costMonth},#{formualShouldMoney},#{laborShouldMoney},#{companyPayShb},#{companyPayGjj},#{createBy},now())
    </insert>

    <insert id="batchSaveCompanyCostCenterData">
        INSERT INTO  `t_company_costs_center` (
            `MONTH_ID`,
            `YEAR`,
            `MONTH`,
            `COST_MONTH`,
            `COMPANY_CODE`,
            `COMPANY_NAME`,
            `COMPANY_CENTER_ID`,
            `COMPANY_CENTER_NAME`,
            `PERSON_NUM`,
            `SHOULD_MONEY`,
            `COMPANY_PAY_SHB`,
            `COMPANY_PAY_GJJ`,
            `create_by`,
            `create_time`
        )
        VALUES
        <foreach item="item" index="index" collection="list" separator=",">
          (
            #{item.monthId},
            #{item.year},
            #{item.month},
            #{item.costMonth},
            #{item.companyCode},
            #{item.companyName},
            #{item.companyCenterId},
            #{item.companyCenterName},
            #{item.personNum},
            #{item.shouldMoney},
            #{item.companyPayShb},
            #{item.companyPayGjj},
            #{item.createBy},
            now()
            )
       </foreach>
    </insert>

    <insert id="batchSaveCompanyCostDeptMonthData">
        INSERT INTO  `t_company_personnel_month_cost` (
            `MONTH_ID`,
            `YEAR`,
            `MONTH`,
            `COST_MONTH`,
            `COMPANY_CODE`,
            `COMPANY_NAME`,
            `IS_FIRST`,
            `DEPT_ID`,
            `DEPT_NAME`,
            `WAGES_TYPE`,
            `PERSON_NUM`,
            `SHOULD_MONEY`,
            `COMPANY_PAY_SHB`,
            `COMPANY_PAY_GJJ`,
            `TOTAL_PERSON_NUM`,
            `TOTAL_SHOULD_MONEY`,
            `TOTAL_COMPANY_PAY_SHB`,
            `TOTAL_COMPANY_PAY_GJJ`,
            `create_by`,
            `create_time`
        )
        VALUES
        <foreach item="item" index="index" collection="list" separator=",">
          (
            #{item.monthId},
            #{item.year},
            #{item.month},
            #{item.costMonth},
            #{item.companyCode},
            #{item.companyName},
            #{item.isFirst},
            #{item.companyCenterId},
            #{item.companyCenterName},
            #{item.wagesType},
            #{item.personNum},
            #{item.shouldMoney},
            #{item.companyPayShb},
            #{item.companyPayGjj},
            #{item.totalPersonNum},
            #{item.totalLhouldMoney},
            #{item.totalCompanyPayShb},
            #{item.totalCompanyPayGj},
            #{item.createBy},
            now()
            )
       </foreach>
    </insert>

    <insert id="batchSaveCompanyCostDeptMonthPostData">
        INSERT INTO  `t_company_personnel_month_post_cost` (
        `MONTH_ID`,
        `YEAR`,
        `MONTH`,
        `COST_MONTH`,
        `COMPANY_CODE`,
        `COMPANY_NAME`,
        `IS_FIRST`,
        `FIRST_DEPT_ID`,
        `FIRST_DEPT_NAME`,
        `DEPT_ID`,
        `DEPT_NAME`,
        `POST_NAME`,
        `COST_TYPE`,
        `WAGES_TYPE`,
        `PERSON_NUM`,
        `SHOULD_MONEY`,
        `REAL_MONEY`,
        `COMPANY_PAY_SHB`,
        `COMPANY_PAY_GJJ`,
        `create_by`,
        `create_time`
        )
        VALUES
        <foreach item="item" index="index" collection="list" separator=",">
            (
            #{item.monthId},
            #{item.year},
            #{item.month},
            #{item.costMonth},
            #{item.companyCode},
            #{item.companyName},
            #{item.isFirst},
            #{item.firstDeptId},
            #{item.firstDeptName},
            #{item.deptId},
            #{item.deptName},
            #{item.postName},
            #{item.costType},
            #{item.wagesType},
            #{item.personNum},
            #{item.shouldMoney},
            #{item.realMoney},
            #{item.companyPayShb},
            #{item.companyPayGjj},
            #{item.createBy},
            now()
            )
        </foreach>
    </insert>

    <select id="queryWagesLaborDataInfo" resultType="com.alibaba.fastjson.JSONObject" parameterType="java.lang.String">
        SELECT
            count(DISTINCT labor_service_companies_user_id) userCount,
            IFNULL(SUM(AES_DECRYPT(FROM_BASE64(wlc.real_pay),'ym3JB46gfD')),0) `realPay`
        FROM t_wages_labor_calculate wlc
        WHERE wlc.`status` = 1  AND DATE_FORMAT(wlc.date,'%Y-%m') = #{date}
    </select>

    <select id="queryWagesLaborDataInfoByCompany" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            count(DISTINCT labor_service_companies_user_id) userCount,
            IFNULL(SUM(AES_DECRYPT(FROM_BASE64(wlc.real_pay),'ym3JB46gfD')),0) `realPay`
        FROM t_wages_labor_calculate wlc
        WHERE wlc.`status` = 1  AND DATE_FORMAT(wlc.date,'%Y-%m') = #{date} and company_code = #{companyCode}
    </select>
</mapper>