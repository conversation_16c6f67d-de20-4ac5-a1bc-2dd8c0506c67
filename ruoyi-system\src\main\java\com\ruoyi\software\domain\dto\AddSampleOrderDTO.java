package com.ruoyi.software.domain.dto;

import javax.validation.constraints.NotNull;

/**
 * 更新打样单状态请求DTO
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public class AddSampleOrderDTO {

    /** 打样单难度类别 */
    @NotNull(message = "打样单难度类别不能为空")
    private String dylb;

    /** 实验室（0 宜侬 1 瀛彩 ） */
    @NotNull(message = "实验室不能为空")
    private String labNo;

    /** 品类 */
    @NotNull(message = "品类不能为空")
    private String categoryText;

    /** 备注 */
    private String remark;

    public String getDylb() {
        return dylb;
    }

    public void setDylb(String dylb) {
        this.dylb = dylb;
    }

    public String getLabNo() {
        return labNo;
    }

    public void setLabNo(String labNo) {
        this.labNo = labNo;
    }

    public String getCategoryText() {
        return categoryText;
    }

    public void setCategoryText(String categoryText) {
        this.categoryText = categoryText;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "UpdateSampleOrderStatusDTO{" +
                "dylb=" + dylb +
                ", labNo='" + labNo + '\'' +
                ", categoryText='" + categoryText + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
