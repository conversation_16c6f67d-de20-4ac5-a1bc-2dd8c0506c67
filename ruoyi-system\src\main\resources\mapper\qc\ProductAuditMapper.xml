<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.qc.mapper.ProductAuditMapper">

    <sql id="selectProductAuditVo">
        select id, product_id, product_code, project_no, laboratory_code, manufacturer, product_name, spec, planned_production_date, order_quantity, delivery_date, product_type, formula_stability_report, formula_stability_report_hover_tip, formula_feasibility_assessment, formula_feasibility_assessment_hover_tip, standard_formula_process, mold_tool_confirmation, mold_tool_confirmation_hover_tip, filling_packaging_feasibility, filling_packaging_feasibility_hover_tip, filling_packaging_sop, finished_product_standard, quality_agreement, quality_agreement_hover_tip, packaging_material_standard, liquid_sample, packaging_material_sample, finished_product_sample, excessive_packaging_confirmation, excessive_packaging_confirmation_hover_tip, registration_completion, registration_completion_hover_tip, formula_process_consistency, formula_process_consistency_hover_tip, documentation_consistency, documentation_consistency_hover_tip, internal_standard_compliance, del_flag, create_by, create_time, update_by, update_time, remark, can_produce, can_produce_remark, can_deliver, can_deliver_remark from product_audit
    </sql>

    <select id="selectProductAuditList" parameterType="ProductAudit" resultType="ProductAudit">
        <include refid="selectProductAuditVo"/>
        <where>
            del_flag = 0
            <if test="productCode != null  and productCode != ''"> and product_code like concat('%', #{productCode}, '%')</if>
            <if test="projectNo != null  and projectNo != ''"> and project_no like concat('%', #{projectNo}, '%')</if>
            <if test="laboratoryCode != null  and laboratoryCode != ''"> and laboratory_code = #{laboratoryCode}</if>
            <if test="manufacturer != null  and manufacturer != ''"> and manufacturer = #{manufacturer}</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="plannedProductionDate != null "> and planned_production_date = #{plannedProductionDate}</if>
            <if test="deliveryDate != null "> and delivery_date = #{deliveryDate}</if>
            <if test="productType != null  and productType != ''"> and product_type = #{productType}</if>
            <if test="registrationCompletionHoverTip != null  and registrationCompletionHoverTip != ''"> and registration_completion_hover_tip like concat('%', #{registrationCompletionHoverTip}, '%')</if>
            <if test="canProduce != null "> and can_produce = #{canProduce}</if>
            <if test="canDeliver != null "> and can_deliver = #{canDeliver}</if>
        </where>
        order by id desc
    </select>

    <select id="selectProductAuditById" parameterType="Long" resultType="ProductAudit" >
        <include refid="selectProductAuditVo"/>
        where id = #{id}
    </select>

    <insert id="insertProductAudit" parameterType="ProductAudit" useGeneratedKeys="true" keyProperty="id">
        insert into product_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="productCode != null and productCode != ''">product_code,</if>
            <if test="projectNo != null and projectNo != ''">project_no,</if>
            <if test="laboratoryCode != null">laboratory_code,</if>
            <if test="manufacturer != null">manufacturer,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="spec != null">spec,</if>
            <if test="plannedProductionDate != null">planned_production_date,</if>
            <if test="orderQuantity != null">order_quantity,</if>
            <if test="deliveryDate != null">delivery_date,</if>
            <if test="productType != null">product_type,</if>
            <if test="formulaStabilityReport != null">formula_stability_report,</if>
            <if test="formulaStabilityReportHoverTip != null">formula_stability_report_hover_tip,</if>
            <if test="formulaFeasibilityAssessment != null">formula_feasibility_assessment,</if>
            <if test="formulaFeasibilityAssessmentHoverTip != null">formula_feasibility_assessment_hover_tip,</if>
            <if test="standardFormulaProcess != null">standard_formula_process,</if>
            <if test="moldToolConfirmation != null">mold_tool_confirmation,</if>
            <if test="moldToolConfirmationHoverTip != null">mold_tool_confirmation_hover_tip,</if>
            <if test="fillingPackagingFeasibility != null">filling_packaging_feasibility,</if>
            <if test="fillingPackagingFeasibilityHoverTip != null">filling_packaging_feasibility_hover_tip,</if>
            <if test="fillingPackagingSop != null">filling_packaging_sop,</if>
            <if test="finishedProductStandard != null">finished_product_standard,</if>
            <if test="qualityAgreement != null">quality_agreement,</if>
            <if test="qualityAgreementHoverTip != null">quality_agreement_hover_tip,</if>
            <if test="packagingMaterialStandard != null">packaging_material_standard,</if>
            <if test="liquidSample != null">liquid_sample,</if>
            <if test="packagingMaterialSample != null">packaging_material_sample,</if>
            <if test="finishedProductSample != null">finished_product_sample,</if>
            <if test="excessivePackagingConfirmation != null">excessive_packaging_confirmation,</if>
            <if test="excessivePackagingConfirmationHoverTip != null">excessive_packaging_confirmation_hover_tip,</if>
            <if test="registrationCompletion != null">registration_completion,</if>
            <if test="registrationCompletionHoverTip != null">registration_completion_hover_tip,</if>
            <if test="formulaProcessConsistency != null">formula_process_consistency,</if>
            <if test="formulaProcessConsistencyHoverTip != null">formula_process_consistency_hover_tip,</if>
            <if test="documentationConsistency != null">documentation_consistency,</if>
            <if test="documentationConsistencyHoverTip != null">documentation_consistency_hover_tip,</if>
            <if test="internalStandardCompliance != null">internal_standard_compliance,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="canProduce != null">can_produce,</if>
            <if test="canProduceRemark != null">can_produce_remark,</if>
            <if test="canDeliver != null">can_deliver,</if>
            <if test="canDeliverRemark != null">can_deliver_remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId},</if>
            <if test="productCode != null and productCode != ''">#{productCode},</if>
            <if test="projectNo != null and projectNo != ''">#{projectNo},</if>
            <if test="laboratoryCode != null">#{laboratoryCode},</if>
            <if test="manufacturer != null">#{manufacturer},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="spec != null">#{spec},</if>
            <if test="plannedProductionDate != null">#{plannedProductionDate},</if>
            <if test="orderQuantity != null">#{orderQuantity},</if>
            <if test="deliveryDate != null">#{deliveryDate},</if>
            <if test="productType != null">#{productType},</if>
            <if test="formulaStabilityReport != null">#{formulaStabilityReport},</if>
            <if test="formulaStabilityReportHoverTip != null">#{formulaStabilityReportHoverTip},</if>
            <if test="formulaFeasibilityAssessment != null">#{formulaFeasibilityAssessment},</if>
            <if test="formulaFeasibilityAssessmentHoverTip != null">#{formulaFeasibilityAssessmentHoverTip},</if>
            <if test="standardFormulaProcess != null">#{standardFormulaProcess},</if>
            <if test="moldToolConfirmation != null">#{moldToolConfirmation},</if>
            <if test="moldToolConfirmationHoverTip != null">#{moldToolConfirmationHoverTip},</if>
            <if test="fillingPackagingFeasibility != null">#{fillingPackagingFeasibility},</if>
            <if test="fillingPackagingFeasibilityHoverTip != null">#{fillingPackagingFeasibilityHoverTip},</if>
            <if test="fillingPackagingSop != null">#{fillingPackagingSop},</if>
            <if test="finishedProductStandard != null">#{finishedProductStandard},</if>
            <if test="qualityAgreement != null">#{qualityAgreement},</if>
            <if test="qualityAgreementHoverTip != null">#{qualityAgreementHoverTip},</if>
            <if test="packagingMaterialStandard != null">#{packagingMaterialStandard},</if>
            <if test="liquidSample != null">#{liquidSample},</if>
            <if test="packagingMaterialSample != null">#{packagingMaterialSample},</if>
            <if test="finishedProductSample != null">#{finishedProductSample},</if>
            <if test="excessivePackagingConfirmation != null">#{excessivePackagingConfirmation},</if>
            <if test="excessivePackagingConfirmationHoverTip != null">#{excessivePackagingConfirmationHoverTip},</if>
            <if test="registrationCompletion != null">#{registrationCompletion},</if>
            <if test="registrationCompletionHoverTip != null">#{registrationCompletionHoverTip},</if>
            <if test="formulaProcessConsistency != null">#{formulaProcessConsistency},</if>
            <if test="formulaProcessConsistencyHoverTip != null">#{formulaProcessConsistencyHoverTip},</if>
            <if test="documentationConsistency != null">#{documentationConsistency},</if>
            <if test="documentationConsistencyHoverTip != null">#{documentationConsistencyHoverTip},</if>
            <if test="internalStandardCompliance != null">#{internalStandardCompliance},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="canProduce != null">#{canProduce},</if>
            <if test="canProduceRemark != null">#{canProduceRemark},</if>
            <if test="canDeliver != null">#{canDeliver},</if>
            <if test="canDeliverRemark != null">#{canDeliverRemark},</if>
        </trim>
    </insert>

    <update id="updateProductAudit" parameterType="ProductAudit">
        update product_audit
        <trim prefix="SET" suffixOverrides=",">
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productCode != null and productCode != ''">product_code = #{productCode},</if>
            <if test="projectNo != null and projectNo != ''">project_no = #{projectNo},</if>
            <if test="laboratoryCode != null">laboratory_code = #{laboratoryCode},</if>
            <if test="manufacturer != null">manufacturer = #{manufacturer},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="spec != null">spec = #{spec},</if>
            <if test="plannedProductionDate != null">planned_production_date = #{plannedProductionDate},</if>
            <if test="orderQuantity != null">order_quantity = #{orderQuantity},</if>
            <if test="deliveryDate != null">delivery_date = #{deliveryDate},</if>
            <if test="productType != null">product_type = #{productType},</if>
            <if test="formulaStabilityReport != null">formula_stability_report = #{formulaStabilityReport},</if>
            <if test="formulaStabilityReportHoverTip != null">formula_stability_report_hover_tip = #{formulaStabilityReportHoverTip},</if>
            <if test="formulaFeasibilityAssessment != null">formula_feasibility_assessment = #{formulaFeasibilityAssessment},</if>
            <if test="formulaFeasibilityAssessmentHoverTip != null">formula_feasibility_assessment_hover_tip = #{formulaFeasibilityAssessmentHoverTip},</if>
            <if test="standardFormulaProcess != null">standard_formula_process = #{standardFormulaProcess},</if>
            <if test="moldToolConfirmation != null">mold_tool_confirmation = #{moldToolConfirmation},</if>
            <if test="moldToolConfirmationHoverTip != null">mold_tool_confirmation_hover_tip = #{moldToolConfirmationHoverTip},</if>
            <if test="fillingPackagingFeasibility != null">filling_packaging_feasibility = #{fillingPackagingFeasibility},</if>
            <if test="fillingPackagingFeasibilityHoverTip != null">filling_packaging_feasibility_hover_tip = #{fillingPackagingFeasibilityHoverTip},</if>
            <if test="fillingPackagingSop != null">filling_packaging_sop = #{fillingPackagingSop},</if>
            <if test="finishedProductStandard != null">finished_product_standard = #{finishedProductStandard},</if>
            <if test="qualityAgreement != null">quality_agreement = #{qualityAgreement},</if>
            <if test="qualityAgreementHoverTip != null">quality_agreement_hover_tip = #{qualityAgreementHoverTip},</if>
            <if test="packagingMaterialStandard != null">packaging_material_standard = #{packagingMaterialStandard},</if>
            <if test="liquidSample != null">liquid_sample = #{liquidSample},</if>
            <if test="packagingMaterialSample != null">packaging_material_sample = #{packagingMaterialSample},</if>
            <if test="finishedProductSample != null">finished_product_sample = #{finishedProductSample},</if>
            <if test="excessivePackagingConfirmation != null">excessive_packaging_confirmation = #{excessivePackagingConfirmation},</if>
            <if test="excessivePackagingConfirmationHoverTip != null">excessive_packaging_confirmation_hover_tip = #{excessivePackagingConfirmationHoverTip},</if>
            <if test="registrationCompletion != null">registration_completion = #{registrationCompletion},</if>
            <if test="registrationCompletionHoverTip != null">registration_completion_hover_tip = #{registrationCompletionHoverTip},</if>
            <if test="formulaProcessConsistency != null">formula_process_consistency = #{formulaProcessConsistency},</if>
            <if test="formulaProcessConsistencyHoverTip != null">formula_process_consistency_hover_tip = #{formulaProcessConsistencyHoverTip},</if>
            <if test="documentationConsistency != null">documentation_consistency = #{documentationConsistency},</if>
            <if test="documentationConsistencyHoverTip != null">documentation_consistency_hover_tip = #{documentationConsistencyHoverTip},</if>
            <if test="internalStandardCompliance != null">internal_standard_compliance = #{internalStandardCompliance},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="canProduce != null">can_produce = #{canProduce},</if>
            <if test="canProduceRemark != null">can_produce_remark = #{canProduceRemark},</if>
            <if test="canDeliver != null">can_deliver = #{canDeliver},</if>
            <if test="canDeliverRemark != null">can_deliver_remark = #{canDeliverRemark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteProductAuditByIds" parameterType="String">
        update  product_audit set del_flag = 2,update_time = now() where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
