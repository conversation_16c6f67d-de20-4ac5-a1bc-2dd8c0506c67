package com.ruoyi.software.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 更新打样单状态请求DTO
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public class UpdateSampleOrderStatusDTO {

    /** 打样单关联表ID */
    @NotNull(message = "打样单关联表ID不能为空")
    private Long id;

    /** 要更新的状态 (1:进行中, 2:已完成, 3:驳回, 4:撤回) 字典：DYD_GCSZT */
    private String status;

    /** 实验编号（状态为已完成时必填） */
    private String laboratoryCode;

    /** 未出样原因（可选） */
    private String reasonForNoSample;

    /** 解决方案（可选） */
    private String solution;

    /** 驳回理由（状态为驳回时建议填写） */
    private String rejectReason;

    /** 关联表的备注 */
    private String remark;

    /** 预计出样时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectedSampleTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLaboratoryCode() {
        return laboratoryCode;
    }

    public void setLaboratoryCode(String laboratoryCode) {
        this.laboratoryCode = laboratoryCode;
    }

    public String getReasonForNoSample() {
        return reasonForNoSample;
    }

    public void setReasonForNoSample(String reasonForNoSample) {
        this.reasonForNoSample = reasonForNoSample;
    }

    public String getSolution() {
        return solution;
    }

    public void setSolution(String solution) {
        this.solution = solution;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getExpectedSampleTime() {
        return expectedSampleTime;
    }

    public void setExpectedSampleTime(Date expectedSampleTime) {
        this.expectedSampleTime = expectedSampleTime;
    }

    @Override
    public String toString() {
        return "UpdateSampleOrderStatusDTO{" +
                "id=" + id +
                ", status='" + status + '\'' +
                ", laboratoryCode='" + laboratoryCode + '\'' +
                ", reasonForNoSample='" + reasonForNoSample + '\'' +
                ", solution='" + solution + '\'' +
                ", rejectReason='" + rejectReason + '\'' +
                ", remark='" + remark + '\'' +
                ", expectedSampleTime='" + expectedSampleTime + '\'' +
                '}';
    }
}
