<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.ProductionPlanAreaHoursMapper">

    <sql id="selectProductionPlanAreaHoursVo">
        SELECT
            h.id,
            h.area_no,
            h.user_code,
            h.minutes,
            h.work_date,
            h.start_time,
            h.end_time,
            h.sailings,
            h.man_minutes,
            h.machine_minutes,
            h.create_by,
            h.create_time,
            h.plan_type,
            h.user_type,
            h.remark,
            h.mes_id,
            h.lot_no,
            h.equipment_no,
            h.op_no,
            u.nick_name,
            u.user_id
        FROM
            t_production_plan_area_hours h
            inner join sys_user u ON u.user_code = h.user_code
            inner join t_production_mes_lot l on l.lot_no = h.lot_no
            inner join t_production_mes_hours m on m.mes_id = h.mes_id
    </sql>

    <select id="selectProductionPlanAreaHoursList" parameterType="ProductionPlanAreaHours" resultType="ProductionPlanAreaHours">
        <include refid="selectProductionPlanAreaHoursVo"/>
        <where>
            <if test="areaNo != null "> and h.area_no = #{areaNo}</if>
            <if test="mesId != null "> and h.mes_id = #{mesId}</if>
            <if test="userCode != null  and userCode != ''"> and h.user_code = #{userCode}</if>
            <if test="userType != null  and userType != ''"> and h.user_type = #{userType}</if>
            <if test="planType != null  and planType != ''"> and h.plan_type = #{planType}</if>
            <if test="sailings != null  and sailings != ''"> and h.sailings = #{sailings}</if>
            <if test="userId != null  and userId != ''"> and u.user_id = #{userId}</if>
            <if test="nickName != null  and nickName != ''"> and u.nick_name like concat('%',#{nickName},'%')</if>
            <if test="lotNo != null  and lotNo != ''"> and h.lot_no = #{lotNo}</if>
            <if test="equipmentNo != null  and equipmentNo != ''"> and h.equipment_no = #{equipmentNo}</if>
            <if test="opNo != null  and opNo != ''"> and h.op_no = #{opNo}</if>
            <if test="params.startDate != null and params.startDate != ''">
                and date(h.work_date) &gt;= date(#{params.startDate})
            </if>
            <if test="params.endDate != null and params.endDate != ''">
                and date(h.work_date) &lt;= date(#{params.endDate})
            </if>
            <if test="scheduleCode != null and scheduleCode != ''" >and SUBSTRING_INDEX( h.lot_no, '-', 2 ) like concat('%',#{scheduleCode},'%')</if>
        </where>
        order by h.id desc
    </select>

    <select id="selectProductionPlanAreaHoursById" parameterType="Long" resultType="ProductionPlanAreaHours" >
        SELECT
            h.id,
            h.area_no,
            h.user_code,
            h.minutes,
            h.work_date,
            h.start_time,
            h.end_time,
            h.sailings,
            h.man_minutes,
            h.machine_minutes,
            h.create_by,
            h.create_time,
            h.remark,
            h.plan_type,
            h.user_type,
            h.mes_id,
            h.lot_no,
            h.equipment_no,
            h.op_no,
            u.nick_name,
            u.user_id
        FROM
            t_production_plan_area_hours h
            inner join sys_user u ON u.user_code = h.user_code
            inner join t_production_mes_hours m on m.mes_id = h.mes_id
        where h.id = #{id}
    </select>

    <insert id="insertProductionPlanAreaHours" parameterType="ProductionPlanAreaHours">
        insert into t_production_plan_area_hours
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="areaNo != null">area_no,</if>
            <if test="userCode != null">user_code,</if>
            <if test="workDate != null">work_date,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="userType != null">user_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="planType != null">plan_type,</if>
            <if test="minutes != null">minutes,</if>
            <if test="sailings != null">sailings,</if>
            <if test="manMinutes != null">man_minutes,</if>
            <if test="machineMinutes != null">machine_minutes,</if>
            <if test="remark != null">remark,</if>
            <if test="mesId">mes_id,</if>
            <if test="lotNo != null">lot_no,</if>
            <if test="equipmentNo != null">equipment_no,</if>
            <if test="opNo != null">op_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="areaNo != null">#{areaNo},</if>
            <if test="userCode != null">#{userCode},</if>
            <if test="workDate != null">#{workDate},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="userType != null">#{userType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="planType != null">#{planType},</if>
            <if test="minutes != null">#{minutes},</if>
            <if test="sailings != null">#{sailings},</if>
            <if test="manMinutes != null">#{manMinutes},</if>
            <if test="machineMinutes != null">#{machineMinutes},</if>
            <if test="remark != null">#{remark},</if>
            <if test="mesId != null">#{mesId},</if>
            <if test="lotNo != null">#{lotNo},</if>
            <if test="equipmentNo != null">#{equipmentNo},</if>
            <if test="opNo != null">#{opNo},</if>
         </trim>
    </insert>

    <update id="updateProductionPlanAreaHours" parameterType="ProductionPlanAreaHours">
        update t_production_plan_area_hours
        <trim prefix="SET" suffixOverrides=",">
            <if test="areaNo != null">area_no = #{areaNo},</if>
            <if test="userCode != null">user_code = #{userCode},</if>
            <if test="workDate != null">work_date = #{workDate},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="planType != null">plan_type = #{planType},</if>
            <if test="minutes != null">minutes = #{minutes},</if>
            <if test="sailings != null">sailings = #{sailings},</if>
            <if test="manMinutes != null">man_minutes = #{manMinutes},</if>
            <if test="machineMinutes != null">machine_minutes = #{machineMinutes},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="mesId != null">mes_id = #{mesId},</if>
            <if test="lotNo != null">lot_no = #{lotNo},</if>
            <if test="equipmentNo != null">equipment_no = #{equipmentNo},</if>
            <if test="opNo != null">op_no = #{opNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProductionPlanAreaHoursByIds" parameterType="String">
        delete from t_production_plan_area_hours where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectRecentProductionDayHoursList" resultType="ProductionDayHours" parameterType="ProductionDayHours" >
        SELECT
            h.work_date,
            s.FACTORY,
            sum( h.minutes ) sumMinutes,
            sum( if(h.user_type = 'user', h.minutes, 0) ) userMinutes,
            sum( if(h.user_type = 'labor', h.minutes, 0) ) laborMinutes,
            sum( if(h.user_type = 'outer', h.minutes, 0) ) outerMinutes,
            count(DISTINCT h.user_code) sumNums,
            count(DISTINCT if(h.user_type = 'user', h.user_code, null)) userNums,
            count(DISTINCT if(h.user_type = 'labor', h.user_code, null)) laborNums,
            count(DISTINCT if(h.user_type = 'outer', h.user_code, null)) outerNums
        FROM
            t_production_plan_area_hours h
            INNER JOIN sys_user u ON u.user_code = h.user_code
            inner join t_production_mes_hours m on m.mes_id = h.mes_id
            INNER JOIN t_order_production_schedule s ON s.CODE = SUBSTRING_INDEX( h.lot_no, '-', 2 )
        <where>
            <if test=" recentFlag != null and recentFlag != '' " >
                h.work_date &gt;= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            </if>
        </where>
        GROUP BY
            h.work_date,
            s.FACTORY
    </select>

    <select id="selectProductionDayHoursByProductionDayHours" parameterType="ProductionDayHours" resultType="ProductionDayHours" >
        SELECT
            sum( h.minutes ) sumMinutes,
            sum( if(h.user_type = 'user', h.minutes, 0) ) userMinutes,
            sum( if(h.user_type = 'labor', h.minutes, 0) ) laborMinutes,
            sum( if(h.user_type = 'outer', h.minutes, 0) ) outerMinutes,
            count(DISTINCT h.user_code) sumNums,
            count(DISTINCT if(h.user_type = 'user', h.user_code, null)) userNums,
            count(DISTINCT if(h.user_type = 'labor', h.user_code, null)) laborNums,
            count(DISTINCT if(h.user_type = 'outer', h.user_code, null)) outerNums
        FROM
            t_production_plan_area_hours h
            INNER JOIN sys_user u ON u.user_code = h.user_code
            inner join t_production_mes_hours m on m.mes_id = h.mes_id
            INNER JOIN t_order_production_schedule s ON s.CODE = SUBSTRING_INDEX( h.lot_no, '-', 2 )
        where
            h.work_date = #{workDate}
            and s.FACTORY = #{factory}
        <if test="sailings != null and sailings !=''" >
            and h.sailings = #{sailings}
        </if>
    </select>
    <!-- 注意这里是按人所在的工厂来切的,即同一个人即使同一天在不同的工厂上班也只按照入职的公司来累计工时 -->
    <select id="selectPlanAreaHoursList" parameterType="ProductionDayHours" resultType="ProductionPlanAreaHours" >
        SELECT
            h.id,
            h.area_no,
            h.user_code,
            h.minutes,
            h.work_date,
            h.start_time,
            h.end_time,
            h.sailings,
            h.man_minutes,
            h.machine_minutes,
            h.create_by,
            h.create_time,
            h.remark,
            h.plan_type,
            h.user_type,
            h.mes_id,
            h.mes_id,
            h.lot_no,
            h.equipment_no,
            u.nick_name,
            u.user_id
        FROM
            t_production_plan_area_hours h
            INNER JOIN sys_user u ON u.user_code = h.user_code
            inner join t_production_mes_hours m on m.mes_id = h.mes_id
            INNER JOIN t_order_production_schedule s ON s.CODE = SUBSTRING_INDEX( h.lot_no, '-', 2 )
        where
            h.work_date = #{workDate}
            and ifnull(u.production_code,u.company_code) = #{factory}
            <if test="sailings != null and sailings != ''" >
                and s.sailings = #{sailings}
            </if>
    </select>

    <delete id="deleteProductionPlanAreaHoursByPlanAreaId" >
        delete from t_production_plan_area_hours where area_no = #{areaNo}
    </delete>

    <select id="selectGroupWorkDateAndSailings" resultType="ProductionPlanAreaHours" >
        SELECT
            h.work_date,
            h.sailings,
            count( DISTINCT h.user_code ) sumNums,
            ifnull(sum(h.minutes),0) sumMinutes
        FROM
            t_production_plan_area_hours h
            INNER JOIN sys_user u ON u.user_code = h.user_code
            inner join t_production_mes_hours m on m.mes_id = h.mes_id
            INNER JOIN t_order_production_schedule s ON s.CODE = SUBSTRING_INDEX(h.lot_no, '-', 2 )
        <where>
            h.lot_no = #{lotNo}
        </where>
        GROUP BY
            h.work_date,
            h.sailings
    </select>

    <select id="selectMinutesByLotNo" resultType="BigDecimal" >
        select ifnull(sum( h.minutes ),0) from t_production_plan_area_hours h where h.lot_no = #{lotNo}
    </select>

    <select id="selectGroupAreaAndWorkDateAndSailings" resultType="ProductionPlanAreaHours" >
        SELECT
            h.area_no,
            h.equipment_no,
            h.work_date,
            h.sailings,
            SUBSTRING_INDEX( h.lot_no, '-', 2 ) scheduleCode,
            GROUP_CONCAT( DISTINCT h.op_no ) op_no
        FROM
            t_production_plan_area_hours h
        GROUP BY
            h.area_no,
            h.equipment_no,
            h.work_date,
            h.sailings,
            SUBSTRING_INDEX( h.lot_no, '-', 2 )
    </select>

</mapper>
