<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.mapper.TProjectExecutionMapper">

    <resultMap type="TProjectExecution" id="TProjectExecutionResult">
        <result property="id"    column="id"    />
        <result property="projectOrderId"    column="project_order_id"    />
        <result property="projectItemCode"    column="project_item_code"    />
        <result property="processUserId"    column="process_user_id"    />
        <result property="step"    column="step"    />
        <result property="currentStep"    column="current_step"    />
        <result property="status"    column="status"    />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="itemOrderCode"    column="item_order_code"    />
        <result property="itemCode"    column="item_code"    />
        <result property="projectCode"    column="code"    />
        <result property="projectNo"    column="project_no"    />
        <result property="customerName"    column="name"    />
        <result property="productName"    column="product_name"    />
        <result property="confirmCode"    column="confirm_code"    />
        <result property="qwTime"    column="qw_time"    />
        <result property="yjTime"    column="yj_time"    />
        <result property="delayYjTime"    column="delay_yj_time"    />
        <result property="firstYjTime"    column="first_yj_time"    />
        <result property="sjTime"    column="sj_time"    />
        <result property="orderCurrentStep"    column="order_current_step"    />
        <result property="assginUserName"    column="assgin_user_name"    />
        <result property="applyCreateTime"    column="apply_create_time"  />
        <result property="fields"    column="fields"  />
        <result property="bomChangeArray"    column="bom_change_array"  />
        <result property="bomChangeReplyArray"    column="bom_change_reply_array"  />
        <result property="orderFields" column="order_fields"/>
        <result property="fieldDatas"    column="field_datas"  />
        <result property="itemFields"    column="item_fields"  />
        <result property="itemType"    column="item_type"  />
        <result property="projectId"    column="project_id"  />
        <result property="projectItemId"    column="project_item_id"  />
        <result property="itemNames"    column="item_names"    />
        <result property="itemName"    column="item_name"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="orderYtTime"    column="order_yj_time"    />
        <result property="orderNum"    column="order_num"    />
        <result property="yjSpDdl"    column="yj_sp_ddl"    />
        <result property="projectItemOrderStatus"   column="project_item_order_status"    />
        <result property="projectOrderStatus"   column="project_order_status"    />
        <result property="isAudit"   column="is_audit"    />
        <result property="itemStatus"   column="item_status"    />
        <result property="customerId"   column="customer_id"    />
        <result property="customerYw"   column="customer_yw"    />
        <result property="projectYw"   column="project_yw"    />
        <result property="projectCreateBy"   column="project_create_by"    />
        <result property="itemRemark"   column="item_remark"    />
        <result property="laboratory"   column="laboratory"    />
        <result property="assginUserName"   column="assgin_user_name"    />
        <result property="customerShortName"   column="short_name"    />
        <result property="itemSqlx"   column="item_sqlx"    />
        <result property="customerCode"   column="customer_code"    />
        <result property="returnTo"   column="retrun_to"    />
        <result property="isMerge"   column="is_merge"    />
        <result property="mergeKey"   column="merge_key"    />
        <result property="executionYjTime"   column="executionYjTime"    />
        <result property="returnToDept"   column="return_to_dept"    />
        <result property="mergeKey"   column="merge_key"    />
        <result property="isMerge"   column="is_merge"    />
        <result property="projectProductId"   column="project_product_id"    />
        <result property="projectType"   column="project_type"    />
        <result property="confirmCode"   column="confirm_code"    />
        <result property="fromType"   column="from_type"    />
        <result property="reloadFromId"   column="reload_from_id"    />
        <result property="itemText"   column="item_text"    />
    </resultMap>

    <sql id="selectTProjectExecutionVo">
        SELECT
            pe.id,
            pe.project_order_id,
            pe.project_item_code,
            pe.process_user_id,
            pe.step,
            pe.assgin_user_name,
            pe.current_step,
            pe.STATUS,
            pe.fields,
            pe.assgin_user_name,
            pe.audit_remark,
            pe.audit_time,
            pe.create_by,
            pe.create_time,
            ifnull(pio.update_by,"") update_by,
            pe.update_time,
            pe.remark,
            pio.code item_order_code,
            pio.confirm_code,
            pio.current_step order_current_step,
            pio.create_time apply_create_time,
            pio.yj_time,
            pio.delay_yj_time,
            pio.qw_time,
            pio.status order_status,
            c.name,
            c.short_name,
            p.id project_id,
            pi.id project_item_id,
            IF( P.PROJECT_TYPE = 0, p.product_name,(select GROUP_CONCAT(bpp.product_name) from t_before_project_product bpp where FIND_IN_SET(bpp.id,pi.project_product_id))) product_name,
            ifnull(pe.lab_no,p.laboratory) laboratory,
            p.item_names,
            p.project_no,
            pi.item_name,
            pe.dept_name,
            IF(pi.type='PROJECT_BOM_APPLY',pe.project_item_code,pi.type) item_type,
            pi.status        item_status,
            pi.`fields`      item_fields,
            pio.order_status project_item_order_status,
            peu.is_audit,
            IFNULL(p.project_yw_name,c.customer_yw) customer_yw,
            c.project_yw,
            c.code           customer_code,
            pio.create_by    project_create_by,
            pio.`fields`     order_fields,
            pe.merge_key,
            pe.is_merge,
            pio.project_type,
            tpp.name item_text
        FROM t_project_execution pe
                 left JOIN t_project_item_order pio ON pe.project_order_id = pio.id
                 left JOIN t_project_item pi ON pio.item_id = pi.id
                 left JOIN t_project p ON pi.project_id = p.id
                 left JOIN t_customer c ON p.customer_id = c.id
                 left JOIN t_project_execution_user peu ON pe.id = peu.execution_id
                 left join t_project_product tpp on  (pi.item_name = tpp.guid and pi.project_id = tpp.project_id)
    </sql>

    <select id="selectTProjectExecutionList" parameterType="TProjectExecution" resultMap="TProjectExecutionResult">
        <include refid="selectTProjectExecutionVo"/>
        <where>
            <if test="isApply==0">
                and is_apply_cf = 0 and is_apply_hh = 0
            </if>
            <if test="isApply==1">
                and (is_apply_cf > 0 or is_apply_hh > 0)
            </if>
            <if test="projectOrderId != null "> and pe.project_order_id = #{projectOrderId}</if>
            <if test="projectType != null "> and P.PROJECT_TYPE = #{projectType}</if>
            <if test="isLog != null "> and ifnull(pe.is_log,0) = #{isLog}</if>
            <if test="projectItemCode != null  and projectItemCode != ''"> and pi.type = #{projectItemCode}</if>
            <if test="itemOrderCode != null  and itemOrderCode != ''"> and  pio.code like concat(#{itemOrderCode}, '%')</if>
            <if test="confirmCode != null  and confirmCode != ''"> and  pio.confirm_code like  concat('%', #{confirmCode}, '%')</if>
            <if test="processUserId != null">
                 and (
                         peu.process_user_id = #{processUserId}
                    or
                        peu.process_post_name in (
                            select r.role_name from sys_user u
                            inner join sys_user_role ur on u.user_id = ur.user_id
                            inner join sys_role r on ur.role_id = r.role_id
                            where u.user_id = #{processUserId}
                        )
                 )
            </if>
            <if test="assginName != null">and pe.assgin_user_name like concat('%',#{assginName},'%')</if>
            <if test="processAssginName != null">and pe.dept_name like concat('%',#{processAssginName},'%')</if>
            <if test="step != null "> and pe.step = #{step}</if>
            <if test="isAudit != null "> and peu.is_audit = #{isAudit}</if>
            <if test="laboratory != null and laboratory!='' "> and ifnull(pe.lab_no,p.laboratory) = #{laboratory}</if>
            <if test="labNo != null and labNo!='' "> and pe.lab_no = #{labNo}</if>
            <if test="currentStep != null  and currentStep != ''"> and pe.current_step = #{currentStep}</if>
            <if test="updateBy != null  and updateBy != ''"> and pe.update_by like concat(#{updateBy},'%')</if>
            <if test="status != null "> and pe.status = #{status}</if>
            <if test="auditRemark != null  and auditRemark != ''"> and pe.audit_remark = #{auditRemark}</if>
            <if test="productName != null  and productName != ''">
             and  IF( P.PROJECT_TYPE = 0, p.product_name,(select GROUP_CONCAT(bpp.product_name) from t_before_project_product bpp where FIND_IN_SET(bpp.id,pi.project_product_id))) like concat('%', #{productName}, '%')
            </if>
            <if test="projectItemCodes != null and projectItemCodes.size() > 0" >
                and pi.type in
                <foreach collection="projectItemCodes" item="projectItemCode" open="(" close=")" separator="," >
                    #{projectItemCode}
                </foreach>
            </if>
            <if test="params.beginCreate != null and params.beginCreate != ''">
                AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
            </if>
            <if test="params.endCreate != null and params.endCreate != ''">
                AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
            </if>
            <if test="params.beginApplyCreate != null and params.beginApplyCreate != ''">
                AND date_format(pe.create_time,'%y%m%d') &gt;= date_format(#{params.beginApplyCreate},'%y%m%d')
            </if>
            <if test="params.endApplyCreate!= null and params.endApplyCreate != ''">
                AND date_format(pe.create_time,'%y%m%d') &lt;= date_format(#{params.endApplyCreate},'%y%m%d')
            </if>
            <if test="params.beginYj != null and params.beginYj != ''">
                AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
            </if>
            <if test="params.endYj != null and params.endYj != ''">
                AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
            </if>
            <if test="params.beginDelayYjRange != null and params.beginDelayYjRange != ''">
                AND date_format(pio.delay_yj_time,'%y%m%d') &gt;= date_format(#{params.beginDelayYjRange},'%y%m%d')
            </if>
            <if test="params.endDelayYjRange != null and params.endDelayYjRange != ''">
                AND date_format(pio.delay_yj_time,'%y%m%d') &lt;= date_format(#{params.endDelayYjRange},'%y%m%d')
            </if>
            <if test=" params.statusArray != null and params.statusArray.length > 0 ">
                and pio.STATUS in
                <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.progressArray != null and params.progressArray.length > 0 ">
                and p.progress in
                <foreach collection="params.progressArray" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.shStatus != null and params.shStatus.length > 0 ">
                and pe.status in
                <foreach collection="params.shStatus" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">
                and pe.current_step in
                <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">
                and p.customer_id != #{params.customerIgnoreId}
            </if>
            <if test="params.customerId != null and params.customerId != ''">
                and p.customer_id = #{params.customerId}
            </if>
            <if test="createBy != null and createBy != ''">
                and pio.create_by = #{createBy}
            </if>
            <if test="projectItemCode != null and projectItemCode != '' and itemSqlx!= null and itemSqlx!=''">
                <choose>
                    <when test="projectItemCode == 'PROJECT_NRW'">
                        and pi.fields->'$.dylx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_RQKF'">
                        and pi.fields->'$.bcfl' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_SHEJI'">
                        and pi.fields->'$.sjlx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_PPT'">
                        and pi.fields->'$.talx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_QCF'">
                        and pi.fields->'$.sqlx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_OFFER'">
                        and pi.fields->'$.bjlx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_ZS'">
                        and pi.fields->'$.zslx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_BOM_APPLY'">
                        and pi.fields->'$.applyType' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_GXCSSQ'">
                        and pi.fields->'$.typeTreeId' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_BA'">
                        and pi.fields->'$.balb' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_SCKXXPG'">
                        and pi.fields->'$.sqlx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_INSPECTION_AND_FILING'">
                        and pi.fields->'$.applyType' = #{itemSqlx}
                    </when>
                </choose>
            </if>
            <if test="projectItemCode != null and projectItemCode != '' and itemSqlxs != null and itemSqlxs.size() > 0 ">
                <choose>
                    <when test="projectItemCode == 'PROJECT_NRW'">
                        and pi.fields->>'$.dylx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_RQKF'">
                        and pi.fields->>'$.bcfl' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_SHEJI'">
                        and pi.fields->>'$.sjlx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_PPT'">
                        and pi.fields->>'$.talx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_QCF'">
                        and pi.fields->>'$.sqlx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_OFFER'">
                        and pi.fields->>'$.bjlx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_ZS'">
                        and pi.fields->>'$.zslx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_BOM_APPLY'">
                        and pi.fields->>'$.applyType' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_GXCSSQ'">
                        and pi.fields->>'$.typeTreeId' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_BA'">
                        and pi.fields->>'$.balb' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_SCKXXPG'">
                        and pi.fields->>'$.sqlx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_INSPECTION_AND_FILING'">
                        and pi.fields->>'$.applyType' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                </choose>
            </if>
        </where>
        order by pe.id desc
    </select>
    <select id="selectTProjectItemOrderExecutionList" parameterType="TProjectExecution" resultMap="TProjectExecutionResult">
        SELECT
        pio.id project_order_id,
        ifnull(pio.update_by,"") update_by,
        pio.code item_order_code,
        pio.confirm_code,
        pio.current_step order_current_step,
        pio.step step,
        pio.create_time apply_create_time,
        pio.yj_time,
        pio.delay_yj_time,
        pio.qw_time,
        pio.status order_status,
        c.name,
        c.short_name,
        p.id project_id,
        pi.id project_item_id,
        p.product_name,
        p.laboratory,
        p.item_names,
        p.project_no,
        pi.item_name,
        pi.type          item_type,
        pi.status        item_status,
        pi.`fields`      item_fields,
        pio.order_status project_item_order_status,
        IFNULL(p.project_yw_name,c.customer_yw) customer_yw,
        c.code           customer_code,
        pio.create_by    project_create_by,
        tpp.name item_text
        from t_project_item_order pio
        left JOIN t_project_item pi ON pio.item_id = pi.id
        left JOIN t_project p ON pi.project_id = p.id
        left JOIN t_customer c ON p.customer_id = c.id
        left join t_project_product tpp on (pi.item_name = tpp.guid and pi.project_id = tpp.project_id)
        <where>
            pio.del_flag = 0
            <if test="projectOrderId != null "> and pe.project_order_id = #{projectOrderId}</if>
            <if test="isLog != null "> and ifnull(pe.is_log,0) = #{isLog}</if>
            <if test="projectItemCode != null  and projectItemCode != ''"> and pi.type = #{projectItemCode}</if>
            <if test="itemOrderCode != null  and itemOrderCode != ''"> and  pio.code like concat(#{itemOrderCode}, '%')</if>
            <if test="confirmCode != null  and confirmCode != ''"> and  pio.confirm_code like  concat('%', #{confirmCode}, '%')</if>
            <if test="processUserId != null">
                AND pio.id in (
                    SELECT pe.PROJECT_ORDER_ID
                    FROM t_project_execution pe
                    INNER JOIN t_project_execution_user peu
                    WHERE  (peu.process_user_id = #{processUserId}
                              OR peu.process_post_name IN (
                                SELECT  r.role_name   FROM  sys_user u
                                INNER JOIN sys_user_role ur ON u.user_id = ur.user_id
                                INNER JOIN sys_role r ON ur.role_id = r.role_id
                                WHERE
                                u.user_id = #{processUserId}
                             ))
                        <if test=" params.shStatus != null and params.shStatus.length > 0 ">
                            and pe.status in
                            <foreach collection="params.shStatus" item="item" separator="," open="(" close=")" >
                                #{item}
                            </foreach>
                        </if>
                        <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">
                            and pe.current_step in
                            <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                                #{item}
                            </foreach>
                        </if>
                        <if test="params.beginApplyCreate != null and params.beginApplyCreate != ''">
                            AND date_format(pe.create_time,'%y%m%d') &gt;= date_format(#{params.beginApplyCreate},'%y%m%d')
                        </if>
                        <if test="params.endApplyCreate!= null and params.endApplyCreate != ''">
                            AND date_format(pe.create_time,'%y%m%d') &lt;= date_format(#{params.endApplyCreate},'%y%m%d')
                        </if>
                 )
            </if>
            <if test="assginName != null">and pe.assgin_user_name like concat('%',#{assginName},'%')</if>
            <if test="processAssginName != null">and pe.dept_name like concat('%',#{processAssginName},'%')</if>
            <if test="step != null "> and pe.step = #{step}</if>
            <if test="isAudit != null "> and peu.is_audit = #{isAudit}</if>
            <if test="laboratory != null and laboratory!='' "> and p.laboratory = #{laboratory}</if>
            <if test="currentStep != null  and currentStep != ''"> and pe.current_step = #{currentStep}</if>
            <if test="updateBy != null  and updateBy != ''"> and pe.update_by like concat(#{updateBy},'%')</if>
            <if test="auditRemark != null  and auditRemark != ''"> and pe.audit_remark = #{auditRemark}</if>
            <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="projectItemCodes != null and projectItemCodes.size() > 0" >
                and pi.type in
                <foreach collection="projectItemCodes" item="projectItemCode" open="(" close=")" separator="," >
                    #{projectItemCode}
                </foreach>
            </if>
            <if test="params.beginCreate != null and params.beginCreate != ''">
                AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
            </if>
            <if test="params.endCreate != null and params.endCreate != ''">
                AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
            </if>
            <if test="params.beginYj != null and params.beginYj != ''">
                AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
            </if>
            <if test="params.endYj != null and params.endYj != ''">
                AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
            </if>
            <if test="params.beginDelayYjRange != null and params.beginDelayYjRange != ''">
                AND date_format(pio.delay_yj_time,'%y%m%d') &gt;= date_format(#{params.beginDelayYjRange},'%y%m%d')
            </if>
            <if test="params.endDelayYjRange != null and params.endDelayYjRange != ''">
                AND date_format(pio.delay_yj_time,'%y%m%d') &lt;= date_format(#{params.endDelayYjRange},'%y%m%d')
            </if>
            <if test=" params.statusArray != null and params.statusArray.length > 0 ">
                and pio.STATUS in
                <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.progressArray != null and params.progressArray.length > 0 ">
                and p.progress in
                <foreach collection="params.progressArray" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">
                and p.customer_id != #{params.customerIgnoreId}
            </if>
            <if test="params.customerId != null and params.customerId != ''">
                and p.customer_id = #{params.customerId}
            </if>
            <if test="createBy != null and createBy != ''">
                and pio.create_by = #{createBy}
            </if>
            <if test="projectItemCode != null and projectItemCode != '' and itemSqlx!= null and itemSqlx!=''">
                <choose>
                    <when test="projectItemCode == 'PROJECT_NRW'">
                        and pi.fields->'$.dylx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_RQKF'">
                        and pi.fields->'$.bcfl' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_SHEJI'">
                        and pi.fields->'$.sjlx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_PPT'">
                        and pi.fields->'$.talx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_QCF'">
                        and pi.fields->'$.sqlx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_OFFER'">
                        and pi.fields->'$.bjlx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_ZS'">
                        and pi.fields->'$.zslx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_BOM_APPLY'">
                        and pi.fields->'$.applyType' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_GXCSSQ'">
                        and pi.fields->'$.typeTreeId' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_BA'">
                        and pi.fields->'$.balb' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_SCKXXPG'">
                        and pi.fields->'$.sqlx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_INSPECTION_AND_FILING'">
                        and pi.fields->'$.applyType' = #{itemSqlx}
                    </when>
                </choose>
            </if>
            <if test="projectItemCode != null and projectItemCode != '' and itemSqlxs != null and itemSqlxs.size() > 0 ">
                <choose>
                    <when test="projectItemCode == 'PROJECT_NRW'">
                        and pi.fields->>'$.dylx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_RQKF'">
                        and pi.fields->>'$.bcfl' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_SHEJI'">
                        and pi.fields->>'$.sjlx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_PPT'">
                        and pi.fields->>'$.talx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_QCF'">
                        and pi.fields->>'$.sqlx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_OFFER'">
                        and pi.fields->>'$.bjlx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_ZS'">
                        and pi.fields->>'$.zslx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_BOM_APPLY'">
                        and pi.fields->>'$.applyType' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_GXCSSQ'">
                        and pi.fields->>'$.typeTreeId' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_BA'">
                        and pi.fields->>'$.balb' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_SCKXXPG'">
                        and pi.fields->>'$.sqlx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_INSPECTION_AND_FILING'">
                        and pi.fields->>'$.applyType' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                </choose>
            </if>
        </where>
        order by pio.id desc
    </select>


    <select id="queryProjectExecutionOrderInfo" parameterType="TProjectExecution" resultType="TProjectExecution">
        SELECT
            pe.id,
            pe.`status`,
            peu.is_audit isAudit,
            pe.step,
            pe.current_step,
            pe.audit_time,pe.audit_remark
        FROM
            t_project_execution pe
                INNER JOIN t_project_execution_user peu ON pe.id = peu.execution_id
        WHERE
            pe.project_order_id = #{projectOrderId}
          AND (
                peu.process_user_id = #{processUserId}
                OR peu.process_post_name IN (
                SELECT
                    r.role_name
                FROM
                    sys_user u
                        INNER JOIN sys_user_role ur ON u.user_id = ur.user_id
                        INNER JOIN sys_role r ON ur.role_id = r.role_id
                WHERE
                    u.user_id = #{processUserId}
            )
            )
        order by pe.id
    </select>

    <select id="selectTProjectExecutionagFormulaList" parameterType="TProjectExecution" resultMap="TProjectExecutionResult">
        SELECT
        pe.id,
        pe.project_order_id,
        pe.project_item_code,
        pe.process_user_id,
        pe.step,
        pe.assgin_user_name,
        pe.current_step,
        pe.STATUS,
        pe.fields,
        pe.assgin_user_name,
        pe.audit_remark,
        pe.audit_time,
        pe.create_by,
        pe.create_time,
        ifnull(pio.update_by,"") update_by,
        pe.update_time,
        pe.remark,
        pio.code item_order_code,
        pio.confirm_code,
        pio.current_step order_current_step,
        pio.create_time apply_create_time,
        pio.yj_time,
        pio.delay_yj_time,
        pio.qw_time,
        pio.status order_status,
        c.name,
        c.short_name,
        p.id project_id,
        pi.id project_item_id,
        IF(P.PROJECT_TYPE = 0, p.product_name,(select GROUP_CONCAT(bpp.product_name) from t_before_project_product bpp where FIND_IN_SET(bpp.id,pi.project_product_id))) product_name,
        ifnull(pe.lab_no,p.laboratory) laboratory,
        p.item_names,
        p.project_no,
        pi.item_name,
        pe.dept_name,
        pi.type          item_type,
        pi.status        item_status,
        pi.`fields`      item_fields,
        pio.order_status project_item_order_status,
        0 is_audit,
        IFNULL(p.project_yw_name,c.customer_yw) customer_yw,
        c.code           customer_code,
        pio.create_by    project_create_by,
        pio.`fields`     order_fields,
        pe.merge_key,
        pe.is_merge,
        pio.project_type,
        p.from_type,
        p.reload_from_id
        FROM t_project_execution pe
        left JOIN t_project_item_order pio ON pe.project_order_id = pio.id
        left JOIN t_project_item pi ON pio.item_id = pi.id
        left JOIN t_project p ON pi.project_id = p.id
        left JOIN t_customer c ON p.customer_id = c.id
        <where>
            pe.step in (1,3) and pi.type = 'PROJECT_QCF'
            <if test="isApply==0">
                and is_apply_cf = 0 and is_apply_hh = 0
            </if>
            <if test="isApply==1">
                and (is_apply_cf > 0 or is_apply_hh > 0)
            </if>
            <if test="labNo!=null and labNo!=''">
                and pio.fields like concat('%',#{labNo},'%')
            </if>
            <if test="projectOrderId != null "> and pe.project_order_id = #{projectOrderId}</if>
            <if test="isLog != null "> and ifnull(pe.is_log,0) = #{isLog}</if>
            <if test="itemOrderCode != null  and itemOrderCode != ''"> and  pio.code like concat(#{itemOrderCode}, '%')</if>
            <if test="confirmCode != null  and confirmCode != ''"> and  pio.confirm_code like  concat('%', #{confirmCode}, '%')</if>
            <if test="assginName != null">and pe.assgin_user_name like concat('%',#{assginName},'%')</if>
            <if test="processAssginName != null">and pe.dept_name like concat('%',#{processAssginName},'%')</if>
            <if test="step != null "> and pe.step = #{step}</if>
            <if test="isAudit != null "> and peu.is_audit = #{isAudit}</if>
            <if test="projectNo != null and projectNo!='' "> and p.project_no = #{projectNo}</if>
            <if test="laboratory != null and laboratory!='' ">and ifnull(pe.lab_no,p.laboratory) = #{laboratory}</if>
            <if test="currentStep != null  and currentStep != ''"> and pe.current_step = #{currentStep}</if>
            <if test="updateBy != null  and updateBy != ''"> and pe.update_by like concat(#{updateBy},'%')</if>
            <if test="status != null "> and pe.status = #{status}</if>
            <if test="auditRemark != null  and auditRemark != ''"> and pe.audit_remark = #{auditRemark}</if>
            <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="params.beginCreate != null and params.beginCreate != ''">
                AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
            </if>
            <if test="params.endCreate != null and params.endCreate != ''">
                AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
            </if>
            <if test="params.beginApplyCreate != null and params.beginApplyCreate != ''">
                AND date_format(pe.create_time,'%y%m%d') &gt;= date_format(#{params.beginApplyCreate},'%y%m%d')
            </if>
            <if test="params.endApplyCreate!= null and params.endApplyCreate != ''">
                AND date_format(pe.create_time,'%y%m%d') &lt;= date_format(#{params.endApplyCreate},'%y%m%d')
            </if>
            <if test="params.beginYj != null and params.beginYj != ''">
                AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
            </if>
            <if test="params.endYj != null and params.endYj != ''">
                AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
            </if>
            <if test="params.beginDelayYjRange != null and params.beginDelayYjRange != ''">
                AND date_format(pio.delay_yj_time,'%y%m%d') &gt;= date_format(#{params.beginDelayYjRange},'%y%m%d')
            </if>
            <if test="params.endDelayYjRange != null and params.endDelayYjRange != ''">
                AND date_format(pio.delay_yj_time,'%y%m%d') &lt;= date_format(#{params.endDelayYjRange},'%y%m%d')
            </if>
            <if test=" params.statusArray != null and params.statusArray.length > 0 ">
                and pio.STATUS in
                <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.progressArray != null and params.progressArray.length > 0 ">
                and p.progress in
                <foreach collection="params.progressArray" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.shStatus != null and params.shStatus.length > 0 ">
                and pe.status in
                <foreach collection="params.shStatus" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">
                and pe.current_step in
                <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">
                and p.customer_id != #{params.customerIgnoreId}
            </if>
            <if test="params.customerId != null and params.customerId != ''">
                and p.customer_id = #{params.customerId}
            </if>
            <if test="createBy != null and createBy != ''">
                and pio.create_by = #{createBy}
            </if>
            <if test="projectItemCode != null and projectItemCode != '' and itemSqlx!= null and itemSqlx!=''">
                <choose>
                    <when test="projectItemCode == 'PROJECT_NRW'">
                        and pi.fields->'$.dylx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_RQKF'">
                        and pi.fields->'$.bcfl' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_SHEJI'">
                        and pi.fields->'$.sjlx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_PPT'">
                        and pi.fields->'$.talx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_QCF'">
                        and pi.fields->'$.sqlx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_OFFER'">
                        and pi.fields->'$.bjlx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_ZS'">
                        and pi.fields->'$.zslx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_BOM_APPLY'">
                        and pi.fields->'$.applyType' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_GXCSSQ'">
                        and pi.fields->'$.typeTreeId' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_BA'">
                        and pi.fields->'$.balb' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_SCKXXPG'">
                        and pi.fields->'$.sqlx' = #{itemSqlx}
                    </when>
                    <when test="projectItemCode == 'PROJECT_INSPECTION_AND_FILING'">
                        and pi.fields->'$.applyType' = #{itemSqlx}
                    </when>
                </choose>
            </if>
            <if test="projectItemCode != null and projectItemCode != '' and itemSqlxs != null and itemSqlxs.size() > 0 ">
                <choose>
                    <when test="projectItemCode == 'PROJECT_NRW'">
                        and pi.fields->>'$.dylx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_RQKF'">
                        and pi.fields->>'$.bcfl' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_SHEJI'">
                        and pi.fields->>'$.sjlx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_PPT'">
                        and pi.fields->>'$.talx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_QCF'">
                        and pi.fields->>'$.sqlx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_OFFER'">
                        and pi.fields->>'$.bjlx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_ZS'">
                        and pi.fields->>'$.zslx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_BOM_APPLY'">
                        and pi.fields->>'$.applyType' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_GXCSSQ'">
                        and pi.fields->>'$.typeTreeId' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_BA'">
                        and pi.fields->>'$.balb' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_SCKXXPG'">
                        and pi.fields->>'$.sqlx' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                    <when test="projectItemCode == 'PROJECT_INSPECTION_AND_FILING'">
                        and pi.fields->>'$.applyType' in
                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                            #{itemSqlx}
                        </foreach>
                    </when>
                </choose>
            </if>
        </where>
        order by pe.id desc
    </select>

    <!--获取数据-->
<!--    <select id="selectTProjectExecutionList_COUNT" parameterType="TProjectExecution" resultType="java.lang.Integer">-->
<!--        SELECT-->
<!--         count(*)-->
<!--        FROM-->
<!--        t_project_item_order pio-->
<!--        inner join t_project_execution pe on pe.project_order_id = pio.id-->
<!--        inner join t_project_item pi on pio.item_id = pi.id-->
<!--        inner join t_project p on pi.project_id = p.id-->
<!--        inner join t_customer c on p.customer_id = c.id-->
<!--        inner join t_project_execution_user peu on pe.id = peu.execution_id-->
<!--        <where>-->
<!--            and pio.del_flag = 0 and pi.del_flag = 0-->
<!--            <if test="projectOrderId != null "> and pe.project_order_id = #{projectOrderId}</if>-->
<!--            <if test="isLog != null "> and ifnull(pe.is_log,0) = #{isLog}</if>-->
<!--            <if test="projectItemCode != null  and projectItemCode != ''"> and pi.type = #{projectItemCode}</if>-->
<!--            <if test="itemOrderCode != null  and itemOrderCode != ''"> and  pio.code like concat(#{itemOrderCode}, '%')</if>-->
<!--            <if test="confirmCode != null  and confirmCode != ''"> and  pio.confirm_code like  concat('%', #{confirmCode}, '%')</if>-->
<!--            <if test="processUserId != null">-->
<!--                and (-->
<!--                    peu.process_user_id = #{processUserId}-->
<!--                    or-->
<!--                    peu.process_post_name in (-->
<!--                        select r.role_name from sys_user u-->
<!--                        inner join sys_user_role ur on u.user_id = ur.user_id-->
<!--                        inner join sys_role r on ur.role_id = r.role_id-->
<!--                        where u.user_id = #{processUserId}-->
<!--                    )-->
<!--                )-->
<!--            </if>-->
<!--            <if test="assginName != null">and pe.assgin_user_name like concat('%',#{assginName},'%')</if>-->
<!--            <if test="processAssginName != null">and pe.dept_name like concat('%',#{processAssginName},'%')</if>-->
<!--            <if test="step != null "> and pe.step = #{step}</if>-->
<!--            <if test="isAudit != null "> and peu.is_audit = #{isAudit}</if>-->
<!--            <if test="laboratory != null and laboratory !='' "> and p.laboratory = #{laboratory}</if>-->
<!--            <if test="currentStep != null  and currentStep != ''"> and pe.current_step = #{currentStep}</if>-->
<!--            <if test="updateBy != null  and updateBy != ''"> and pe.update_by like concat(#{updateBy},'%')</if>-->
<!--            <if test="status != null "> and pe.status = #{status}</if>-->
<!--            <if test="auditRemark != null  and auditRemark != ''"> and pe.audit_remark = #{auditRemark}</if>-->
<!--            <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>-->
<!--            <if test="projectItemCodes != null and projectItemCodes.size() > 0" >-->
<!--                and pi.type in-->
<!--                <foreach collection="projectItemCodes" item="projectItemCode" open="(" close=")" separator="," >-->
<!--                    #{projectItemCode}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="params.beginCreate != null and params.beginCreate != ''">-->
<!--                AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')-->
<!--            </if>-->
<!--            <if test="params.endCreate != null and params.endCreate != ''">-->
<!--                AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')-->
<!--            </if>-->
<!--            <if test="params.beginApplyCreate != null and params.beginApplyCreate != ''">-->
<!--                AND date_format(pe.create_time,'%y%m%d') &gt;= date_format(#{params.beginApplyCreate},'%y%m%d')-->
<!--            </if>-->
<!--            <if test="params.endApplyCreate!= null and params.endApplyCreate != ''">-->
<!--                AND date_format(pe.create_time,'%y%m%d') &lt;= date_format(#{params.endApplyCreate},'%y%m%d')-->
<!--            </if>-->
<!--            <if test="params.beginYj != null and params.beginYj != ''">-->
<!--                AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')-->
<!--            </if>-->
<!--            <if test="params.endYj != null and params.endYj != ''">-->
<!--                AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')-->
<!--            </if>-->
<!--            <if test="params.beginDelayYjRange != null and params.beginDelayYjRange != ''">-->
<!--                AND date_format(pio.delay_yj_time,'%y%m%d') &gt;= date_format(#{params.beginDelayYjRange},'%y%m%d')-->
<!--            </if>-->
<!--            <if test="params.endDelayYjRange != null and params.endDelayYjRange != ''">-->
<!--                AND date_format(pio.delay_yj_time,'%y%m%d') &lt;= date_format(#{params.endDelayYjRange},'%y%m%d')-->
<!--            </if>-->
<!--            <if test=" params.statusArray != null and params.statusArray.length > 0 ">-->
<!--                and pio.STATUS in-->
<!--                <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test=" params.progressArray != null and params.progressArray.length > 0 ">-->
<!--                and p.progress in-->
<!--                <foreach collection="params.progressArray" item="item" separator="," open="(" close=")" >-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test=" params.shStatus != null and params.shStatus.length > 0 ">-->
<!--                and pe.status in-->
<!--                <foreach collection="params.shStatus" item="item" separator="," open="(" close=")" >-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">-->
<!--                and pe.current_step in-->
<!--                <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">-->
<!--                and p.customer_id != #{params.customerIgnoreId}-->
<!--            </if>-->
<!--            <if test="params.customerId != null and params.customerId != ''">-->
<!--                and p.customer_id = #{params.customerId}-->
<!--            </if>-->
<!--            <if test="createBy != null and createBy != ''">-->
<!--                and pio.create_by = #{createBy}-->
<!--            </if>-->
<!--            <if test="projectItemCode != null and projectItemCode != '' and itemSqlx!= null and itemSqlx!=''">-->
<!--                <choose>-->
<!--                    <when test="projectItemCode == 'PROJECT_NRW'">-->
<!--                        and pi.fields->'$.dylx' = #{itemSqlx}-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_RQKF'">-->
<!--                        and pi.fields->'$.bcfl' = #{itemSqlx}-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_SHEJI'">-->
<!--                        and pi.fields->'$.sjlx' = #{itemSqlx}-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_PPT'">-->
<!--                        and pi.fields->'$.talx' = #{itemSqlx}-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_QCF'">-->
<!--                        and pi.fields->'$.sqlx' = #{itemSqlx}-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_OFFER'">-->
<!--                        and pi.fields->'$.bjlx' = #{itemSqlx}-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_ZS'">-->
<!--                        and pi.fields->'$.zslx' = #{itemSqlx}-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_BOM_APPLY'">-->
<!--                        and pi.fields->'$.applyType' = #{itemSqlx}-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_GXCSSQ'">-->
<!--                        and pi.fields->'$.typeTreeId' = #{itemSqlx}-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_BA'">-->
<!--                        and pi.fields->'$.balb' = #{itemSqlx}-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_SCKXXPG'">-->
<!--                        and pi.fields->'$.sqlx' = #{itemSqlx}-->
<!--                    </when>-->
<!--                </choose>-->
<!--            </if>-->
<!--            <if test="projectItemCode != null and projectItemCode != '' and itemSqlxs != null and itemSqlxs.size() > 0 ">-->
<!--                <choose>-->
<!--                    <when test="projectItemCode == 'PROJECT_NRW'">-->
<!--                        and pi.fields->>'$.dylx' in-->
<!--                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >-->
<!--                            #{itemSqlx}-->
<!--                        </foreach>-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_RQKF'">-->
<!--                        and pi.fields->>'$.bcfl' in-->
<!--                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >-->
<!--                            #{itemSqlx}-->
<!--                        </foreach>-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_SHEJI'">-->
<!--                        and pi.fields->>'$.sjlx' in-->
<!--                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >-->
<!--                            #{itemSqlx}-->
<!--                        </foreach>-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_PPT'">-->
<!--                        and pi.fields->>'$.talx' in-->
<!--                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >-->
<!--                            #{itemSqlx}-->
<!--                        </foreach>-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_QCF'">-->
<!--                        and pi.fields->>'$.sqlx' in-->
<!--                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >-->
<!--                            #{itemSqlx}-->
<!--                        </foreach>-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_OFFER'">-->
<!--                        and pi.fields->>'$.bjlx' in-->
<!--                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >-->
<!--                            #{itemSqlx}-->
<!--                        </foreach>-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_ZS'">-->
<!--                        and pi.fields->>'$.zslx' in-->
<!--                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >-->
<!--                            #{itemSqlx}-->
<!--                        </foreach>-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_BOM_APPLY'">-->
<!--                        and pi.fields->>'$.applyType' in-->
<!--                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >-->
<!--                            #{itemSqlx}-->
<!--                        </foreach>-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_GXCSSQ'">-->
<!--                        and pi.fields->>'$.typeTreeId' in-->
<!--                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >-->
<!--                            #{itemSqlx}-->
<!--                        </foreach>-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_BA'">-->
<!--                        and pi.fields->>'$.balb' in-->
<!--                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >-->
<!--                            #{itemSqlx}-->
<!--                        </foreach>-->
<!--                    </when>-->
<!--                    <when test="projectItemCode == 'PROJECT_SCKXXPG'">-->
<!--                        and pi.fields->>'$.sqlx' in-->
<!--                        <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >-->
<!--                            #{itemSqlx}-->
<!--                        </foreach>-->
<!--                    </when>-->
<!--                </choose>-->
<!--            </if>-->
<!--        </where>-->
<!--        order by pio.id desc-->
<!--    </select>-->

    <select id="selectTProjectExecutionShowList" parameterType="TProjectExecution" resultMap="TProjectExecutionResult">
        SELECT
        pe.id,
        pe.project_order_id,
        pe.project_item_code,
        pe.process_user_id,
        pe.step,
        pe.assgin_user_name,
        pe.current_step,
        pe.STATUS,
        pe.fields,
        pe.assgin_user_name,
        pe.audit_remark,
        pe.audit_time,
        pe.create_by,
        pe.create_time,
        ifnull(pio.update_by,"") update_by,
        pe.update_time,
        pe.remark,
        pio.code item_order_code,
        pio.confirm_code,
        pio.current_step order_current_step,
        pio.create_time apply_create_time,
        pio.yj_time,
        pio.qw_time,
        c.name,
        c.short_name,
        p.id project_id,
        pi.id project_item_id,
        p.product_name,
        ifnull(pe.lab_no,p.laboratory) laboratory,
        p.item_names,
        pi.item_name,
        pi.type item_type,
        pi.status item_status,
        pi.`fields` item_fields,
        pio.order_status project_item_order_status,
        IFNULL(p.project_yw_name,c.customer_yw) customer_yw,
        p.create_by project_create_by
        FROM
        t_project_item_order pio
        inner join t_project_execution pe  on pe.project_order_id = pio.id
        inner join t_project_item pi on pio.item_id = pi.id
        inner join t_project p on pi.project_id = p.id
        inner join t_customer c on p.customer_id = c.id
        <where>
            and pio.del_flag = 0 and pi.del_flag = 0
            <if test="projectOrderId != null "> and pe.project_order_id = #{projectOrderId}</if>
            <if test="isLog != null "> and ifnull(pe.is_log,0) = #{isLog}</if>
            <if test="projectItemCode != null  and projectItemCode != ''"> and pi.type = #{projectItemCode}</if>
            <if test="itemOrderCode != null  and itemOrderCode != ''"> and  pio.code like concat(#{itemOrderCode},'%')</if>
            <if test="confirmCode != null  and confirmCode != ''"> and  pio.confirm_code like concat('%',#{confirmCode},'%')</if>
            <if test="processUserId != null ">and p.customer_id in (SELECT customer_id FROM t_customer_assist WHERE status = 0 and user_id = #{processUserId})</if>
            <if test="step != null "> and pe.step = #{step}</if>
            <if test="currentStep != null  and currentStep != ''"> and pe.current_step = #{currentStep}</if>
            <if test="status != null "> and pe.status = #{status}</if>
            <if test="auditRemark != null  and auditRemark != ''"> and pe.audit_remark = #{auditRemark}</if>
            <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="params.beginCreate != null and params.beginCreate != ''">
                AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
            </if>
            <if test="params.endCreate != null and params.endCreate != ''">
                AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
            </if>
            <if test="params.beginApplyCreate != null and params.beginApplyCreate != ''">
                AND date_format(pe.create_time,'%y%m%d') &gt;= date_format(#{params.beginApplyCreate},'%y%m%d')
            </if>
            <if test="params.endApplyCreate!= null and params.endApplyCreate != ''">
                AND date_format(pe.create_time,'%y%m%d') &lt;= date_format(#{params.endApplyCreate},'%y%m%d')
            </if>
            <if test="params.beginYj != null and params.beginYj != ''">
                AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
            </if>
            <if test="params.endYj != null and params.endYj != ''">
                AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
            </if>
            <if test=" params.statusArray != null and params.statusArray.length > 0 ">
                and pio.STATUS in
                <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.progressArray != null and params.progressArray.length > 0 ">
                and p.progress in
                <foreach collection="params.progressArray" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.shStatus != null and params.shStatus.length > 0 ">
                and pe.status in
                <foreach collection="params.shStatus" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">
                and pe.current_step in
                <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">
                and p.customer_id != #{params.customerIgnoreId}
            </if>
            <if test="params.customerId != null and params.customerId != ''">
                and p.customer_id = #{params.customerId}
            </if>
            <if test="createBy != null and createBy != ''">
                and pio.create_by = #{createBy}
            </if>
        </where>
        order by pio.id desc
    </select>

    <select id="selectTProjectExecutionById" parameterType="Long" resultMap="TProjectExecutionResult">
        SELECT
            pe.id,
            pe.project_order_id,
            pe.project_item_code,
            pe.process_user_id,
            pe.step,
            pe.current_step,
            pe.STATUS,
            pe.audit_remark,
            pe.audit_time,
            pe.create_by,
            pe.create_time,
            pe.update_by,
            pe.update_time,
            pe.remark,
            pe.assgin_user_name,
            pio.code item_order_code,
            pi.code item_code,
            pi.type item_type,
            p.code,
            c.name,
            IF(P.PROJECT_TYPE = 0, p.product_name,(select GROUP_CONCAT(bpp.product_name) from t_before_project_product bpp where FIND_IN_SET(bpp.id,pi.project_product_id))) product_name,
            pe.confirm_code,
            pio.customer_id,
            pio.qw_time,
            pio.yj_time,
            pio.first_yj_time,
            pio.sj_time,
            pio.create_time apply_create_time,
            pio.fields field_datas,
            pio.bom_change_array,
            pio.bom_change_reply_array,
            pe.fields,
            p.id project_id,
            pi.id project_item_id,
            pi.project_product_id,
            pi.fields item_fields,
            pi.remark item_remark,
            p.item_names,
            pi.item_name,
            p.order_status,
            p.order_yj_time,
            p.order_num,
            p.yj_sp_ddl,
            pio.order_status project_item_order_status,
            pio.status project_order_status,
            pe.retrun_to,
            pe.is_merge,
            pe.merge_key,
            pe.yj_time executionYjTime,
            pe.return_to_dept,
            p.project_type,
            pe.lab_no,
            tpp.name item_text
        FROM
            t_project_execution pe
            left join t_project_item_order pio on pe.project_order_id = pio.id
            left join t_project_item pi on pio.item_id = pi.id
            left join t_project p on pi.project_id = p.id
            left join t_customer c on p.customer_id = c.id
            left join t_project_product tpp on (pi.item_name = tpp.guid and pi.project_id = tpp.project_id)
        where pe.id = #{id}
    </select>

    <select id="selectTProjectExecutionByOrderId" parameterType="Long" resultMap="TProjectExecutionResult">
        SELECT
            pe.id,
            pe.project_order_id,
            pe.project_item_code,
            pe.process_user_id,
            pe.step,
            pe.current_step,
            pe.STATUS,
            pe.audit_remark,
            pe.audit_time,
            pe.create_by,
            pe.create_time,
            pe.update_by,
            pe.update_time,
            pe.remark,
            pe.assgin_user_name,
            pio.code item_order_code,
            pi.code item_code,
            pi.type item_type,
            p.code,
            c.name,
            IF(P.PROJECT_TYPE = 0, p.product_name,(select GROUP_CONCAT(bpp.product_name) from t_before_project_product bpp where FIND_IN_SET(bpp.id,pi.project_product_id))) product_name,
            pio.confirm_code,
            pio.qw_time,
            pio.yj_time,
            pio.first_yj_time,
            pio.sj_time,
            pio.create_time apply_create_time,
            pio.fields field_datas,
            pe.fields,
            p.id project_id,
            pi.id project_item_id,
            pi.fields item_fields,
            pi.remark item_remark,
            p.item_names,
            pi.item_name,
            p.order_status,
            p.order_yj_time,
            p.order_num,
            p.yj_sp_ddl,
            pio.order_status project_item_order_status
        FROM
            t_project_execution pe
                left join t_project_item_order pio on pe.project_order_id = pio.id
                left join t_project_item pi on pio.item_id = pi.id
                left join t_project p on pi.project_id = p.id
                left join t_customer c on p.customer_id = c.id
        where pe.project_order_id = #{id} and pe.status in (0,4) and pe.current_step = '执行'
        order by pe.id desc limit 1
    </select>

    <insert id="insertTProjectExecution" parameterType="TProjectExecution" useGeneratedKeys="true" keyProperty="id">
        insert into t_project_execution
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectOrderId != null">project_order_id,</if>
            <if test="projectItemCode != null">project_item_code,</if>
            <if test="processUserId != null">process_user_id,</if>
            <if test="step != null">step,</if>
            <if test="currentStep != null">current_step,</if>
            <if test="fields != null">fields,</if>
            <if test="status != null">status,</if>
            <if test="auditRemark != null">audit_remark,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="assginUserName != null">assgin_user_name,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="isMerge != null">is_merge,</if>
            <if test="mergeKey != null">merge_key,</if>
            <if test="returnToDept != null">return_to_dept,</if>
            <if test="labNo != null">lab_no,</if>
            <if test="confirmCode != null">confirm_code,</if>
            <if test="addStyle != null">add_style,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectOrderId != null">#{projectOrderId},</if>
            <if test="projectItemCode != null">#{projectItemCode},</if>
            <if test="processUserId != null">#{processUserId},</if>
            <if test="step != null">#{step},</if>
            <if test="currentStep != null">#{currentStep},</if>
            <if test="fields != null">#{fields},</if>
            <if test="status != null">#{status},</if>
            <if test="auditRemark != null">#{auditRemark},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="assginUserName != null">#{assginUserName},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="isMerge != null">#{isMerge},</if>
            <if test="mergeKey != null">#{mergeKey},</if>
            <if test="returnToDept != null">#{returnToDept},</if>
            <if test="labNo != null">#{labNo},</if>
            <if test="confirmCode != null">#{confirmCode},</if>
            <if test="addStyle != null">#{addStyle},</if>
        </trim>
    </insert>

    <update id="updateTProjectExecution" parameterType="TProjectExecution">
        update t_project_execution
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectOrderId != null">project_order_id = #{projectOrderId},</if>
            <if test="projectItemCode != null">project_item_code = #{projectItemCode},</if>
            <if test="processUserId != null">process_user_id = #{processUserId},</if>
            <if test="step != null">step = #{step},</if>
            <if test="currentStep != null">current_step = #{currentStep},</if>
            <if test="fields != null">fields = #{fields},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="assginUserName != null">assgin_user_name = #{assginUserName},</if>
            <if test="isLog != null">is_log = #{isLog},</if>
            <if test="returnTo != null">retrun_to = #{returnTo},</if>
            <if test="isMerge != null">is_merge = #{isMerge},</if>
            <if test="mergeKey != null">merge_key = #{mergeKey},</if>
            <if test="yjTime != null">yj_time = #{yjTime},</if>
            <if test="returnToDept != null">return_to_dept = #{returnToDept},</if>
            <if test="labNo != null">lab_no = #{labNo},</if>
            <if test="confirmCode != null">confirm_code = #{confirmCode},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateProjectExecutionApplyData" parameterType="java.util.Map">
        update t_project_execution set fields = #{fields} where id = #{projectExecutionId}
    </update>

    <delete id="deleteTProjectExecutionById" parameterType="Long">
        delete from t_project_execution where id = #{id}
    </delete>

    <delete id="deleteTProjectExecutionByIds" parameterType="String">
        delete from t_project_execution where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <delete id="deleteProjectExecutionUserInfo" parameterType="Long">
       delete from t_project_execution_user where execution_id in (
         select id from t_project_execution where project_order_id = #{id}
       )
    </delete>

    <delete id="deleteProjectExecutionInfo" parameterType="Long">
        delete from t_project_execution where project_order_id = #{id}
    </delete>

    <select id="selectProjectItemOrderExecutionList" parameterType="java.lang.Long" resultType="java.util.Map">
        select pe.id,pe.`status`,pe.current_step currentStep,pe.audit_remark auditRemark,pe.fields,
               dept_name deptName,
               pe.step,
               assgin_user_name assginUserName,
               pe.remark,
               pe.confirm_code confirmCode,
               DATE_FORMAT(ifnull(pe.audit_time,pe.update_time),'%Y-%m-%d %H:%i:%s') auditTime,DATE_FORMAT(pe.create_time,'%Y-%m-%d %H:%i:%s') createTime,
               ifnull(u.nick_name,u.user_name) userName,
               pe.is_merge isMerge,
               pe.merge_key mergeKey,
               DATE_FORMAT(pe.yj_time,'%Y-%m-%d') yiTime
        from t_project_execution pe
        left join sys_user u on pe.process_user_id = u.user_id
        where pe.project_order_id = #{orderId}
        order by pe.id
    </select>

    <update id="updateProjectExecutionInfo" parameterType="TProjectExecution">
        update t_project_execution
        <trim prefix="SET" suffixOverrides=",">
            <if test="currentStep != null">current_step = #{currentStep},</if>
            <if test="processUserId != null">process_user_id = #{processUserId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isLog != null">is_log = #{isLog},</if>
        </trim>
        where project_order_id = #{projectOrderId} and status = 0
    </update>

    <select id="selectExecutionOrderList" parameterType="TProjectExecution" resultType="TProjectExecution" >
        SELECT
            pe.id,
            pe.project_order_id,
            pe.current_step
        FROM
            t_project_execution pe
            inner join t_project_execution_user peu ON pe.id = peu.execution_id
        WHERE
            peu.process_user_id = #{processUserId}
            and peu.is_audit = 0
          AND pe.STATUS IN (0,4)
    </select>

    <select id="selectExecutionStatistics" parameterType="TProjectExecution" resultType="Map" >
        SELECT
            sum(if(pe.`current_step`='初审' or pe.`current_step`='复审',1,0)) sh,
            sum(if(pe.`current_step`='执行',1,0)) zx
        FROM
            t_project_execution pe
            INNER JOIN t_project_execution_user peu ON peu.execution_id = pe.id
        where pe.`status` = 0
          and peu.process_user_id = #{processUserId}
    </select>

    <select id="selectExecutionAuditList" parameterType="TProjectExecution" resultType="Map" >
        SELECT
            pe.id,
            pio.id projectItemOrderId,
            p.id projectId,
            p.project_no projectNo,
            IF(P.PROJECT_TYPE = 0, p.product_name,(select GROUP_CONCAT(bpp.product_name) from t_before_project_product bpp where FIND_IN_SET(bpp.id,pi.project_product_id))) product_name,
            p.item_names itemNames,
            pi.id itemId,
            pi.`status` itemStatus,
            pi.finished_time finishedTime,
            pi.confirm_code itemConfirmCode,
            pi.hs_time itemHsTime,
            pi.type itemType,
            pi.average_rate itemAverageRate,
            pi.`fields` itemFields,
            pio.create_time itemCreateTime,
            pio.qw_time qwTime,
            pio.yj_time yjTime,
            pio.sj_time sjTime,
            pio.code projectItemOrderCode,
            pe.current_step currentStep,
            pe.current_step executionCurrentStep,
            pio.`status`,
            pe.`status` executionStatus,
            pio.order_status orderStatus,
            pio.sh_status shStatus,
            pio.sh_status1 shStatus1,
            pio.confirm_code confirmCode,
            pio.create_time createTime,
            pio.update_time updateTime,
            pio.timely_rate timelyRate,
            ifnull(c.short_name,c.`name`) customerName,
            ifnull(pio.update_by,"") update_by,
            peu.is_audit,
            c.short_name shortName
        FROM
         t_project_item_order pio
        inner join t_project_execution pe on pe.project_order_id = pio.id
        inner join t_project_item pi on pio.item_id = pi.id
        inner join t_project p on pi.project_id = p.id
        inner join t_customer c on p.customer_id = c.id
        inner join t_project_execution_user peu on pe.id = peu.execution_id
        WHERE pio.del_flag = 0
        AND pi.del_flag = 0
        AND p.`status` in ('0','4')
        AND  pe.`status` in (0,4)
        <if test="processUserId!=null">
            and (
                peu.process_user_id = #{processUserId}
                or
                peu.process_post_name in (
                select r.role_name from sys_user u
                inner join sys_user_role ur on u.user_id = ur.user_id
                inner join sys_role r on ur.role_id = r.role_id
                where u.user_id = #{processUserId}
                )
            )
        </if>
        <if test="productName != null and productName != ''">
            and (
                p.product_name like concat('%',#{productName},'%')
                or p.project_no like concat('%',#{productName},'%')
            )
        </if>
        <if test="customerName != null and customerName != ''">
            and c.name like concat('%',#{customerName},'%')
        </if>
        <if test="customerId != null">
            and p.customer_id = #{customerId}
        </if>
        <if test="projectId != null">
            and p.id = #{projectId}
        </if>
        <if test="currentStep != null and currentStep != ''">
            and pio.current_step = #{currentStep}
        </if>
        <if test="projectItemType != null and projectItemType != ''">
            and pi.type = #{projectItemType}
        </if>
        <if test="params.beginCreate != null and params.beginCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
        </if>
        <if test="params.endCreate != null and params.endCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
        </if>
        <if test="params.beginApplyCreate != null and params.beginApplyCreate != ''">
            AND date_format(pe.create_time,'%y%m%d') &gt;= date_format(#{params.beginApplyCreate},'%y%m%d')
        </if>
        <if test="params.endApplyCreate!= null and params.endApplyCreate != ''">
            AND date_format(pe.create_time,'%y%m%d') &lt;= date_format(#{params.endApplyCreate},'%y%m%d')
        </if>
        <if test="params.beginYj != null and params.beginYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
        </if>
        <if test="params.endYj != null and params.endYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
        </if>
        order by peu.id,pi.id desc
    </select>

    <select id="selectAuditCustomerList" parameterType="TProjectExecution" resultType="Customer" >
        SELECT
            c.id,
            c.`name`,
            c.short_name,
            c.customer_level,
            c.create_time
        FROM
            t_customer c
        WHERE
        c.id IN (
            SELECT
                pio.customer_id
            FROM
                t_project_execution pe
                INNER JOIN t_project_item_order pio ON pe.project_order_id = pio.id
                INNER JOIN t_project p ON pio.project_id = p.id
                INNER JOIN t_project_execution_user peu ON pe.id = peu.execution_id
            WHERE
                pio.del_flag = 0
            AND p.`status` = '0'
            AND peu.process_user_id = #{processUserId}
            AND peu.is_audit = 0
        )
        <if test="customerName != null  and customerName != ''"> and c.name like concat('%', #{customerName}, '%')</if>
        <if test="customerLevel != null  and customerLevel != ''"> and c.customer_level = #{customerLevel}</if>
    </select>

    <select id="selectProjectExecutionList" parameterType="TProjectExecution" resultType="ProjectExecution" >
        SELECT
            pio.id order_id,
            pio.code,
            pio.current_step,
            pio.create_time,
            cb.brand_name,
            c.`name` customerName,
            c.customer_level customerLevel,
            p.net_content,
            p.product_name,
            pio.create_by,
            pio.remark,
            pio.`fields`
        FROM   t_project_item_order pio
                   left join t_project p on pio.project_id = p.id
                   left join t_customer_brand cb on p.customer_brand_id  = cb.brand_code
                   left join t_customer c on p.customer_id = c.id
        where
            pio.del_flag = 0  and pio.add_style = 0 and pio.id in (
            select project_order_id from t_project_execution pe where  pe.project_item_code = 'PROJECT_NRWBJ'
            <if test=" params.shStatus != null and params.shStatus.length > 0 ">
                and pe.status in
                <foreach collection="params.shStatus" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="params.beginApplyCreate != null and params.beginApplyCreate != ''">
                AND date_format(pe.create_time,'%y%m%d') &gt;= date_format(#{params.beginApplyCreate},'%y%m%d')
            </if>
            <if test="params.endApplyCreate!= null and params.endApplyCreate != ''">
                AND date_format(pe.create_time,'%y%m%d') &lt;= date_format(#{params.endApplyCreate},'%y%m%d')
            </if>
        )
        <if test="itemOrderCode != null  and itemOrderCode != ''"> and  pio.code like concat(#{itemOrderCode}, '%')</if>
        <if test="confirmCode != null  and confirmCode != ''"> and  pio.confirm_code like  concat('%', #{confirmCode}, '%')</if>
        <if test="laboratory != null "> and p.laboratory = #{laboratory}</if>
        <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>
        <if test="params.beginCreate != null and params.beginCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
        </if>
        <if test="params.endCreate != null and params.endCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
        </if>
        <if test="params.beginYj != null and params.beginYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
        </if>
        <if test="params.endYj != null and params.endYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
        </if>
        <if test=" params.statusArray != null and params.statusArray.length > 0 ">
            and pio.STATUS in
            <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">
            and pio.current_step in
            <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">
            and p.customer_id != #{params.customerIgnoreId}
        </if>
        <if test="params.customerId != null and params.customerId != ''">
            and p.customer_id = #{params.customerId}
        </if>
        <if test="createBy != null and createBy != ''">
            and pio.create_by = #{createBy}
        </if>
        order by pio.id desc
    </select>

    <select id="selectFinishedProductQuotationList" parameterType="TProjectExecution" resultType="FinishedProductQuotation" >
        SELECT
            pio.id order_id,
            pio.code,
            pio.current_step,
            pio.create_time,
            cb.brand_name,
            c.`name` customerName,
            p.net_content,
            p.product_name,
            pio.create_by,
            pio.remark,
            pio.`fields`,
            p.packing_type,
            p.product_type,
            p.yj_sp_ddl,
            p.order_status,
            p.order_num
        FROM   t_project_item_order pio
                   left join t_project p on pio.project_id = p.id
                   left join t_customer_brand cb on p.customer_brand_id  = cb.brand_code
                   left join t_customer c on p.customer_id = c.id
        where
            pio.del_flag = 0 and pio.id in (
            select project_order_id from t_project_execution pe where  pe.project_item_code = 'PROJECT_CHPBJ'
            <if test=" params.shStatus != null and params.shStatus.length > 0 ">
                and pe.status in
                <foreach collection="params.shStatus" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="params.beginApplyCreate != null and params.beginApplyCreate != ''">
                AND date_format(pe.create_time,'%y%m%d') &gt;= date_format(#{params.beginApplyCreate},'%y%m%d')
            </if>
            <if test="params.endApplyCreate!= null and params.endApplyCreate != ''">
                AND date_format(pe.create_time,'%y%m%d') &lt;= date_format(#{params.endApplyCreate},'%y%m%d')
            </if>
        )
        <if test="itemOrderCode != null  and itemOrderCode != ''"> and  pio.code like concat(#{itemOrderCode}, '%')</if>
        <if test="confirmCode != null  and confirmCode != ''"> and  pio.confirm_code like  concat('%', #{confirmCode}, '%')</if>
        <if test="laboratory != null "> and p.laboratory = #{laboratory}</if>
        <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>
        <if test="params.beginCreate != null and params.beginCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
        </if>
        <if test="params.endCreate != null and params.endCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
        </if>
        <if test="params.beginYj != null and params.beginYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
        </if>
        <if test="params.endYj != null and params.endYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
        </if>
        <if test=" params.statusArray != null and params.statusArray.length > 0 ">
            and pio.STATUS in
            <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">
            and pio.current_step in
            <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">
            and p.customer_id != #{params.customerIgnoreId}
        </if>
        <if test="params.customerId != null and params.customerId != ''">
            and p.customer_id = #{params.customerId}
        </if>
        <if test="createBy != null and createBy != ''">
            and pio.create_by = #{createBy}
        </if>
        order by pio.id desc
    </select>

    <select id="selectProjectExecutionQcfList" parameterType="TProjectExecution" resultType="ProjectQcf" >
        SELECT
        pio.id order_id,
        pio.code,
        pio.current_step,
        pio.create_time,
        pio.confirm_code,
        cb.brand_name,
        c.`name` customerName,
        p.net_content,
        p.product_name,
        pio.create_by,
        pio.remark,
        pio.`fields`,
        pio.sj_time
        FROM   t_project_item_order pio
        left join t_project p on pio.project_id = p.id
        left join t_customer_brand cb on p.customer_brand_id  = cb.brand_code
        left join t_customer c on p.customer_id = c.id
        where
        pio.del_flag = 0 and pio.id in (
        select project_order_id from t_project_execution pe where  pe.project_item_code = 'PROJECT_QCF'
        <if test=" params.shStatus != null and params.shStatus.length > 0 ">
            and pe.status in
            <foreach collection="params.shStatus" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="params.beginApplyCreate != null and params.beginApplyCreate != ''">
            AND date_format(pe.create_time,'%y%m%d') &gt;= date_format(#{params.beginApplyCreate},'%y%m%d')
        </if>
        <if test="params.endApplyCreate!= null and params.endApplyCreate != ''">
            AND date_format(pe.create_time,'%y%m%d') &lt;= date_format(#{params.endApplyCreate},'%y%m%d')
        </if>
        )
        <if test="itemOrderCode != null  and itemOrderCode != ''"> and  pio.code like concat(#{itemOrderCode}, '%')</if>
        <if test="confirmCode != null  and confirmCode != ''"> and  pio.confirm_code like  concat('%', #{confirmCode}, '%')</if>
        <if test="laboratory != null "> and p.laboratory = #{laboratory}</if>
        <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>
        <if test="params.beginCreate != null and params.beginCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
        </if>
        <if test="params.endCreate != null and params.endCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
        </if>
        <if test="params.beginYj != null and params.beginYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
        </if>
        <if test="params.endYj != null and params.endYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
        </if>
        <if test=" params.statusArray != null and params.statusArray.length > 0 ">
            and pio.STATUS in
            <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">
            and pio.current_step in
            <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">
            and p.customer_id != #{params.customerIgnoreId}
        </if>
        <if test="params.customerId != null and params.customerId != ''">
            and p.customer_id = #{params.customerId}
        </if>
        <if test="createBy != null and createBy != ''">
            and pio.create_by = #{createBy}
        </if>
        order by pio.id desc
    </select>

    <select id="selectProjectExecutionNrwPaidanList" parameterType="TProjectExecution" resultType="NrwArrangementPaidan" >
        SELECT
        pio.id order_id,
        pio.code,
        pio.current_step,
        pio.create_time,
        pio.confirm_code,
        cb.brand_name,
        ifnull(c.short_name,c.`name`) customerName,
        p.net_content,
        p.product_name,
        p.ck_cp,
       p.zb,
       ifnull(pe.lab_no,p.laboratory) laboratory,
       c.customer_level,
       p.level,
        IFNULL(p.project_yw_name,c.customer_yw) customerAssist,
        pio.create_by,
        pe.create_time csTime,
        pio.sh_status,
        pe.fields auditFields,
        pio.remark,
        pio.`fields`,
        pio.sj_time,
        pio.qw_time
        FROM   t_project_item_order pio
        inner join t_project p on pio.project_id = p.id
        inner join t_customer_brand cb on p.customer_brand_id  = cb.brand_code
        inner join t_customer c on p.customer_id = c.id
        inner join t_project_execution pe on pe.project_order_id = pio.id
        inner join t_project_execution_user peu on pe.id = peu.execution_id
        where
        pio.del_flag = 0 and pe.project_item_code = #{projectItemType} and pe.current_step = '初审'
        and (
                peu.process_user_id = #{processUserId}
                or
                peu.process_post_name in (
                    select r.role_name from sys_user u
                    inner join sys_user_role ur on u.user_id = ur.user_id
                    inner join sys_role r on ur.role_id = r.role_id
                    where u.user_id = #{processUserId}
                )
            )
        <if test="itemOrderCode != null  and itemOrderCode != ''"> and  pio.code like concat(#{itemOrderCode}, '%')</if>
        <if test="confirmCode != null  and confirmCode != ''"> and  pio.confirm_code like  concat('%', #{confirmCode}, '%')</if>
        <if test="laboratory != null "> and p.laboratory = #{laboratory}</if>
        <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>
        <if test="params.beginCreate != null and params.beginCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
        </if>
        <if test="params.endCreate != null and params.endCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
        </if>
        <if test="params.beginApplyCreate != null and params.beginApplyCreate != ''">
            AND date_format(pe.create_time,'%y%m%d') &gt;= date_format(#{params.beginApplyCreate},'%y%m%d')
        </if>
        <if test="params.endApplyCreate!= null and params.endApplyCreate != ''">
            AND date_format(pe.create_time,'%y%m%d') &lt;= date_format(#{params.endApplyCreate},'%y%m%d')
        </if>
        <if test="params.beginYj != null and params.beginYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
        </if>
        <if test="params.endYj != null and params.endYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
        </if>
        <if test=" params.statusArray != null and params.statusArray.length > 0 ">
            and pio.STATUS in
            <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">
            and pe.current_step in
            <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test=" params.shStatus != null and params.shStatus.length > 0 ">
            and pe.status in
            <foreach collection="params.shStatus" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">
            and p.customer_id != #{params.customerIgnoreId}
        </if>
        <if test="params.customerId != null and params.customerId != ''">
            and p.customer_id = #{params.customerId}
        </if>
        <if test="createBy != null and createBy != ''">
            and pio.create_by = #{createBy}
        </if>
        order by pio.id desc
    </select>

    <select id="selectProjectExecutionQcfPaidanList" parameterType="TProjectExecution" resultType="ProjectQcfPaidan" >
        SELECT
        pio.id order_id,
        pio.code,
        pio.current_step,
        pio.create_time,
        pio.confirm_code,
        cb.brand_name,
        ifnull(c.short_name,c.`name`) customerName,
        p.net_content,
        p.product_name,
        pio.create_by,
        pe.create_time csTime,
        pio.sh_status,
        pe.fields auditFields,
        pio.remark,
        pio.`fields`,
        pio.sj_time,
        pio.qw_time
        FROM t_project_item_order pio
        inner join t_project_item pi on pio.item_id = pi.id
        inner join t_project p on pio.project_id = p.id
        inner join t_customer_brand cb on p.customer_brand_id = cb.brand_code
        inner join t_customer c on p.customer_id = c.id
        inner join t_project_execution pe on pe.project_order_id = pio.id
        inner join t_project_execution_user peu on pe.id = peu.execution_id
        where
        pio.del_flag = 0  and pio.current_step = '初审'  and pe.`status` = 0
        <if test="projectItemType!='PROJECT_INSPECTION_AND_FILING'">
            and pe.project_item_code = #{projectItemType}
        </if>
        <if test="projectItemType=='PROJECT_INSPECTION_AND_FILING'">
            and ((pi.fields->'$.applyType'  in ('0','4') and  pe.project_item_code = #{projectItemType}) or pe.project_item_code = 'PROJECT_SONGJIAN')
        </if>
        and (
              peu.process_user_id = #{processUserId}
                or
                peu.process_post_name in (
                select r.role_name from sys_user u
                inner join sys_user_role ur on u.user_id = ur.user_id
                inner join sys_role r on ur.role_id = r.role_id
                where u.user_id = #{processUserId}
                )
            )
        <if test="itemOrderCode != null  and itemOrderCode != ''"> and  pio.code like concat(#{itemOrderCode}, '%')</if>
        <if test="confirmCode != null  and confirmCode != ''"> and  pio.confirm_code like  concat('%', #{confirmCode}, '%')</if>
        <if test="laboratory != null "> and p.laboratory = #{laboratory}</if>
        <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>
        <if test="params.beginCreate != null and params.beginCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
        </if>
        <if test="params.endCreate != null and params.endCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
        </if>
        <if test="params.beginApplyCreate != null and params.beginApplyCreate != ''">
            AND date_format(pe.create_time,'%y%m%d') &gt;= date_format(#{params.beginApplyCreate},'%y%m%d')
        </if>
        <if test="params.endApplyCreate!= null and params.endApplyCreate != ''">
            AND date_format(pe.create_time,'%y%m%d') &lt;= date_format(#{params.endApplyCreate},'%y%m%d')
        </if>
        <if test="params.beginYj != null and params.beginYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
        </if>
        <if test="params.endYj != null and params.endYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
        </if>
        <if test=" params.statusArray != null and params.statusArray.length > 0 ">
            and pio.STATUS in
            <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">
            and pe.current_step in
            <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test=" params.shStatus != null and params.shStatus.length > 0 ">
            and pe.status in
            <foreach collection="params.shStatus" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">
            and p.customer_id != #{params.customerIgnoreId}
        </if>
        <if test="params.customerId != null and params.customerId != ''">
            and p.customer_id = #{params.customerId}
        </if>
        <if test="createBy != null and createBy != ''">
            and pio.create_by = #{createBy}
        </if>
        order by pio.id desc
    </select>

    <select id="selectProjectItemOrderExcelList" parameterType="TProjectExecution" resultType="ProjectItemOrderExcel" >
        SELECT
        pio.id,
        pio.item_id,
        pio.CODE itemOrderCode,
        pio.dyn_code,
        pio.STATUS orderStatus,
        pio.customer_id,
        pio.step,
        pio.current_step,
        pio.sh_status,
        pio.sh_status1,
        pio.confirm_code itemConfirmCode,
        pio.qw_time,
        pio.yj_time,
        pio.first_yj_time,
        pio.delay_yj_time,
        pio.sj_time,
        pio.timely_rate,
        pio.amendments,
        pio.amendments1,
        pio.fields,
        pio.audit_fields,
        pio.remark,
        pio.del_flag,
        pio.user_id,
        pio.create_by applyUser,
        pio.create_time applyTime,
        pio.update_time,
        ifnull(pio.update_by,"") update_by,
        pio.order_status projectItemOrderStatus,
        pio.is_revoke,
        p.`project_no` projectNo,
        p.product_name,
        c.name customer_name,
        c.customer_level,
        IFNULL(p.project_yw_name,c.customer_yw) customerAssist,
        pi.`code` projectItemCode,
        pi.item_name projectItemName,
        p.id project_id,
        pi.fields item_fields,
        pi.type project_item_type,
        pi.status item_status,
        pio.confirm_code finalConfirmCode,
        pi.item_name,
        p.item_names,
        p.order_yj_time,
        p.order_num,
        p.laboratory,
        p.item_status project_item_status,
        p.yj_sp_ddl,
        p.product_type,
        p.level projectLevel,
        p.ck_cp ckCp,
        p.filer
        FROM
        t_project_item_order pio
        inner join t_project_item pi on pi.id = pio.item_id
        INNER JOIN t_project p on p.id = pi.project_id
        left join t_customer c on p.customer_id = c.id
        where
        pio.del_flag = 0 and pio.project_type = 0  and pio.id in (
        select project_order_id from t_project_execution pe
        inner join t_project_execution_user peu on pe.id = peu.execution_id where
        (
                peu.process_user_id = #{searchUserId}
                    or
                    peu.process_post_name in (
                        select r.role_name from sys_user u
                        inner join sys_user_role ur on u.user_id = ur.user_id
                        inner join sys_role r on ur.role_id = r.role_id
                        where u.user_id = #{searchUserId}
                    )
                )
            <if test="projectItemCode != null  and projectItemCode != ''"> and pi.type = #{projectItemCode}</if>
            <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">
                and pe.current_step in
                <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.shStatus != null and params.shStatus.length > 0 ">
                and pe.status in
                <foreach collection="params.shStatus" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="params.beginApplyCreate != null and params.beginApplyCreate != ''">
                AND date_format(pe.create_time,'%y%m%d') &gt;= date_format(#{params.beginApplyCreate},'%y%m%d')
            </if>
            <if test="params.endApplyCreate!= null and params.endApplyCreate != ''">
                AND date_format(pe.create_time,'%y%m%d') &lt;= date_format(#{params.endApplyCreate},'%y%m%d')
            </if>
        )
        <if test="projectItemCode != null and projectItemCode != '' and itemSqlxs != null and itemSqlxs.size() > 0 ">
            <choose>
                <when test="projectItemCode == 'PROJECT_NRW'">
                    and pi.fields->>'$.dylx' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_RQKF'">
                    and pi.fields->>'$.bcfl' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_SHEJI'">
                    and pi.fields->>'$.sjlx' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_PPT'">
                    and pi.fields->>'$.talx' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_QCF'">
                    and pi.fields->>'$.sqlx' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_OFFER'">
                    and pi.fields->>'$.bjlx' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_ZS'">
                    and pi.fields->>'$.zslx' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_BOM_APPLY'">
                    and pi.fields->>'$.applyType' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_GXCSSQ'">
                    and pi.fields->>'$.typeTreeId' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_BA'">
                    and pi.fields->>'$.balb' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_SCKXXPG'">
                    and pi.fields->>'$.sqlx' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
            </choose>
        </if>
        <if test="itemOrderCode != null  and itemOrderCode != ''"> and  pio.code like concat(#{itemOrderCode}, '%')</if>
        <if test="confirmCode != null  and confirmCode != ''"> and  pio.confirm_code like  concat('%', #{confirmCode}, '%')</if>
        <if test="laboratory != null "> and p.laboratory = #{laboratory}</if>
        <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>
        <if test="params.beginCreate != null and params.beginCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
        </if>
        <if test="params.endCreate != null and params.endCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
        </if>
        <if test="params.beginYj != null and params.beginYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
        </if>
        <if test="params.endYj != null and params.endYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
        </if>
        <if test=" params.statusArray != null and params.statusArray.length > 0 ">
            and pio.STATUS in
            <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">
            and p.customer_id != #{params.customerIgnoreId}
        </if>
        <if test="params.customerId != null and params.customerId != ''">
            and p.customer_id = #{params.customerId}
        </if>
        <if test="createBy != null and createBy != ''">
            and pio.create_by = #{createBy}
        </if>
        order by pio.id desc
    </select>

    <select id="selectProjectItemExcelList" parameterType="TProjectExecution" resultType="ProjectItemExcel">
        SELECT
        pi.id,
        pi.project_id,
        pi.type projectItemType,
        pi.dyn_code,
        pi.CODE itemOrderCode,
        pi.STATUS orderStatus,
        pi.item_name,
        p.item_names,
        pi.difficulty,
        pi.confirm_code itemConfirmCode,
        pi.change_reason,
        pi.success_rate,
        pi.hs_time,
        pi.average_rate,
        pi.finished_time sjTime,
        pi.FIELDS itemFields,
        pi.files,
        pi.del_flag,
        pi.remark,
        pi.create_by applyUser,
        pi.create_time applyTime,
        pi.update_by,
        pi.update_time,
        pi.change_reason,
        p.`code` projectCode,
        p.product_type productType,
        ifnull(c.short_name,c.name) customer_name,
        p.product_name,
        p.project_no,
        p.level projectLevel,
        p.laboratory,
        p.item_status,
        p.id project_id,
        c.id customer_id,
        c.customer_level,
        IFNULL(p.project_yw_name,c.customer_yw) customerAssist,
        FROM
        t_project_item pi
        inner join t_project p on p.id = pi.project_id
        inner join t_customer c on p.customer_id = c.id
        where
        pi.del_flag = 0 and pi.project_type = 0  and pi.id in (
            select item_id
            FROM  t_project_item_order pio
            inner  join t_project_execution pe on pio.id = pe.project_order_id
            inner join t_project_execution_user peu on pe.id = peu.execution_id where (peu.process_user_id = #{searchUserId} or
                        peu.process_post_name in (
                        select r.role_name from sys_user u
                        inner join sys_user_role ur on u.user_id = ur.user_id
                        inner join sys_role r on ur.role_id = r.role_id
                        where u.user_id = #{searchUserId}
                        ))
                <if test="projectItemCode != null  and projectItemCode != ''"> and pe.project_item_code like concat(#{projectItemCode},'%')</if>
                <if test="params.beginApplyCreate != null and params.beginApplyCreate != ''">
                    AND date_format(pe.create_time,'%y%m%d') &gt;= date_format(#{params.beginApplyCreate},'%y%m%d')
                </if>
                <if test="params.endApplyCreate!= null and params.endApplyCreate != ''">
                    AND date_format(pe.create_time,'%y%m%d') &lt;= date_format(#{params.endApplyCreate},'%y%m%d')
                </if>
                <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">
                    and pe.current_step in
                    <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                        #{item}
                    </foreach>
                </if>
                <if test=" params.shStatus != null and params.shStatus.length > 0 ">
                    and pe.status in
                    <foreach collection="params.shStatus" item="item" separator="," open="(" close=")" >
                        #{item}
                    </foreach>
                </if>
            )
        <if test="projectItemCode != null and projectItemCode != '' and itemSqlxs != null and itemSqlxs.size() > 0 ">
            <choose>
                <when test="projectItemCode == 'PROJECT_NRW'">
                    and pi.fields->>'$.dylx' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_RQKF'">
                    and pi.fields->>'$.bcfl' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_SHEJI'">
                    and pi.fields->>'$.sjlx' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_PPT'">
                    and pi.fields->>'$.talx' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_QCF'">
                    and pi.fields->>'$.sqlx' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_OFFER'">
                    and pi.fields->>'$.bjlx' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_ZS'">
                    and pi.fields->>'$.zslx' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_BOM_APPLY'">
                    and pi.fields->>'$.applyType' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_GXCSSQ'">
                    and pi.fields->>'$.typeTreeId' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_BA'">
                    and pi.fields->>'$.balb' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
                <when test="projectItemCode == 'PROJECT_SCKXXPG'">
                    and pi.fields->>'$.sqlx' in
                    <foreach collection="itemSqlxs" item="itemSqlx" separator="," open="(" close=")" >
                        #{itemSqlx}
                    </foreach>
                </when>
            </choose>
        </if>
        <if test="itemOrderCode != null  and itemOrderCode != ''"> and  pi.code like concat(#{itemOrderCode}, '%')</if>
        <if test="laboratory != null and laboratory!= '' "> and p.laboratory = #{laboratory}</if>
        <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>
        <if test="params.beginCreate != null and params.beginCreate != ''">
            AND date_format(pi.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
        </if>
        <if test="params.endCreate != null and params.endCreate != ''">
            AND date_format(pi.create_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
        </if>
        <if test=" params.statusArray != null and params.statusArray.length > 0 ">
            and pi.STATUS in
            <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">
            and p.customer_id != #{params.customerIgnoreId}
        </if>
        <if test="params.customerId != null and params.customerId != ''">
            and p.customer_id = #{params.customerId}
        </if>
        <if test="createBy != null and createBy != ''">
            and pi.create_by = #{createBy}
        </if>
        order by pi.id desc
    </select>

    <select id="selectProjectOrderCompareSoftData" parameterType="TProjectExecution" resultType="java.util.Map">
        select pio.id,pio.`code`,p.project_no,
        p.product_name,
        if(p.laboratory='0','宜侬','瀛彩') laboratory,
        ifnull(c.short_name,c.name) customer_name,
        pio.confirm_code,
        DATE_FORMAT(pio.sj_time,'%Y-%m-%d') sj_time,
        tpi.confirm_code itemConfirmCode,
        DATE_FORMAT(tpi.finished_time,'%Y-%m-%d') finishedTime
        from t_project_item_order pio
         inner join t_project_item tpi on pio.item_id = tpi.id
         inner join t_project p on pio.project_id = p.id
         inner join t_customer c on pio.customer_id = c.id
        where tpi.`type` = 'PROJECT_NRW'
        and pio.`status` = '3'
        and tpi.fields->>'$.dylx' = '0'
        <if test="itemOrderCode != null  and itemOrderCode != ''"> and  pio.code like concat(#{itemOrderCode}, '%')</if>
        <if test="confirmCode != null  and confirmCode != ''"> and  pio.confirm_code like  concat('%', #{confirmCode}, '%')</if>
        <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>
        <if test="params.beginCreate != null and params.beginCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
        </if>
        <if test="params.endCreate != null and params.endCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
        </if>
        <if test="params.beginYj != null and params.beginYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
        </if>
        <if test="params.endYj != null and params.endYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
        </if>
        <if test="params.beginDelayYjRange != null and params.beginDelayYjRange != ''">
            AND date_format(pio.delay_yj_time,'%y%m%d') &gt;= date_format(#{params.beginDelayYjRange},'%y%m%d')
        </if>
        <if test="params.endDelayYjRange != null and params.endDelayYjRange != ''">
            AND date_format(pio.delay_yj_time,'%y%m%d') &lt;= date_format(#{params.endDelayYjRange},'%y%m%d')
        </if>
        <if test=" params.statusArray != null and params.statusArray.length > 0 ">
            and pio.STATUS in
            <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">
            and pio.current_step in
            <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">
            and p.customer_id != #{params.customerIgnoreId}
        </if>
        <if test="params.customerId != null and params.customerId != ''">
            and p.customer_id = #{params.customerId}
        </if>
        <if test="createBy != null and createBy != ''">
            and pio.create_by = #{createBy}
        </if>
        order by pio.id desc
    </select>

    <select id="queryProjectExecutionDataDetail" resultType="TProjectExecution" parameterType="TProjectExecution">
        SELECT
            pe.id,
            pe.project_item_code,
            pe.project_order_id,
            pe.step,
            pe.current_step,
            pe.audit_remark,
            pe.remark,
            pe.assgin_user_name,
            pe.fields,
            pe.dept_name
        FROM
            t_project_execution pe
        where pe.step = #{returnTo} and project_order_id = #{projectOrderId}
        order by id desc limit 1
    </select>

    <!--获取流程信息-->
    <select id="queryProjectExecutionDataList" parameterType="TProjectExecution" resultType="TProjectExecution">
        SELECT id,status,is_merge,`fields`,merge_key,dept_name,audit_time,audit_remark,yj_time
        from t_project_execution
        where project_item_code = #{projectItemCode} and project_order_id = #{projectOrderId}
        and step = #{step} and is_merge = 1
    </select>

    <select id="queryProjectMergeExecutionDataList"  parameterType="TProjectExecution" resultType="TProjectExecution">
        SELECT
        pe.id,
        pe.project_item_code,
        pe.project_order_id,
        pe.step,
        pe.current_step,
        pe.audit_remark,
        pe.remark,
        pe.assgin_user_name,
        pe.fields,
        pe.dept_name
        ,pe.is_merge,pe.merge_key
        FROM
        t_project_execution pe
        where pe.step = #{returnTo} and project_order_id = #{projectOrderId}
        and pe.merge_key = #{mergeKey} order by id desc limit 1
    </select>

    <!--保存备案数据-->
    <insert id="insertIcpData" parameterType="java.util.Map">
        insert into t_legal_icp(customer_id,project_id,project_item_order_id,order_apply_type,create_by,create_time)
        values
        (#{customerId},#{projectId},#{projectItemOrderId},#{orderApplyType},#{createBy},now())
    </insert>

    <!--更新备案数据-->
    <update id="updateIcpData" parameterType="java.util.Map">
        update t_legal_icp set del_flag = #{delFlag},update_time= now() where project_id = #{projectId} and project_item_order_id = #{projectItemOrderId}
    </update>

    <!--查询是否存在-->
    <select id="queryLegalIcpCount" parameterType="java.util.Map" resultType="java.lang.Integer">
         select count(*) from t_legal_icp where project_id = #{projectId} and project_item_order_id = #{projectItemOrderId}
    </select>

    <!--获取送检备案执行流程数据-->
    <select id="queryProjectSjbaExecutionDataList" resultType="TProjectExecution" parameterType="com.ruoyi.project.domain.excel.ProjectSjbaDataExport">
               SELECT
               pe.id,pe.CURRENT_STEP currentStep,pe.step,pe.status,
               pe.create_time createTime,
               pe.audit_remark auditRemark,pe.audit_time auditTime,pe.merge_key mergeKey,
               pe.fields
               from t_project_execution  pe
               where pe.project_order_id = #{id}
               order by pe.id desc
    </select>

    <!--获取复审备案信息-->
    <select id="queryProjectSjbaPassExecutionData" parameterType="java.lang.Long" resultType="com.alibaba.fastjson.JSONObject">
        SELECT id,`fields` from t_project_execution
        where project_order_id = #{id}
        and merge_key = 'yanfa'
        and step = 2
        and status = 0
    </select>

    <!--更新信息-->
    <update id="updateProjectExecutionSjbaInfo" parameterType="java.util.Map">
       update t_project_execution set status= 1,audit_time = now(),
                       process_user_id = #{userId},
                      `fields` = #{fields},
                      update_time = now(),
                      update_by = '管理员'
                where id = #{executionId}
    </update>

    <!--获取复审数据-->
    <select id="queryProjectExecutionSjbaDataList" parameterType="java.lang.Long" resultType="TProjectExecution">
        SELECT id,merge_key,`fields`,`status`,audit_time,is_merge,dept_name,audit_remark from t_project_execution
        where project_order_id = #{projectOrderId}
        and step = 2
        and is_merge = 1
        order by id desc
    </select>

    <!--获取是否有正在进行中订单-->
    <select id="queryProjectSjbaProcessCount" resultType="java.lang.Integer" parameterType="java.lang.Long">
        SELECT count(*) from t_project_execution
        where project_order_id = #{projectOrderId}
        and step = 3
        and status in (0,4)
    </select>

    <select id="selectTProjectExecutionStatusById" parameterType="Long" resultType="TProjectExecution">
        select id,status,lab_no from t_project_execution where id = #{id}
    </select>

    <!-- 获取供应商生产商信息 -->
    <select id="queryMaterialSupplierDataList" parameterType="java.lang.Long" resultType="com.alibaba.fastjson.JSONObject">
        select id,material_code,agent_name,supplier_name
        from zdy_material_supplier
        where is_exists = 0
        <if test="supplierId!=null">
            and id = #{supplierId}
        </if>
        order by id desc limit 100
    </select>

    <select id="selectFlowerRejectList" parameterType="TProjectExecution" resultType="ProjectItemOrderExcel" >
        SELECT
        pio.id,
        pio.item_id,
        pio.CODE itemOrderCode,
        pio.dyn_code,
        pio.STATUS orderStatus,
        pio.customer_id,
        pio.step,
        pio.current_step,
        pio.sh_status,
        pio.sh_status1,
        pio.confirm_code itemConfirmCode,
        pio.qw_time,
        pio.yj_time,
        pio.first_yj_time,
        pio.delay_yj_time,
        pio.sj_time,
        pio.timely_rate,
        pio.amendments,
        pio.amendments1,
        pio.audit_fields,
        pio.remark,
        pio.del_flag,
        pio.user_id,
        pio.create_by applyUser,
        pio.create_time applyTime,
        pio.update_time,
        ifnull(pio.update_by,"") update_by,
        pio.order_status projectItemOrderStatus,
        pio.is_revoke,
        p.`project_no` projectNo,
        p.product_name,
        c.name customer_name,
        c.customer_level,
        IFNULL(p.project_yw_name,c.customer_yw) customerAssist,
        p.id project_id,
        pio.confirm_code finalConfirmCode,
        p.item_names,
        p.order_yj_time,
        p.order_num,
        p.laboratory,
        p.item_status project_item_status,
        p.yj_sp_ddl,
        p.product_type,
        p.level projectLevel,
        p.ck_cp ckCp,
        p.filer,
        pe.fields
        FROM
        t_project_item_order pio
        INNER JOIN t_project p ON p.id = pio.project_id
        INNER JOIN t_customer c ON c.id = p.customer_id
        INNER JOIN t_project_execution pe ON pe.project_order_id = pio.id
        where
            pio.del_flag = 0
            and pe.project_item_code = 'PROJECT_MANUSCRIPT_REVIEW'
            AND pe.`status` = 2
            and pe.step = 3
        <if test="projectItemCode != null  and projectItemCode != ''"> and pi.type = #{projectItemCode}</if>
        <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">
            and pe.current_step in
            <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="params.beginApplyCreate != null and params.beginApplyCreate != ''">
            AND date_format(pe.create_time,'%y%m%d') &gt;= date_format(#{params.beginApplyCreate},'%y%m%d')
        </if>
        <if test="params.endApplyCreate!= null and params.endApplyCreate != ''">
            AND date_format(pe.create_time,'%y%m%d') &lt;= date_format(#{params.endApplyCreate},'%y%m%d')
        </if>
        <if test="itemOrderCode != null  and itemOrderCode != ''"> and  pio.code like concat(#{itemOrderCode}, '%')</if>
        <if test="confirmCode != null  and confirmCode != ''"> and  pio.confirm_code like  concat('%', #{confirmCode}, '%')</if>
        <if test="laboratory != null "> and p.laboratory = #{laboratory}</if>
        <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>
        <if test="params.beginCreate != null and params.beginCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
        </if>
        <if test="params.endCreate != null and params.endCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
        </if>
        <if test="params.beginYj != null and params.beginYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
        </if>
        <if test="params.endYj != null and params.endYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
        </if>
        <if test=" params.statusArray != null and params.statusArray.length > 0 ">
            and pio.STATUS in
            <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">
            and p.customer_id != #{params.customerIgnoreId}
        </if>
        <if test="params.customerId != null and params.customerId != ''">
            and p.customer_id = #{params.customerId}
        </if>
        <if test="createBy != null and createBy != ''">
            and pio.create_by = #{createBy}
        </if>
        order by pio.id desc
    </select>


    <select id="queryUserDeptInfo" parameterType="java.lang.Long" resultType="java.lang.String">
        SELECT d.dept_no from sys_user u
        INNER JOIN sys_dept d on u.dept_id = d.dept_id
        where u.user_id = #{userId} AND D.del_flag = 0
    </select>


    <select id="queryProjectExecutionOrderDataList" parameterType="TProjectExecution" resultType="com.alibaba.fastjson.JSONObject">
        SELECT id,status  FROM t_project_execution where step = #{step} and PROJECT_ORDER_ID = #{projectOrderId} order by id desc
    </select>

    <update id="updateTProjectExecutionByMergeData" parameterType="TProjectExecution">
        update t_project_execution set status = 2,is_log= 1,current_step=#{currentStep},audit_time = now(),
                          process_user_id = #{processUserId}
             where  step = #{step} and PROJECT_ORDER_ID = #{projectOrderId}
        and status = 0
    </update>

    <select id="queryProcessDeptDataList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT `text` from sys_tree_data
        where id in
        <foreach collection="list" item="id" separator="," close=")" open="(">
            #{id}
        </foreach>
    </select>

    <select id="queryProjectProcessMsgDataByOrderId" parameterType="java.lang.Long" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            pio.id,
            pi.type projectItemCode,
            pio.code,
            p.project_no projectNo,
            p.product_name productName,
            u.work_user_id workUserId
        FROM
            t_project_item_order pio
                INNER JOIN t_project_item pi ON pio.item_id = pi.id
                INNER JOIN t_project p ON pi.project_id = p.id
                INNER JOIN SYS_USER U ON PIO.create_by = U.nick_name
        WHERE
            pio.id = #{id}
            LIMIT 1
    </select>
    <select id="queryProjectProcessMsgDataByExecutionId" parameterType="java.lang.Long" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            pe.id,
            pe.project_item_code projectItemCode,
            pio.code,
            p.project_no projectNo,
            p.product_name productName,
            u.work_user_id workUserId
        FROM
            t_project_execution pe
                INNER JOIN t_project_item_order pio ON pio.id = pe.project_order_id
                INNER JOIN t_project_item pi ON pio.item_id = pi.id
                INNER JOIN t_project p ON pi.project_id = p.id
                INNER JOIN SYS_USER U ON PIO.create_by = U.nick_name
        WHERE
            pe.id = #{id}
            LIMIT 1
    </select>
    <select id="queryProjectExecutionByOrderId" parameterType="java.lang.Long" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            pe.id,
            pe.project_item_code projectItemCode,
            pio.customer_id customerId,
            pio.code,
            p.project_no projectNo,
            p.product_name productName,
            u.work_user_id workUserId
        FROM
            t_project_execution pe
                INNER JOIN t_project_item_order pio ON pio.id = pe.project_order_id
                INNER JOIN t_project_item pi ON pio.item_id = pi.id
                INNER JOIN t_project p ON pi.project_id = p.id
                INNER JOIN SYS_USER U ON PIO.create_by = U.nick_name
        WHERE
            pio.id = #{id} and pio.is_step = 1 and pio.status in ( '0', '3', '4','1','2' )  and pe.step = 3 and pi.status in ('0','2','1','3','4')
           LIMIT 1
    </select>

</mapper>
