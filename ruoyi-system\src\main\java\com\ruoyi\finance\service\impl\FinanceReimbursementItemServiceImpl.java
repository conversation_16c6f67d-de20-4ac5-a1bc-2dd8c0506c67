package com.ruoyi.finance.service.impl;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.finance.domain.FinanceSummaryAmount;
import com.ruoyi.finance.domain.FinanceSummaryDept;
import com.ruoyi.finance.mapper.FinanceSummaryAmountMapper;
import com.ruoyi.finance.mapper.FinanceSummaryDeptMapper;
import com.ruoyi.finance.mapper.FinanceSummaryTypeMapper;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysDictDataMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.finance.mapper.FinanceReimbursementItemMapper;
import com.ruoyi.finance.domain.FinanceReimbursementItem;
import com.ruoyi.finance.service.IFinanceReimbursementItemService;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 费用报销付款明细子Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@Service
public class FinanceReimbursementItemServiceImpl implements IFinanceReimbursementItemService 
{
    @Resource
    private FinanceReimbursementItemMapper financeReimbursementItemMapper;
    @Resource
    private SysUserMapper sysUserMapper;

    @Override
    public List<FinanceReimbursementItem> selectFinanceReimbursementItemList(FinanceReimbursementItem item) {
        return financeReimbursementItemMapper.selectFinanceReimbursementItemList(item);
    }

    @Override
    public void insertFinanceReimbursementItem(FinanceReimbursementItem financeReimbursementItem)
    {
        financeReimbursementItem.setCreateTime(DateUtils.getNowDate());
        financeReimbursementItem.setCreateBy(SecurityUtils.getUsername());
        financeReimbursementItemMapper.insertFinanceReimbursementItem(financeReimbursementItem);
    }

    @Override
    public void batchReimbursementItem(Map<String,Object>  map) {
        String o = map.get("financeReimbursementItems").toString();
        List<FinanceReimbursementItem> financeReimbursementItems = JSON.parseArray(o, FinanceReimbursementItem.class);

        if(map.get("deleteIds") != null && !"".equals(map.get("deleteIds"))){
            String[] split = map.get("deleteIds").toString().split(",");
            Long[] ids = Arrays.stream(split).map(Long::parseLong).toArray(Long[]::new);
            financeReimbursementItemMapper.updateItemStateByIds(ids);
        }
        if(!financeReimbursementItems.isEmpty()){
            for (FinanceReimbursementItem financeReimbursementItem : financeReimbursementItems){
                if(financeReimbursementItem.getId() == null){
                    SysUser sysUser = sysUserMapper.selectUserById(financeReimbursementItem.getUserId());
                    financeReimbursementItem.setDeptId(sysUser.getDeptId());
                    this.insertFinanceReimbursementItem(financeReimbursementItem);
                }else{
                    this.updateFinanceReimbursementItem(financeReimbursementItem);
                }
            }
        }
    }

    @Override
    public void updateFinanceReimbursementItem(FinanceReimbursementItem financeReimbursementItem)
    {
        financeReimbursementItem.setUpdateTime(DateUtils.getNowDate());
        financeReimbursementItem.setUpdateBy(SecurityUtils.getUsername());
        financeReimbursementItemMapper.updateFinanceReimbursementItem(financeReimbursementItem);
    }

    @Override
    public void updateItemStateByIds(Long[] ids) {
        financeReimbursementItemMapper.updateItemStateByIds(ids);
    }

    @Override
    public int deleteItem(Long[] ids) {
        return financeReimbursementItemMapper.deleteItem(ids);
    }



    @Resource
    private FinanceSummaryDeptMapper financeSummaryDeptMapper;
    @Resource
    private FinanceSummaryTypeMapper financeSummaryTypeMapper;
    @Resource
    private FinanceSummaryAmountMapper  financeSummaryAmountMapper;
    @Resource
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private SysDictDataMapper dictDataMapper;

    @Override
    public void getItemExcelSummary(Map<String, Object> map) {
        String date = DateUtils.dateTimeNow("yyyy-MM-dd");
        if (map.get("date")!= null){
            date = map.get("date").toString();
        }
        // 清理数据
        financeSummaryDeptMapper.clearDept(date);
        financeSummaryTypeMapper.clearType(date);
        financeSummaryAmountMapper.clearAmount(date);

        // ****************************************初始化数据 start**********************************************
        SysDept dept = new SysDept();
        dept.setDeptNo("YN");
        Long deptId = sysDeptMapper.selectDeptListFy(dept).get(0).getDeptId();

        List<SysDept> topDepts = financeReimbursementItemMapper.getTopDept(deptId);
        for (SysDept topDept : topDepts) {
            FinanceSummaryDept summaryDept = new FinanceSummaryDept();
            BeanUtils.copyProperties(topDept,summaryDept,"Id","createTime");
            summaryDept.setCreateTime(DateUtils.parseDate(date));
            financeSummaryDeptMapper.insertFinanceSummaryDept(summaryDept);
            List<SysDept> depts = financeReimbursementItemMapper.getTopDept(topDept.getDeptId()); // 二级
            topDept.setParentId(topDept.getDeptId());
            depts.add(topDept); // 添加一级部门作为二级部门
            for (SysDept deptTow : depts){
                FinanceSummaryDept summaryDeptTow = new FinanceSummaryDept();
                BeanUtils.copyProperties(deptTow,summaryDeptTow,"Id","createTime");
                summaryDeptTow.setParentId(summaryDept.getDeptId());
                summaryDeptTow.setCreateTime(DateUtils.parseDate(date));
                financeSummaryDeptMapper.insertFinanceSummaryDept(summaryDeptTow);
            }
        }


        financeReimbursementItemMapper.getSummaryType(date); // 获取费用类型



        List<SysDictData> companyList = dictDataMapper.selectDictDataByType("COMPANY_NAME");
        map.put("companys",Arrays.asList("COMPANY_YN","COMPANY_YC","COMPANY_YS"));
        map.put("invoicSupplierNot",companyList.stream().map(SysDictData::getDictLabel).collect(Collectors.toList()));
        List<Map<String, Object>> summaryDate = financeReimbursementItemMapper.getSummaryDate(map);// 获取费用日期
        FinanceSummaryDept summaryDept = new FinanceSummaryDept();
        summaryDept.setDate(date);
        summaryDept.setParentId(deptId);
        List<FinanceSummaryDept> topDept = financeSummaryDeptMapper.selectFinanceSummaryDeptList(summaryDept);
        summaryDept.setParentId(topDept.get(topDept.size()-1).getDeptId()); // 总经办
        List<FinanceSummaryDept> topDept2 = financeSummaryDeptMapper.selectFinanceSummaryDeptList(summaryDept);
        Long topDeptId = topDept2.get(0).getId();

        for (Map<String, Object> obj : summaryDate){
            FinanceSummaryAmount summaryAmount = new FinanceSummaryAmount();
            summaryAmount.setAmountUnpaid(new BigDecimal(obj.get("amountUnpaid").toString()));
            summaryAmount.setAmountReimbur(new BigDecimal(obj.get("amountReimbur").toString()));
            summaryAmount.setYear(obj.get("year").toString());
            summaryAmount.setMonth(obj.get("month").toString());
            summaryAmount.setPayWay(Integer.valueOf(obj.get("payWay").toString()));
            summaryAmount.setTypeId(financeSummaryTypeMapper.selectFinanceSummaryTypeByType(Long.valueOf(obj.get("type").toString()),date).getId());

            if("YN".equals(obj.get("deptNo").toString())){
                summaryAmount.setDeptId(topDeptId);
            }else{
                FinanceSummaryDept splitDept = new FinanceSummaryDept();
                splitDept.setDate(date);
                String[] split = obj.get("ancestors").toString().split(",");
                String deptID = obj.get("deptId").toString();
                String dept_split = "";
                if(split.length>3){
                    dept_split = split[3];
                }else if(split.length == 3){
                    dept_split = deptID;
                }else{
                    dept_split = deptID;
                    splitDept.setParentId(Long.valueOf(dept_split)); // 本身作为二级部门
                }

                splitDept.setDeptId(Long.valueOf(dept_split));
                List<FinanceSummaryDept> summaryDepts = financeSummaryDeptMapper.selectFinanceSummaryDeptList(splitDept);
                if(!summaryDepts.isEmpty()){
                    summaryAmount.setDeptId(summaryDepts.get(0).getId());
                }
            }

            summaryAmount.setCreateTime(DateUtils.parseDate(date));
            financeSummaryAmountMapper.insertFinanceSummaryAmount(summaryAmount);
        }

        // ****************************************初始化数据 end **********************************************
        FinanceReimbursementItem item = new FinanceReimbursementItem();
        item.setState(0);
        List<FinanceReimbursementItem> financeReimbursementItems = financeReimbursementItemMapper.selectFinanceReimbursementItemList(item);
        for (FinanceReimbursementItem financeReimbursementItem : financeReimbursementItems) {
            if(financeReimbursementItem.getReimburementDate()!=null){
                Date reimburementDate = financeReimbursementItem.getReimburementDate();
                if(DateUtil.format(DateUtils.parseDate(date), DateUtils.YYYY_MM).equals(DateUtil.format(reimburementDate, DateUtils.YYYY_MM))){
                    financeReimbursementItem.setState(3); // 已汇总
                    financeReimbursementItemMapper.updateFinanceReimbursementItem(financeReimbursementItem);
                }
            }
        }
    }

    @Override
    public void updateTimeoutState() {
        FinanceReimbursementItem item = new FinanceReimbursementItem();
        item.setStates(new ArrayList<>(Arrays.asList(0, 3)));
        List<FinanceReimbursementItem> financeReimbursementItems = financeReimbursementItemMapper.selectFinanceReimbursementItemList(item);
        for (FinanceReimbursementItem financeReimbursementItem : financeReimbursementItems) {
            Date reimburementDate = financeReimbursementItem.getReimburementDate();
            LocalDate localDate = reimburementDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate nextMonthFirstDay = localDate.plusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
            LocalDate plusDate = nextMonthFirstDay.plusDays(45);
            if(LocalDate.now().isAfter(plusDate)){
                financeReimbursementItem.setState(2); // 超过下个月首天的45天 更新为已超时
                financeReimbursementItemMapper.updateFinanceReimbursementItem(financeReimbursementItem);
            }
        }
    }


    public static void main(String[] args) {
        // 假设指定时间为 "2023-10-15"
        String specifiedDateStr = "2025-02-01";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate specifiedDate = LocalDate.parse(specifiedDateStr, formatter);


//        LocalDate localDate = specifiedDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate nextMonthFirstDay = specifiedDate.plusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        System.out.println(nextMonthFirstDay.plusDays(45));
        System.out.println(LocalDate.now().isAfter(nextMonthFirstDay.plusDays(45)));
    }
}
