package com.ruoyi.software.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 配方spec 检测项目标准对象 t_un_material_formula_spec_ffbz
 * 
 * <AUTHOR>
 * @date 2024-02-26
 */
public class SoftwareMaterialFormulaSpecFfbz extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 配方ID */
    @Excel(name = "配方ID")
    private Long formulaId;

    //模板ID
    private Long templateId;

    //检测项目
    private String jcxmJson;

    /** 外观 */
    @Excel(name = "外观")
    private String waiguan;

    /** 颜色 */
    @Excel(name = "颜色")
    private String yanse;

    /** 气味 */
    @Excel(name = "气味")
    private String qiwei;

    /** 使用感/肤感 */
    @Excel(name = "使用感/肤感")
    private String shiyonggan;

    /** 比重 */
    @Excel(name = "比重")
    private String bizhong;

    /** PH */
    @Excel(name = "PH")
    private String ph;

    /** 黏度 */
    @Excel(name = "黏度")
    private String niandu;

    /** 硬度 */
    @Excel(name = "硬度")
    private String yingdu;

    /** 跌落测试 */
    @Excel(name = "跌落测试")
    private String dieluoceshi;

    /** 涂搽测试 */
    @Excel(name = "涂搽测试")
    private String tucaceshi;

    /** 25℃断裂强度（g） */
    @Excel(name = "25℃断裂强度", readConverterExp = "g=")
    private String duanlieqiangdu;

    /** 熔点 */
    @Excel(name = "熔点")
    private String rongdian;

    /** 疏水性 */
    @Excel(name = "疏水性")
    private String shushuixing;

    /** 细度（0.125mm）% */
    @Excel(name = "细度", readConverterExp = "0=.125mm")
    private String xidu;

    /** 自定义测试项目1 */
    @Excel(name = "自定义测试项目1")
    private String zidingyi1;

    /** 自定义测试项目2 */
    @Excel(name = "自定义测试项目2")
    private String zidingyi2;

    /** 操作人 */
    @Excel(name = "操作人")
    private String operator;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdTime;

    /** 是否删除 0,否 1,是 */
    @Excel(name = "是否删除 0,否 1,是")
    private Integer isDel;

    /** 最后修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastModifiedTime;

    /** 备注 */
    @Excel(name = "备注")
    private String note;

    /** 离心 */
    @Excel(name = "离心")
    private String lixin;


    private Integer wxId;
    private String inspectBasis;
    private String jlPl;
    private String jqPl;
    private String jlStandard;
    private String mjStandard;
    private String microbeRemark;
    private String periodOfValidity;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setFormulaId(Long formulaId) 
    {
        this.formulaId = formulaId;
    }

    public Long getFormulaId() 
    {
        return formulaId;
    }
    public void setWaiguan(String waiguan) 
    {
        this.waiguan = waiguan;
    }

    public String getWaiguan() 
    {
        return waiguan;
    }
    public void setYanse(String yanse) 
    {
        this.yanse = yanse;
    }

    public String getYanse() 
    {
        return yanse;
    }
    public void setQiwei(String qiwei) 
    {
        this.qiwei = qiwei;
    }

    public String getQiwei() 
    {
        return qiwei;
    }
    public void setShiyonggan(String shiyonggan) 
    {
        this.shiyonggan = shiyonggan;
    }

    public String getShiyonggan() 
    {
        return shiyonggan;
    }
    public void setBizhong(String bizhong) 
    {
        this.bizhong = bizhong;
    }

    public String getBizhong() 
    {
        return bizhong;
    }
    public void setPh(String ph) 
    {
        this.ph = ph;
    }

    public String getPh() 
    {
        return ph;
    }
    public void setNiandu(String niandu) 
    {
        this.niandu = niandu;
    }

    public String getNiandu() 
    {
        return niandu;
    }
    public void setYingdu(String yingdu) 
    {
        this.yingdu = yingdu;
    }

    public String getYingdu() 
    {
        return yingdu;
    }
    public void setDieluoceshi(String dieluoceshi) 
    {
        this.dieluoceshi = dieluoceshi;
    }

    public String getDieluoceshi() 
    {
        return dieluoceshi;
    }
    public void setTucaceshi(String tucaceshi) 
    {
        this.tucaceshi = tucaceshi;
    }

    public String getTucaceshi() 
    {
        return tucaceshi;
    }
    public void setDuanlieqiangdu(String duanlieqiangdu) 
    {
        this.duanlieqiangdu = duanlieqiangdu;
    }

    public String getDuanlieqiangdu() 
    {
        return duanlieqiangdu;
    }
    public void setRongdian(String rongdian) 
    {
        this.rongdian = rongdian;
    }

    public String getRongdian() 
    {
        return rongdian;
    }
    public void setShushuixing(String shushuixing) 
    {
        this.shushuixing = shushuixing;
    }

    public String getShushuixing() 
    {
        return shushuixing;
    }
    public void setXidu(String xidu) 
    {
        this.xidu = xidu;
    }

    public String getXidu() 
    {
        return xidu;
    }
    public void setZidingyi1(String zidingyi1) 
    {
        this.zidingyi1 = zidingyi1;
    }

    public String getZidingyi1() 
    {
        return zidingyi1;
    }
    public void setZidingyi2(String zidingyi2) 
    {
        this.zidingyi2 = zidingyi2;
    }

    public String getZidingyi2() 
    {
        return zidingyi2;
    }
    public void setOperator(String operator) 
    {
        this.operator = operator;
    }

    public String getOperator() 
    {
        return operator;
    }
    public void setCreatedTime(Date createdTime) 
    {
        this.createdTime = createdTime;
    }

    public Date getCreatedTime() 
    {
        return createdTime;
    }
    public void setIsDel(Integer isDel) 
    {
        this.isDel = isDel;
    }

    public Integer getIsDel() 
    {
        return isDel;
    }
    public void setLastModifiedTime(Date lastModifiedTime) 
    {
        this.lastModifiedTime = lastModifiedTime;
    }

    public Date getLastModifiedTime() 
    {
        return lastModifiedTime;
    }
    public void setNote(String note) 
    {
        this.note = note;
    }

    public String getNote() 
    {
        return note;
    }
    public void setLixin(String lixin) 
    {
        this.lixin = lixin;
    }

    public String getLixin() 
    {
        return lixin;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("formulaId", getFormulaId())
            .append("waiguan", getWaiguan())
            .append("yanse", getYanse())
            .append("qiwei", getQiwei())
            .append("shiyonggan", getShiyonggan())
            .append("bizhong", getBizhong())
            .append("ph", getPh())
            .append("niandu", getNiandu())
            .append("yingdu", getYingdu())
            .append("dieluoceshi", getDieluoceshi())
            .append("tucaceshi", getTucaceshi())
            .append("duanlieqiangdu", getDuanlieqiangdu())
            .append("rongdian", getRongdian())
            .append("shushuixing", getShushuixing())
            .append("xidu", getXidu())
            .append("zidingyi1", getZidingyi1())
            .append("zidingyi2", getZidingyi2())
            .append("remark", getRemark())
            .append("operator", getOperator())
            .append("createdTime", getCreatedTime())
            .append("isDel", getIsDel())
            .append("lastModifiedTime", getLastModifiedTime())
            .append("note", getNote())
            .append("lixin", getLixin())
            .toString();
    }

    public Integer getWxId() {
        return wxId;
    }

    public void setWxId(Integer wxId) {
        this.wxId = wxId;
    }

    public String getInspectBasis() {
        return inspectBasis;
    }

    public void setInspectBasis(String inspectBasis) {
        this.inspectBasis = inspectBasis;
    }

    public String getJlPl() {
        return jlPl;
    }

    public void setJlPl(String jlPl) {
        this.jlPl = jlPl;
    }

    public String getJqPl() {
        return jqPl;
    }

    public void setJqPl(String jqPl) {
        this.jqPl = jqPl;
    }

    public String getJlStandard() {
        return jlStandard;
    }

    public void setJlStandard(String jlStandard) {
        this.jlStandard = jlStandard;
    }

    public String getMjStandard() {
        return mjStandard;
    }

    public void setMjStandard(String mjStandard) {
        this.mjStandard = mjStandard;
    }

    public String getMicrobeRemark() {
        return microbeRemark;
    }

    public void setMicrobeRemark(String microbeRemark) {
        this.microbeRemark = microbeRemark;
    }

    public String getPeriodOfValidity() {
        return periodOfValidity;
    }

    public void setPeriodOfValidity(String periodOfValidity) {
        this.periodOfValidity = periodOfValidity;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getJcxmJson() {
        return jcxmJson;
    }

    public void setJcxmJson(String jcxmJson) {
        this.jcxmJson = jcxmJson;
    }
}
