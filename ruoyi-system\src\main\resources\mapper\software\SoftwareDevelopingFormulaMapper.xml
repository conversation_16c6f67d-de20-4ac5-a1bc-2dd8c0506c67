<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.software.mapper.SoftwareDevelopingFormulaMapper">

    <sql id="selectSoftwareDevelopingFormulaListVo">
        SELECT
            a.id,
            a.formula_code,
            a.project_no,
            a.laboratory_code,
            a.product_name,
            a.formula_name,
            a.dept_id deptId,
            a.item_name,
            a.item_name_text,
            a.stability_status,
            a.ba_status,
            a.waxc_status,
            a.ckcf_status,
            a.ba_release_status,
            a.zs_status,
            a.hh_status,
            a.customer_name,
            a.material_cycle,
            ifnull(a.brand_name,d.brand_name) brandName,
            CONCAT( e.name,'-',f.name ) clissifyName,
            a.english_name,
            a.purpose,
            a.category,
            a.created_time,
            a.operator,
            a.is_draft,
            a.is_result,
            a.is_gt,
            a.is_ciji,
            a.is_zhimin,
            a.import_country_info,
            a.bom_same,
            a.erp_code,
            a.formula_status,
            a.bom_remark,
            a.bom_update_time,
            a.bom_update_by,
            fo.type shareType,
            a.formula_remark,
            ifnull(a.zs_time,a.ba_time) zsTime,
            a.ba_time
        FROM
            t_un_formula a
           left join t_un_formula_classify f on f.id=a.category
           left join t_un_formula_classify e on f.parent_id=e.id
           left join t_un_brand d on a.brand_id = d.id
           left join t_un_formula_dept fo on a.id = fo.FORMULA_ID
    </sql>
    <sql id="selectSoftwareDevelopingFormulaVo">
        SELECT
            a.id,
            a.formula_name,
            a.english_name,
            a.price,
            a.weight,
            a.is_materal,
            a.status,
            a.remark,
            a.operator,
            a.created_time,
            a.is_del,
            a.last_modified_time,
            a.note,
            a.formula_code,
            a.laboratory_code,
            a.product_name,
            a.brand_id,
            a.customer_code,
            a.customer_name,
            a.series_name,
            a.appearance,
            a.colour,
            a.ph,
            a.viscosity,
            a.stabilityresult,
            a.category,
            a.category_text,
            a.brand_name,
            a.standard,
            a.intro_file,
            a.organization_id,
            a.dept_id,
            a.old_formula_code,
            a.wendingxing_file,
            a.gongyi_file,
            a.xiangrongxing_file,
            a.weishenwu_file,
            a.xiaofeizhe_file,
            a.qita_file,
            a.exec_number,
            a.is_draft,
            a.gxxc,
            a.gxxc_other,
            a.zybw,
            a.syrq,
            a.cpjx,
            a.pflx,
            a.cpfldm,
            a.exec_number_id,
            a.aqpgjl,
            a.gongyijianshu,
            a.gongyijianshu_beian,
            a.ranfalei,
            a.qubanmeibailei,
            a.fangshailei,
            a.sfa,
            a.pa,
            a.yushousfa,
            a.xingongxiao,
            a.xingongxiaocontent,
            a.ftl_time,
            a.fil_code,
            a.ba_code,
            a.ba_time,
            a.fil_code_note,
            a.ba_code_note,
            a.waxc_name,
            a.waxc_othername,
            a.waxc_status,
            a.ba_status,
            a.formula_pid,
            a.bp_note,
            a.zs_time,
            a.zs_code,
            a.gongyijianshu_zs,
            a.yf_file,
            a.zs_file,
            a.is_love,
            a.up_rate,
            a.ori_price,
            a.level_num,
            a.purpose,
            a.formula_remark,
            a.project_no,
            a.item_name,
            a.item_name_text,
            a.formula_image,
            a.is_realse,
            a.material_status_info,
            a.import_country_info,
            a.operator_name,
            a.stability_status,
            a.is_result,
            a.is_gt,
            a.material_cycle,
            a.cir_id,
            a.duli_id,
            a.cir_text,
            a.duli_text,
            a.jcxm_json,
            a.hh_status,
            a.hh_code,
            a.hh_time,
            a.ba_release_status,a.ckcf_status,
            a.current_template_id,
            a.cosmetic_classification,
            a.cosmetic_case,
            a.cosmetic_case_first,
            a.weishenwu_result,
            a.weishenwu_remark,
            a.xiangrongxing_result,
            a.xiangrongxing_remark,
            a.wendingxing_result,
            a.wendingxing_remark,
            a.bom_same,
            a.erp_code,
            a.formula_status,
            a.bom_remark,
            a.bom_update_time,
            a.bom_update_by,
            a.cosmetic_case_second,
            a.current_version,
            a.gxgs,
            a.formula_construction_ideas
        FROM
            t_un_formula a
    </sql>

    <select id="selectSoftwareDevelopingFormulaList" parameterType="SoftwareDevelopingFormula" resultType="SoftwareDevelopingFormula">
        <include refid="selectSoftwareDevelopingFormulaListVo"/>
        <where>
            a.is_del = 0
            <if test="formulaCode != null  and formulaCode != ''"> and a.FORMULA_CODE like concat(#{formulaCode}, '%')</if>
            <if test="laboratoryCode != null  and laboratoryCode != ''"> and a.LABORATORY_CODE like concat(#{laboratoryCode}, '%')</if>
            <if test="productName != null  and productName != ''"> and a.PRODUCT_NAME like concat('%', #{productName}, '%')</if>
            <if test="projectNo != null  and projectNo != ''"> and a.project_no like concat('%', #{projectNo}, '%')</if>
            <if test="formulaRemark != null  and formulaRemark != ''"> and a.formula_remark like concat('%', #{formulaRemark}, '%')</if>
            <if test="organizationId!=null and organizationId>0">
               and fo.dept_id in
                <foreach collection="organizationIdList" item="deptId" index="index" separator="," close=")" open="(">
                    #{deptId}
                </foreach>
            </if>
            <if test="bomSame !=null and bomSame != ''"> and a.bom_same = #{bomSame} </if>
            <if test="erpCode !=null and erpCode != ''"> and a.erp_code = #{erpCode} </if>
            <if test="searchUserId!=null and searchUserId>0">
                and a.category in (select classify_id from t_un_formula_classify_user g where g.user_id=#{searchUserId})
            </if>
            <if test="isAdmin!=null and isAdmin==1">
                and fo.type = 1
            </if>
            <if test="category!=null">
                and a.category = #{category}
            </if>
            <if test="formulaStatusNew!=null">
                and a.formula_status  in (0,1)
            </if>
            <if test="isDraft!=null">
                and a.is_draft = #{isDraft}
            </if>
            <if test="purpose!=null and purpose!=''">
                and a.purpose = #{purpose}
            </if>
            <if test="isResult!=null">
                and a.is_result = #{isResult}
            </if>
            <if test="isTimeout==1">
                and length(a.material_cycle) > 0
            </if>
            <if test="isTimeout==0">
                and ifnull(a.material_cycle,'') = ''
            </if>
            <if test="isImport==1">
                and a.import_country_info like '%日本%'
            </if>
            <if test="isImport==0">
                and (a.import_country_info not like '%日本%' or a.import_country_info is null)
            </if>
            <if test="isGt!=null">
                and a.is_gt = #{isGt}
            </if>
            <if test="isCiji!=null">
                and a.is_ciji = #{isCiji}
            </if>
            <if test="isZhimin!=null">
                and a.is_zhimin = #{isZhimin}
            </if>
            <if test="stabilityStatus!=null and stabilityStatus!=''">
                and a.stability_status like CONCAT('%',#{stabilityStatus},'%')
            </if>
            <if test="formulaStatus!=null">
                <if test="formulaStatus==2">
                    and ckcf_status > 0
                </if>
                <if test="formulaStatus==4">
                    and ba_release_status > 0 and status not like '%cancel%'
                </if>
                <if test="formulaStatus==6">
                    and zs_status > 0
                </if>
                <if test="formulaStatus==8">
                    and hh_status > 0
                </if>
            </if>
            <if test="materialName!=null and materialName!=''">
                and exists (
                    select  b.FORMULA_ID from t_un_material c
                    inner join t_un_material_formula b on c.id = b.MATERIAL_ID where
                    c.material_code = #{materialName} and b.formula_id = a.id
                 )
            </if>
            <if test="isCondition==1">
                and  exists (
                    select mf.FORMULA_ID from t_un_material m
                    inner join t_un_material_formula mf on mf.material_id = m.id
                    where m.status = 3 and mf.formula_id = a.id
                )
            </if>
            <if test="zsStatus!=null and zsStatus==1">
                and (a.zs_status > 0 or a.LABORATORY_CODE like 'B%')
            </if>
            <if test="minMaterialProportion != null and minMaterialProportion != ''">
                and exists (
                 select b.FORMULA_ID from t_un_material c
                 inner join t_un_material_formula b  on c.id = b.MATERIAL_ID
                 where b.formula_id = a.id
                 <if test="materialName!=null and materialName!=''">
                    AND  c.material_code = #{materialName}
                 </if>
                  AND b.percentage >= #{minMaterialProportion}
               )
            </if>
            <if test="maxMaterialProportion != null and maxMaterialProportion != ''">
                and exists
                (
                    select b.FORMULA_ID from t_un_material c
                    inner join t_un_material_formula b  on c.id = b.MATERIAL_ID
                    where b.formula_id = a.id
                    <if test="materialName!=null and materialName!=''">
                        AND  c.material_code = #{materialName}
                    </if>
                    AND b.percentage &lt;= #{maxMaterialProportion}
                )
            </if>
            <if test="params.beginDate != null and params.beginDate != '' and params.endDate != null and params.endDate != ''"> and DATE_FORMAT(a.created_time,'%Y-%m-%d')  between #{params.beginDate} and #{params.endDate}</if>
            <if test="formulaName != null and formulaName !=''">
                and (a.formula_name like CONCAT('%',#{formulaName},'%')
                or
                a.english_name like CONCAT('%',#{formulaName},'%')
                or
                a.formula_code like CONCAT('%',#{formulaName},'%')
                or
                a.project_no like CONCAT('%',#{formulaName},'%')
                or
                a.customer_code like CONCAT('%',#{formulaName},'%')
                or
                a.customer_name like CONCAT('%',#{formulaName},'%')
                or
                a.series_name like CONCAT('%',#{formulaName},'%')
                or
                d.maintain like CONCAT('%',#{formulaName},'%')
                or
                a.operator like CONCAT('%',#{formulaName},'%')
                or
                d.brand_name like CONCAT('%',#{formulaName},'%')
                or
                a.brand_name like CONCAT('%',#{formulaName},'%')
                or
                a.PRODUCT_NAME like CONCAT('%',#{formulaName},'%')
                or
                a.LABORATORY_CODE like CONCAT('%',#{formulaName},'%')
                )
            </if>
        </where>
        order by fo.id desc
    </select>
    <select id="selectSoftwareDevelopingMaterialFormulaList" parameterType="SoftwareDevelopingFormula" resultType="SoftwareDevelopingFormula">
        SELECT
        a.id,
        a.formula_code,
        a.project_no,
        a.laboratory_code,
        a.product_name,
        a.formula_name,
        a.dept_id deptId,
        a.item_name,
        a.item_name_text,
        a.stability_status,
        a.ba_status,
        a.waxc_status,
        a.ckcf_status,
        a.ba_release_status,
        a.zs_status,
        a.hh_status,
        a.customer_name,
        a.material_cycle,
        ifnull(a.brand_name,d.brand_name) brandName,
        CONCAT( e.name,'-',f.name ) clissifyName,
        a.english_name,
        a.purpose,
        a.category,
        a.created_time,
        a.operator,
        a.is_draft,
        a.is_result,
        a.is_gt,
        a.import_country_info,
        a.bom_same,
        a.erp_code,
        a.bom_remark,
        a.bom_update_time,
        a.bom_update_by,
        fo.type shareType,
        mf.percentage
        FROM
        t_un_formula a
        left join t_un_formula_classify f on f.id=a.category
        left join t_un_formula_classify e on f.parent_id=e.id
        left join t_un_brand d on a.brand_id = d.id
        left join t_un_formula_dept fo on (a.id = fo.FORMULA_ID AND FO.TYPE = 1)
        left join t_un_material_formula mf on  (mf.formula_id = a.id and mf.type = 0)
        <where>
            a.is_del = 0 and mf.material_id = #{materialId}
            <if test="formulaCode != null  and formulaCode != ''"> and a.FORMULA_CODE like concat(#{formulaCode}, '%')</if>
            <if test="laboratoryCode != null  and laboratoryCode != ''"> and a.LABORATORY_CODE like concat(#{laboratoryCode}, '%')</if>
            <if test="productName != null  and productName != ''"> and a.PRODUCT_NAME like concat('%', #{productName}, '%')</if>
            <if test="projectNo != null  and projectNo != ''"> and a.project_no like concat('%', #{projectNo}, '%')</if>
            <if test="organizationId!=null and organizationId>0">
                and fo.dept_id in
                <foreach collection="organizationIdList" item="deptId" index="index" separator="," close=")" open="(">
                    #{deptId}
                </foreach>
            </if>
            <if test="bomSame !=null and bomSame != ''"> and a.bom_same = #{bomSame} </if>
            <if test="erpCode !=null and erpCode != ''"> and a.erp_code = #{erpCode} </if>
            <if test="searchUserId!=null and searchUserId>0">
                and a.category in (select classify_id from t_un_formula_classify_user g where g.user_id=#{searchUserId})
            </if>
            <if test="isAdmin!=null and isAdmin==1">
                and fo.type = 1
            </if>
            <if test="category!=null">
                and a.category = #{category}
            </if>
            <if test="isDraft!=null">
                and a.is_draft = #{isDraft}
            </if>
            <if test="purpose!=null and purpose!=''">
                and a.purpose = #{purpose}
            </if>
            <if test="isResult!=null">
                and a.is_result = #{isResult}
            </if>
            <if test="isTimeout==1">
                and length(a.material_cycle) > 0
            </if>
            <if test="isTimeout==0">
                and ifnull(a.material_cycle,'') = ''
            </if>
            <if test="isImport==1">
                and a.import_country_info like '%日本%'
            </if>
            <if test="isImport==0">
                and (a.import_country_info not like '%日本%' or a.import_country_info is null)
            </if>
            <if test="isGt!=null">
                and a.is_gt = #{isGt}
            </if>
            <if test="stabilityStatus!=null and stabilityStatus!=''">
                and a.stability_status like CONCAT('%',#{stabilityStatus},'%')
            </if>
            <if test="formulaStatus!=null">
                <if test="formulaStatus==2">
                    and ckcf_status > 0
                </if>
                <if test="formulaStatus==4">
                    and ba_release_status > 0
                </if>
                <if test="formulaStatus==6">
                    and zs_status > 0
                </if>
                <if test="formulaStatus==8">
                    and hh_status > 0
                </if>
            </if>
            <if test="materialName!=null and materialName!=''">
                and exists (
                    select  b.FORMULA_ID from t_un_material c
                    inner join t_un_material_formula b on c.id = b.MATERIAL_ID where
                    c.material_code = #{materialName} and b.formula_id = a.id
                 )
            </if>
            <if test="isCondition==1">
                and  exists (
                    select mf.FORMULA_ID from t_un_material m
                    inner join t_un_material_formula mf on mf.material_id = m.id
                    where m.status = 3 and mf.formula_id = a.id
                )
            </if>
            <if test="zsStatus!=null and zsStatus==1">
                and a.zs_status > 0
            </if>
            <if test="minMaterialProportion != null and minMaterialProportion != ''">
                and mf.percentage >= #{minMaterialProportion}
            </if>
            <if test="maxMaterialProportion != null and maxMaterialProportion != ''">
                and mf.percentage &lt;= #{maxMaterialProportion}
            </if>
            <if test="params.beginDate != null and params.beginDate != '' and params.endDate != null and params.endDate != ''"> and DATE_FORMAT(a.created_time,'%Y-%m-%d')  between #{params.beginDate} and #{params.endDate}</if>
            <if test="formulaName != null and formulaName !=''">
                and (a.formula_name like CONCAT('%',#{formulaName},'%')
                or
                a.english_name like CONCAT('%',#{formulaName},'%')
                or
                a.formula_code like CONCAT('%',#{formulaName},'%')
                or
                a.project_no like CONCAT('%',#{formulaName},'%')
                or
                a.customer_code like CONCAT('%',#{formulaName},'%')
                or
                a.customer_name like CONCAT('%',#{formulaName},'%')
                or
                a.series_name like CONCAT('%',#{formulaName},'%')
                or
                d.maintain like CONCAT('%',#{formulaName},'%')
                or
                a.operator like CONCAT('%',#{formulaName},'%')
                or
                d.brand_name like CONCAT('%',#{formulaName},'%')
                or
                a.brand_name like CONCAT('%',#{formulaName},'%')
                or
                a.PRODUCT_NAME like CONCAT('%',#{formulaName},'%')
                or
                a.LABORATORY_CODE like CONCAT('%',#{formulaName},'%')
                )
            </if>
        </where>
        order by fo.id desc
    </select>

    <select id="selectSoftwareDevelopingFormulaById" parameterType="Long" resultType="SoftwareDevelopingFormula" >
        <include refid="selectSoftwareDevelopingFormulaVo"/>
        where a.ID = #{id}
    </select>

    <select id="selectSoftwareDevelopingProductSafetyAssessmentDataFormulaById" parameterType="Long" resultType="SoftwareDevelopingFormula" >
        SELECT
            a.id,
            a.formula_name,
            a.english_name,
            a.price,
            a.weight,
            a.is_materal,
            a.status,
            a.remark,
            a.operator,
            a.created_time,
            a.is_del,
            a.last_modified_time,
            a.note,
            a.formula_code,
            a.laboratory_code,
            a.product_name,
            a.brand_id,
            a.customer_code,
            a.customer_name,
            a.series_name,
            a.appearance,
            a.colour,
            a.ph,
            a.viscosity,
            a.stabilityresult,
            a.category,
            a.category_text,
            a.brand_name,
            a.standard,
            a.intro_file,
            a.organization_id,
            a.dept_id,
            a.old_formula_code,
            a.wendingxing_file,
            a.gongyi_file,
            a.xiangrongxing_file,
            a.weishenwu_file,
            a.xiaofeizhe_file,
            a.qita_file,
            a.exec_number,
            a.is_draft,
            a.gxxc,
            a.gxxc_other,
            a.zybw,
            a.syrq,
            a.cpjx,
            a.pflx,
            a.cpfldm,
            a.exec_number_id,
            a.aqpgjl,
            a.gongyijianshu,
            a.gongyijianshu_beian,
            a.ranfalei,
            a.qubanmeibailei,
            a.fangshailei,
            a.sfa,
            a.pa,
            a.yushousfa,
            a.xingongxiao,
            a.xingongxiaocontent,
            a.ftl_time,
            a.fil_code,
            a.ba_code,
            a.ba_time,
            a.fil_code_note,
            a.ba_code_note,
            a.waxc_name,
            a.waxc_othername,
            a.waxc_status,
            a.ba_status,
            a.formula_pid,
            a.bp_note,
            a.zs_time,
            a.zs_code,
            a.gongyijianshu_zs,
            a.yf_file,
            a.zs_file,
            a.is_love,
            a.up_rate,
            a.ori_price,
            a.level_num,
            a.purpose,
            a.formula_remark,
            a.project_no,
            a.item_name,
            a.item_name_text,
            a.formula_image,
            a.is_realse,
            a.material_status_info,
            a.import_country_info,
            a.operator_name,
            a.stability_status,
            a.is_result,
            a.is_gt,
            a.material_cycle,
            a.cir_id,
            a.duli_id,
            a.cir_text,
            a.duli_text,
            a.jcxm_json,
            a.hh_status,
            a.hh_code,
            a.hh_time,
            a.ba_release_status,a.ckcf_status,
            a.current_template_id,
            a.cosmetic_classification,
            a.cosmetic_case,
            a.cosmetic_case_first,
            a.weishenwu_result,
            a.weishenwu_remark,
            a.xiangrongxing_result,
            a.xiangrongxing_remark,
            a.wendingxing_result,
            a.wendingxing_remark,
            a.bom_same,
            a.erp_code,
            a.formula_status,
            a.bom_remark,
            a.bom_update_time,
            a.bom_update_by,
            a.cosmetic_case_second,
            a.jcxm_json,
            a.apxq formulaProductSafetyAssessmentData
        FROM
            t_un_formula a  where a.ID = #{id}
    </select>

    <insert id="insertSoftwareDevelopingFormula" parameterType="SoftwareDevelopingFormula" useGeneratedKeys="true" keyProperty="id">
        insert into t_un_formula
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="formulaName != null">FORMULA_NAME,</if>
            <if test="englishName != null">ENGLISH_NAME,</if>
            <if test="price != null">PRICE,</if>
            <if test="isCalc != null">IS_CALC,</if>
            <if test="weight != null">WEIGHT,</if>
            <if test="isMateral != null">IS_MATERAL,</if>
            <if test="status != null">STATUS,</if>
            <if test="remark != null">REMARK,</if>
            <if test="operator != null">OPERATOR,</if>
            <if test="createdTime != null">CREATED_TIME,</if>
            <if test="isDel != null">IS_DEL,</if>
            <if test="lastModifiedTime != null">LAST_MODIFIED_TIME,</if>
            <if test="note != null">NOTE,</if>
            <if test="formulaCode != null">FORMULA_CODE,</if>
            <if test="laboratoryCode != null">LABORATORY_CODE,</if>
            <if test="productName != null">PRODUCT_NAME,</if>
            <if test="brandId != null">BRAND_ID,</if>
            <if test="customerCode != null">customer_code,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="seriesName != null">series_name,</if>
            <if test="appearance != null">appearance,</if>
            <if test="colour != null">colour,</if>
            <if test="ph != null">ph,</if>
            <if test="viscosity != null">viscosity,</if>
            <if test="stabilityresult != null">stabilityResult,</if>
            <if test="category != null">category,</if>
            <if test="brandName != null">brand_name,</if>
            <if test="standard != null">standard,</if>
            <if test="introFile != null">INTRO_FILE,</if>
            <if test="organizationId != null">ORGANIZATION_ID,</if>
            <if test="deptId != null">DEPT_ID,</if>
            <if test="oldFormulaCode != null">OLD_FORMULA_CODE,</if>
            <if test="wendingxingFile != null">WENDINGXING_FILE,</if>
            <if test="gongyiFile != null">GONGYI_FILE,</if>
            <if test="xiangrongxingFile != null">XIANGRONGXING_FILE,</if>
            <if test="weishenwuFile != null">WEISHENWU_FILE,</if>
            <if test="xiaofeizheFile != null">XIAOFEIZHE_FILE,</if>
            <if test="qitaFile != null">QITA_FILE,</if>
            <if test="execNumber != null">EXEC_NUMBER,</if>
            <if test="isDraft != null">IS_DRAFT,</if>
            <if test="gxxc != null">GXXC,</if>
            <if test="gxxcOther != null">GXXC_OTHER,</if>
            <if test="zybw != null">ZYBW,</if>
            <if test="syrq != null">SYRQ,</if>
            <if test="cpjx != null">CPJX,</if>
            <if test="pflx != null">PFLX,</if>
            <if test="cpfldm != null">CPFLDM,</if>
            <if test="execNumberId != null">EXEC_NUMBER_ID,</if>
            <if test="aqpgjl != null">AQPGJL,</if>
            <if test="gongyijianshu != null">GONGYIJIANSHU,</if>
            <if test="gongyijianshuBeian != null">GONGYIJIANSHU_BEIAN,</if>
            <if test="ranfalei != null">ranfalei,</if>
            <if test="qubanmeibailei != null">qubanmeibailei,</if>
            <if test="fangshailei != null">fangshailei,</if>
            <if test="sfa != null">sfa,</if>
            <if test="pa != null">pa,</if>
            <if test="yushousfa != null">yushousfa,</if>
            <if test="xingongxiao != null">xingongxiao,</if>
            <if test="xingongxiaocontent != null">xingongxiaocontent,</if>
            <if test="ftlTime != null">ftl_time,</if>
            <if test="filCode != null">FIL_CODE,</if>
            <if test="baCode != null">BA_CODE,</if>
            <if test="baTime != null">ba_time,</if>
            <if test="filCodeNote != null">FIL_CODE_NOTE,</if>
            <if test="baCodeNote != null">BA_CODE_NOTE,</if>
            <if test="waxcName != null">waxc_name,</if>
            <if test="waxcOthername != null">waxc_othername,</if>
            <if test="waxcStatus != null">waxc_status,</if>
            <if test="baStatus != null">ba_status,</if>
            <if test="formulaPid != null">formula_pid,</if>
            <if test="bpNote != null">BP_NOTE,</if>
            <if test="zsTime != null">zs_time,</if>
            <if test="zsCode != null">zs_code,</if>
            <if test="gongyijianshuZs != null">GONGYIJIANSHU_ZS,</if>
            <if test="yfFile != null">YF_FILE,</if>
            <if test="zsFile != null">ZS_FILE,</if>
            <if test="isLove != null">IS_LOVE,</if>
            <if test="upRate != null">UP_RATE,</if>
            <if test="oriPrice != null">ORI_PRICE,</if>
            <if test="levelNum != null">LEVEL_NUM,</if>
            <if test="purpose != null">purpose,</if>
            <if test="formulaRemark != null">formula_remark,</if>
            <if test="projectNo != null">PROJECT_NO,</if>
            <if test="itemName != null">ITEM_NAME,</if>
            <if test="itemNameText != null">ITEM_NAME_TEXT,</if>
            <if test="formulaImage != null">formula_image,</if>
            <if test="isRealse != null">is_realse,</if>
            <if test="materialStatusInfo != null">material_status_info,</if>
            <if test="importCountryInfo != null">import_country_info,</if>
            <if test="operatorName != null">operator_name,</if>
            <if test="stabilityStatus != null">stability_status,</if>
            <if test="isResult != null">is_result,</if>
            <if test="isGt != null">is_gt,</if>
            <if test="materialCycle != null">material_cycle,</if>
            <if test="categoryText != null">category_text,</if>
            <if test="cirId != null">cir_id,</if>
            <if test="duliId != null">duli_id,</if>
            <if test="cirText != null">cir_text,</if>
            <if test="duliText != null">duli_text,</if>
            <if test="jcxmJson != null">JCXM_JSON,</if>
            <if test="currentTemplateId != null">current_template_id,</if>
            <if test="copyFormulaId != null">copy_formula_id,</if>
            <if test="cosmeticClassification != null">cosmetic_classification,</if>
            <if test="cosmeticCase != null">cosmetic_case,</if>
            <if test="cosmeticCaseFirst != null">cosmetic_case_first,</if>
            <if test="cosmeticCaseSecond != null">cosmetic_case_second,</if>
            <if test="weishenwuResult != null">weishenwu_result,</if>
            <if test="weishenwuRemark != null">weishenwu_remark,</if>
            <if test="xiangrongxingResult != null">xiangrongxing_result,</if>
            <if test="xiangrongxingRemark != null">xiangrongxing_remark,</if>
            <if test="wendingxingResult != null">wendingxing_result,</if>
            <if test="wendingxingRemark != null">wendingxing_remark,</if>
            <if test="bomSame != null">bom_same,</if>
            <if test="erpCode != null">erp_code,</if>
            <if test="formulaStatus != null">formula_status,</if>
            <if test="bomRemark != null">bom_remark,</if>
            <if test="bomUpdateTime != null">bom_update_time,</if>
            <if test="bomUpdateBy != null">bom_update_by,</if>
            <if test="formulaConstructionIdeas != null">formula_construction_ideas,</if>
            <if test="gxgs != null">gxgs,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="formulaName != null">#{formulaName},</if>
            <if test="englishName != null">#{englishName},</if>
            <if test="price != null">#{price},</if>
            <if test="isCalc != null">#{isCalc},</if>
            <if test="weight != null">#{weight},</if>
            <if test="isMateral != null">#{isMateral},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="operator != null">#{operator},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="lastModifiedTime != null">#{lastModifiedTime},</if>
            <if test="note != null">#{note},</if>
            <if test="formulaCode != null">#{formulaCode},</if>
            <if test="laboratoryCode != null">#{laboratoryCode},</if>
            <if test="productName != null">#{productName},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="customerCode != null">#{customerCode},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="seriesName != null">#{seriesName},</if>
            <if test="appearance != null">#{appearance},</if>
            <if test="colour != null">#{colour},</if>
            <if test="ph != null">#{ph},</if>
            <if test="viscosity != null">#{viscosity},</if>
            <if test="stabilityresult != null">#{stabilityresult},</if>
            <if test="category != null">#{category},</if>
            <if test="brandName != null">#{brandName},</if>
            <if test="standard != null">#{standard},</if>
            <if test="introFile != null">#{introFile},</if>
            <if test="organizationId != null">#{organizationId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="oldFormulaCode != null">#{oldFormulaCode},</if>
            <if test="wendingxingFile != null">#{wendingxingFile},</if>
            <if test="gongyiFile != null">#{gongyiFile},</if>
            <if test="xiangrongxingFile != null">#{xiangrongxingFile},</if>
            <if test="weishenwuFile != null">#{weishenwuFile},</if>
            <if test="xiaofeizheFile != null">#{xiaofeizheFile},</if>
            <if test="qitaFile != null">#{qitaFile},</if>
            <if test="execNumber != null">#{execNumber},</if>
            <if test="isDraft != null">#{isDraft},</if>
            <if test="gxxc != null">#{gxxc},</if>
            <if test="gxxcOther != null">#{gxxcOther},</if>
            <if test="zybw != null">#{zybw},</if>
            <if test="syrq != null">#{syrq},</if>
            <if test="cpjx != null">#{cpjx},</if>
            <if test="pflx != null">#{pflx},</if>
            <if test="cpfldm != null">#{cpfldm},</if>
            <if test="execNumberId != null">#{execNumberId},</if>
            <if test="aqpgjl != null">#{aqpgjl},</if>
            <if test="gongyijianshu != null">#{gongyijianshu},</if>
            <if test="gongyijianshuBeian != null">#{gongyijianshuBeian},</if>
            <if test="ranfalei != null">#{ranfalei},</if>
            <if test="qubanmeibailei != null">#{qubanmeibailei},</if>
            <if test="fangshailei != null">#{fangshailei},</if>
            <if test="sfa != null">#{sfa},</if>
            <if test="pa != null">#{pa},</if>
            <if test="yushousfa != null">#{yushousfa},</if>
            <if test="xingongxiao != null">#{xingongxiao},</if>
            <if test="xingongxiaocontent != null">#{xingongxiaocontent},</if>
            <if test="ftlTime != null">#{ftlTime},</if>
            <if test="filCode != null">#{filCode},</if>
            <if test="baCode != null">#{baCode},</if>
            <if test="baTime != null">#{baTime},</if>
            <if test="filCodeNote != null">#{filCodeNote},</if>
            <if test="baCodeNote != null">#{baCodeNote},</if>
            <if test="waxcName != null">#{waxcName},</if>
            <if test="waxcOthername != null">#{waxcOthername},</if>
            <if test="waxcStatus != null">#{waxcStatus},</if>
            <if test="baStatus != null">#{baStatus},</if>
            <if test="formulaPid != null">#{formulaPid},</if>
            <if test="bpNote != null">#{bpNote},</if>
            <if test="zsTime != null">#{zsTime},</if>
            <if test="zsCode != null">#{zsCode},</if>
            <if test="gongyijianshuZs != null">#{gongyijianshuZs},</if>
            <if test="yfFile != null">#{yfFile},</if>
            <if test="zsFile != null">#{zsFile},</if>
            <if test="isLove != null">#{isLove},</if>
            <if test="upRate != null">#{upRate},</if>
            <if test="oriPrice != null">#{oriPrice},</if>
            <if test="levelNum != null">#{levelNum},</if>
            <if test="purpose != null">#{purpose},</if>
            <if test="formulaRemark != null">#{formulaRemark},</if>
            <if test="projectNo != null">#{projectNo},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="itemNameText != null">#{itemNameText},</if>
            <if test="formulaImage != null">#{formulaImage},</if>
            <if test="isRealse != null">#{isRealse},</if>
            <if test="materialStatusInfo != null">#{materialStatusInfo},</if>
            <if test="importCountryInfo != null">#{importCountryInfo},</if>
            <if test="operatorName != null">#{operatorName},</if>
            <if test="stabilityStatus != null">#{stabilityStatus},</if>
            <if test="isResult != null">#{isResult},</if>
            <if test="isGt != null">#{isGt},</if>
            <if test="materialCycle != null">#{materialCycle},</if>
            <if test="categoryText != null">#{categoryText},</if>
            <if test="cirId != null">#{cirId},</if>
            <if test="duliId != null">#{duliId},</if>
            <if test="cirText != null">#{cirText},</if>
            <if test="duliText != null">#{duliText},</if>
            <if test="jcxmJson != null">#{jcxmJson},</if>
            <if test="currentTemplateId != null">#{currentTemplateId},</if>
            <if test="copyFormulaId != null">#{copyFormulaId},</if>
            <if test="cosmeticClassification != null">#{cosmeticClassification},</if>
            <if test="cosmeticCase != null">#{cosmeticCase},</if>
            <if test="cosmeticCaseFirst != null">#{cosmeticCaseFirst},</if>
            <if test="cosmeticCaseSecond != null">#{cosmeticCaseSecond},</if>
            <if test="weishenwuResult != null">#{weishenwuResult},</if>
            <if test="weishenwuRemark != null">#{weishenwuRemark},</if>
            <if test="xiangrongxingResult != null">#{xiangrongxingResult},</if>
            <if test="xiangrongxingRemark != null">#{xiangrongxingRemark},</if>
            <if test="wendingxingResult != null">#{wendingxingResult},</if>
            <if test="wendingxingRemark != null">#{wendingxingRemark},</if>
            <if test="bomSame != null">#{bomSame},</if>
            <if test="erpCode != null">#{erpCode},</if>
            <if test="formulaStatus != null">#{formulaStatus},</if>
            <if test="bomRemark != null">#{bomRemark},</if>
            <if test="bomUpdateTime != null">#{bomUpdateTime},</if>
            <if test="bomUpdateBy != null">#{bomUpdateBy},</if>
            <if test="formulaConstructionIdeas != null">#{formulaConstructionIdeas},</if>
            <if test="gxgs != null">#{gxgs},</if>
        </trim>
    </insert>

    <update id="updateSoftwareDevelopingFormula" parameterType="SoftwareDevelopingFormula">
        update t_un_formula
        <trim prefix="SET" suffixOverrides=",">
            <if test="formulaName != null">FORMULA_NAME = #{formulaName},</if>
            <if test="englishName != null">ENGLISH_NAME = #{englishName},</if>
            <if test="price != null">PRICE = #{price},</if>
            <if test="isCalc != null">IS_CALC = #{isCalc},</if>
            <if test="weight != null">WEIGHT = #{weight},</if>
            <if test="isMateral != null">IS_MATERAL = #{isMateral},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="remark != null">REMARK = #{remark},</if>
            <if test="operator != null">OPERATOR = #{operator},</if>
            <if test="createdTime != null">CREATED_TIME = #{createdTime},</if>
            <if test="isDel != null">IS_DEL = #{isDel},</if>
            <if test="lastModifiedTime != null">LAST_MODIFIED_TIME = #{lastModifiedTime},</if>
            <if test="note != null">NOTE = #{note},</if>
            <if test="formulaCode != null">FORMULA_CODE = #{formulaCode},</if>
            <if test="laboratoryCode != null">LABORATORY_CODE = #{laboratoryCode},</if>
            <if test="productName != null">PRODUCT_NAME = #{productName},</if>
            <if test="brandId != null">BRAND_ID = #{brandId},</if>
            <if test="customerCode != null">customer_code = #{customerCode},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="seriesName != null">series_name = #{seriesName},</if>
            <if test="appearance != null">appearance = #{appearance},</if>
            <if test="colour != null">colour = #{colour},</if>
            <if test="ph != null">ph = #{ph},</if>
            <if test="viscosity != null">viscosity = #{viscosity},</if>
            <if test="stabilityresult != null">stabilityResult = #{stabilityresult},</if>
            <if test="category != null">category = #{category},</if>
            <if test="brandName != null">brand_name = #{brandName},</if>
            <if test="standard != null">standard = #{standard},</if>
            <if test="introFile != null">INTRO_FILE = #{introFile},</if>
            <if test="organizationId != null">ORGANIZATION_ID = #{organizationId},</if>
            <if test="deptId != null">DEPT_ID = #{deptId},</if>
            <if test="oldFormulaCode != null">OLD_FORMULA_CODE = #{oldFormulaCode},</if>
            <if test="wendingxingFile != null">WENDINGXING_FILE = #{wendingxingFile},</if>
            <if test="gongyiFile != null">GONGYI_FILE = #{gongyiFile},</if>
            <if test="xiangrongxingFile != null">XIANGRONGXING_FILE = #{xiangrongxingFile},</if>
            <if test="weishenwuFile != null">WEISHENWU_FILE = #{weishenwuFile},</if>
            <if test="xiaofeizheFile != null">XIAOFEIZHE_FILE = #{xiaofeizheFile},</if>
            <if test="qitaFile != null">QITA_FILE = #{qitaFile},</if>
            <if test="execNumber != null">EXEC_NUMBER = #{execNumber},</if>
            <if test="isDraft != null">IS_DRAFT = #{isDraft},</if>
            <if test="gxxc != null">GXXC = #{gxxc},</if>
            <if test="gxxcOther != null">GXXC_OTHER = #{gxxcOther},</if>
            <if test="zybw != null">ZYBW = #{zybw},</if>
            <if test="syrq != null">SYRQ = #{syrq},</if>
            <if test="cpjx != null">CPJX = #{cpjx},</if>
            <if test="pflx != null">PFLX = #{pflx},</if>
            <if test="cpfldm != null">CPFLDM = #{cpfldm},</if>
            <if test="execNumberId != null">EXEC_NUMBER_ID = #{execNumberId},</if>
            <if test="aqpgjl != null">AQPGJL = #{aqpgjl},</if>
            <if test="gongyijianshu != null">GONGYIJIANSHU = #{gongyijianshu},</if>
            <if test="gongyijianshuBeian != null">GONGYIJIANSHU_BEIAN = #{gongyijianshuBeian},</if>
            <if test="ranfalei != null">ranfalei = #{ranfalei},</if>
            <if test="qubanmeibailei != null">qubanmeibailei = #{qubanmeibailei},</if>
            <if test="fangshailei != null">fangshailei = #{fangshailei},</if>
            <if test="sfa != null">sfa = #{sfa},</if>
            <if test="pa != null">pa = #{pa},</if>
            <if test="yushousfa != null">yushousfa = #{yushousfa},</if>
            <if test="xingongxiao != null">xingongxiao = #{xingongxiao},</if>
            <if test="xingongxiaocontent != null">xingongxiaocontent = #{xingongxiaocontent},</if>
            <if test="ftlTime != null">ftl_time = #{ftlTime},</if>
            <if test="filCode != null">FIL_CODE = #{filCode},</if>
            <if test="baCode != null">BA_CODE = #{baCode},</if>
            <if test="baTime != null">ba_time = #{baTime},</if>
            <if test="filCodeNote != null">FIL_CODE_NOTE = #{filCodeNote},</if>
            <if test="baCodeNote != null">BA_CODE_NOTE = #{baCodeNote},</if>
            <if test="waxcName != null">waxc_name = #{waxcName},</if>
            <if test="waxcOthername != null">waxc_othername = #{waxcOthername},</if>
            <if test="waxcStatus != null">waxc_status = #{waxcStatus},</if>
            <if test="baStatus != null">ba_status = #{baStatus},</if>
            <if test="formulaPid != null">formula_pid = #{formulaPid},</if>
            <if test="bpNote != null">BP_NOTE = #{bpNote},</if>
            <if test="zsTime != null">zs_time = #{zsTime},</if>
            <if test="zsCode != null">zs_code = #{zsCode},</if>
            <if test="gongyijianshuZs != null">GONGYIJIANSHU_ZS = #{gongyijianshuZs},</if>
            <if test="yfFile != null">YF_FILE = #{yfFile},</if>
            <if test="zsFile != null">ZS_FILE = #{zsFile},</if>
            <if test="isLove != null">IS_LOVE = #{isLove},</if>
            <if test="upRate != null">UP_RATE = #{upRate},</if>
            <if test="oriPrice != null">ORI_PRICE = #{oriPrice},</if>
            <if test="levelNum != null">LEVEL_NUM = #{levelNum},</if>
            <if test="purpose != null">purpose = #{purpose},</if>
            <if test="formulaRemark != null">formula_remark = #{formulaRemark},</if>
            <if test="projectNo != null">PROJECT_NO = #{projectNo},</if>
            <if test="itemName != null">ITEM_NAME = #{itemName},</if>
            <if test="itemNameText != null">ITEM_NAME_TEXT = #{itemNameText},</if>
            <if test="formulaImage != null">formula_image = #{formulaImage},</if>
            <if test="isRealse != null">is_realse = #{isRealse},</if>
            <if test="materialStatusInfo != null">material_status_info = #{materialStatusInfo},</if>
            <if test="importCountryInfo != null">import_country_info = #{importCountryInfo},</if>
            <if test="operatorName != null">operator_name = #{operatorName},</if>
            <if test="stabilityStatus != null">stability_status = #{stabilityStatus},</if>
            <if test="isResult != null">is_result = #{isResult},</if>
            <if test="isGt != null">is_gt = #{isGt},</if>
            <if test="materialCycle != null">material_cycle = #{materialCycle},</if>
            <if test="categoryText != null">category_text = #{categoryText},</if>
            <if test="cirId != null">cir_id = #{cirId},</if>
            <if test="duliId != null">duli_id = #{duliId},</if>
            <if test="cirText != null">cir_text = #{cirText},</if>
            <if test="duliText != null">duli_text = #{duliText},</if>
            <if test="jcxmJson != null">JCXM_JSON = #{jcxmJson},</if>
            <if test="currentTemplateId != null">current_template_id = #{currentTemplateId},</if>
            <if test="copyFormulaId != null">copy_formula_id = #{copyFormulaId},</if>
            <if test="cosmeticClassification != null">cosmetic_classification = #{cosmeticClassification},</if>
            <if test="cosmeticCase != null">cosmetic_case = #{cosmeticCase},</if>
            <if test="cosmeticCaseFirst != null">cosmetic_case_first = #{cosmeticCaseFirst},</if>
            <if test="cosmeticCaseSecond != null">cosmetic_case_second = #{cosmeticCaseSecond},</if>
            <if test="weishenwuResult != null">weishenwu_result = #{weishenwuResult},</if>
            <if test="weishenwuRemark != null">weishenwu_remark = #{weishenwuRemark},</if>
            <if test="xiangrongxingResult != null">xiangrongxing_result = #{xiangrongxingResult},</if>
            <if test="xiangrongxingRemark != null">xiangrongxing_remark = #{xiangrongxingRemark},</if>
            <if test="wendingxingResult != null">wendingxing_result = #{wendingxingResult},</if>
            <if test="wendingxingRemark != null">wendingxing_remark = #{wendingxingRemark},</if>
            <if test="bomSame != null">bom_same = #{bomSame},</if>
            <if test="erpCode != null">erp_code = #{erpCode},</if>
            <if test="formulaStatus != null">formula_status = #{formulaStatus},</if>
            <if test="bomRemark != null">bom_remark = #{bomRemark},</if>
            <if test="bomUpdateTime != null">bom_update_time = #{bomUpdateTime},</if>
            <if test="bomUpdateBy != null">bom_update_by = #{bomUpdateBy},</if>
            <if test="isCiji != null">is_ciji = #{isCiji},</if>
            <if test="isZhimin != null">is_zhimin = #{isZhimin},</if>
            <if test="formulaConstructionIdeas != null">formula_construction_ideas = #{formulaConstructionIdeas},</if>
            <if test="gxgs != null">gxgs = #{gxgs},</if>
        </trim>
        where ID = #{id}
    </update>
    <update id="updateSoftwareDevelopingFormulaFiles" parameterType="SoftwareDevelopingFormula">
        update t_un_formula
        <trim prefix="SET" suffixOverrides=",">
            <if test="introFile != null">INTRO_FILE = #{introFile},</if>
            <if test="wendingxingFile != null">WENDINGXING_FILE = #{wendingxingFile},</if>
            <if test="gongyiFile != null">GONGYI_FILE = #{gongyiFile},</if>
            <if test="xiangrongxingFile != null">XIANGRONGXING_FILE = #{xiangrongxingFile},</if>
            <if test="weishenwuFile != null">WEISHENWU_FILE = #{weishenwuFile},</if>
            <if test="xiaofeizheFile != null">XIAOFEIZHE_FILE = #{xiaofeizheFile},</if>
            <if test="qitaFile != null">QITA_FILE = #{qitaFile},</if>
            <if test="weishenwuResult != null">weishenwu_result = #{weishenwuResult},</if>
            <if test="weishenwuRemark != null">weishenwu_remark = #{weishenwuRemark},</if>
            <if test="xiangrongxingResult != null">xiangrongxing_result = #{xiangrongxingResult},</if>
            <if test="xiangrongxingRemark != null">xiangrongxing_remark = #{xiangrongxingRemark},</if>
            <if test="wendingxingResult != null">wendingxing_result = #{wendingxingResult},</if>
            <if test="wendingxingRemark != null">wendingxing_remark = #{wendingxingRemark},</if>
        </trim>
        where ID = #{id}
    </update>

    <update id="deleteSoftwareDevelopingFormulaByIds" parameterType="String">
        update  t_un_formula set is_del = 1,LAST_MODIFIED_TIME = now() where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!--更新价格-->
    <update id="updateFormulaPriceData" parameterType="java.util.Map">
        update t_un_formula set price = #{totalPrice},IS_CALC = #{isCalc} where id = #{formulaId}
    </update>

    <!--更新出口国家-->
    <update id="updateFormulaImportInfo" parameterType="java.lang.Long">
        update t_un_formula f set import_country_info =
          (select GROUP_CONCAT(DISTINCT `import_country`) from t_un_material_formula mf
              left join t_un_material m on mf.MATERIAL_ID = m.ID
           where mf.FORMULA_ID = f.id and m.is_import = 1)
        where f.id in (
            SELECT DISTINCT formula_id from t_un_material_formula
            where material_id = #{materialId}
        )
    </update>

    <update id="updateFormulaCycleInfoInfo" parameterType="java.lang.Long">
        update t_un_formula f set material_cycle =
      (select GROUP_CONCAT(DISTINCT m.`material_code`) from t_un_material_formula mf
          left join t_un_material m on mf.MATERIAL_ID = m.ID
       where mf.FORMULA_ID = f.id and ORDERINGCYCLE >= 30)
        where f.id in (
            SELECT DISTINCT formula_id from t_un_material_formula
            where material_id = #{materialId}
        )
    </update>

    <!--获取配方类别数据-->
    <select id="queryFormulaClassifyData" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            id categoryId,
            NAME categoryName,
            IFNULL( parent_id, 0 ) parentId
        FROM
            t_un_formula_classify
        WHERE
            is_del = 0
        ORDER BY
            id DESC
    </select>

    <!--
        <if test="productName!=null">
            and PRODUCT_NAME != #{productName}
        </if>-->
    <select id="queryFormulaLabCount" parameterType="SoftwareDevelopingFormula" resultType="java.lang.Integer">
        select count(*) from t_un_formula where laboratory_code = #{laboratoryCode}
        and PROJECT_NO  is not null
        <if test="customerName!=null">
            and customer_name != #{customerName}
        </if>
        <if test="id!=null">
            and id != #{id}
        </if>
    </select>

    <select id="queryFormulaCount" resultType="java.lang.Integer">
        select count(*) from t_un_formula where YEAR(CREATED_TIME)=YEAR(NOW())
    </select>

    <update id="updateFormulaMaterialData" parameterType="java.lang.Long">
        update t_un_formula f set import_country_info =
          (select GROUP_CONCAT(DISTINCT `import_country`) from t_un_material_formula mf
             left join t_un_material m on mf.MATERIAL_ID = m.ID
           where mf.FORMULA_ID = f.id  and m.is_import = 1)
        where f.id = #{id}
            order by f.id desc
    </update>


    <update id="updateFormulaMaterialCycleData" parameterType="java.lang.Long">
        update t_un_formula f set material_cycle =
          (select GROUP_CONCAT(DISTINCT m.`material_code`) from t_un_material_formula mf
              left join t_un_material m on mf.MATERIAL_ID = m.ID
           where  mf.FORMULA_ID = f.id and  m.ORDERINGCYCLE >= 30)
        where f.id = #{id}
            order by f.id desc
    </update>


    <!--查找配方作为原料信息-->
    <select id="queryFormulaDataInfo" parameterType="SoftwareMaterial" resultType="SoftwareDevelopingFormula">
        select id,product_name productName,
               item_name_text itemNameText,
               laboratory_code,price,
               project_no projectNo,
               item_name itemName,
              (select count(*) from t_un_material_formula mf where mf.FORMULA_ID = f.id and mf.type = 1) materialRsCount
        from t_un_formula f where formula_code = #{formulaCode} and is_del = 0 limit 1
    </select>

    <select id="queryAllFormulaData" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            a.id,
            a.laboratory_code,
            a.PRODUCT_NAME productName
        FROM t_un_formula a
        where a.is_del  = 0
        order by a.id desc
    </select>

    <!-- 批量插入配方原料修改记录数据 -->
    <insert id="batchMaterialFormulaRecordList"  parameterType="java.util.List">
        insert into t_un_material_formula_modified_log (material_id,formula_id,PERCENTAGE_OLD,PERCENTAGE_NEW,MODIFIED_TIME,`remark`,operator,IS_DEL,CREATED_TIME)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.materialId}, #{item.formulaId}, #{item.percentageOld},#{item.percentageNew},now(),#{item.remark},#{item.operator},0,now())
        </foreach>
    </insert>

    <!--获取变更记录-->
    <select id="queryFormualMaterialRecipeChangeHistoryData" parameterType="java.lang.Long" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            fml.id,
            fml.PERCENTAGE_OLD percentageOld,
            fml.PERCENTAGE_NEW percentageNew,
            DATE_FORMAT(fml.MODIFIED_TIME,'%Y-%m-%d') modifiedTime,
            fml.operator,
            fml.remark,
            m.MATERIAL_CODE materialCode,
            m.INCI_NAME inciName
        FROM
            t_un_material_formula_modified_log fml
                LEFT JOIN t_un_material m ON fml.material_id = m.id
        WHERE
            fml.formula_id = #{id}
        ORDER BY
            FML.ID DESC
    </select>

    <!--获取变更记录-->
    <select id="queryFormulaZxbzDataList" parameterType="java.lang.Long" resultType="com.alibaba.fastjson.JSONObject">
        select
            id,
            zxbzh
        from
            t_un_formula_zxbz
        where is_del = 0
          and status in ('现行','即将被替代')
        order by id desc
    </select>

    <!--获取变更记录-->
    <select id="queryFormulaZxbzDataDetail" parameterType="java.lang.Long" resultType="com.alibaba.fastjson.JSONObject">
        select
            `id`,
            `zxbzh`,
            `bzmc`,
            `dingyi`,
            `waiguanjiph`,
            `naire`,
            `naihan`,
            `note`,
             DATE_FORMAT(`publish_time`,'%Y-%m-%d') publishTime,
            `status`,
            `ownershop_company` ownershopCompany,
            `author`,
            `replace_bz` replaceBz,
            `bzxz`,
             DATE_FORMAT(`abolition_date`,'%Y-%m-%d') abolitionDate
        from
            t_un_formula_zxbz
        where id = #{id}
     </select>


    <!--获取成分列表数据-->
    <select id="queryIngredientExcelList" parameterType="java.lang.Long" resultType="java.util.Map">
        select
            mf.id mfId,
            m.id mid,
            ifnull(m.status,0) status,
            M.MATERIAL_CODE materialCode,
            m.MATERIAL_CHI_NAME materialChiName,
            m.MATERIAL_GOODS_NAME materialGoodsName,
            m.INCI_NAME inciName,
            m.INCI_NAME_ENG inciNameEng,
            m.PURPOSE_OF_USER purposeOfUser,
            m.USE_LIMIT useLimit,
            m.PROPORTION proportionSingle,
            m.PROPORTION proportion,
            m.typeical,
            m.sj_proportion sjProportionSingle,
            m.sj_proportion sjProportion,
            m.CAS_NO casNo,
            mf.PERCENTAGE percentage,
            m.inic_symd inicSymd,
            m.inic_remark inicRemark,
            11 inicRemark1,
            m.inic_nmjyl inicNmjyl,
            m.inic_yzwsybw inicYzwsybw,
            m.inic_bxjmcjyl inicBxjmcjyl,
            m.inic_lbyl inicLbyl,
            m.ylbsm,
            m.`RJX` rjx,
            m.`zsgx` zsgx,
            m.`zyjl` zyjl,
            m.`twcsjl` twcsjl,
            m.`tncsjl` tncsjl,
            m.`sfyyxdz` sfyyxdz,
            m.`gxgs` gxgs,
            m.`zlh` zlh,
            m.`rzhzs` rzhzs,
            m.`yllydq` yllydq,
            m.`ycdzm` ycdzm,
            m.`ylscg` ylscg,
            m.`ycgs` ycgs,
            m.`jxdx` jxdx,
            m.`cjx` cjx,
            m.orderingcycle,
            m.is_import isImport,
            m.import_country importCountry,
            m.`pfzmx` pfzmx,
            m.`pfgdx` pfgdx,
            m.`gbtfy` gbtfy,
            m.`ztbx` ztbx,
            m.`cfjlbd` cfjlbd,
            m.`fxhszdx` fxhszdx,
            m.`mxdx` mxdx,
            m.`dddlx` dddlx,
            m.`rqaqzl` rqaqzl,
            m.`whsb` whsb,
            m.`jlfygxpg` jlfygxpg,
            m.`blpg` blpg,
            m.`fxtzms` fxtzms,
            m.`fxpgfx` fxpgfx,
            m.`fxkzcs` fxkzcs,
            m.`fxpgjl` fxpgjl,
            m.`ckwx` ckwx,
            m.`appearance` appearance,
            m.`color` color,
            m.`odor` odor,
            m.`ph` ph,
            m.`rd` rd,
            m.`fd` fd,
            m.`sd` sd,
            m.`randian` randian,
            m.`hd` hd,
            m.`srjx` srjx,
            m.`fjwd` fjwd,
            m.`bztx` bztx,
            m.`gyjs` gyjs,
            m.`pfzysx` pfzysx,
            m.`sycplx` sycplx,
            m.`ylly` ylly,
            m.`jytjl` jytjl,
            m.`yyal` yyal,
            m.`zsgx` zsgx,
            m.`lhzb_remark`lhzbRemark,
            m.`GX_REMARK` gxRemark,
            m.`AQXX_FILE` aqxFile,
            m.`GX_FILE` gxFile,
            m.`IS_GXYL` isGxyl,
            m.`is_fx` isFx,
            m.material_supplier materialSupplier,
            if(m.IS_LIMIT=1,'是','否') isLimit,
            ifnull(m.MAX_CONCENTRATION,'/') maxConcentration,
            ifnull(m.ATTENTION,'/') attention,
            ifnull(m.REMINDER,'/') reminder,
            m.USE_LIMIT_DETAIL useLimitDetail,
            'securityRisk' securityRisk,
            'activeIngredient' activeIngredient,
            'riskOfBeans' riskOfBeans,
            'effect' effect,
            mf.SUB_ITEM subItem,
            mf.SYMD_INFO symdInfo,
            mf.designated_use designatedUse,
            m.IS_NEW_MATERIAL isNewMaterial,
            concat(m.ZC_BA_CODE,'/',m.ba_code) zcBaCode,
            f.id formulaId,
            f.formula_code formulaCode,
            m.zdy_data materialZdyData,
            m.cf_is_lake cfIsLake,
            m.cf_lake cfLake,
            m.cf_nmjyl cfNmjyl,
            m.cf_sybw cfSybw,
            m.is_edit_ifra isEditIfra
        from t_un_material m
                 left join t_un_material_formula mf on mf.MATERIAL_ID = m.ID
                 left join t_un_formula f on f.id = mf.FORMULA_ID
        where m.IS_DEL = 0
          and f.ID=#{idValue}
          and mf.type = 0
        order by m.id desc
    </select>

     <select id="queryIngredientNewExcelList" parameterType="java.util.List" resultType="java.util.Map">
        select
        mf.id mfId,
        m.id mid,
        ifnull(m.status,0) status,
        M.MATERIAL_CODE materialCode,
        m.MATERIAL_CHI_NAME materialChiName,
        m.MATERIAL_GOODS_NAME materialGoodsName,
        m.INCI_NAME inciName,
        m.INCI_NAME_ENG inciNameEng,
        m.PURPOSE_OF_USER purposeOfUser,
        m.USE_LIMIT useLimit,
        m.PROPORTION proportionSingle,
        m.PROPORTION proportion,
        m.sj_proportion sjProportionSingle,
        m.sj_proportion sjProportion,
        m.CAS_NO casNo,
        mf.PERCENTAGE percentage,
        m.inic_symd inicSymd,
        m.inic_remark inicRemark,
        11 inicRemark1,
        m.typeical,
        m.inic_nmjyl inicNmjyl,
        m.inic_yzwsybw inicYzwsybw,
        m.inic_bxjmcjyl inicBxjmcjyl,
        m.inic_lbyl inicLbyl,
        m.ylbsm,
        m.`RJX` rjx,
        m.`is_fx` isFx,
        m.`zsgx` zsgx,
        m.`zyjl` zyjl,
        m.`twcsjl` twcsjl,
        m.`tncsjl` tncsjl,
        m.`sfyyxdz` sfyyxdz,
        m.`gxgs` gxgs,
        m.`zlh` zlh,
        m.`rzhzs` rzhzs,
        m.`yllydq` yllydq,
        m.`ycdzm` ycdzm,
        m.`ylscg` ylscg,
        m.`ycgs` ycgs,
        m.`jxdx` jxdx,
        m.`cjx` cjx,
        m.`pfzmx` pfzmx,
        m.`pfgdx` pfgdx,
        m.`gbtfy` gbtfy,
        m.`ztbx` ztbx,
        m.`cfjlbd` cfjlbd,
        m.`fxhszdx` fxhszdx,
        m.`mxdx` mxdx,
        m.`dddlx` dddlx,
        m.`rqaqzl` rqaqzl,
        m.`whsb` whsb,
        m.`jlfygxpg` jlfygxpg,
        m.`blpg` blpg,
        m.`fxtzms` fxtzms,
        m.`fxpgfx` fxpgfx,
        m.`fxkzcs` fxkzcs,
        m.`fxpgjl` fxpgjl,
        m.`ckwx` ckwx,
        m.`appearance` appearance,
        m.`color` color,
        m.`odor` odor,
        m.`ph` ph,
        m.`rd` rd,
        m.`fd` fd,
        m.`sd` sd,
        m.`randian` randian,
        m.`hd` hd,
        m.`srjx` srjx,
        m.`fjwd` fjwd,
        m.`bztx` bztx,
        m.`gyjs` gyjs,
        m.`pfzysx` pfzysx,
        m.`sycplx` sycplx,
        m.`ylly` ylly,
        m.`jytjl` jytjl,
        m.`yyal` yyal,
        m.`zsgx` zsgx,
        m.`lhzb_remark`lhzbRemark,
        m.`GX_REMARK` gxRemark,
        m.`AQXX_FILE` aqxFile,
        m.`GX_FILE` gxFile,
        m.`IS_GXYL` isGxyl,
        m.zdy_data materialZdyData,
        m.material_supplier materialSupplier,
        if(m.IS_LIMIT=1,'是','否') isLimit,
        ifnull(m.MAX_CONCENTRATION,'/') maxConcentration,
        ifnull(m.ATTENTION,'/') attention,
        ifnull(m.REMINDER,'/') reminder,
        m.USE_LIMIT_DETAIL useLimitDetail,
        'securityRisk' securityRisk,
        'activeIngredient' activeIngredient,
        'riskOfBeans' riskOfBeans,
        'effect' effect,
        mf.SUB_ITEM subItem,
        mf.SYMD_INFO symdInfo,
        m.IS_NEW_MATERIAL isNewMaterial,
        concat(m.ZC_BA_CODE,'/',m.ba_code) zcBaCode,
        f.id formulaId,
        f.formula_code formulaCode,
         m.cf_is_lake cfIsLake,
         m.cf_lake cfLake,
         m.cf_nmjyl cfNmjyl,
         m.cf_sybw cfSybw,
         m.is_edit_ifra isEditIfra
        from t_un_material m
        left join t_un_material_formula mf on mf.MATERIAL_ID = m.ID
        left join t_un_formula f on f.id = mf.FORMULA_ID
        where m.IS_DEL=0
        and mf.type = 0
        and f.ID in
        <foreach collection="list" close=")" item="idMap" separator="," open="(">
            #{idMap.formulaId}
        </foreach>
        order by mf.id desc
    </select>

    <!--获取特殊原料替换信息-->
    <select id="queryMaterialFormulaSpecialInfo" parameterType="java.lang.Long" resultType="java.util.Map">
        select
            id,
            formula_id formulaId,
            material_id materialId,
            mf_id mfId,
            material_code materialCode,
            inci_name inciName,
            repalace_inci_name repalaceInciName
        from t_un_material_formula_special
        where FORMULA_ID = #{id}
        order by id desc
    </select>


    <!-- 删除特殊原料 -->
    <delete id="deleteTipsMaterialFormulaInfo" parameterType="java.lang.Long">
        delete from t_un_material_formula_special where FORMULA_ID = #{formulaId}
    </delete>

    <!-- 保存特殊原料 -->
    <insert id="batchTipsMaterialFormulaInfo">
        insert into t_un_material_formula_special
        (FORMULA_ID,MATERIAL_ID,MF_ID,MATERIAL_CODE,INCI_NAME,REPALACE_INCI_NAME,OPERATOR,CREATED_TIME)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.formulaId}, #{item.materialId},#{item.mfId},#{item.materialCode},
            #{item.inciName},#{item.replaceInciName},#{item.operator},now()
            )
        </foreach>
    </insert>

    <!-- 获取配方使用目的 -->
    <select id="queryFormulaSymdInfo" parameterType="java.lang.Long" resultType="java.util.Map">
        select
            id,
            FORMULA_ID formulaId,
            CHI_NAME chiName,
            symd
        from t_un_formula_symd
        where FORMULA_ID = #{formulaId}
          and is_del = 0
        order by id desc
    </select>

    <!-- 删除使用目的 -->
    <delete id="deleteFormulaSmdInfo" parameterType="java.lang.Long">
        delete from t_un_formula_symd where FORMULA_ID = #{formulaId}
    </delete>

    <!-- 保存使用目的 -->
    <insert id="batchFormulaSymdInfo">
        insert into t_un_formula_symd (FORMULA_ID,CHI_NAME,SYMD,OPERATOR,CREATED_TIME,IS_DEL)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.formulaId}, #{item.chiName},#{item.symdInfo},#{item.operator},now(),0)
        </foreach>
    </insert>

    <!-- 获取配方数目 -->
    <select id="queryPFormulaCountInfo" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(*) from t_un_formula where FORMULA_PID = #{formulaId}
        and FORMULA_CODE like 'P%'
        and is_del = 0
    </select>

    <!-- 获取配方数目 -->
    <select id="queryMaxPFormulaInfo" parameterType="java.lang.Long" resultType="java.lang.String">
        select FORMULA_CODE formulaCode from t_un_formula where FORMULA_PID = #{formulaId}
        and FORMULA_CODE like 'P%'
        and is_del = 0
        order by id desc limit 1
    </select>

    <resultMap id="formualMaterialMap" type="Map">
        <id property="id" column="ID"/>
        <result property="formulaCode"  column="FORMULA_CODE"/>
        <result property="laboratoryCode" column="LABORATORY_CODE"/>
        <result property="materialCode" column="material_code"/>
        <collection property="materialDatas" column="id" ofType="Map" javaType="ArrayList" select="queryFormulaMaterialDataList"></collection>
    </resultMap>

    <select id="queryPFormulaMaterialInfo" resultMap="formualMaterialMap" parameterType="java.lang.Long">
        select f.id,f.FORMULA_CODE,f.LABORATORY_CODE,
               m.material_code
        from t_un_formula f
                 left join t_un_material m on m.FORMULA_PID = f.id
        where f.formula_pid = #{id}
          and f.FORMULA_CODE like 'P%'
          and f.is_del = 0
        order by f.id desc
    </select>

    <select id="queryFormulaMaterialDataList" resultType="Map" parameterType="Integer">
        select c.id,
               c.MATERIAL_CHI_NAME materialChiName,
               c.MATERIAL_ENGLISH_NAME materialEnglishName,
               c.ERP_CODE erpCode,
               c.MATERIAL_GOODS_NAME materialGoodsName,
               c.INCI_NAME inciName,
               c.MATERIAL_CODE materialCode,
               c.PURPOSE_OF_USER purposeOfUser,
               c.PRICE price,
               b.percentage,
               b.material_id materialId
        FROM t_un_material c
                 left join t_un_material_formula  b on  b.MATERIAL_ID = c.id
                 left join t_un_formula f on b.formula_id = f.id
        where b.FORMULA_ID = #{id}
    </select>


    <!-- 获取是否已生成 -->
    <select id="queryMaterialCountInfo" parameterType="java.lang.Long" resultType="java.lang.String">
        select MATERIAL_CODE materialCode from t_un_material where formula_pid = #{id} and is_del = 0 limit 1
    </select>

    <!-- 获取P配方 -->
    <select id="queryPFormulaDataList" parameterType="java.lang.Long" resultType="java.lang.String">
        select formula_code formulaCode from t_un_formula
        where FORMULA_PID  = #{id}
          and FORMULA_CODE like 'P%'
          and is_del = 0
    </select>

    <!-- 更新信息 -->
    <update id="updateBpNoteInfo" parameterType="SoftwareDevelopingFormula">
        update t_un_formula set BP_NOTE = #{bpNote}
        where id = #{id}
    </update>


    <select id="queryFormualBMaterialInfo" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select count(*) from t_un_formula f
        left join t_un_material m on f.id = m.formula_pid
        where f.formula_pid = #{id}  and m.is_del = 0
    </select>

    <resultMap id="formualMaterialNewMap" type="Map">
        <id property="id" column="ID"/>
        <result property="formulaCode"  column="FORMULA_CODE"/>
        <collection property="materialDatas" column="id" ofType="Map" javaType="ArrayList" select="queryFormulaMaterialDataList"></collection>
    </resultMap>

    <select id="queryFormulaBMaterialDataList" parameterType="java.lang.Long" resultMap="formualMaterialNewMap">
        select ID,formula_code formulaCode
        from t_un_formula f where f.formula_pid = #{id}  and f.is_del = 0
    </select>

    <!-- 获取已生成配方原料ID -->
    <select id="queryPFormulaIdDataInfo" parameterType="java.lang.Long" resultType="java.lang.Long">
        select mf.MATERIAL_ID materialId from t_un_formula f
         left join t_un_material_formula mf on f.id = mf.FORMULA_ID
        where FORMULA_PID  = #{id}
          and FORMULA_CODE like 'P%'
    </select>

    <select id="queryFormulaMaterialNewInfo" resultMap="formualMaterialMap" parameterType="java.lang.Long">
        select f.id,f.FORMULA_CODE,f.LABORATORY_CODE,
               m.material_code
        from t_un_formula f
           left join t_un_material m on m.FORMULA_PID = f.id
        where f.formula_pid = #{id}
          and f.is_del = 0
        order by f.id desc
    </select>

    <!--获取查询列表-->
    <select id="querySoftwareFormulaReleaseDataList" parameterType="SoftwareDevelopingFormula" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            id,
            FORMULA_CODE formulaCode,
            PRODUCT_NAME productName,
            customer_name customerName,
            LABORATORY_CODE laboratoryCode,
            PROJECT_NO projectNo,
            purpose,
            is_gt isGt,
            is_result isResult,
            is_ciji isCiji,
            is_zhimin isZhimin,
            IFNULL(f.ckcf_status,0) ckcfStatus,
            IFNULL(f.hh_status,0) hhStatus,
            IFNULL(f.ba_release_status,0) baReleaseStatus,
            f.weishenwu_result weishenwuResult,
            f.weishenwu_remark weishenwuRemark,
            f.xiangrongxing_result xiangrongxingResult,
            f.xiangrongxing_remark xiangrongxingRemark,
            f.wendingxing_result wendingxingResult,
            f.wendingxing_remark wendingxingRemark,
            f.wendingxing_file wendingxingFile,
            f.xiangrongxing_file xiangrongxingFile,
            f.weishenwu_file weishenwuFile,
            (select count(*) FROM t_un_material_formula mf
            INNER JOIN t_un_material m on mf.material_id = m.id
            where mf.FORMULA_ID = f.id and m.IS_NEW_MATERIAL = '是' ) newMaterialCount
        FROM
            t_un_formula f
        WHERE  f.formula_status in (0,1) and LABORATORY_CODE IN
           <foreach collection="labArr" separator="," item="item" open="(" close=")">
               #{item}
           </foreach>
           <if test="projectNo!=null and projectNo!=''">and PROJECT_NO = #{projectNo}</if>
    </select>

    <!--获取查询列表-->
    <select id="querySoftwareFormulaReleaseByIdDataList"   resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            uf.id,
            uf.FORMULA_CODE formulaCode,
            uf.PRODUCT_NAME productName,
            uf.customer_name customerName,
            uf.LABORATORY_CODE laboratoryCode,
            uf.PROJECT_NO projectNo,
            uf.purpose,
            is_gt isGt,
            is_result isResult,
            IFNULL(uf.ckcf_status,0) ckcfStatus,
            IFNULL(uf.hh_status,0) hhStatus,
            IFNULL(uf.ba_release_status,0) baReleaseStatus,
            uf.weishenwu_result weishenwuResult,
            uf.weishenwu_remark weishenwuRemark,
            uf.xiangrongxing_result xiangrongxingResult,
            uf.xiangrongxing_remark xiangrongxingRemark,
            uf.wendingxing_result wendingxingResult,
            uf.wendingxing_remark wendingxingRemark,
            uf.wendingxing_file wendingxingFile,
            uf.xiangrongxing_file xiangrongxingFile,
            uf.weishenwu_file weishenwuFile,
            (select count(*) FROM t_un_material_formula mf
            INNER JOIN t_un_material m on mf.material_id = m.id
            where mf.FORMULA_ID = uf.id and m.IS_NEW_MATERIAL = '是' ) newMaterialCount
        FROM   t_un_formula uf
        WHERE uf.id in
        <foreach collection="list" close=")" open="(" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateSoftwareDevelopingFormulaCkcf">
        update t_un_formula set status = concat(status,',2'),ckcf_status = ifnull(ckcf_status,0) + 1,
          FIL_CODE = #{voucherCode},ftl_time = now()
        where id in
        <foreach collection="list" close=")" open="(" item="item" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="updateSoftwareDevelopingFormulaHuahe">
        update t_un_formula set status = concat(status,',8'),hh_status = ifnull(hh_status,0) + 1,
        hh_code = #{voucherCode},hh_time = now()
        where id in
        <foreach collection="list" close=")" open="(" item="item" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="updateSoftwareDevelopingFormulaFiling">
        update t_un_formula set status = concat(REPLACE(`STATUS`,'cancel',''),',4,6'),ba_release_status = ifnull(ba_release_status,0) + 1,
        BA_CODE = #{voucherCode},zs_code = #{voucherCode},zs_time = now(),ba_time = now(),
        zs_status = ifnull(zs_status,0) + 1,ba_release_name = #{userName}
        where id in
        <foreach collection="list" close=")" open="(" item="item" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="updateSoftwareDevelopingFormulaZs">
        update t_un_formula set status = concat(`STATUS`,',6'),zs_code = #{voucherCode},zs_time = now(),ba_time = now(),
        zs_status = ifnull(zs_status,0) + 1
        where id = #{formulaId}
    </update>

    <update id="updateSoftwareDevelopingFormulaCancelFiling">
        update t_un_formula set status = concat(REPLACE(`STATUS`,',4,6',''),',cancel'),ba_release_status = 0  where id = #{id}
    </update>

    <insert id="saveFormulaDeptDataInfo" parameterType="java.util.Map">
        INSERT INTO t_un_formula_dept
        (`FORMULA_ID`, `DEPT_ID`,`SHARE_DEPT_ID`, `TYPE`, `CREATED_TIME`, `OPERATOR` )
          VALUES
        ( #{formulaId}, #{deptId},#{shareDeptId}, #{type}, now(), #{userName})
    </insert>


    <delete id="deleteFormulaDeptDataInfo" parameterType="java.lang.Long">
         delete from t_un_formula_dept where FORMULA_ID = #{formulaId} and type = 0
    </delete>

    <!-- 获取配方价格数据 -->
    <select id="queryFormulaMaterialPriceInfo" parameterType="java.lang.Long" resultType="java.util.Map">
        SELECT
            a.id,
            a.material_id materialId,
            a.formula_id formulaId,
            a.type,
            ROUND(a.percentage, 6) percentage,
            a.allocation,
            a.remark,
            a.operator,
            a.created_time createdTime,
            b.material_chi_name materialName,
            b.material_goods_name materialGoodsName,
            b.use_limit useLimit,
            b.stock,
            b.MATERIAL_CODE materialCode,
            CASE
                WHEN a.type = 1 THEN
                    ROUND(b1.price, 6)
                ELSE
                    ROUND(b.price, 6)
                END materialPrice,
            CASE
                WHEN a.type = 1 THEN
                    ROUND(
                            b1.price * a.percentage / 100,
                            6
                        )
                ELSE
                    ROUND(b.price * a.percentage / 100, 6)
                END formulaPrice
        FROM
            t_un_material_formula a
                LEFT JOIN t_un_material b ON (
                    a.material_id = b.id
                    AND a.type = 0
                )
                LEFT JOIN t_un_formula b1 ON (
                    a.material_id = b1.id
                    AND a.type = 1
                )
        WHERE
            ifnull(a.is_del, 0) = 0
          AND a.formula_id = #{formula_id}
        ORDER BY
            a.id DESC
    </select>


    <!--获取分类数据-->
    <select id="queryUserFormulaCategory" resultType="com.alibaba.fastjson.JSONObject" parameterType="SoftwareFormulaRelease">
        select
            a.id,a.`name`,a.operator,a.create_time createdTime,a.parent_id parentId,b.`name` parentName
        from t_un_formula_classify a
          LEFT JOIN t_un_formula_classify b on a.parent_id=b.id
        where a.is_del = 0  and a.parent_id is not null
          and a.id not in (
            select c.classify_id from t_un_formula_classify_user c
             where c.user_id=#{userId}
         )
        <if test="voucherCode!=null and voucherCode!=''">
            and (
                a.name like concat('%',#{voucherCode},'%')
                or
                b.name like concat('%',#{voucherCode},'%')
            )
        </if>
    </select>

    <!--获取分类数据-->
    <select id="queryUserUseFormulaCategory" resultType="com.alibaba.fastjson.JSONObject" parameterType="SoftwareFormulaRelease">
        select
            a.id,a.`name`,a.operator,a.create_time createdTime,a.parent_id parentId,b.`name` parentName
        from t_un_formula_classify a
          LEFT JOIN t_un_formula_classify b on a.parent_id=b.id
        where a.is_del = 0  and a.parent_id is not null
          and a.id in (
            select c.classify_id from t_un_formula_classify_user c
             where c.user_id=#{userId}
         )
        <if test="voucherCode!=null and voucherCode!=''">
            and (
            a.name like concat('%',#{voucherCode},'%')
            or
            b.name like concat('%',#{voucherCode},'%')
            )
        </if>
    </select>

    <insert id="addFormulaCategoryData" parameterType="java.util.Map">
        insert into t_un_formula_classify_user(classify_id,user_id,operator,created_time)
        values
        (#{categoryId},#{userId},#{name},now())
    </insert>

    <delete id="deleteSoftwareFormulaCategoryUser" parameterType="SoftwareFormulaRelease">
        delete from t_un_formula_classify_user where classify_id = #{id} and user_id = #{userId}
    </delete>

    <select id="queryFormulaShareDeptDataDetail" resultType="java.lang.Long" parameterType="java.lang.Long">
        SELECT DEPT_ID from t_un_formula_dept  where FORMULA_ID = #{formulaId} and type = 0
    </select>


    <select id="queryFormulaSpecZxbzCount" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(*) from t_un_material_formula_spec where formula_id = #{formulaId}
    </select>


    <select id="selectSoftwareDevelopingFormulaPriceList" parameterType="SoftwareDevelopingFormula" resultType="SoftwareDevelopingFormula">
        SELECT
        a.id,
        a.formula_code,
        a.project_no,
        a.laboratory_code,
        a.product_name,
        a.formula_name,
        a.dept_id deptId,
        a.stability_status,
        a.ba_status,
        a.waxc_status,
        a.customer_name,
        a.material_cycle,
        ifnull(a.brand_name,d.brand_name) brandName,
        CONCAT( e.name,'-',f.name ) clissifyName,
        a.english_name,
        a.purpose,
        a.category,
        a.created_time,
        a.operator,
        a.is_draft,
        a.is_result,
        a.is_gt,
        a.import_country_info,
        fo.type shareType,
        fsf.bizhong,
        a.remark
        FROM
        t_un_formula a
        left join t_un_formula_classify f on f.id=a.category
        left join t_un_formula_classify e on f.parent_id=e.id
        left join t_un_brand d on a.brand_id = d.id
        left join t_un_formula_dept fo on a.id = fo.FORMULA_ID
        left join t_un_material_formula_spec_ffbz fsf on a.id = fsf.formula_id
        <where>
            a.is_del = 0
            <if test="formulaCode != null  and formulaCode != ''"> and a.FORMULA_CODE like concat(#{formulaCode}, '%')</if>
            <if test="laboratoryCode != null  and laboratoryCode != ''"> and a.LABORATORY_CODE like concat(#{laboratoryCode}, '%')</if>
            <if test="productName != null  and productName != ''"> and a.PRODUCT_NAME like concat('%', #{productName}, '%')</if>
            <if test="organizationId!=null and organizationId>0">
                and fo.dept_id in
                <foreach collection="organizationIdList" item="deptId" index="index" separator="," close=")" open="(">
                    #{deptId}
                </foreach>
            </if>

            <if test="searchUserId!=null and searchUserId>0">
                and a.category in (select classify_id from t_un_formula_classify_user g where g.user_id=#{searchUserId})
            </if>
            <if test="isAdmin!=null and isAdmin==1">
                and fo.type = 1
            </if>
            <if test="isDraft!=null">
                and a.is_draft = #{isDraft}
            </if>
            <if test="formulaName != null and formulaName !=''">
                and (a.formula_name like CONCAT('%',#{formulaName},'%')
                or
                a.english_name like CONCAT('%',#{formulaName},'%')
                or
                a.formula_code like CONCAT('%',#{formulaName},'%')
                or
                a.project_no like CONCAT('%',#{formulaName},'%')
                or
                a.customer_code like CONCAT('%',#{formulaName},'%')
                or
                a.customer_name like CONCAT('%',#{formulaName},'%')
                or
                a.series_name like CONCAT('%',#{formulaName},'%')
                or
                d.maintain like CONCAT('%',#{formulaName},'%')
                or
                a.operator like CONCAT('%',#{formulaName},'%')
                or
                d.brand_name like CONCAT('%',#{formulaName},'%')
                or
                a.brand_name like CONCAT('%',#{formulaName},'%')
                or
                a.PRODUCT_NAME like CONCAT('%',#{formulaName},'%')
                or
                a.LABORATORY_CODE like CONCAT('%',#{formulaName},'%')
                )
            </if>
        </where>
        order by a.id desc
    </select>

    <insert id="insertFormulaBcpTemplateData" parameterType="SoftwareDevelopingFormula">
        INSERT INTO  `t_un_material_formula_spec_ffbz` (`FORMULA_ID`, `TEMPLATE_ID`, `JCXM_JSON`, `WAIGUAN`, `YANSE`, `QIWEI`, `SHIYONGGAN`, `BIZHONG`, `PH`, `NIANDU`, `YINGDU`, `DIELUOCESHI`, `TUCACESHI`, `DUANLIEQIANGDU`, `RONGDIAN`, `SHUSHUIXING`, `XIDU`, `ZIDINGYI1`, `ZIDINGYI2`, `REMARK`, `OPERATOR`, `CREATED_TIME`, `IS_DEL`, `LAST_MODIFIED_TIME`, `NOTE`, `LIXIN`)
        SELECT #{id}, `TEMPLATE_ID`, `JCXM_JSON`, `WAIGUAN`, `YANSE`, `QIWEI`, `SHIYONGGAN`, `BIZHONG`, `PH`, `NIANDU`, `YINGDU`, `DIELUOCESHI`, `TUCACESHI`, `DUANLIEQIANGDU`, `RONGDIAN`, `SHUSHUIXING`, `XIDU`, `ZIDINGYI1`, `ZIDINGYI2`, `REMARK`,#{operator}, NOW(), `IS_DEL`, NOW(), `NOTE`, `LIXIN`
        FROM  t_un_material_formula_spec_ffbz WHERE  FORMULA_ID = #{copyFormulaId}
    </insert>


    <select id="queryFormulaMaterialList" parameterType="java.lang.Long" resultType="TUnMaterialFormula">
        SELECT  a.id,a.material_id materialId,a.formula_id formulaId,a.sub_item subItem,
                ifnull(a.designated_use,"/") designatedUse,
                a.type,
                a.percentage,a.allocation,a.price,a.remark,a.operator,
                a.SYMD_INFO symdInfo,
                a.created_time createdTime,b.material_chi_name materialName,b.material_goods_name materialGoodsName,
                b.price materialPrice,b.use_limit useLimit,b.stock  ,b.MATERIAL_CODE materialCode ,b.INCI_NAME inciName,
                b.status,
                b.orderingCycle,
                b.is_import isImport,
                b.import_country importCountry,
                b1.price materialPrice1,
                b1.product_name productName,
                b1.LABORATORY_CODE materialCode1,
                b1.formula_code formulaCode,
                b1.project_no projectNo,
                b1.item_name itemName
        from t_un_material_formula a
                 left join t_un_material b on (a.material_id = b.id and a.type = 0)
                 left join t_un_formula b1 on (a.material_id = b1.id and a.type = 1)
        where a.formula_id = #{id}
          and a.is_del=0
        order by a.id
    </select>

    <!-- 批量保存数据 -->
    <insert id="batchInsertInfo" parameterType="java.util.List">
        insert into t_un_material_formula_audit(FORMULA_ID,FORMULA_AUDIT_ID,FORMULA_AUDIT_CODE,MATERIAL_ID,OPERATOR,CREATED_TIME,IS_DEL,PERCENTAGE)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.formulaId}, #{item.formulaAuditId},#{item.formulaAuditCode}, #{item.materialId},#{item.operator},now(),0,#{item.percentage})
        </foreach>
    </insert>


    <select id="queryFormulaSpecDataList" resultType="com.alibaba.fastjson.JSONObject">
        select
            `id`,
             `formula_id` formulaId,
            `waiguan`,
            `yanse`,
            `qiwei`,
            `shiyonggan`,
            `bizhong`,
            `ph`,
            `niandu`,
            `yingdu`,
            `dieluoceshi`,
            `tucaceshi`,
            `duanlieqiangdu`,
            `rongdian`,
            `shushuixing`,
            `xidu`,
            `lixin`
       from t_un_material_formula_spec_ffbz
        where JCXM_JSON is null
        order by id desc
    </select>

    <update id="updateFormulaSpecData" parameterType="java.util.Map">
        update t_un_formula set JCXM_JSON = #{json}  where id = #{id} and JCXM_JSON is null
    </update>

    <update id="updateMaterialFormulaSymdInfo" parameterType="java.util.Map">
        update t_un_material_formula set SYMD_INFO = #{symdInfo} where id = #{id}
    </update>

    <select id="queryFormulaDataList" resultType="java.lang.Long">
        select id from t_un_formula f  where  f.create_time BETWEEN DATE_SUB(NOW(), INTERVAL 3 DAY) AND NOW() order by id desc
    </select>

    <select id="queryFormulaProjectList" resultType="com.alibaba.fastjson.JSONObject" parameterType="java.lang.String">
        SELECT
            id,
            product_name productName,
            item_name itemName,
            item_name_text itemNameText
        FROM
            t_un_formula
        WHERE
            PROJECT_NO = #{projectNo}
    </select>

    <update id="updateFormulaProductNameData" parameterType="java.util.Map">
        update t_un_formula set product_name = #{productName},item_name_text = #{newItemName} where id = #{id}
    </update>


    <select id="queryFormulaInfo" resultType="com.alibaba.fastjson.JSONObject" parameterType="java.lang.String">
        select id,formula_code formulaCode from t_un_formula where LABORATORY_CODE = #{labNo} order by id desc limit 1
    </select>

    <select id="queryFormulaMaterialCostDataList" resultType="com.alibaba.fastjson.JSONObject" parameterType="java.lang.Long">
        SELECT
            m.id,
            m.material_code materialCode,
            m.erp_code_info erpCodeInfo,
            mf.percentage,
            m.price
        FROM
            t_un_formula f
            INNER JOIN t_un_material_formula mf ON f.id = mf.FORMULA_ID
            INNER JOIN t_un_material m ON mf.MATERIAL_ID = m.id
        WHERE
            f.id = #{formulaId}
     </select>

    <select id="queryOrderFormulaDataList" resultType="com.alibaba.fastjson.JSONObject" parameterType="java.lang.String">
        select
               id formulaId,
               formula_code formulaCode,
               product_name productName,
               item_name_text itemNameText,
               category_text categoryText,
               category,
               status,
               0 isMain
        from t_un_formula
        where LABORATORY_CODE = #{labNo} and is_del = 0 and is_draft = 0 and formula_status in (0,1)
        order by id desc
    </select>

    <select id="queryFormulaProductName" parameterType="ProjectItemOrder" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            id,
            product_name productName,
            item_name_text itemNameText
        FROM
            t_un_formula
        WHERE
            LABORATORY_CODE = #{confirmCode}
    </select>

    <select id="queryFormulaAppointMaterialDataList" resultType="com.alibaba.fastjson.JSONObject" parameterType="java.lang.Long">
        SELECT id,material_sub_code materialSubCode  FROM t_un_material_producer where use_priority != '3' and del_flag = 0 and MATERIAL_ID = #{id}
    </select>

    <select id="selectSoftwareDevelopingFormulaListByErpCode" resultType="SoftwareDevelopingFormula" >
        SELECT
            id,
            laboratory_code,
            formula_code
        FROM
            t_un_formula
        where erp_code =#{erpCode}
    </select>

    <select id="queryFormulaCategoryDataInfo" resultType="com.alibaba.fastjson.JSONObject" parameterType="QcBcpStandard">
        select a.id,
               a.category,category_text categoryText,
               a.jcxm_json jcxmJson,
               fsf.wx_id wxId,
               fsf.inspect_basis inspectBasis,
               fsf.jl_pl jlPl,
               fsf.jl_standard jlStandard,
               fsf.mj_standard mjStandard,
               fsf.jq_pl jqPl,
               fsf.period_Of_Validity periodOfValidity,
               fsf.microbe_remark microbeRemark
        from  t_un_formula a
        left join t_un_material_formula_spec_ffbz fsf on a.id = fsf.formula_id
        where a.id = #{id}
    </select>

    <select id="queryFormulaCategoryDataList" resultType="com.alibaba.fastjson.JSONObject" parameterType="QcBcpStandard">
        select a.id,a.formula_code formulaCode,a.product_name productName,
               a.category,category_text categoryText,
               a.ba_release_status baReleaseStatus,
               a.stability_status stabilityStatus,
               a.laboratory_code labNo,
               a.waxc_status waxcStatus,
               a.hh_status hhStatus,
               a.ckcf_status ckcfStatus,
               a.customer_name customerName,
               a.purpose,
               CONCAT( e.name,'-',f.name ) clissifyName,a.jcxm_json jcxmJson,
               fsf.wx_id wxId,
               fsf.inspect_basis inspectBasis,
               fsf.jl_pl jlPl,
               fsf.jl_standard jlStandard,
               fsf.mj_standard mjStandard,
               fsf.jq_pl jqPl,
               fsf.period_Of_Validity periodOfValidity,
               fsf.microbe_remark microbeRemark
        from  t_un_formula a
        left join t_un_formula_classify f on f.id = a.category
        left join t_un_formula_classify e on f.parent_id=e.id
        left join t_un_material_formula_spec_ffbz fsf on a.id = fsf.formula_id
        where a.laboratory_code = #{labCode} and a.is_del = 0 and a.IS_DRAFT = 0
        order by a.id desc
    </select>

    <update id="updateSoftwareDevelopingFormulaProductSafetyAssessmentData" parameterType="SoftwareDevelopingFormula">
        update t_un_formula set apxq = #{formulaProductSafetyAssessmentData} where id = #{id}
    </update>

    <select id="querySoftwareFormulaDataList" resultType="java.lang.Long">
        SELECT id from t_un_formula where id &lt;= 12846l order by id desc
    </select>

    <select id="querySoftwareFormulaDataListByUpdateTime" resultType="java.lang.Long">
        SELECT id from t_un_formula where  LAST_MODIFIED_TIME >= CURDATE() - INTERVAL 1 DAY
    </select>

    <update id="updateSoftwareDevelopingFormulaVersion" parameterType="java.lang.Long">
        update t_un_formula set current_version = IFNULL(current_version,0) +  1 where id = #{id}
    </update>

    <select id="queryFormulaGxList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT id,gxxc,wdx,fftz from zdy_formula_product_desc
    </select>

    <update id="updateZdyFormulaDesc" parameterType="java.util.Map">
        update zdy_formula_product_desc
         set GXXC_DESC = #{result},WDX_DESC=#{wdxResult},FFTZ_DESC=#{wswResult} where  id = #{id}
    </update>

    <select id="queryFormulaAiDataList" parameterType="com.ruoyi.common.vo.AiDto" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        a.formula_code formulaCode,
        a.PRODUCT_NAME productName,
        a.LABORATORY_CODE labNo,
        a.dept_id companyName,
        a.gxgs,
        CONCAT( e.NAME, '-', f.NAME ) categoryName,
        gxxc,
        jcxm_json jcxmJson,
        wendingxing_result wendingxingResult,
        xiangrongxing_result xiangrongxingResult,
        weishenwu_result weishenwuResult
        FROM
        t_un_formula a
        LEFT JOIN t_un_formula_classify f ON f.id = a.category
        LEFT JOIN t_un_formula_classify e ON f.parent_id = e.id
        WHERE
        a.DEPT_ID IN ( 7, 8, 32 )
        AND a.CREATED_TIME >=  #{startTime}
        AND a.CREATED_TIME &lt;= #{endTime}
        AND a.FORMULA_CODE NOT LIKE 'P%'
        ORDER BY  a.id DESC
    </select>


    <!-- 获取宜侬配方 备案 -->
    <select id="queryEnowFormulaDataList" parameterType="com.ruoyi.common.vo.AiDto" resultType="com.alibaba.fastjson.JSONObject">
        select a.id,a.status,a.ckcf_status ckcfStatus,a.hh_status hhStatus from t_un_formula a where a.LABORATORY_CODE = #{code} and a.is_draft = 0 order by id desc
    </select>

    <select id="selectFormulaIdByFormulaCode" resultType="java.lang.Long" parameterType="java.lang.String">
        select  id from t_un_formula where formula_code = #{formulaId}
    </select>

    <select id="queryFormulaCategoryDetailData" resultType="com.alibaba.fastjson.JSONObject" parameterType="com.alibaba.fastjson.JSONObject">
        select f.id,category,category_text categoryText,c2.name fName,c1.name
        from  t_un_formula f
        left join t_un_formula_classify c1 on c1.id = f.category
        left join t_un_formula_classify c2 on c1.parent_id = c2.id
        where f.laboratory_code =#{labCode}  or f.erp_code  = #{erpCode} order by f.id desc limit 1
    </select>

    <select id="selectLabCodeByErpCode" resultType="String" >
        select LABORATORY_CODE from t_un_formula where erp_code = #{erpCode} order by id desc limit 1
    </select>

    <select id="queryMaterialFormulaSpecZxbzDataList" parameterType="java.lang.Long" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            id,
            formula_id formulaId,
            wx_id wxId,
            inspect_basis inspectBasis,
            jl_pl jlPl,
            jl_standard jlStandard,
            mj_standard mjStandard,
            jq_pl jqPl,
            period_Of_Validity periodOfValidity,
            microbe_remark microbeRemark
        FROM
            t_un_material_formula_spec_ffbz
        WHERE
            formula_id = #{id}
          AND is_del = 0
        ORDER BY
            id DESC
        LIMIT 1
    </select>


    <select id="selectRdFormulaList" parameterType="SoftwareDevelopingFormula" resultType="SoftwareDevelopingFormula" >
        select
            id,
            LABORATORY_CODE
        from
            t_un_formula
        where is_del = 0
        <if test="laboratoryCode != null and laboratoryCode != ''" >
            and laboratory_code =#{laboratoryCode}
        </if>
    </select>

</mapper>
