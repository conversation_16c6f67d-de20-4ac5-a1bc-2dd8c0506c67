package com.ruoyi.qc.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 产品准入检查对象 product_audit
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
public class ProductAudit extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 产品ID */
    @Excel(name = "产品ID")
    private String productId;

    /** 产品代码 */
    @Excel(name = "产品代码")
    private String productCode;

    /** 项目编号 */
    @Excel(name = "项目编号")
    private String projectNo;

    /** 实验室编号 */
    @Excel(name = "实验室编号")
    private String laboratoryCode;

    /** 生产企业 */
    @Excel(name = "生产企业")
    private String manufacturer;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String productName;

    /** 规格 */
    @Excel(name = "规格")
    private String spec;

    /** 预计生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预计生产日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date plannedProductionDate;

    /** 订单数量 */
    @Excel(name = "订单数量")
    private Long orderQuantity;

    /** 产品交期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "产品交期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deliveryDate;

    /** 产品类型 */
    @Excel(name = "产品类型")
    private String productType;

    /** 配方稳定性报告状态 */
    @Excel(name = "配方稳定性报告状态")
    private Integer formulaStabilityReport;

    /** 配方稳定性报告风险内容 */
    @Excel(name = "配方稳定性报告风险内容")
    private String formulaStabilityReportHoverTip;

    /** 配制可行性评估状态 */
    @Excel(name = "配制可行性评估状态")
    private Integer formulaFeasibilityAssessment;

    /** 配制可行性评估风险内容 */
    @Excel(name = "配制可行性评估风险内容")
    private String formulaFeasibilityAssessmentHoverTip;

    /** 标准配制工艺单状态 */
    @Excel(name = "标准配制工艺单状态")
    private Integer standardFormulaProcess;

    /** 生产模具治具确认状态 */
    @Excel(name = "生产模具治具确认状态")
    private Integer moldToolConfirmation;

    /** 生产模具治具确认预计时间 */
    @Excel(name = "生产模具治具确认预计时间")
    private String moldToolConfirmationHoverTip;

    /** 灌包可行性评估状态 */
    @Excel(name = "灌包可行性评估状态")
    private Integer fillingPackagingFeasibility;

    /** 灌包可行性评估风险内容 */
    @Excel(name = "灌包可行性评估风险内容")
    private String fillingPackagingFeasibilityHoverTip;

    /** 灌装/包装SOP状态 */
    @Excel(name = "灌装/包装SOP状态")
    private Integer fillingPackagingSop;

    /** 成品检验标准状态 */
    @Excel(name = "成品检验标准状态")
    private Integer finishedProductStandard;

    /** 质量协议状态 */
    @Excel(name = "质量协议状态")
    private Integer qualityAgreement;

    /** 质量协议合同类型是独立还是主框架合同 */
    @Excel(name = "质量协议合同类型是独立还是主框架合同")
    private String qualityAgreementHoverTip;

    /** 包材标准状态 */
    @Excel(name = "包材标准状态")
    private Integer packagingMaterialStandard;

    /** 料体标样状态 */
    @Excel(name = "料体标样状态")
    private Integer liquidSample;

    /** 包材标准样状态 */
    @Excel(name = "包材标准样状态")
    private Integer packagingMaterialSample;

    /** 成品标样状态 */
    @Excel(name = "成品标样状态")
    private Integer finishedProductSample;

    /** 过度包装确认状态 */
    @Excel(name = "过度包装确认状态")
    private Integer excessivePackagingConfirmation;

    /** 过度包装风险内容 */
    @Excel(name = "过度包装风险内容")
    private String excessivePackagingConfirmationHoverTip;

    /** 注册备案是否完成状态 */
    @Excel(name = "注册备案是否完成状态")
    private Integer registrationCompletion;

    /** 备案号 */
    @Excel(name = "备案号")
    private String registrationCompletionHoverTip;

    /** 大货标准配方工艺单与注册/备案一致性状态 */
    @Excel(name = "大货标准配方工艺单与注册/备案一致性状态")
    private Integer formulaProcessConsistency;

    /** 风险内容 */
    @Excel(name = "风险内容")
    private String formulaProcessConsistencyHoverTip;

    /** 大货文案与备案资料一致性状态 */
    @Excel(name = "大货文案与备案资料一致性状态")
    private Integer documentationConsistency;

    /** 风险内容 */
    @Excel(name = "风险内容")
    private String documentationConsistencyHoverTip;

    /** 产品内控标准符合备案执行标准状态 */
    @Excel(name = "产品内控标准符合备案执行标准状态")
    private Integer internalStandardCompliance;

    /** 删除标志（0存在，2删除） */
    private Integer delFlag;

    /** 是否可生产（0=不可，1=可生产） */
    @Excel(name = "是否可生产", readConverterExp = "0==不可，1=可生产")
    private Integer canProduce;

    /** 是否可生产备注 */
    @Excel(name = "是否可生产备注")
    private String canProduceRemark;

    /** 是否可出库（0=不可，1=可出库） */
    @Excel(name = "是否可出库", readConverterExp = "0==不可，1=可出库")
    private Integer canDeliver;

    /** 是否可出库备注 */
    @Excel(name = "是否可出库备注")
    private String canDeliverRemark;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setProductId(String productId)
    {
        this.productId = productId;
    }

    public String getProductId()
    {
        return productId;
    }
    public void setProductCode(String productCode)
    {
        this.productCode = productCode;
    }

    public String getProductCode()
    {
        return productCode;
    }
    public void setLaboratoryCode(String laboratoryCode)
    {
        this.laboratoryCode = laboratoryCode;
    }

    public String getLaboratoryCode()
    {
        return laboratoryCode;
    }
    public void setManufacturer(String manufacturer)
    {
        this.manufacturer = manufacturer;
    }

    public String getManufacturer()
    {
        return manufacturer;
    }
    public void setProductName(String productName)
    {
        this.productName = productName;
    }

    public String getProductName()
    {
        return productName;
    }
    public void setSpec(String spec)
    {
        this.spec = spec;
    }

    public String getSpec()
    {
        return spec;
    }
    public void setPlannedProductionDate(Date plannedProductionDate)
    {
        this.plannedProductionDate = plannedProductionDate;
    }

    public Date getPlannedProductionDate()
    {
        return plannedProductionDate;
    }
    public void setOrderQuantity(Long orderQuantity)
    {
        this.orderQuantity = orderQuantity;
    }

    public Long getOrderQuantity()
    {
        return orderQuantity;
    }
    public void setDeliveryDate(Date deliveryDate)
    {
        this.deliveryDate = deliveryDate;
    }

    public Date getDeliveryDate()
    {
        return deliveryDate;
    }
    public void setProductType(String productType)
    {
        this.productType = productType;
    }

    public String getProductType()
    {
        return productType;
    }
    public void setFormulaStabilityReport(Integer formulaStabilityReport)
    {
        this.formulaStabilityReport = formulaStabilityReport;
    }

    public Integer getFormulaStabilityReport()
    {
        return formulaStabilityReport;
    }
    public void setFormulaStabilityReportHoverTip(String formulaStabilityReportHoverTip)
    {
        this.formulaStabilityReportHoverTip = formulaStabilityReportHoverTip;
    }

    public String getFormulaStabilityReportHoverTip()
    {
        return formulaStabilityReportHoverTip;
    }
    public void setFormulaFeasibilityAssessment(Integer formulaFeasibilityAssessment)
    {
        this.formulaFeasibilityAssessment = formulaFeasibilityAssessment;
    }

    public Integer getFormulaFeasibilityAssessment()
    {
        return formulaFeasibilityAssessment;
    }
    public void setFormulaFeasibilityAssessmentHoverTip(String formulaFeasibilityAssessmentHoverTip)
    {
        this.formulaFeasibilityAssessmentHoverTip = formulaFeasibilityAssessmentHoverTip;
    }

    public String getFormulaFeasibilityAssessmentHoverTip()
    {
        return formulaFeasibilityAssessmentHoverTip;
    }
    public void setStandardFormulaProcess(Integer standardFormulaProcess)
    {
        this.standardFormulaProcess = standardFormulaProcess;
    }

    public Integer getStandardFormulaProcess()
    {
        return standardFormulaProcess;
    }
    public void setMoldToolConfirmation(Integer moldToolConfirmation)
    {
        this.moldToolConfirmation = moldToolConfirmation;
    }

    public Integer getMoldToolConfirmation()
    {
        return moldToolConfirmation;
    }
    public void setMoldToolConfirmationHoverTip(String moldToolConfirmationHoverTip)
    {
        this.moldToolConfirmationHoverTip = moldToolConfirmationHoverTip;
    }

    public String getMoldToolConfirmationHoverTip()
    {
        return moldToolConfirmationHoverTip;
    }
    public void setFillingPackagingFeasibility(Integer fillingPackagingFeasibility)
    {
        this.fillingPackagingFeasibility = fillingPackagingFeasibility;
    }

    public Integer getFillingPackagingFeasibility()
    {
        return fillingPackagingFeasibility;
    }
    public void setFillingPackagingFeasibilityHoverTip(String fillingPackagingFeasibilityHoverTip)
    {
        this.fillingPackagingFeasibilityHoverTip = fillingPackagingFeasibilityHoverTip;
    }

    public String getFillingPackagingFeasibilityHoverTip()
    {
        return fillingPackagingFeasibilityHoverTip;
    }
    public void setFillingPackagingSop(Integer fillingPackagingSop)
    {
        this.fillingPackagingSop = fillingPackagingSop;
    }

    public Integer getFillingPackagingSop()
    {
        return fillingPackagingSop;
    }
    public void setFinishedProductStandard(Integer finishedProductStandard)
    {
        this.finishedProductStandard = finishedProductStandard;
    }

    public Integer getFinishedProductStandard()
    {
        return finishedProductStandard;
    }
    public void setQualityAgreement(Integer qualityAgreement)
    {
        this.qualityAgreement = qualityAgreement;
    }

    public Integer getQualityAgreement()
    {
        return qualityAgreement;
    }
    public void setQualityAgreementHoverTip(String qualityAgreementHoverTip)
    {
        this.qualityAgreementHoverTip = qualityAgreementHoverTip;
    }

    public String getQualityAgreementHoverTip()
    {
        return qualityAgreementHoverTip;
    }
    public void setPackagingMaterialStandard(Integer packagingMaterialStandard)
    {
        this.packagingMaterialStandard = packagingMaterialStandard;
    }

    public Integer getPackagingMaterialStandard()
    {
        return packagingMaterialStandard;
    }
    public void setLiquidSample(Integer liquidSample)
    {
        this.liquidSample = liquidSample;
    }

    public Integer getLiquidSample()
    {
        return liquidSample;
    }
    public void setPackagingMaterialSample(Integer packagingMaterialSample)
    {
        this.packagingMaterialSample = packagingMaterialSample;
    }

    public Integer getPackagingMaterialSample()
    {
        return packagingMaterialSample;
    }
    public void setFinishedProductSample(Integer finishedProductSample)
    {
        this.finishedProductSample = finishedProductSample;
    }

    public Integer getFinishedProductSample()
    {
        return finishedProductSample;
    }
    public void setExcessivePackagingConfirmation(Integer excessivePackagingConfirmation)
    {
        this.excessivePackagingConfirmation = excessivePackagingConfirmation;
    }

    public Integer getExcessivePackagingConfirmation()
    {
        return excessivePackagingConfirmation;
    }
    public void setExcessivePackagingConfirmationHoverTip(String excessivePackagingConfirmationHoverTip)
    {
        this.excessivePackagingConfirmationHoverTip = excessivePackagingConfirmationHoverTip;
    }

    public String getExcessivePackagingConfirmationHoverTip()
    {
        return excessivePackagingConfirmationHoverTip;
    }
    public void setRegistrationCompletion(Integer registrationCompletion)
    {
        this.registrationCompletion = registrationCompletion;
    }

    public Integer getRegistrationCompletion()
    {
        return registrationCompletion;
    }
    public void setRegistrationCompletionHoverTip(String registrationCompletionHoverTip)
    {
        this.registrationCompletionHoverTip = registrationCompletionHoverTip;
    }

    public String getRegistrationCompletionHoverTip()
    {
        return registrationCompletionHoverTip;
    }
    public void setFormulaProcessConsistency(Integer formulaProcessConsistency)
    {
        this.formulaProcessConsistency = formulaProcessConsistency;
    }

    public Integer getFormulaProcessConsistency()
    {
        return formulaProcessConsistency;
    }
    public void setFormulaProcessConsistencyHoverTip(String formulaProcessConsistencyHoverTip)
    {
        this.formulaProcessConsistencyHoverTip = formulaProcessConsistencyHoverTip;
    }

    public String getFormulaProcessConsistencyHoverTip()
    {
        return formulaProcessConsistencyHoverTip;
    }
    public void setDocumentationConsistency(Integer documentationConsistency)
    {
        this.documentationConsistency = documentationConsistency;
    }

    public Integer getDocumentationConsistency()
    {
        return documentationConsistency;
    }
    public void setDocumentationConsistencyHoverTip(String documentationConsistencyHoverTip)
    {
        this.documentationConsistencyHoverTip = documentationConsistencyHoverTip;
    }

    public String getDocumentationConsistencyHoverTip()
    {
        return documentationConsistencyHoverTip;
    }
    public void setInternalStandardCompliance(Integer internalStandardCompliance)
    {
        this.internalStandardCompliance = internalStandardCompliance;
    }

    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    public Integer getInternalStandardCompliance()
    {
        return internalStandardCompliance;
    }
    public void setDelFlag(Integer delFlag)
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag()
    {
        return delFlag;
    }
    public void setCanProduce(Integer canProduce)
    {
        this.canProduce = canProduce;
    }

    public Integer getCanProduce()
    {
        return canProduce;
    }
    public void setCanProduceRemark(String canProduceRemark)
    {
        this.canProduceRemark = canProduceRemark;
    }

    public String getCanProduceRemark()
    {
        return canProduceRemark;
    }
    public void setCanDeliver(Integer canDeliver)
    {
        this.canDeliver = canDeliver;
    }

    public Integer getCanDeliver()
    {
        return canDeliver;
    }
    public void setCanDeliverRemark(String canDeliverRemark)
    {
        this.canDeliverRemark = canDeliverRemark;
    }

    public String getCanDeliverRemark()
    {
        return canDeliverRemark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("productId", getProductId())
                .append("productCode", getProductCode())
                .append("projectNo", getProjectNo())
                .append("laboratoryCode", getLaboratoryCode())
                .append("manufacturer", getManufacturer())
                .append("productName", getProductName())
                .append("spec", getSpec())
                .append("plannedProductionDate", getPlannedProductionDate())
                .append("orderQuantity", getOrderQuantity())
                .append("deliveryDate", getDeliveryDate())
                .append("productType", getProductType())
                .append("formulaStabilityReport", getFormulaStabilityReport())
                .append("formulaStabilityReportHoverTip", getFormulaStabilityReportHoverTip())
                .append("formulaFeasibilityAssessment", getFormulaFeasibilityAssessment())
                .append("formulaFeasibilityAssessmentHoverTip", getFormulaFeasibilityAssessmentHoverTip())
                .append("standardFormulaProcess", getStandardFormulaProcess())
                .append("moldToolConfirmation", getMoldToolConfirmation())
                .append("moldToolConfirmationHoverTip", getMoldToolConfirmationHoverTip())
                .append("fillingPackagingFeasibility", getFillingPackagingFeasibility())
                .append("fillingPackagingFeasibilityHoverTip", getFillingPackagingFeasibilityHoverTip())
                .append("fillingPackagingSop", getFillingPackagingSop())
                .append("finishedProductStandard", getFinishedProductStandard())
                .append("qualityAgreement", getQualityAgreement())
                .append("qualityAgreementHoverTip", getQualityAgreementHoverTip())
                .append("packagingMaterialStandard", getPackagingMaterialStandard())
                .append("liquidSample", getLiquidSample())
                .append("packagingMaterialSample", getPackagingMaterialSample())
                .append("finishedProductSample", getFinishedProductSample())
                .append("excessivePackagingConfirmation", getExcessivePackagingConfirmation())
                .append("excessivePackagingConfirmationHoverTip", getExcessivePackagingConfirmationHoverTip())
                .append("registrationCompletion", getRegistrationCompletion())
                .append("registrationCompletionHoverTip", getRegistrationCompletionHoverTip())
                .append("formulaProcessConsistency", getFormulaProcessConsistency())
                .append("formulaProcessConsistencyHoverTip", getFormulaProcessConsistencyHoverTip())
                .append("documentationConsistency", getDocumentationConsistency())
                .append("documentationConsistencyHoverTip", getDocumentationConsistencyHoverTip())
                .append("internalStandardCompliance", getInternalStandardCompliance())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .append("canProduce", getCanProduce())
                .append("canProduceRemark", getCanProduceRemark())
                .append("canDeliver", getCanDeliver())
                .append("canDeliverRemark", getCanDeliverRemark())
                .toString();
    }
}
