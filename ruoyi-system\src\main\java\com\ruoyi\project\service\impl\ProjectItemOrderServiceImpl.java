package com.ruoyi.project.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.ai.service.FlowTools;
import com.ruoyi.common.constant.CustomerLevel;
import com.ruoyi.common.constant.ProjectConstant;
import com.ruoyi.common.constant.RemindType;
import com.ruoyi.common.constant.UserID;
import com.ruoyi.common.util.ErpDataReqUtil;
import com.ruoyi.common.util.HrCostUtil;
import com.ruoyi.common.utils.*;
import com.ruoyi.customer.domain.Customer;
import com.ruoyi.customer.mapper.TCustomerMapper;
import com.ruoyi.order.domain.OrderStandardLoss;
import com.ruoyi.order.mapper.OrderStandardLossMapper;
import com.ruoyi.product.domain.TCategory;
import com.ruoyi.product.mapper.TCategoryMapper;
import com.ruoyi.product.service.ITCategoryService;
import com.ruoyi.project.domain.*;
import com.ruoyi.project.domain.excel.NrwArrangement;
import com.ruoyi.project.domain.excel.ProjectItemOrderExcel;
import com.ruoyi.project.domain.excel.ProjectSjbaDataExport;
import com.ruoyi.project.mapper.*;
import com.ruoyi.project.service.*;
import com.ruoyi.project.util.ProjectDydUtil;
import com.ruoyi.project.util.ProjectExecutionUtil;
import com.ruoyi.project.util.ProjectOfferUtil;
import com.ruoyi.project.vo.ProjectOfferOrder;
import com.ruoyi.software.mapper.FormulaMapper;
import com.ruoyi.software.mapper.SoftwareDevelopingFormulaMapper;
import com.ruoyi.software.service.IEngineerSampleOrderService;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.work.domain.MessageParams;
import com.ruoyi.work.service.MessageService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 子项目订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-09-23
 */
@Service
public class ProjectItemOrderServiceImpl implements IProjectItemOrderService
{
    @Resource
    private ProjectItemOrderMapper projectItemOrderMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectProductMapper projectProductMapper;

    @Resource
    private IEngineerSampleOrderService engineerSampleOrderService;
    @Resource
    private ProjectItemMapper projectItemMapper;
    @Resource
    private ITProjectExecutionService projectExecutionService;
    @Resource
    private TProjectExecutionMapper projectExecutionMapper;
    @Resource
    private ProjectRemindMapper projectRemindMapper;
    @Resource
    private ISysUserService sysUserService;
    @Resource
    private TCustomerMapper customerMapper;
    @Resource
    private TCategoryMapper categoryMapper;
    @Resource
    private IProjectPriceLogService projectPriceLogService;
    @Resource
    private ProjectPriceLogMapper projectPriceLogMapper;
    @Resource
    private ProjectBcLogMapper bcLogMapper;
    @Resource
    private SysUserMapper userMapper;
    @Resource
    private FormulaMapper formulaMapper;
    @Resource
    private HrCostUtil hrCostUtil;
    @Resource
    private IProjectBcService projectBcService;
    @Resource
    private IProjectProductService productService;
    @Resource
    private BeforeProjectFormulaRequirementsMapper beforeProjectFormulaRequirementsMapper;
    @Resource
    private BeforeProjectFormulaRequirementsOtherMapper beforeProjectFormulaRequirementsOtherMapper;
    @Resource
    private ITProjectExecutionService tProjectExecutionService;
    @Resource
    private OrderStandardLossMapper orderStandardLossMapper;
    @Resource
    private SoftwareDevelopingFormulaMapper softwareDevelopingFormulaMapper;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private ProjectOfferNrwProfitMapper projectOfferNrwProfitMapper;
    @Resource
    private ProjectOfferBcLossMapper projectOfferBcLossMapper;
    @Resource
    private ProjectOfferBcPriceMapper projectOfferBcPriceMapper;
    @Resource
    private FlowTools flowTools;
    @Resource
    private ITCategoryService categoryService;
    @Resource
    private ProjectDydUtil projectDydUtil;
    @Resource
    private IBeforeProjectService beforeProjectService;
    @Resource
    private MessageService messageService;

    @Override
    public List<ProjectItemOrder> selectProjectItemOrderList(ProjectItemOrder projectItemOrder)
    {
        if(!SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            projectItemOrder.setSearchUserId(SecurityUtils.getUserId());
        }
        if(SecurityUtils.isLookAdmin(SecurityUtils.getUserId())){
            projectItemOrder.setSearchUserId(null);
        }
        if(UserID.isSales(SecurityUtils.getUserId())){
            projectItemOrder.setSearchUserId(UserID.LUXIAOPING);
        }
        projectItemOrder.setProjectType(0);
        return projectItemOrderMapper.selectProjectItemOrderList(projectItemOrder);
    }

    @Override
    public List<ProjectItemOrder> selectProjectItemOrderListBySamplePrice(ProjectItemOrder projectItemOrder)
    {
        return projectItemOrderMapper.selectProjectItemOrderListBySamplePrice(projectItemOrder);
    }

    @Override
    public List<ProjectItemOrder> selectProjectItemOrderReleaseList(ProjectItemOrder projectItemOrder)
    {
        return projectItemOrderMapper.selectProjectItemOrderReleaseList(projectItemOrder);
    }


    @Override
    public List<JSONObject> selectProjectItemOrderFeeList(ProjectItemOrder projectItemOrder)
    {
        return projectItemOrderMapper.selectProjectItemOrderFeeList(projectItemOrder);
    }


    @Override
    @Transactional
    public int insertProjectItemOrder(ProjectItemOrder projectItemOrder)
    {
        projectItemOrder.setCreateTime(DateUtils.getNowDate());
        projectItemOrder.setStatus(ProjectConstant.JXZ_STATUS);
        Integer number = projectItemOrderMapper.queryProjectItemOrderCount(projectItemOrder);
        ProjectItem projectItemInfo = projectItemMapper.selectProjectItemById(projectItemOrder.getItemId());
        if(projectItemOrder.getProjectType().intValue()==1 && ProjectConstant.NRW_APPLY.equals(projectItemInfo.getType())){
            Integer count = projectItemOrderMapper.queryProjectItemOrderApplyCount(projectItemOrder);
            String auditUsers =  sysConfigService.selectConfigByKey("BEFORE_PROJECT_NRW_APPLY");
            auditUsers = StringUtils.convertDefaultValue(auditUsers,"1");
            if(count>=Integer.valueOf(auditUsers)){
                return  5;
            }
        }
        //校验必填项
        if(!ProjectConstant.JXZ_STATUS.equals(projectItemInfo.getProjectStatus())){
            return Integer.valueOf(projectItemInfo.getProjectStatus()) + 10;
        }
        if(ProjectConstant.SJBA_APPLY.equals(projectItemInfo.getType())
                || ProjectConstant.BA_APPLY.equals(projectItemInfo.getType())
                || ProjectConstant.PROJECT_MANUSCRIPT_REVIEW.equals(projectItemInfo.getType())
                || ProjectConstant.PROJECT_INSPECTION_AND_FILING.equals(projectItemInfo.getType())){
            Project projectInfo = projectMapper.selectProjectById(projectItemInfo.getProjectId());
            if(StringUtils.isNull(projectInfo.getFactory())){
                return 2; //请选择一级项目备案相关-生产企业
            }
            String filer = projectInfo.getFiler();
            if(StringUtils.isNull(filer)){
                return 3; //请选择一级项目备案相关-备案人
            }
            if ("0".equals(filer) || "2".equals(filer)){
                if(StringUtils.isNull(projectInfo.getGuide())){
                    return 4; //请选择一级项目备案相关-客户引导语
                }
            }
        }
        String dynCode= GenerateNumberUtil.generaterNumber(3,null,number,projectItemInfo.getDynCode());
        String code = GenerateNumberUtil.generaterDynNumber(3,null,number,projectItemInfo.getCode());
        projectItemOrder.setCode(code);
        projectItemOrder.setDynCode(dynCode);
        projectItemOrder.setProjectId(projectItemInfo.getProjectId());
        projectItemInfo.setCustomerId(projectItemInfo.getCustomerId());
        projectItemOrder.setPreItemFields(JSONObject.toJSONString(projectItemInfo));
        int result = projectItemOrderMapper.insertProjectItemOrder(projectItemOrder);

        projectItemOrder.setProjectItemType(projectItemInfo.getType());//新增的时候参数中没有类型,这里补充下
        List<ProjectBc> bcList = projectItemOrder.getBcList();
        if(CollUtil.isNotEmpty(bcList)) {
            projectBcService.saveOrderBcList(projectItemOrder,bcList);
        }

        TProjectExecution tProjectExecution  = new TProjectExecution();
        tProjectExecution.setProjectOrderId(projectItemOrder.getId());
        if(ProjectConstant.OFFER_APPLY.equals(projectItemInfo.getType())){
            String fields = projectItemInfo.getFields();
            if(StringUtils.isNotNull(fields)){
                JSONObject obj = JSONObject.parseObject(fields);
                if(obj!=null && obj.containsKey("bjlx")){
                    String sqlx =  obj.getString("bjlx");
                    if(StringUtils.isNotNull(sqlx)){
                        projectItemInfo.setType(sqlx);
                    }
                }
            }
        }else if(ProjectConstant.PROJECT_BOM_APPLY.equals(projectItemInfo.getType())){
            String fields = projectItemOrder.getFields();
            if(StringUtils.isNotNull(fields)){
                JSONObject obj = JSONObject.parseObject(fields);
                if(obj!=null && obj.containsKey("bgxz")){
                    String bgxz =  obj.getString("bgxz");
                    if(StringUtils.isNotNull(bgxz) && "1".equals(bgxz)){  //BOM变更
                        projectItemInfo.setType(ProjectConstant.PROJECT_BOM_CHANGE);
                    }
                }
            }
        }else if(ProjectConstant.PROJECT_INSPECTION_AND_FILING.equals(projectItemInfo.getType())){
            String fields = projectItemInfo.getFields();
            if(StringUtils.isNotNull(fields)){
                JSONObject obj = JSONObject.parseObject(fields);
                if(obj!=null && obj.containsKey("applyType")){
                    String sqlx =  obj.getString("applyType");
                    if(StringUtils.isNotNull(sqlx) && "2".equals(sqlx)){  //稿件审核
                        projectItemInfo.setType(ProjectConstant.PROJECT_MANUSCRIPT_REVIEW);
                    }else if(StringUtils.isNotNull(sqlx) && "0".equals(sqlx)){  //送检(非备案)
                        projectItemInfo.setType(ProjectConstant.PROJECT_SUBMISSION);
                    }
                }
            }
            {
                ProjectPriceLog projectPriceLog = new ProjectPriceLog();
                projectPriceLog.setProjectId(projectItemInfo.getProjectId());
                projectPriceLog.setCustomerId(projectItemInfo.getCustomerId());
                projectPriceLog.setItemCode(projectItemInfo.getType());
                projectPriceLog.setOrderNo(code);
                String payStyle = "未知";
                String fukuanMoney = "";
                String itemFields = projectItemInfo.getFields();
                if(StringUtils.isNotNull(itemFields)){
                    JSONObject itemObj = JSONObject.parseObject(itemFields);
                    if(itemObj!=null && ("0".equals(itemObj.getString("applyType")) || "4".equals(itemObj.getString("applyType")))){
                        String fields1 = projectItemOrder.getFields();
                        if(StringUtils.isNotNull(fields1)){
                            JSONObject objData = JSONObject.parseObject(fields1);
                            if(objData!=null){
                                JSONObject obj = objData.getJSONObject("songjianFields");
                                if(obj.containsKey("fukuan") && StringUtils.isNotNull(obj.getString("fukuan"))){
                                    String fukuan = obj.getString("fukuan");
                                    String fukuanStyle = DictUtils.getDictLabel("PAY_STYLE",fukuan);
                                    if(StringUtils.isNotNull(fukuanStyle)){
                                        payStyle = fukuanStyle;
                                    }
                                }
                                if(obj.containsKey("fukuanMoney") && StringUtils.isNotNull(obj.getString("fukuanMoney"))){
                                    String money = obj.getString("fukuanMoney");
                                    if(StringUtils.isNotNull(money)){
                                        fukuanMoney = money;
                                    }
                                }
                            }
                        }
                        projectPriceLog.setPayStyle(payStyle);
                        projectPriceLog.setPrice(fukuanMoney);
                        projectPriceLog.setOrderId(projectItemOrder.getId());
                        projectPriceLogService.insertProjectPriceLog(projectPriceLog);
                    }
                }
            }
        }else if(ProjectConstant.PROJECT_GXCSSQ.equals(projectItemInfo.getType())){
            String fields = projectItemInfo.getFields();
            boolean isRecordPrice = false;
            String remark = "";
            if(StringUtils.isNotNull(fields)){
                JSONObject obj = JSONObject.parseObject(fields);
                if(obj!=null && (obj.containsKey("sqlx") || obj.containsKey("typeTreeId"))){
                    String sqlx =  obj.getString("sqlx");
                    long typeTreeId =  obj.getLongValue("typeTreeId");
                    if(1824L == typeTreeId || "0".equals(sqlx) ){
                        isRecordPrice = true;
                        remark = "体外实验";
                        sqlx = "0";
                    } else if(1825L == typeTreeId || 1828L == typeTreeId || "1".equals(sqlx)){
                        isRecordPrice = true;
                        if(1825L == typeTreeId) {
                            remark = "人体功效实验";
                        } else if(1828L == typeTreeId) {
                            remark = "消费者调研";
                        }
                        sqlx = "1";
                    }else if(1826L == typeTreeId ||"2".equals(sqlx)){
                        isRecordPrice = true;
                        remark = "斑贴";
                        sqlx = "2";
                    }else if(1827L == typeTreeId ||"3".equals(sqlx)){
                        isRecordPrice = true;
                        remark = "感官评估";
                        sqlx= "3";
                    }
                    if(StringUtils.isNotNull(sqlx)){
                        projectItemInfo.setType(projectItemInfo.getType()+sqlx);
                    }
                }
            }
            if(StringUtils.isNotNull(remark) && isRecordPrice){
                ProjectPriceLog projectPriceLog = new ProjectPriceLog();
                projectPriceLog.setProjectId(projectItemInfo.getProjectId());
                projectPriceLog.setRemark(remark);
                projectPriceLog.setCustomerId(projectItemInfo.getCustomerId());
                projectPriceLog.setItemCode(projectItemInfo.getType());
                projectPriceLog.setOrderNo(code);
                String payStyle = "未知";
                String fukuanMoney = "";
                String orderFields = projectItemOrder.getFields();
                if(StringUtils.isNotNull(fields)){
                    JSONObject obj = JSONObject.parseObject(orderFields);
                    if(obj.containsKey("fycdf") && StringUtils.isNotNull(obj.getString("fycdf"))){
                        String fukuan = obj.getString("fycdf");
                        String fukuanStyle = DictUtils.getDictLabel("PAY_STYLE",fukuan);
                        if(StringUtils.isNotNull(fukuanStyle)){
                            payStyle = fukuanStyle;
                        }
                    }
                    if(obj.containsKey("money") && StringUtils.isNotNull(obj.getString("money"))){
                        String money = obj.getString("money");
                        if(StringUtils.isNotNull(money)){
                            fukuanMoney = money;
                        }
                    }
                }
                projectPriceLog.setPayStyle(payStyle);
                projectPriceLog.setPrice(fukuanMoney);
                projectPriceLog.setOrderId(projectItemOrder.getId());
                projectPriceLogService.insertProjectPriceLog(projectPriceLog);
            }
        }
        else if(ProjectConstant.RQKF_APPLY.equals(projectItemInfo.getType())){
            if(StringUtils.isNotNull(projectItemInfo.getFields())){
                JSONObject obj = JSONObject.parseObject(projectItemInfo.getFields());
                if(obj!=null && obj.containsKey("bcfl")){
                    String bcfl =  obj.getString("bcfl");
                    if(StringUtils.isNotNull(bcfl)){
                        projectItemInfo.setType(projectItemInfo.getType()+bcfl);
                    }else{
                        projectItemInfo.setType(projectItemInfo.getType()+"ALL");
                    }
                }else{
                    projectItemInfo.setType(projectItemInfo.getType()+"ALL");
                }
            }
            if(StringUtils.isNull(projectItemInfo.getType()) || ProjectConstant.RQKF_APPLY.equals(projectItemInfo.getType())){
                projectItemInfo.setType(projectItemInfo.getType()+"ALL");
            }
            ProjectPriceLog projectPriceLog = new ProjectPriceLog();
            projectPriceLog.setProjectId(projectItemInfo.getProjectId());
            projectPriceLog.setCustomerId(projectItemInfo.getCustomerId());
            projectPriceLog.setItemCode(projectItemInfo.getType());
            projectPriceLog.setOrderNo(code);
            String payStyle = "未知";
            String fukuanMoney = "";
            String orderFields = projectItemOrder.getFields();
            if(StringUtils.isNotNull(orderFields)){
                JSONObject obj = JSONObject.parseObject(orderFields);
                if(obj.containsKey("bcdyf") && StringUtils.isNotNull(obj.getString("bcdyf"))){
                    String fukuan = obj.getString("bcdyf");
                    String fukuanStyle = DictUtils.getDictLabel("PAY_STYLE",fukuan);
                    if(StringUtils.isNotNull(fukuanStyle)){
                        payStyle = fukuanStyle;
                    }
                }
                if(obj.containsKey("money") && StringUtils.isNotNull(obj.getString("money"))){
                    String money = obj.getString("money");
                    if(StringUtils.isNotNull(money)){
                        fukuanMoney = money;
                    }
                }
            }
            projectPriceLog.setPayStyle(payStyle);
            projectPriceLog.setPrice(fukuanMoney);
            projectPriceLog.setOrderId(projectItemOrder.getId());
            projectPriceLogService.insertProjectPriceLog(projectPriceLog);
        }
        else if(ProjectConstant.SJBA_APPLY.equals(projectItemInfo.getType())){
            ProjectPriceLog projectPriceLog = new ProjectPriceLog();
            projectPriceLog.setProjectId(projectItemInfo.getProjectId());
            projectPriceLog.setCustomerId(projectItemInfo.getCustomerId());
            projectPriceLog.setItemCode(projectItemInfo.getType());
            projectPriceLog.setOrderNo(code);
            String payStyle = "未知";
            String fukuanMoney = "";
            String fields = projectItemOrder.getFields();
            if(StringUtils.isNotNull(fields)){
                JSONObject obj = JSONObject.parseObject(fields);
                if(obj.containsKey("fukuan") && StringUtils.isNotNull(obj.getString("fukuan"))){
                    String fukuan = obj.getString("fukuan");
                    String fukuanStyle = DictUtils.getDictLabel("PAY_STYLE",fukuan);
                    if(StringUtils.isNotNull(fukuanStyle)){
                        payStyle = fukuanStyle;
                    }
                }
                if(obj.containsKey("fukuanMoney") && StringUtils.isNotNull(obj.getString("fukuanMoney"))){
                    String money = obj.getString("fukuanMoney");
                    if(StringUtils.isNotNull(money)){
                        fukuanMoney = money;
                    }
                }
            }
            projectPriceLog.setPayStyle(payStyle);
            projectPriceLog.setPrice(fukuanMoney);
            projectPriceLog.setOrderId(projectItemOrder.getId());
            projectPriceLogService.insertProjectPriceLog(projectPriceLog);
        }
        else if(ProjectConstant.SC_APPLY.equals(projectItemInfo.getType())){
            ProjectPriceLog projectPriceLog = new ProjectPriceLog();
            projectPriceLog.setProjectId(projectItemInfo.getProjectId());
            projectPriceLog.setCustomerId(projectItemInfo.getCustomerId());
            projectPriceLog.setItemCode(projectItemInfo.getType());
            projectPriceLog.setOrderNo(code);
            String payStyle = "未知";
            String fukuanMoney = "";
            if(StringUtils.isNotNull(projectItemInfo.getFields())){
                JSONObject obj = JSONObject.parseObject(projectItemInfo.getFields());
                if(obj!=null && obj.containsKey("sqlx")){
                    String sqlx =  obj.getString("sqlx");
                    if(StringUtils.isNotNull(sqlx)){
                        projectItemInfo.setType(projectItemInfo.getType()+sqlx);
                    }
                }
                if(obj.containsKey("fycdf") && StringUtils.isNotNull(obj.getString("fycdf"))){
                    String fukuan = obj.getString("fycdf");
                    String fukuanStyle = DictUtils.getDictLabel("PAY_STYLE",fukuan);
                    if(StringUtils.isNotNull(fukuanStyle)){
                        payStyle = fukuanStyle;
                    }
                }
                if(obj.containsKey("money") && StringUtils.isNotNull(obj.getString("money"))){
                    String money = obj.getString("money");
                    if(StringUtils.isNotNull(money)){
                        fukuanMoney = money;
                    }
                }
            }
            projectPriceLog.setPayStyle(payStyle);
            projectPriceLog.setPrice(fukuanMoney);
            projectPriceLog.setOrderId(projectItemOrder.getId());
            projectPriceLogService.insertProjectPriceLog(projectPriceLog);
        }

        saveBcLog(projectItemOrder,projectItemInfo);//这里只会保存 bom 物料测试 生产可行性里面除配制可行性的记录

        tProjectExecution.setProjectItemCode(projectItemInfo.getType());
        tProjectExecution.setLabNo(projectItemOrder.getLabNo());
        projectExecutionService.insertTProjectExecution(tProjectExecution);

        if(ProjectConstant.NRW_APPLY.equals(projectItemOrder.getProjectItemType())) {
            //领导内容物打样单提醒
            //saveLeaderNrwRemind(projectItemOrder);
            processProjectFormulaRecordData(projectItemOrder,projectItemInfo);
        }else if(ProjectConstant.CHP_BJ_APPLY.equals(projectItemOrder.getProjectItemType())
        ||ProjectConstant.OFFER_APPLY.equals(projectItemOrder.getProjectItemType())
        ||ProjectConstant.RQ_BC_APPLY.equals(projectItemOrder.getProjectItemType())
        ||ProjectConstant.NRW_BJ_APPLY.equals(projectItemOrder.getProjectItemType())){
            new Thread(){
                public void run(){
                    try {
                        Thread.sleep(5000L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    hrCostUtil.generateProjectOfferGrossProfit(code);
                }
            }.start();
        }
        //成品报价
        if(ProjectConstant.CHP_BJ_APPLY.equals(projectItemInfo.getType())){
            new Thread(){
                public void run(){
                    try {
                        Thread.sleep(5000L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    String productTabs = projectItemOrder.getProductTabs();
                    boolean isProcess = false;
                    if(StringUtils.isNotNull(productTabs) && JSONArray.isValidArray(productTabs)){
                        isProcess = hrCostUtil.processPackagingMaterialDatas(productTabs);
                    }else {
                        isProcess = hrCostUtil.processPackagingMaterialDatas(tProjectExecution.getProjectOrderId());
                    }
                    if(isProcess){  //无需包开审核
                            Map<String,Object> params = new HashMap<String,Object>();
                            params.put("itemOrderId",tProjectExecution.getProjectOrderId());
                            params.put("mergeKey","packagingMaterialDevelopment");
                            //获取执行流程操作
                            TProjectExecution execution = projectItemOrderMapper.queryProjectItemOrderExecutionDataInfo(params);
                            if(execution!=null && StringUtils.isNotNull(execution.getId())){
                                //审核确认操作
                                TProjectExecution projectExecution = new TProjectExecution();
                                projectExecution.setId(execution.getId());
                                projectExecution.setProjectOrderId(projectItemOrder.getId());
                                projectExecution.setProjectItemCode(ProjectConstant.CHP_BJ_APPLY);
                                projectExecution.setCurrentStep("复审");
                                projectExecution.setItemType("PROJECT_OFFER");
                                projectExecution.setMergeKey(params.get("mergeKey").toString());
                                projectExecution.setStep(2);
                                projectExecution.setConfirmCode(execution.getItemOrderCode());
                                projectExecution.setStatus(1);
                                projectExecution.setSjTime(DateUtils.getNowDate());
                                projectItemOrder.setYjTime(DateUtils.addDays(DateUtils.getNowDate(),1));
                                projectItemOrder.setFirstYjTime(DateUtils.addDays(DateUtils.getNowDate(),1));
                                tProjectExecutionService.updateTProjectExecution(projectExecution);
                                ProjectItemOrder projectItemOrderParams = new ProjectItemOrder();
                                projectItemOrderParams.setBcReplyTime(DateUtils.getNowDate());
                                projectItemOrderParams.setId(tProjectExecution.getProjectOrderId());
                                projectItemOrderParams.setBcReplyUser("系统自动通过");
                                projectItemOrderMapper.updateProjectItemOrder(projectItemOrderParams);
                            }
                    }
                }
            }.start();
        }

        //同步打样数量
        productService.updateProductDkNums(projectItemInfo.getItemName(),projectItemInfo.getProjectId());
        return result;
    }

    @Override
    @Transactional
    public int insertProjectItemOrderSoftware(ProjectItemOrder projectItemOrder)
    {
        Integer fromType = projectItemOrder.getFromType();
        fromType = StringUtils.convertDefaultValue(fromType,0);
        String labNo = projectItemOrder.getLabNo();  // 0 宜侬 1 瀛彩
        labNo = StringUtils.convertDefaultValue(labNo,"0");
        projectItemOrder.setCreateTime(DateUtils.getNowDate());
        projectItemOrder.setStatus(ProjectConstant.JXZ_STATUS);
        if(fromType.intValue()==0){  //手动添加
            if("0".equals(labNo)){
                projectItemOrder.setItemId(ProjectDydUtil.YN_DY_ITEM_ID);
            }else if("1".equals(labNo)){
                projectItemOrder.setItemId(ProjectDydUtil.YC_DY_ITEM_ID);
            }
        }
        Integer number = projectItemOrderMapper.queryProjectItemOrderCount(projectItemOrder);
        ProjectItem projectItemInfo = projectItemMapper.selectProjectItemById(projectItemOrder.getItemId());
        String dynCode= GenerateNumberUtil.generaterNumber(3,null,number,projectItemInfo.getDynCode());
        String code = GenerateNumberUtil.generaterDynNumber(3,null,number,projectItemInfo.getCode());
        projectItemOrder.setCode(code);
        projectItemOrder.setDynCode(dynCode);
        projectItemOrder.setProjectId(projectItemInfo.getProjectId());
        projectItemInfo.setCustomerId(projectItemInfo.getCustomerId());
        projectItemOrder.setFromType(1);
        projectItemOrder.setCreateBy(SecurityUtils.getUsername());
        projectItemOrder.setQwTime(DateUtils.addDays(DateUtils.getNowDate(),1));
        int result = projectItemOrderMapper.insertProjectItemOrder(projectItemOrder);

        projectItemOrder.setProjectItemType(projectItemInfo.getType());//新增的时候参数中没有类型,这里补充下


        TProjectExecution tProjectExecution  = new TProjectExecution();
        tProjectExecution.setProjectOrderId(projectItemOrder.getId());
        tProjectExecution.setProjectItemCode(projectItemInfo.getType());
        tProjectExecution.setLabNo(projectItemOrder.getLabNo());
        projectExecutionService.insertTProjectExecution(tProjectExecution);

        Long userId = SecurityUtils.getUserId();
        projectItemOrder.setUserId(userId);
        projectItemOrder.setProjectType(2);
        //进入排班系统
        JSONObject returnObj = autoScheduleSampleOrder(projectItemOrder);
        System.out.println("returnObj"+returnObj);
        return result;
    }

    private void saveBcLog(ProjectItemOrder projectItemOrder,ProjectItem projectItemInfo) {
        bcLogMapper.deleteBcLogByOrderId(projectItemOrder.getId());
        String type = projectItemInfo.getType();
        if(ProjectConstant.RQKF_APPLY.equals(type)) {
            if(StringUtils.isNotNull(projectItemOrder.getFields())) {
                JSONObject fields = JSONObject.parseObject(projectItemOrder.getFields());
                JSONArray array = new JSONArray();
                List<String> keys = new ArrayList<>((Arrays.asList("baocaiDatasNBC","baocaiDatasHUAHE",
                        "baocaiDatasZHONGBAO","baocaiDatasWAIXIANG","baocaiDatasLIHE","baocaiDatasBIAOQIAN",
                        "baocaiDatasSHUOMINGSHU","baocaiDatasTIANCHONGCAILIAO","baocaiDatasFUZHUGONGJU","baocaiDatas")));
                for(String key: keys) {
                    if(fields.containsKey(key)) {
                        JSONArray arr = fields.getJSONArray(key);
                        if(arr != null && arr.size() > 0) {
                            array.addAll(arr);
                        }
                    }
                }
                List<ProjectBcLog> bcLogList = new ArrayList<>();
                for(int i = 0;i<array.size();i++) {
                    JSONObject o = array.getJSONObject(i);
                    if(o.containsKey("projectBcId")) {
                        ProjectBcLog bcLog = new ProjectBcLog();
                        bcLog.setProjectItemOrderId(projectItemOrder.getId());
                        bcLog.setProjectBcId(o.getLong("projectBcId"));
                        bcLog.setCreateBy(SecurityUtils.getUsername());
                        bcLog.setType("5");
                        bcLog.setProjectId(projectItemOrder.getProjectId());
                        bcLog.setProjectItemOrderCode(projectItemOrder.getCode());
                        bcLog.setBussinessKey(o.getString("key"));
                        bcLogList.add(bcLog);
                    }
                }
                if(bcLogList.size() > 0) {
                    bcLogMapper.insertBatchBcLog(bcLogList);
                }
            }
        }
        else if(type.startsWith(ProjectConstant.SC_APPLY)) {
            if(StringUtils.isNotEmpty(projectItemInfo.getFields())) {
                JSONObject itemFields = JSONObject.parseObject(projectItemInfo.getFields());
                if(itemFields.containsKey("sqlx")) {
                    String sqlx = itemFields.getString("sqlx");
                    if(StringUtils.isNotNull(projectItemOrder.getFields())) {
                        JSONObject fields = JSONObject.parseObject(projectItemOrder.getFields());
                        if(fields.containsKey("sckxxbgData")) {
                            JSONArray sckxxbgData = fields.getJSONArray("sckxxbgData");
                            if(sckxxbgData != null && sckxxbgData.size() > 0) {
                                List<ProjectBcLog> bcLogList = new ArrayList<>();
                                for(int i = 0;i<sckxxbgData.size();i++) {
                                    JSONObject o = sckxxbgData.getJSONObject(i);
                                    if(o.containsKey("projectBcId")) {
                                        ProjectBcLog bcLog = new ProjectBcLog();
                                        bcLog.setProjectItemOrderId(projectItemOrder.getId());
                                        bcLog.setProjectBcId(o.getLong("projectBcId"));
                                        bcLog.setCreateBy(SecurityUtils.getUsername());
                                        if("1".equals(sqlx)) { //灌装可行性
                                            bcLog.setType("2");
                                        } else if("2".equals(sqlx)) { //包装可行性
                                            bcLog.setType("3");
                                        } else if("3".equals(sqlx)) { //工位图
                                            bcLog.setType("4");
                                        }
                                        bcLog.setProjectId(projectItemOrder.getProjectId());
                                        bcLog.setProjectItemOrderCode(projectItemOrder.getCode());
                                        bcLog.setBussinessKey(o.getString("key"));
                                        bcLogList.add(bcLog);
                                    }
                                }
                                if(bcLogList.size() > 0) {
                                    bcLogMapper.insertBatchBcLog(bcLogList);
                                }
                            }
                        }
                    }
                }
            }
        }
        else if(ProjectConstant.CLCS_APPLY.equals(projectItemInfo.getType())) { // 物料测试
            if(StringUtils.isNotNull(projectItemOrder.getFields())) {
                JSONObject fields = JSONObject.parseObject(projectItemOrder.getFields());
                if(fields.containsKey("itemDataInfos")) {
                    JSONArray itemDataInfos = fields.getJSONArray("itemDataInfos");
                    if(itemDataInfos != null && itemDataInfos.size() > 0) {
                        List<ProjectBcLog> bcLogList = new ArrayList<>();
                        for(int i = 0;i<itemDataInfos.size();i++) {
                            JSONObject o = itemDataInfos.getJSONObject(i);
                            if(o.containsKey("projectBcId")) {
                                ProjectBcLog bcLog = new ProjectBcLog();
                                bcLog.setProjectItemOrderId(projectItemOrder.getId());
                                bcLog.setProjectBcId(o.getLong("projectBcId"));
                                bcLog.setCreateBy(SecurityUtils.getUsername());
                                bcLog.setType("0");
                                bcLog.setProjectId(projectItemOrder.getProjectId());
                                bcLog.setProjectItemOrderCode(projectItemOrder.getCode());
                                bcLog.setNums(o.getBigDecimal("bcsl"));
                                bcLog.setBussinessKey(o.getString("key"));
                                bcLogList.add(bcLog);
                            }
                        }
                        if(bcLogList.size() > 0) {
                            bcLogMapper.insertBatchBcLog(bcLogList);
                        }
                    }
                }
            }
        }
        else if(ProjectConstant.PROJECT_BOM_APPLY.equals(projectItemInfo.getType())) { // BOM
            if(StringUtils.isNotNull(projectItemOrder.getFields())) {
                JSONObject fields = JSONObject.parseObject(projectItemOrder.getFields());
                if(fields.containsKey("pfbgData")) {
                    JSONArray pfbgData = fields.getJSONArray("pfbgData");
                    if(pfbgData != null && pfbgData.size() > 0) {
                        List<ProjectBcLog> bcLogList = new ArrayList<>();
                        for(int i = 0;i<pfbgData.size();i++) {
                            JSONObject o = pfbgData.getJSONObject(i);
                            if(o.containsKey("projectBcId")) {
                                ProjectBcLog bcLog = new ProjectBcLog();
                                bcLog.setProjectItemOrderId(projectItemOrder.getId());
                                bcLog.setProjectBcId(o.getLong("projectBcId"));
                                bcLog.setCreateBy(SecurityUtils.getUsername());
                                bcLog.setType("1");
                                bcLog.setProjectId(projectItemOrder.getProjectId());
                                bcLog.setProjectItemOrderCode(projectItemOrder.getCode());
                                bcLog.setBussinessKey(o.getString("key"));
                                bcLogList.add(bcLog);
                            }
                        }
                        if(bcLogList.size() > 0) {
                            bcLogMapper.insertBatchBcLog(bcLogList);
                        }
                    }
                }
            }
        }
    }

    /**
     * 20250319 取消使用
     * @param projectItemOrder
     */
    private void saveLeaderNrwRemind(ProjectItemOrder projectItemOrder) {
        Customer customer = customerMapper.selectTCustomerById(projectItemOrder.getCustomerId());
        Integer count = projectItemOrderMapper.selectNrwCount(projectItemOrder.getItemId());

        ProjectRemind remind = new ProjectRemind();
        remind.setProjectType(ProjectConstant.NRW_APPLY);
        remind.setCustomerName(customer.getName());
        remind.setProductName(projectItemOrder.getProductName());
        remind.setType(RemindType.DY);
        remind.setProjectItemOrderId(projectItemOrder.getId());
        remind.setCreateTime(new Date());

        JSONObject msg = new JSONObject();
        msg.put("customerName",customer.getName());
        msg.put("code",projectItemOrder.getCode());//三级项目编号
        msg.put("productName",projectItemOrder.getProductName());
        msg.put("projectId",projectItemOrder.getProjectId());
        msg.put("count",count);

        projectRemindMapper.insertProjectRemind(remind);

        ProjectRemindUser remindUser = new ProjectRemindUser();
        remindUser.setProjectRemindId(remind.getId());
        Set<Long> sendUserIds = new HashSet<>();
        List<Long> leaderIds = sysUserService.queryUserLeaderAndCustomerAssistDataInfo(remind.getUserId(),customer.getId());

        if(customer.getCustomerLevel() != null) {
            if(customer.getCustomerLevel().equals(CustomerLevel.S) || customer.getCustomerLevel().equals(CustomerLevel.A)) {
                if(count > 2 ) { //苗总
                    sendUserIds.add(UserID.MIAO);
//                sendUserIds.add(UserID.YAN);
                } else if(count == 1) {//王总
                    sendUserIds.add(UserID.WANG);
                } else if(count == 0) { //研发总监
                    sendUserIds.addAll(leaderIds);
                    if(sendUserIds.contains(UserID.MIAO)) {
                        sendUserIds.remove(UserID.MIAO);
//                    sendUserIds.remove(UserID.YAN);
                    }
                    if(sendUserIds.contains(UserID.WANG)) {
                        sendUserIds.remove(UserID.WANG);
                    }
                }
            } else if(customer.getCustomerLevel().equals(CustomerLevel.B)) {
                if(count > 2 ) { //王总
                    sendUserIds.add(UserID.WANG);
                } else {
                    sendUserIds.addAll(leaderIds);
                    if(sendUserIds.contains(UserID.MIAO)) {
                        sendUserIds.remove(UserID.MIAO);
//                    sendUserIds.remove(UserID.YAN);
                    }
                    if(sendUserIds.contains(UserID.WANG)) {
                        sendUserIds.remove(UserID.WANG);
                    }
                }
//          if(count == 1) {//研发总监
//
//            } else if(count == 0) { //研发经理
//
//            }
            } else if(customer.getCustomerLevel().equals(CustomerLevel.C)) {
                sendUserIds.addAll(leaderIds);
                if(sendUserIds.contains(UserID.MIAO)) {
                    sendUserIds.remove(UserID.MIAO);
                }
                if(sendUserIds.contains(UserID.WANG)) {
                    sendUserIds.remove(UserID.WANG);
                }
                if(count > 2 ) { //研发总监

                } else if(count == 1) {//研发经理

                } else if(count == 0) { //研发主管

                }
            }
        }

        if(sendUserIds!=null && sendUserIds.size()>0){
            projectRemindMapper.batchRemindUser(remind.getId(),remind.getProjectType(),new ArrayList<>(sendUserIds));
        }
    }

    private void processProjectFormulaRecordData(ProjectItemOrder projectItemOrder,ProjectItem projectItemInfo){
        Integer projectType = projectItemOrder.getProjectType();
        projectType = StringUtils.convertDefaultValue(projectType,0);
        Long dataId = null;
        Integer isEdit = projectItemOrder.getIsEdit();
        String fields = projectItemOrder.getFields();
        if(StringUtils.isNotNull(fields)){
            JSONObject fieldsObj = JSONObject.parseObject(fields);
            if(fieldsObj!=null && fieldsObj.containsKey("nrwObj")){
                JSONObject nrwObj = fieldsObj.getJSONObject("nrwObj");
                if(nrwObj!=null && nrwObj.containsKey("pfPrice")){
                    String pfPrice = nrwObj.getString("pfPrice");
                    if (StringUtils.isNotNull(pfPrice)) {
                        Map<String,Object> params = new HashMap<String,Object>();
                        boolean isUpdate = false;
                        if(projectType==1){
                            Long projectProductId = nrwObj.getLong("projectProductId");
                            if(StringUtils.isNotNull(projectProductId)){
                                ProjectProduct beforeProjectProduct = projectProductMapper.selectProjectProductById(projectProductId);
                                String oldPfPrice = beforeProjectProduct!=null ? beforeProjectProduct.getPfPrice() : "";
                                if(!pfPrice.equals(oldPfPrice)){
                                    isUpdate = true;
                                    params.put("projectType",projectType);
                                    params.put("dataId",projectProductId);
                                    params.put("oldPrice",oldPfPrice);
                                    params.put("newPrice",pfPrice);
                                    params.put("createBy",SecurityUtils.getUsername());


                                    ProjectProduct p1 = new ProjectProduct();
                                    p1.setId(projectProductId);
                                    p1.setPfPrice(pfPrice);
                                    projectProductMapper.updateProjectProduct(p1);
                                }
                            }
                        }else{
                            Long projectProductId = nrwObj.getLong("projectProductId");
                            if(StringUtils.isNotNull(projectProductId)) {
                                ProjectProduct beforeProjectProduct = projectProductMapper.selectProjectProductById(projectProductId);
                                String oldPfPrice = beforeProjectProduct.getPfPrice();
                                if(!pfPrice.equals(oldPfPrice)){
                                    isUpdate = true;
                                    params.put("projectType",projectType);
                                    params.put("dataId",projectProductId);
                                    params.put("oldPrice",oldPfPrice);
                                    params.put("newPrice",pfPrice);
                                    params.put("createBy",SecurityUtils.getUsername());
                                    ProjectProduct p1 = new ProjectProduct();
                                    p1.setId(projectProductId);
                                    p1.setPfPrice(pfPrice);
                                    projectProductMapper.updateProjectProduct(p1);
                                }
                            }
                        }
                        if(isUpdate){
                            projectMapper.insertProjectFormulaPriceChange(params);
                        }
                    }
                }
                if(StringUtils.isNotNull(isEdit) && isEdit.intValue()==1){
                    if(nrwObj!=null && nrwObj.containsKey("projectFormulaRequirementsDatas")){
                        JSONObject formulaRequirementsObj = nrwObj.getJSONObject("projectFormulaRequirementsDatas");
                        BeforeProjectFormulaRequirements beforeProjectFormulaRequirements = new BeforeProjectFormulaRequirements();
                        beforeProjectFormulaRequirements = JSONObject.parseObject(formulaRequirementsObj.toJSONString(), BeforeProjectFormulaRequirements.class);

                        beforeProjectFormulaRequirements.setProjectId(projectItemOrder.getProjectId());

                        if(StringUtils.isNotNull(beforeProjectFormulaRequirements.getId())){
                            BeforeProjectFormulaRequirements params1 = new BeforeProjectFormulaRequirements();
                            params1.setDelFlag(2);
                            params1.setId(beforeProjectFormulaRequirements.getId());
                            beforeProjectFormulaRequirementsMapper.updateBeforeProjectFormulaRequirements(params1);


                            beforeProjectFormulaRequirements.setCreateBy(SecurityUtils.getUsername());
                            beforeProjectFormulaRequirements.setCreateTime(DateUtils.getNowDate());
                            beforeProjectFormulaRequirementsMapper.insertBeforeProjectFormulaRequirements(beforeProjectFormulaRequirements);

                        }else{
                            beforeProjectFormulaRequirements.setCreateBy(SecurityUtils.getUsername());
                            beforeProjectFormulaRequirements.setCreateTime(DateUtils.getNowDate());
                            beforeProjectFormulaRequirementsMapper.insertBeforeProjectFormulaRequirements(beforeProjectFormulaRequirements);
                        }

                        String projectFormulaRequirementsOtherDatas= beforeProjectFormulaRequirements.getProjectFormulaRequirementsOtherDatas();
                        if(StringUtils.isNotNull(projectFormulaRequirementsOtherDatas)){
                            JSONArray projectFormulaRequirementOtherArray = JSONArray.parseArray(projectFormulaRequirementsOtherDatas);
                            if(projectFormulaRequirementOtherArray!=null && projectFormulaRequirementOtherArray.size()>0){
                                for(int j =0;j<projectFormulaRequirementOtherArray.size();j++){
                                    JSONObject projectFormulaRequirementOtherObj = projectFormulaRequirementOtherArray.getJSONObject(j);
                                    BeforeProjectFormulaRequirementsOther beforeProjectFormulaRequirementsOther = JSONObject.parseObject(projectFormulaRequirementOtherObj.toJSONString(),BeforeProjectFormulaRequirementsOther.class);
                                    beforeProjectFormulaRequirementsOther.setProjectId(projectItemOrder.getProjectId());
                                    beforeProjectFormulaRequirementsOther.setFormulaRequirementId(beforeProjectFormulaRequirements.getId());
                                    if(StringUtils.isNotNull(beforeProjectFormulaRequirementsOther.getId())){
                                        if(StringUtils.isNotNull(isEdit) && isEdit.intValue()==1){
                                            BeforeProjectFormulaRequirementsOther params = new BeforeProjectFormulaRequirementsOther();
                                            params.setDelFlag(2);
                                            params.setId(beforeProjectFormulaRequirementsOther.getId());
                                            beforeProjectFormulaRequirementsOtherMapper.updateBeforeProjectFormulaRequirementsOther(params);

                                            beforeProjectFormulaRequirementsOther.setCreateBy(SecurityUtils.getUsername());
                                            beforeProjectFormulaRequirementsOther.setCreateTime(DateUtils.getNowDate());
                                            beforeProjectFormulaRequirementsOtherMapper.insertBeforeProjectFormulaRequirementsOther(beforeProjectFormulaRequirementsOther);

                                        }
                                    }else{
                                        beforeProjectFormulaRequirementsOther.setCreateBy(SecurityUtils.getUsername());
                                        beforeProjectFormulaRequirementsOther.setCreateTime(DateUtils.getNowDate());
                                        beforeProjectFormulaRequirementsOtherMapper.insertBeforeProjectFormulaRequirementsOther(beforeProjectFormulaRequirementsOther);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    /**
     * 修改子项目订单
     *
     * @param projectItemOrder 子项目订单
     * @return 结果
     */
    @Override
    public int updateProjectItemOrder(ProjectItemOrder projectItemOrder)
    {
        projectItemOrder.setUpdateTime(DateUtils.getNowDate());
        projectItemOrder.setShStatus(0);
        projectItemOrder.setShStatus1(0);
        projectItemOrder.setAmendments("");
        projectItemOrder.setAmendments1("");
        projectItemOrder.setIsRevoke(0);
        projectItemOrder.setStatus(ProjectConstant.JXZ_STATUS);
        projectItemOrder.setCurrentStep("初审");
        projectItemOrder.setIsSync(0);
        projectItemOrder.setMergeFields("revokeOpr");
        int result = projectItemOrderMapper.updateProjectItemOrder(projectItemOrder);

        List<ProjectBc> bcList = projectItemOrder.getBcList();
        if(CollUtil.isNotEmpty(bcList)) {
            projectBcService.saveOrderBcList(projectItemOrder,bcList);
        }

        //更新信息 流程重新走一遍
        //projectExecutionMapper.deleteProjectExecutionUserInfo(projectItemOrder.getId());
        //projectExecutionMapper.deleteProjectExecutionInfo(projectItemOrder.getId());
        ProjectItem projectItemInfo = projectItemMapper.selectProjectItemById(projectItemOrder.getItemId());
        TProjectExecution tProjectExecution  = new TProjectExecution();
        tProjectExecution.setProjectOrderId(projectItemOrder.getId());
        if(ProjectConstant.OFFER_APPLY.equals(projectItemInfo.getType())){
            String fields = projectItemInfo.getFields();
            if(StringUtils.isNotNull(fields)){
                JSONObject obj = JSONObject.parseObject(fields);
                if(obj!=null && obj.containsKey("bjlx")){
                    String bjlx =  obj.getString("bjlx");
                    if(StringUtils.isNotNull(bjlx)){
                        projectItemInfo.setType(bjlx);
                    }
                }
            }
        }else if(ProjectConstant.PROJECT_BOM_APPLY.equals(projectItemInfo.getType())){
            String fields = projectItemOrder.getFields();
            if(StringUtils.isNotNull(fields)){
                JSONObject obj = JSONObject.parseObject(fields);
                if(obj!=null && obj.containsKey("bgxz")){
                    String bgxz =  obj.getString("bgxz");
                    if(StringUtils.isNotNull(bgxz) && "1".equals(bgxz)){  //BOM变更
                        projectItemInfo.setType(ProjectConstant.PROJECT_BOM_CHANGE);
                    }
                }
            }
        }else if(ProjectConstant.PROJECT_INSPECTION_AND_FILING.equals(projectItemInfo.getType())){
            String fields = projectItemInfo.getFields();
            if(StringUtils.isNotNull(fields)){
                JSONObject obj = JSONObject.parseObject(fields);
                if(obj!=null && obj.containsKey("applyType")){
                    String sqlx =  obj.getString("applyType");
                    if(StringUtils.isNotNull(sqlx) && "2".equals(sqlx)){  //稿件审核
                        projectItemInfo.setType(ProjectConstant.PROJECT_MANUSCRIPT_REVIEW);
                    }else if(StringUtils.isNotNull(sqlx) && "0".equals(sqlx)){ //送检(非备案)
                        projectItemInfo.setType(ProjectConstant.PROJECT_SUBMISSION);
                    }
                }
            }
            {
                ProjectPriceLog paramProjectPriceLog = new ProjectPriceLog();
                paramProjectPriceLog.setOrderId(projectItemOrder.getId());
                paramProjectPriceLog.setItemCode(projectItemInfo.getType());
                List<ProjectPriceLog> projectPriceLogList = projectPriceLogMapper.selectProjectPriceLogList(paramProjectPriceLog);
                if(projectPriceLogList!=null && projectPriceLogList.size()>0){
                    ProjectPriceLog projectPriceLog = new ProjectPriceLog();
                    projectPriceLog.setId(projectPriceLogList.get(0).getId());
                    String payStyle = "未知";
                    String fukuanMoney = "";
                    String itemFields = projectItemInfo.getFields();
                    if(StringUtils.isNotNull(itemFields)){
                        JSONObject itemObj = JSONObject.parseObject(itemFields);
                        if(itemObj!=null && ("0".equals(itemObj.getString("applyType")) || "4".equals(itemObj.getString("applyType")))){
                            String fields1 = projectItemOrder.getFields();
                            if(StringUtils.isNotNull(fields1)){
                                JSONObject objData = JSONObject.parseObject(fields1);
                                if(objData!=null){
                                    JSONObject obj = objData.getJSONObject("songjianFields");
                                    if(obj.containsKey("fukuan") && StringUtils.isNotNull(obj.getString("fukuan"))){
                                        String fukuan = obj.getString("fukuan");
                                        String fukuanStyle = DictUtils.getDictLabel("PAY_STYLE",fukuan);
                                        if(StringUtils.isNotNull(fukuanStyle)){
                                            payStyle = fukuanStyle;
                                        }
                                    }
                                    if(obj.containsKey("fukuanMoney") && StringUtils.isNotNull(obj.getString("fukuanMoney"))){
                                        String money = obj.getString("fukuanMoney");
                                        if(StringUtils.isNotNull(money)){
                                            fukuanMoney = money;
                                        }
                                    }
                                }
                            }
                            projectPriceLog.setPayStyle(payStyle);
                            projectPriceLog.setPrice(fukuanMoney);
                            projectPriceLog.setOrderId(projectItemOrder.getId());
                            projectPriceLogService.updateProjectPriceLog(projectPriceLog);
                        }
                    }
                }
            }
        }
        else if(ProjectConstant.PROJECT_GXCSSQ.equals(projectItemInfo.getType())){
            String fields = projectItemInfo.getFields();
            if(StringUtils.isNotNull(fields)){
                JSONObject obj = JSONObject.parseObject(fields);
                if(obj!=null && (obj.containsKey("sqlx") || obj.containsKey("typeTreeId"))){
                    String sqlx =  obj.getString("sqlx");
                    long typeTreeId =  obj.getLongValue("typeTreeId");
                    if(1824L == typeTreeId || "0".equals(sqlx) ){
                        sqlx = "0";
                    } else if(1825L == typeTreeId || 1828L == typeTreeId || "1".equals(sqlx)){
                        sqlx = "1";
                    }else if(1826L == typeTreeId ||"2".equals(sqlx)){
                        sqlx = "2";
                    }else if(1827L == typeTreeId ||"3".equals(sqlx)){
                        sqlx= "3";
                    }
                    if(StringUtils.isNotNull(sqlx)){
                        projectItemInfo.setType(projectItemInfo.getType()+sqlx);
                    }
                }
            }
            ProjectPriceLog paramProjectPriceLog = new ProjectPriceLog();
            paramProjectPriceLog.setOrderId(projectItemOrder.getId());
            paramProjectPriceLog.setItemCode(projectItemInfo.getType());
            List<ProjectPriceLog> projectPriceLogList = projectPriceLogMapper.selectProjectPriceLogList(paramProjectPriceLog);
            if(projectPriceLogList!=null && projectPriceLogList.size()>0){
                ProjectPriceLog projectPriceLog = new ProjectPriceLog();
                projectPriceLog.setId(projectPriceLogList.get(0).getId());
                String payStyle = "未知";
                String fukuanMoney = "";
                String orderFields = projectItemOrder.getFields();
                if(StringUtils.isNotNull(orderFields)){
                    JSONObject obj = JSONObject.parseObject(orderFields);
                    if(obj.containsKey("fycdf") && StringUtils.isNotNull(obj.getString("fycdf"))){
                        String fukuan = obj.getString("fycdf");
                        String fukuanStyle = DictUtils.getDictLabel("PAY_STYLE",fukuan);
                        if(StringUtils.isNotNull(fukuanStyle)){
                            payStyle = fukuanStyle;
                        }
                    }
                    if(obj.containsKey("money") && StringUtils.isNotNull(obj.getString("money"))){
                        String money = obj.getString("money");
                        if(StringUtils.isNotNull(money)){
                            fukuanMoney = money;
                        }
                    }
                }
                projectPriceLog.setPayStyle(payStyle);
                projectPriceLog.setPrice(fukuanMoney);
                projectPriceLogService.updateProjectPriceLog(projectPriceLog);
            }
        }
        else if(ProjectConstant.RQKF_APPLY.equals(projectItemInfo.getType())){
            String fields = projectItemInfo.getFields();
            if(StringUtils.isNotNull(fields)){
                JSONObject obj = JSONObject.parseObject(fields);
                if(obj!=null && obj.containsKey("bcfl")){
                    String bcfl =  obj.getString("bcfl");
                    if(StringUtils.isNotNull(bcfl)){
                        projectItemInfo.setType(projectItemInfo.getType()+bcfl);
                    }else{
                        projectItemInfo.setType(projectItemInfo.getType()+"ALL");
                    }
                }
            }else{
                projectItemInfo.setType(projectItemInfo.getType()+"ALL");
            }
            if(StringUtils.isNull(projectItemInfo.getType()) || ProjectConstant.RQKF_APPLY.equals(projectItemInfo.getType())){
                projectItemInfo.setType(projectItemInfo.getType()+"ALL");
            }
            ProjectPriceLog paramProjectPriceLog = new ProjectPriceLog();
            paramProjectPriceLog.setOrderId(projectItemOrder.getId());
            paramProjectPriceLog.setItemCode(projectItemInfo.getType());
            List<ProjectPriceLog> projectPriceLogList = projectPriceLogMapper.selectProjectPriceLogList(paramProjectPriceLog);
            if(projectPriceLogList!=null && projectPriceLogList.size()>0){
                ProjectPriceLog projectPriceLog = new ProjectPriceLog();
                projectPriceLog.setId(projectPriceLogList.get(0).getId());
                String payStyle = "未知";
                String fukuanMoney = "";
                String orderFields = projectItemOrder.getFields();
                if(StringUtils.isNotNull(orderFields)){
                    JSONObject obj = JSONObject.parseObject(orderFields);
                    if(obj.containsKey("bcdyf") && StringUtils.isNotNull(obj.getString("bcdyf"))){
                        String fukuan = obj.getString("bcdyf");
                        String fukuanStyle = DictUtils.getDictLabel("PAY_STYLE",fukuan);
                        if(StringUtils.isNotNull(fukuanStyle)){
                            payStyle = fukuanStyle;
                        }
                    }
                    if(obj.containsKey("money") && StringUtils.isNotNull(obj.getString("money"))){
                        String money = obj.getString("money");
                        if(StringUtils.isNotNull(money)){
                            fukuanMoney = money;
                        }
                    }
                }
                projectPriceLog.setPayStyle(payStyle);
                projectPriceLog.setPrice(fukuanMoney);
                projectPriceLogService.updateProjectPriceLog(projectPriceLog);
            }
        }
        else if(ProjectConstant.SJBA_APPLY.equals(projectItemInfo.getType())){
            ProjectPriceLog paramProjectPriceLog = new ProjectPriceLog();
            paramProjectPriceLog.setOrderId(projectItemOrder.getId());
            paramProjectPriceLog.setItemCode(projectItemInfo.getType());
            List<ProjectPriceLog> projectPriceLogList = projectPriceLogMapper.selectProjectPriceLogList(paramProjectPriceLog);
            if(projectPriceLogList!=null && projectPriceLogList.size()>0){
                ProjectPriceLog projectPriceLog = new ProjectPriceLog();
                projectPriceLog.setId(projectPriceLogList.get(0).getId());
                String payStyle = "未知";
                String fukuanMoney = "";
                String fields = projectItemOrder.getFields();
                if(StringUtils.isNotNull(fields)){
                    JSONObject obj = JSONObject.parseObject(fields);
                    if(obj.containsKey("fukuan") && StringUtils.isNotNull(obj.getString("fukuan"))){
                        String fukuan = obj.getString("fukuan");
                        String fukuanStyle = DictUtils.getDictLabel("PAY_STYLE",fukuan);
                        if(StringUtils.isNotNull(fukuanStyle)){
                            payStyle = fukuanStyle;
                        }
                    }
                    if(obj.containsKey("fukuanMoney") && StringUtils.isNotNull(obj.getString("fukuanMoney"))){
                        String money = obj.getString("fukuanMoney");
                        if(StringUtils.isNotNull(money)){
                            fukuanMoney = money;
                        }
                    }
                }
                projectPriceLog.setPayStyle(payStyle);
                projectPriceLog.setPrice(fukuanMoney);
                projectPriceLogService.updateProjectPriceLog(projectPriceLog);
            }
        }
        else if(ProjectConstant.SC_APPLY.equals(projectItemInfo.getType())){
            String fields1 = projectItemInfo.getFields();
            if(StringUtils.isNotNull(fields1)){
                JSONObject obj = JSONObject.parseObject(fields1);
                if(obj!=null && obj.containsKey("sqlx")){
                    String sqlx =  obj.getString("sqlx");
                    if(StringUtils.isNotNull(sqlx)){
                        projectItemInfo.setType(projectItemInfo.getType()+sqlx);
                    }
                }
            }

            ProjectPriceLog paramProjectPriceLog = new ProjectPriceLog();
            paramProjectPriceLog.setOrderId(projectItemOrder.getId());
            paramProjectPriceLog.setItemCode(projectItemInfo.getType());
            List<ProjectPriceLog> projectPriceLogList = projectPriceLogMapper.selectProjectPriceLogList(paramProjectPriceLog);
            if(projectPriceLogList!=null && projectPriceLogList.size()>0){
                ProjectPriceLog projectPriceLog = new ProjectPriceLog();
                projectPriceLog.setId(projectPriceLogList.get(0).getId());
                String payStyle = "未知";
                String fukuanMoney = "";
                String fields = projectItemOrder.getFields();
                if(StringUtils.isNotNull(fields)){
                    JSONObject obj = JSONObject.parseObject(fields);
                    if(obj.containsKey("fycdf") && StringUtils.isNotNull(obj.getString("fycdf"))){
                        String fukuan = obj.getString("fycdf");
                        String fukuanStyle = DictUtils.getDictLabel("PAY_STYLE",fukuan);
                        if(StringUtils.isNotNull(fukuanStyle)){
                            payStyle = fukuanStyle;
                        }
                    }
                    if(obj.containsKey("money") && StringUtils.isNotNull(obj.getString("money"))){
                        String money = obj.getString("money");
                        if(StringUtils.isNotNull(money)){
                            fukuanMoney = money;
                        }
                    }
                }
                projectPriceLog.setPayStyle(payStyle);
                projectPriceLog.setPrice(fukuanMoney);
                projectPriceLogService.updateProjectPriceLog(projectPriceLog);
            }
        }

        ProjectBcLog bcLog = new ProjectBcLog();
        bcLog.setProjectItemOrderId(projectItemOrder.getId());
        String type = projectItemInfo.getType();
        if(ProjectConstant.SC_APPLY.equals(type)) {
            if(StringUtils.isNotEmpty(projectItemInfo.getFields())) {
                JSONObject itemFields = JSONObject.parseObject(projectItemInfo.getFields());
                if (itemFields.containsKey("sqlx")) {
                    String sqlx = itemFields.getString("sqlx");
                    if("1".equals(sqlx)) { //灌装可行性
                        bcLog.setType("2");
                    } else if("2".equals(sqlx)) { //包装可行性
                        bcLog.setType("3");
                    } else if("3".equals(sqlx)) { //工位图
                        bcLog.setType("4");
                    }
                }
            }
        }
        else if(ProjectConstant.CLCS_APPLY.equals(type)) {
            bcLog.setType("0");
        }
        else if(ProjectConstant.PROJECT_BOM_APPLY.equals(type)) {
            bcLog.setType("1");
        }
        if(bcLog.getType() != null) {
            bcLogMapper.deleteProjectBc(bcLog);
        }
        saveBcLog(projectItemOrder,projectItemInfo);

        if(ProjectConstant.NRW_APPLY.equals(projectItemOrder.getProjectItemType())) {
            processProjectFormulaRecordData(projectItemOrder,projectItemInfo);
        }

        tProjectExecution.setProjectItemCode(projectItemInfo.getType());
        tProjectExecution.setLabNo(projectItemOrder.getLabNo());
        projectExecutionService.insertTProjectExecution(tProjectExecution);
        String code = projectItemOrder.getCode();
        if(ProjectConstant.NRW_APPLY.equals(projectItemOrder.getProjectItemType())) {
            //领导内容物打样单提醒
            //saveLeaderNrwRemind(projectItemOrder);
            processProjectFormulaRecordData(projectItemOrder,projectItemInfo);
        }else if(ProjectConstant.CHP_BJ_APPLY.equals(projectItemOrder.getProjectItemType())
                ||ProjectConstant.OFFER_APPLY.equals(projectItemOrder.getProjectItemType())
                ||ProjectConstant.RQ_BC_APPLY.equals(projectItemOrder.getProjectItemType())
                ||ProjectConstant.NRW_BJ_APPLY.equals(projectItemOrder.getProjectItemType())){
            new Thread(){
                public void run(){
                    try {
                        Thread.sleep(5000L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    hrCostUtil.generateProjectOfferGrossProfit(code);
                }
            }.start();
        }
        //成品报价
        if(ProjectConstant.CHP_BJ_APPLY.equals(projectItemInfo.getType())){
            new Thread(){
                public void run(){
                    try {
                        Thread.sleep(5000L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    String productTabs = projectItemOrder.getProductTabs();
                    boolean isProcess = false;
                    if(StringUtils.isNotNull(productTabs) && JSONArray.isValidArray(productTabs)){
                        isProcess = hrCostUtil.processPackagingMaterialDatas(productTabs);
                    }else {
                        isProcess = hrCostUtil.processPackagingMaterialDatas(tProjectExecution.getProjectOrderId());
                    }
                    if(isProcess){  //无需包开审核
                        Map<String,Object> params = new HashMap<String,Object>();
                        params.put("itemOrderId",tProjectExecution.getProjectOrderId());
                        params.put("mergeKey","packagingMaterialDevelopment");
                        //获取执行流程操作
                        TProjectExecution execution = projectItemOrderMapper.queryProjectItemOrderExecutionDataInfo(params);
                        if(execution!=null && StringUtils.isNotNull(execution.getId())){
                            //审核确认操作
                            TProjectExecution projectExecution = new TProjectExecution();
                            projectExecution.setId(execution.getId());
                            projectExecution.setProjectOrderId(projectItemOrder.getId());
                            projectExecution.setProjectItemCode(ProjectConstant.CHP_BJ_APPLY);
                            projectExecution.setCurrentStep("复审");
                            projectExecution.setItemType("PROJECT_OFFER");
                            projectExecution.setMergeKey(params.get("mergeKey").toString());
                            projectExecution.setStep(2);
                            projectExecution.setConfirmCode(execution.getItemOrderCode());
                            projectExecution.setStatus(1);
                            projectExecution.setSjTime(DateUtils.getNowDate());
                            projectItemOrder.setYjTime(DateUtils.addDays(DateUtils.getNowDate(),1));
                            projectItemOrder.setFirstYjTime(DateUtils.addDays(DateUtils.getNowDate(),1));
                            tProjectExecutionService.updateTProjectExecution(projectExecution);
                            ProjectItemOrder projectItemOrderParams = new ProjectItemOrder();
                            projectItemOrderParams.setBcReplyTime(DateUtils.getNowDate());
                            projectItemOrderParams.setId(tProjectExecution.getProjectOrderId());
                            projectItemOrderParams.setBcReplyUser("系统自动通过");
                            projectItemOrderMapper.updateProjectItemOrder(projectItemOrderParams);
                        }
                    }
                }
            }.start();
        }
        return result;
    }

    @Override
    @Transactional
    public int revokeProjectItemOrder(ProjectItemOrder projectItemOrder) {
        //同步打样数量
        ProjectItemOrder order = projectItemOrderMapper.selectProjectItemOrderById(projectItemOrder.getId());
        projectItemOrder.setIsRevoke(1);
        projectItemOrder.setStatus(ProjectConstant.YCH_STATUS);
        projectItemOrder.setCurrentStep("已撤回");
        projectItemOrder.setUpdateBy(SecurityUtils.getUsername());
        projectItemOrder.setUpdateTime(DateUtils.getNowDate());
        projectItemOrder.setStep(1);
        projectItemOrder.setShStatus1(0);
        projectItemOrder.setShStatus(0);
        //
        String projectItemType = order.getProjectItemType();
        Long engineerId = order.getEngineerId();
        String labNo = order.getLabNo();
        labNo = StringUtils.convertDefaultValue(labNo,"-1");
        Integer isStep = order.getIsStep();
        isStep = StringUtils.convertDefaultValue(isStep,0);
        int result = 0;
        if(ProjectConstant.NRW_APPLY.equals(projectItemType) && isStep==1){
            result = projectItemOrderMapper.updateProjectItemOrderRevokeNrw(projectItemOrder);

        }else{
            result = projectItemOrderMapper.updateProjectItemOrderRevoke(projectItemOrder);
        }
        if(result>0){
            //撤销审核订单
            TProjectExecution projectExecution = new TProjectExecution();
            projectExecution.setProjectOrderId(projectItemOrder.getId());
            projectExecution.setIsLog(1);
            projectExecution.setStatus(2);
            projectExecution.setUpdateTime(DateUtils.getNowDate());
            projectExecution.setUpdateBy(SecurityUtils.getUsername());
            projectExecution.setCurrentStep("已撤回");
            projectExecution.setProcessUserId(SecurityUtils.getUserId());
            projectExecutionMapper.updateProjectExecutionInfo(projectExecution);
            //this.saveProjectRemindInfo(projectItemOrder.getId(),RemindType.CH);
            if(ProjectConstant.NRW_APPLY.equals(projectItemType) && isStep==1){
                String workUserId = order.getWorkUserId();
                //提醒分配工程师
                Set<String> workUserIds = CollUtil.newHashSet("xuezhangna");//通过之后发送消息提醒给原料录入员
                if("0".equals(labNo)){
                    workUserIds.add("yangfang");
                }
                if(StringUtils.isNotNull(workUserId)){
                    workUserIds.add(workUserId);
                }
                MessageParams msg = new MessageParams();
                msg.setWorkUserIds(workUserIds);
                msg.setMsgType("textcard");
                msg.setTitle("打样单撤销提醒【"+order.getCode()+"】");
                StringBuilder sb = new StringBuilder();
                sb.append("客户:"+order.getCustomerName()+",产品:"+order.getProductName()+".提交的打样单已被撤回!");
                msg.setDescription(sb.toString());
                msg.setUrl("https://enow.enowmill.com");
                messageService.sendWorkMessage(msg);
                if(StringUtils.isNotNull(order.getEngineerId())){
                    Boolean r = engineerSampleOrderService.withdrawSampleOrder(order.getEngineerId());
                }
            }

            productService.updateProductDkNums(order.getItemName(),order.getProjectId());
        }

        return result;
    }

    public void saveProjectRemindInfo(Long projectItemOrderId,Integer type){
        ProjectItemOrder projectItemOrderInfo = projectItemOrderMapper.selectProjectItemOrderById(projectItemOrderId);
        //保存撤销提醒
        ProjectRemind remind = new ProjectRemind();
        remind.setProjectItemOrderId(projectItemOrderId);
        remind.setYjTime(projectItemOrderInfo.getYjTime());
        remind.setType(type);
        remind.setUserId(projectItemOrderInfo.getUserId());
        remind.setProjectType(projectItemOrderInfo.getProjectItemType());
        remind.setCreateTime(DateUtils.getNowDate());

        JSONObject msg = new JSONObject();
        msg.put("customerName",projectItemOrderInfo.getCustomerName());
        msg.put("code",projectItemOrderInfo.getCode());//三级项目编号
        msg.put("productName",projectItemOrderInfo.getProductName());
        msg.put("projectId",projectItemOrderInfo.getProjectId());
        msg.put("yqDays","");
        remind.setMsg(msg.toJSONString());

        Set<Long> sendUserIds = new HashSet<Long>();
        //退回或者不通过  通知前面审核人员
        if(type.intValue()==RemindType.NO_PASS || type.intValue()==RemindType.BH){
            List<TProjectExecution> executions = projectRemindMapper.queryProcessOrderUserDataList(projectItemOrderId);
            if(executions!=null && executions.size()>0){
                for(TProjectExecution execution: executions) {
                    if(execution.getProcessUserId() != null) {
                        sendUserIds.add(execution.getProcessUserId());
                    }
                    Set<String> roleNames = new HashSet<>();
                    if(execution.getProcessPostName() != null) {
                        roleNames.add(execution.getProcessPostName());
                    }
                    if(roleNames.size() > 0) {
                        Set<Long> userIds = userMapper.selectUserIdByRoleNames(roleNames);
                        if(userIds.size() > 0) {
                            sendUserIds.addAll(userIds);
                        }
                    }
                }
            }
        }
        if(type.intValue()==RemindType.NO_PASS.intValue()){
            sendUserIds.add(projectItemOrderInfo.getUserId());
        }else{
            List<Long> leaderIds = sysUserService.queryUserLeaderAndCustomerAssistDataInfo(projectItemOrderInfo.getUserId(),projectItemOrderInfo.getCustomerId());
            sendUserIds.addAll(leaderIds);
        }
        projectRemindMapper.insertProjectRemind(remind);
        if(sendUserIds!=null && sendUserIds.size()>0){
            List<Long> allUserId = new ArrayList<Long>(sendUserIds);
            projectRemindMapper.batchRemindUser(remind.getId(),remind.getProjectType(),allUserId);
        }
    }

    @Override
    public Integer selectItemIsComplete(Long id) {
        return projectItemOrderMapper.selectItemIsComplete(id);
    }

    /**
     * 更新二级下三级订单状态（已暂停|已终止）
     * @param projectItemOrder
     */
    @Override
    public void updateProjectItemOrderStatus(ProjectItemOrder projectItemOrder) {
        projectItemOrderMapper.updateProjectItemOrderStatus(projectItemOrder);
    }

    @Override
    public List<Map<String, Object>> projectItemOfferDataList(ProjectItemOrder projectItemOrder) {
        List<Map<String, Object>> mapList = new ArrayList<Map<String,Object>>();
        List<Map<String,Object>> resultMap = projectItemOrderMapper.projectItemOfferDataList(projectItemOrder);
        for(Map<String,Object> result : resultMap){
            if(result!=null && result.containsKey("fields")){
                Object fields = result.get("fields");
                Object itemFields = result.get("itemFields");
                if(StringUtils.isNotNull(fields) && StringUtils.isNotNull(itemFields)){
                    JSONObject obj = JSONObject.parseObject(String.valueOf(fields));
                    JSONObject objItem = JSONObject.parseObject(String.valueOf(itemFields));
                    String key =  "BZCL";
                    String bzlxKey = "BZLX";
                    boolean isBzfl = false;
                    String  bcfl = "";
                    if (objItem!=null && objItem.containsKey("bcfl")){
                        bcfl = objItem.getString("bcfl");
                        if("HUAHE".equals(bcfl)){
                            bzlxKey = "HHLX";
                        }else if("ZHONGBAO".equals(bcfl)){
                            bzlxKey = "ZBLX";
                        }else if("WAIXIANG".equals(bcfl)){
                            bzlxKey = "WXLX";
                        }else if("LIHE".equals(bcfl)){
                            bzlxKey = "LHLX";
                        }else if("BIAOQIAN".equals(bcfl)){
                            key = "BQCL";
                            bzlxKey = "BQLX";
                        }else if("SHUOMINGSHU".equals(bcfl)){
                            key = "BQCL";
                            bzlxKey = "SMSLX";
                        }else if("TIANCHONGCAILIAO".equals(bcfl)){
                            key = "TCCL";
                            bzlxKey = "TCCLLX";
                        }else if("FUZHUGONGJU".equals(bcfl)){
                            bzlxKey = "FZGJLX";
                            isBzfl = true;
                        }
                    }
                    if(obj!=null && obj.containsKey("baocaiDatas")){
                        JSONArray dataArr = obj.getJSONArray("baocaiDatas");
                        for(int i = 0;i<dataArr.size();i++){
                            Map<String, Object> mapInfo = new HashMap<String,Object>();
                            mapInfo.put("bzfl",DictUtils.getDictLabel("BCFL",String.valueOf(bcfl)));
                            mapInfo.put("bzflCode",bcfl);
                            JSONObject objValue = dataArr.getJSONObject(i);
                            mapInfo.put("bianma",objValue.getString("code"));
                            mapInfo.put("xtbj",objValue.getString("cankaojiage"));
                            mapInfo.put("type","0");
                            if(isBzfl){
                                mapInfo.put("bzclValue",objValue.getString("bzcl"));
                            }else{
                                if(objValue!=null && objValue.containsKey("bzcl")){
                                    Object bzcl = objValue.get("bzcl");
                                    String dictLabel = DictUtils.getDictLabel(key,String.valueOf(bzcl));
                                    mapInfo.put("bzclValue",dictLabel);
                                }else{
                                    mapInfo.put("bzclValue","");
                                }
                            }
                            if(objValue!=null && objValue.containsKey("bzlx")){
                                Object bzlx = objValue.get("bzlx");
                                String dictLabel = DictUtils.getDictLabel(bzlxKey,String.valueOf(bzlx));
                                mapInfo.put("bzlxValue",dictLabel);
                            }else{
                                mapInfo.put("bzlxValue","");
                            }
                            if(objValue!=null && objValue.containsKey("gongyi")){
                                Object gongyi = objValue.get("gongyi");
                                String dictLabel = DictUtils.getDictLabel("ZSGC",String.valueOf(gongyi));
                                mapInfo.put("gongyiValue",dictLabel);
                            }else{
                                mapInfo.put("gongyiValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzgg")){
                                Object bzgg = objValue.getString("bzgg");
                                mapInfo.put("bzggValue",bzgg);
                            }else{
                                mapInfo.put("bzggValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzccchang")){
                                Object bzccchang = objValue.getString("bzccchang");
                                mapInfo.put("bzccchangValue",bzccchang);
                            }else{
                                mapInfo.put("bzccchangValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzcckuan")){
                                Object bzcckuan = objValue.getString("bzcckuan");
                                mapInfo.put("bzcckuanValue",bzcckuan);
                            }else{
                                mapInfo.put("bzcckuanValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzccgao")){
                                Object bzccgao = objValue.getString("bzccgao");
                                mapInfo.put("bzccgaoValue",bzccgao);
                            }else{
                                mapInfo.put("bzccgaoValue","");
                            }
                            if(objValue!=null && objValue.containsKey("xinghao")){
                                Object xinghao = objValue.getString("xinghao");
                                mapInfo.put("xinghaoValue",xinghao);
                            }else{
                                mapInfo.put("xinghaoValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzm")){
                                Object bzm = objValue.getString("bzm");
                                mapInfo.put("bzmValue",bzm);
                            }else{
                                mapInfo.put("bzmValue","");
                            }
                            if(objValue!=null && objValue.containsKey("gongyingshang")){
                                Object gongyingshang = objValue.getString("gongyingshang");
                                mapInfo.put("gongyingshangValue",gongyingshang);
                            }else{
                                mapInfo.put("gongyingshangValue","");
                            }
                            mapList.add(mapInfo);
                        }
                    }
                }

            }
        }
        return mapList;
    } @Override

    public List<Map<String, Object>> projectItemBomDataList(ProjectItemOrder projectItemOrder) {
        List<Map<String, Object>> mapList = new ArrayList<Map<String,Object>>();
        List<Map<String,Object>> resultMap = projectItemOrderMapper.projectItemBomDataList(projectItemOrder);
        for(Map<String,Object> result : resultMap){
            if(result!=null && result.containsKey("fields")){
                Object fields = result.get("fields");
                if(StringUtils.isNotNull(fields)){
                    JSONObject obj = JSONObject.parseObject(String.valueOf(fields));
                    if(obj!=null && obj.containsKey("pfbgData")){
                        JSONArray dataArr = obj.getJSONArray("pfbgData");
                        for(int i = 0;i<dataArr.size();i++){
                            Map<String, Object> mapInfo = new HashMap<String,Object>();
                            JSONObject objValue = dataArr.getJSONObject(i);
                            if(objValue!=null && objValue.containsKey("yicizhuangshi")){
                                Object yicizhuangshi = objValue.get("yicizhuangshi");
                                String dictLabel = DictUtils.getDictLabel("bc-zs",String.valueOf(yicizhuangshi));
                                mapInfo.put("yicizhuangshiValue",dictLabel);
                            }else{
                                mapInfo.put("yicizhuangshiValue","");
                            }
                            if(objValue!=null && objValue.containsKey("ercizhuangshi")){
                                Object ercizhuangshi = objValue.get("ercizhuangshi");
                                String dictLabel = DictUtils.getDictLabel("bc-zs",String.valueOf(ercizhuangshi));
                                mapInfo.put("ercizhuangshiValue",dictLabel);
                            }else{
                                mapInfo.put("ercizhuangshiValue","");
                            }
                            mapInfo.put("bianma",objValue.getString("wldm"));
                            mapInfo.put("type","BOM"+"("+result.get("confirmCode")+")");
                            mapInfo.put("bzmValue",objValue.getString("wlmc"));
                            mapInfo.put("bzclValue",objValue.getString("bzcl"));
                            mapInfo.put("bzlxValue",objValue.getString("wllx"));
                            mapInfo.put("bzggValue",objValue.getString("bzgg"));
                            mapInfo.put("chicun",objValue.getString("chicun"));
                            mapInfo.put("gongyingshang",objValue.getString("gongyingshang"));
                            mapInfo.put("attribute",objValue.getString("attribute"));
                            mapInfo.put("xinghaoValue",objValue.getString("xinghao"));
                            mapInfo.put("gongyingshangValue",objValue.getString("gongyingshang"));
                            mapList.add(mapInfo);
                        }
                    }
                }

            }
        }
        return mapList;
    }

    @Override
    @Transactional
    public void batchSaveRemind() {

        //查询所有逾期订单  20250319暂停执行
        List<ProjectRemind> remindList =  new ArrayList<>();//projectItemOrderMapper.selectOrderRemindList();
        if(remindList!=null && remindList.size()>0){
            //删除之前过期提醒内容
            projectRemindMapper.deleteProjectRemindUserData();
            projectRemindMapper.deleteProjectRemindData();
        }
        for(ProjectRemind projectRemind: remindList) {
            Set<Long> sendUserIds = new HashSet<>();
            if(StringUtils.isNotEmpty(projectRemind.getProcessUsers())) {
                String[] process = projectRemind.getProcessUsers().split(",");
                for (String processUserId: process) {
                    sendUserIds.add(Long.valueOf(processUserId));
                }
            }
            if(StringUtils.isNotEmpty(projectRemind.getProcessRoles())) {
                Set<String> roleNames = new HashSet<>();
                String[] roles = projectRemind.getProcessRoles().split(",");
                for (String role: roles) {
                    roleNames.add(role);
                }
                if(roleNames.size() > 0) {
                    Set<Long> userIds = userMapper.selectUserIdByRoleNames(roleNames);
                    if(userIds.size() > 0) {
                        sendUserIds.addAll(userIds);
                    }
                }
            }
            if(projectRemind.getYqDays() > 2) { //逾期超过两天
                List<Long> leaderIds = sysUserService.queryUserLeaderAndCustomerAssistDataInfo(projectRemind.getUserId(),projectRemind.getCustomerId());
                sendUserIds.addAll(leaderIds);
            } else {
                sendUserIds.add(projectRemind.getUserId());
            }

            ProjectRemind remind = new ProjectRemind();
            remind.setProjectItemOrderId(projectRemind.getProjectItemOrderId());
            remind.setYjTime(projectRemind.getYjTime());
            remind.setType(RemindType.TX);
            remind.setUserId(projectRemind.getUserId());
            remind.setProjectType(projectRemind.getProjectType());
            remind.setCreateTime(new Date());

            JSONObject msg = new JSONObject();
            msg.put("customerName",projectRemind.getCustomerName());
            msg.put("code",projectRemind.getCode());//三级项目编号
            msg.put("productName",projectRemind.getProductName());
            msg.put("projectId",projectRemind.getProjectId());
            msg.put("yqDays",projectRemind.getYqDays());

            remind.setMsg(msg.toJSONString());

//            projectRemindMapper.insertProjectRemind(remind);
//            //批量保存发送人
//            if(sendUserIds.size() > 0) {
//                projectRemindMapper.batchRemindUser(remind.getId(),projectRemind.getProjectType(),new ArrayList<>(sendUserIds));
//            }
        }

    }

    /**
     * 获取数据
     * @param projectItemOrder
     * @return
     */
    @Override
    public List<NrwArrangement> exportNrwArrangement(ProjectItemOrder projectItemOrder) {
        List<Map<String,Object>> dataList =projectItemOrderMapper.queryNrwArrangementDataList(projectItemOrder);
        List<NrwArrangement> arrangementDataList = new ArrayList<NrwArrangement>();
        for(Map<String,Object> data : dataList){
            NrwArrangement nrwArrangement = new NrwArrangement();
            Object obj = data.get("fields");
            // p.bhy_cf,
            // p.gn_sq,
            // p.gxtj,
            Object bhy_cf = data.get("bhy_cf");  //不含有成分
            Object gn_sq = data.get("gn_sq");  //概念诉求
            Object gxtj = data.get("gxtj");   //功效添加
            if(StringUtils.isNotNull(bhy_cf)){
                 JSONArray arr = JSONArray.parseArray(String.valueOf(bhy_cf));
                 StringBuffer sb = new StringBuffer();
                 for(int i = 0;i<arr.size();i++){
                    JSONObject o = arr.getJSONObject(i);
                     sb.append("时间:").append(o.getString("time")).append(",内容:").append(o.getString("text")).append(String.valueOf((char)10));;
                 }
                nrwArrangement.setBhyCf(sb.toString());
            }
            if(StringUtils.isNotNull(gn_sq)){
                JSONArray arr = JSONArray.parseArray(String.valueOf(gn_sq));
                StringBuffer sb = new StringBuffer();
                for(int i = 0;i<arr.size();i++){
                    JSONObject o = arr.getJSONObject(i);
                    sb.append("时间:").append(o.getString("time")).append(",内容:").append(o.getString("text")).append(String.valueOf((char)10));;
                }
                nrwArrangement.setGnSq(sb.toString());
            }
            if(StringUtils.isNotNull(gxtj)){
                JSONArray arr = JSONArray.parseArray(String.valueOf(gxtj));
                StringBuffer sb = new StringBuffer();
                for(int i = 0;i<arr.size();i++){
                    JSONObject o = arr.getJSONObject(i);
                    sb.append("时间:").append(o.getString("time")).append(",内容:").append(o.getString("text")).append(String.valueOf((char)10));;
                }
                nrwArrangement.setGxtj(sb.toString());
            }
            if(StringUtils.isNotNull(obj)){
                JSONObject objJson = JSONObject.parseObject(String.valueOf(obj));
                String xqsl = objJson.getString("xqsl");
                nrwArrangement.setCysl(xqsl);
                JSONArray arr = objJson.getJSONArray("dyrqDatas");
                StringBuffer strBuff = new StringBuffer();
                if(arr!=null && arr.size()>0){
                    for(int i =0;i<arr.size();i++){
                        JSONObject dyrqObj = arr.getJSONObject(i);
                        Object dyrq = dyrqObj.get("dyrq");
                        String val = DictUtils.getDictLabel("DYRQ",String.valueOf(dyrq));
                        strBuff.append("打样容器").append(":").append(val).append(",样品数量").append(":").append(dyrqObj.get("sl")).append(String.valueOf((char)10));
                    }
                }
                nrwArrangement.setSku(strBuff.toString());
                nrwArrangement.setDylb(DictUtils.getDictLabel("project_nrw_dylb",String.valueOf(objJson.get("dylb"))));
            }
            String ckCp = data.get("ck_cp")+"";
            if(StringUtils.isNotNull(ckCp) && !"null".equals(ckCp)){
                nrwArrangement.setCkCp(ckCp);
            }
            nrwArrangement.setQwTime(String.valueOf(data.get("qw_time")));
            nrwArrangement.setId(Long.valueOf(String.valueOf(data.get("id"))));
            nrwArrangement.setCode(String.valueOf(data.get("code")));
            nrwArrangement.setCreateBy(String.valueOf(data.get("create_by")));
            if(StringUtils.isNotNull(data.get("customerAssist"))){
                nrwArrangement.setCustomerAssist(StringUtils.convertDefaultValue(String.valueOf(data.get("customerAssist")),""));
            }
            nrwArrangement.setCustomerName(String.valueOf(data.get("name")));
            String level = String.valueOf(data.get("level"));
            if(StringUtils.isNotNull(level)){
                level = level.replaceAll("/","");
                if("null".equals(level) || "undefined".equals(level)){
                    level = "";
                }
            }
            String customerLevel = String.valueOf(data.get("customer_level"));
            if(StringUtils.isNotNull(customerLevel)){
                 if("null".equals(customerLevel) || "undefined".equals(customerLevel)){
                    customerLevel = "";
                }
            }
            nrwArrangement.setProductNameAndLevel(customerLevel+level);
            nrwArrangement.setProductName(String.valueOf(data.get("product_name")));
            nrwArrangement.setDifficulty(String.valueOf(data.get("difficulty")));
            nrwArrangement.setLaboratory(DictUtils.getDictLabel("project_lab",String.valueOf(data.get("laboratory"))));
            nrwArrangement.setZb(DictUtils.getDictLabel("project_zb",String.valueOf(data.get("zb"))));
            Object status = data.get("status");
            String statusStr = "审核中";
            if(StringUtils.isNotNull(status) && "5".equals(String.valueOf(status))){
                statusStr = "已审阅";
            }
            nrwArrangement.setStatus(statusStr);
            arrangementDataList.add(nrwArrangement);
        }
        return arrangementDataList;
    }

    @Override
    public List<ProjectItemOrder> projectItemOrderConfirmCodeByItemId(ProjectItemOrder projectItemOrder) {
        List<ProjectItemOrder> itemOrderDataList =  projectItemOrderMapper.projectItemOrderConfirmCodeByItemId(projectItemOrder);
        List<ProjectItemOrder> newItemOrderDataList = new ArrayList<ProjectItemOrder>();
        for(ProjectItemOrder projectItemOrderInfo : itemOrderDataList){
            String confirmCode = projectItemOrderInfo.getConfirmCode();
            if(StringUtils.isNotNull(confirmCode)){
                confirmCode = confirmCode.replaceAll("，",",");
                if(confirmCode.indexOf(",")!=-1){
                    String [] codeArr = StringUtils.split(confirmCode,",");
                    for(String code : codeArr){
                        ProjectItemOrder itemOrder = new ProjectItemOrder();
                        itemOrder.setId(projectItemOrderInfo.getId());
                        itemOrder.setConfirmCode(code);
                        newItemOrderDataList.add(itemOrder);
                    }
                }else{
                    newItemOrderDataList.add(projectItemOrderInfo);
                }
            }
        }
        return newItemOrderDataList;
    }

    @Override
    public List<ProjectItemOrderExcel> selectProjectItemOrderExcelList(ProjectItemOrder projectItemOrder) {
        if(!SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            projectItemOrder.setSearchUserId(SecurityUtils.getUserId());
        }
        if(SecurityUtils.isLookAdmin(SecurityUtils.getUserId())){
            projectItemOrder.setSearchUserId(null);
        }
        if(UserID.isSales(SecurityUtils.getUserId())){
            projectItemOrder.setSearchUserId(UserID.LUXIAOPING);
        }
        List<TCategory> tCategories = categoryMapper.selectTCategoryList(new TCategory());
        List<ProjectItemOrderExcel> dataList = projectItemOrderMapper.selectProjectItemOrderExcelList(projectItemOrder);
        List<Long> orderIdList = new ArrayList<Long>();
        List<Long> finishedOrderIdList = new ArrayList<Long>();
        for(ProjectItemOrderExcel projectItemOrderExcel : dataList){
            orderIdList.add(projectItemOrderExcel.getId());
            if(ProjectConstant.YWC_STATUS.equals(projectItemOrderExcel.getOrderStatus())){
                finishedOrderIdList.add(projectItemOrderExcel.getId());
            }
            Set<String> nameSet = new HashSet<String>();
            String projectItemType = projectItemOrderExcel.getProjectItemType();
            if(ProjectConstant.PROJECT_INSPECTION_AND_FILING.equals(projectItemType)){
                String orderFields = projectItemOrderExcel.getFields();
                if(StringUtils.isNotNull(orderFields)){
                    JSONObject orderObj = JSONObject.parseObject(orderFields);
                    if(orderObj!=null && orderObj.containsKey("sjData")){
                        JSONArray sjData = orderObj.getJSONArray("sjData");
                        if(sjData!=null && sjData.size()>0){
                            for(int i = 0;i<sjData.size();i++){
                                JSONObject obj = sjData.getJSONObject(i);
                                if(obj!=null && obj.containsKey("subName")){
                                    String subName = obj.getString("subName");
                                    if(StringUtils.isNotNull(subName)){
                                        nameSet.add(subName);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if(nameSet.size()>0){
                projectItemOrderExcel.setOrderProductName(StringUtils.join(nameSet,","));
            }
        }
        List<Map<String,Object>> feedbackMapsData = new ArrayList<Map<String,Object>>();
        if(finishedOrderIdList!=null && finishedOrderIdList.size()>0){
            feedbackMapsData = projectItemOrderMapper.queryProjectItemOrderFeedbackDataList(finishedOrderIdList);
        }
        List<Map<String,Object>> mapsData = new ArrayList<Map<String,Object>>();
        if(orderIdList!=null && orderIdList.size()>0){
            mapsData = projectItemOrderMapper.queryProjectItemOrderExecutionDataList(orderIdList);
        }
        for(int i = 0;i<dataList.size();i++){
            ProjectItemOrderExcel orderInfo = dataList.get(i);
            String itemConfirmCode = orderInfo.getItemConfirmCode();
            String finalConfirmCode = orderInfo.getFinalConfirmCode();
            if(StringUtils.isNotNull(itemConfirmCode) && StringUtils.isNotNull(finalConfirmCode)){
                if(itemConfirmCode.trim().equals(finalConfirmCode.trim())){
                    orderInfo.setFinalConfirmCode(finalConfirmCode);
                }else{
                    orderInfo.setFinalConfirmCode("");
                }
            }else{
                orderInfo.setFinalConfirmCode("");
            }

            String orderId = String.valueOf(orderInfo.getId());

            //获取反馈信息
            if(feedbackMapsData!=null && feedbackMapsData.size()>0){
                StringBuffer feedBuffer = new StringBuffer();
                for (Map<String,Object> feedbackMap : feedbackMapsData){
                    Object item_order_id = feedbackMap.get("item_order_id");
                    Object fk_source = feedbackMap.get("fk_source");
                    Object fk_detail = feedbackMap.get("fk_detail");
                    if(StringUtils.isNotNull(item_order_id)){
                        if(String.valueOf(item_order_id).equals(orderId)){
                            if(StringUtils.isNotNull(fk_source)){
                                String fkSource = DictUtils.getDictLabel("project_item_order_feedback_resource",String.valueOf(fk_source));
                                feedBuffer.append("反馈来源:").append(fkSource).append(".");
                            }
                            if(StringUtils.isNotNull(fk_detail)){
                                feedBuffer.append("反馈详情:").append(fk_detail).append(";");
                            }
                            //feedbackMapsData.remove(feedbackMap);
                        }
                    }
                }
                orderInfo.setFkDetail(feedBuffer.toString());
            }
            orderInfo.setSeq(String.valueOf(i+1));
            //项目子名称
            String itemName = orderInfo.getItemName();
            String itemNames = orderInfo.getItemNames();
            orderInfo.setSku(String.valueOf(1));
            if(StringUtils.isNotNull(itemNames)){
                JSONArray itemNamesArr = JSONArray.parseArray(itemNames);
                if(itemNamesArr.size()>0){
                    orderInfo.setSku(String.valueOf(itemNamesArr.size()));
                }
            }
            if(StringUtils.isNotNull(itemName) && StringUtils.isNotNull(itemNames)){
                JSONArray itemNamesArr = JSONArray.parseArray(itemNames);
                for(int j = 0;j<itemNamesArr.size();j++){
                    JSONObject itemNameObj = itemNamesArr.getJSONObject(j);
                    if(itemNameObj.containsKey("id") && itemName.equals(itemNameObj.getString("id"))){
                        orderInfo.setItemName(itemNameObj.getString("text"));
                    }
                }
            }
            String laboratory = orderInfo.getLaboratory();
            if(StringUtils.isNotNull(laboratory)){
                orderInfo.setLaboratory(DictUtils.getDictLabel("project_lab",laboratory));
            }
            orderInfo.setProjectItemType(DictUtils.getDictLabel("PROJECT_ITEM",projectItemOrder.getProjectItemType()));
            if(StringUtils.isNotEmpty(orderInfo.getProductType())) {
                List<String> array = new ArrayList<>();
                for (String f: orderInfo.getProductType().split(",")) {
                    for (TCategory c: tCategories) {
                        if(f.equals(c.getCategoryId().toString())) {
                            array.add(c.getCategoryName());
                        }
                    }
                }
                if(array.size() > 0) {
                    orderInfo.setProductType(StringUtils.join(array,"|"));
                }
            }
            //二级类别
            String itemFields = orderInfo.getItemFields();
            if(StringUtils.isNotNull(itemFields)){
                JSONObject obj = JSONObject.parseObject(itemFields);
                orderInfo.setItemType(ProjectItemTypeUtil.selectProjectItemType(projectItemOrder.getProjectItemType(),obj));
            }

            Double timelyRate = orderInfo.getTimelyRate();
            if(StringUtils.isNotNull(timelyRate)){
                orderInfo.setJsl(Arith.removeZero(String.valueOf(timelyRate))+"%");
            }


            String orderStatus = orderInfo.getOrderStatus();
            if(StringUtils.isNotNull(orderStatus)){
                orderInfo.setOrderStatus(DictUtils.getDictLabel("project_status",orderStatus));
            }

            String customerLevel = orderInfo.getCustomerLevel();
            String projectLevel  = orderInfo.getProjectLevel();
            if(StringUtils.isNotNull(projectLevel)){
                projectLevel = projectLevel.replaceAll("/","");
            }
            orderInfo.setProjectLevel(customerLevel+projectLevel);
            StringBuffer firstAuditRemark = new StringBuffer(";");
            StringBuffer secondAuditRemark  =new StringBuffer(";");
            StringBuffer processAuditRemark  =new StringBuffer(";");
            for(Map<String,Object> data : mapsData){
                //初审
                Object projectOrderId = data.get("project_order_id");
                String executionStatus = String.valueOf(data.get("status"));
                if(orderId.equals(String.valueOf(projectOrderId))){
                   String currentStep = String.valueOf(data.get("current_step"));
                    String step = String.valueOf(data.get("step"));
                   if("1".equals(step)){
                       if("0".equals(executionStatus) || "1".equals(executionStatus) || "4".equals(executionStatus)) {
                           orderInfo.setFirstAuditUser(String.valueOf(data.get("nick_name")));
                           String assginUser = String.valueOf(data.get("assgin_user_name"));
                           if (StringUtils.isNotNull(assginUser) && !"null".equals(assginUser)) {
                               if (JSONArray.isValidArray(assginUser)) {
                                   JSONArray array = JSONArray.parseArray(assginUser);
                                   orderInfo.setSecondAssginUser(StringUtils.join(array, "|"));
                               } else {
                                   orderInfo.setSecondAssginUser(assginUser);
                               }
                           }
                           Date createTime = DateUtils.parseDate(data.get("createTime"));
                           Date auditTime = DateUtils.parseDate(data.get("auditTime"));
                           if (StringUtils.isNotNull(createTime) && StringUtils.isNotNull(auditTime)) {
                               long diffHours = DateUtils.differDays(createTime, auditTime);
                               orderInfo.setFirstHsTime(String.valueOf(diffHours));
                           } else {
                               orderInfo.setFirstHsTime(String.valueOf("0"));
                           }
                       }
                       if(StringUtils.isNotNull(data.get("auditRemark"))){
                           firstAuditRemark.append(String.valueOf(data.get("auditRemark"))).append(";");
                       }
                   }else if("2".equals(step)){
                       if("0".equals(executionStatus) || "1".equals(executionStatus) || "4".equals(executionStatus)) {
                           orderInfo.setSecondAuditUser(String.valueOf(data.get("nick_name")));
                           String assginUser = String.valueOf(data.get("assgin_user_name"));
                           if (StringUtils.isNotNull(assginUser) && !"null".equals(assginUser)) {
                               if (JSONArray.isValidArray(assginUser)) {
                                   JSONArray array = JSONArray.parseArray(assginUser);
                                   orderInfo.setSecondAssginUser(StringUtils.join(array, "|"));
                               } else {
                                   orderInfo.setSecondAssginUser(assginUser);
                               }
                           }
                           Date createTime = DateUtils.parseDate(data.get("createTime"));
                           Date auditTime = DateUtils.parseDate(data.get("auditTime"));
                           if (StringUtils.isNotNull(createTime) && StringUtils.isNotNull(auditTime)) {
                               long diffHours = DateUtils.differDays(createTime, auditTime);
                               orderInfo.setSecondHsTime(String.valueOf(diffHours));
                           } else {
                               orderInfo.setSecondHsTime(String.valueOf("0"));
                           }
                       }
                       if(StringUtils.isNotNull(data.get("auditRemark"))){
                           secondAuditRemark.append(String.valueOf(data.get("auditRemark"))).append(";");
                       }
                   }else if("3".equals(step)){
                       if("0".equals(executionStatus) || "1".equals(executionStatus) || "4".equals(executionStatus)) {
                           orderInfo.setProcessAuditUser(String.valueOf(data.get("nick_name")));
                           if ("3".equals(orderStatus)) {
                               Date applyTime = orderInfo.getApplyTime();
                               Date auditTime = DateUtils.parseDate(data.get("auditTime"));
                               long diffHours = DateUtils.differDays(applyTime, auditTime);
                               orderInfo.setOrderHsTime(String.valueOf(diffHours));
                           }
                           Object auditFields = data.get("fields");
                           Object projectItemCode = data.get("project_item_code");
                           if (StringUtils.isNotNull(auditFields)) {
                               JSONObject auditObj = JSONObject.parseObject(String.valueOf(auditFields));
                               if (ProjectConstant.BA_APPLY.equals(String.valueOf(projectItemCode))) {
                                   orderInfo.setJdTime(auditObj.containsKey("batjsj") ? auditObj.getString("batjsj") : "");
                               } else if (ProjectConstant.SJBA_APPLY.equals(String.valueOf(projectItemCode))) {
                                   orderInfo.setJdTime(auditObj.containsKey("sjrq") ? auditObj.getString("sjrq") : "");
                               } else if (ProjectConstant.CLCS_APPLY.equals(String.valueOf(projectItemCode))) {
                                   orderInfo.setJdTime(auditObj.containsKey("csrq") ? auditObj.getString("csrq") : "");
                               } else if (ProjectConstant.SC_APPLY.equals(String.valueOf(projectItemCode))) {
                                   orderInfo.setJdTime(auditObj.containsKey("openDate") ? auditObj.getString("openDate") : "");
                               }
                               if (auditObj.containsKey("delayRemark") && StringUtils.isNotNull(auditObj.getString("delayRemark"))) {
                                   orderInfo.setDelay("是");
                                   orderInfo.setDelayReamrk(auditObj.getString("delayRemark"));
                               }
                           }
                       }
                       if(StringUtils.isNotNull(data.get("remark"))){
                           processAuditRemark.append(String.valueOf(data.get("remark"))).append(";");
                       }
                   }
                }
            }
            orderInfo.setAmendments(firstAuditRemark.toString().substring(1));
            orderInfo.setAmendments1(secondAuditRemark.toString().substring(1));
            orderInfo.setProcessAuditRemark(processAuditRemark.toString().substring(1));
        }
        return dataList;
    }

    @Override
    public void updateProjectExecutionStatus(ProjectItemOrder projectItemOrder) {
        projectItemOrderMapper.updateProjectExecutionStatus(projectItemOrder);
    }

    @Override
    public List<Map<String, Object>> prjectItemOrderOfferNrwPriceInfo(ProjectItemOrder projectItemOrder) {
        List<Map<String, Object>> mapsList = projectItemOrderMapper.prjectItemOrderOfferNrwPriceInfo(projectItemOrder);
        return mapsList;
    }

    @Override
    public List<Map<String, Object>> projectItemOrderBcBomDataList(ProjectItemOrder projectItemOrder) {
        String projectItemType = projectItemOrder.getProjectItemType();
        List<Map<String,Object>> mapsData = new ArrayList<Map<String,Object>>();
        List<Map<String, Object>> maps  = projectItemOrderMapper.projectItemOrderBcBomDataList(projectItemOrder);
        if(maps!=null && maps.size()>0){
            for(Map<String,Object> map : maps){
                Map<String,Object> m = new HashMap<String,Object>();
                String type = String.valueOf(map.get("type"));
                Object id = map.get("id");
                Object confirmCode = map.get("confirmCode");
                if("2".equals(projectItemType)){
                    confirmCode = map.get("itemConfirmCode");
                    id = map.get("itemId");
                }
                Object itemFields = map.get("itemFields");
                m.put("id",id);
                if(ProjectConstant.PROJECT_BOM_APPLY.equals(type)){
                    m.put("type",1);  //BOM
                    m.put("confirmCode",confirmCode);
                }else if(ProjectConstant.RQKF_APPLY.equals(type)){
                    m.put("type",2);  //包材
                    JSONObject obj = JSONObject.parseObject(String.valueOf(itemFields));
                    String dictValue = DictUtils.getDictLabel("BCFL",obj.getString("bcfl"));
                    m.put("confirmCode",dictValue+"("+confirmCode+")");
                }else if(ProjectConstant.OFFER_APPLY.equals(type)){
                    m.put("type",3);  //包材报价确认
                    m.put("confirmCode",confirmCode);
                }
                mapsData.add(m);
            }
        }
        return mapsData;
    }

    @Override
    public List<Map<String, Object>> projectItemOfferDataListNew(ProjectItemOrder projectItemOrder) {
        List<Map<String, Object>> mapList = new ArrayList<Map<String,Object>>();
        List<Map<String,Object>> resultMap = projectItemOrderMapper.projectItemOfferDataListNew(projectItemOrder);
        for(Map<String,Object> result : resultMap){
            if(result!=null && result.containsKey("fields")){
                Object fields = result.get("fields");
                Object itemFields = result.get("itemFields");
                if(StringUtils.isNotNull(fields) && StringUtils.isNotNull(itemFields)){
                    Object auditFields = result.get("auditFields");
                    if(StringUtils.isNotNull(auditFields)){
                        fields = auditFields;
                    }
                    JSONObject obj = JSONObject.parseObject(String.valueOf(fields));
                    JSONObject objItem = JSONObject.parseObject(String.valueOf(itemFields));
                    String key =  "BZCL";
                    String bzlxKey = "BZLX";
                    boolean isBzfl = false;
                    String  bcfl = "";
                    if (objItem!=null && objItem.containsKey("bcfl")){
                        bcfl = objItem.getString("bcfl");
                        if("HUAHE".equals(bcfl)){
                            bzlxKey = "HHLX";
                        }else if("ZHONGBAO".equals(bcfl)){
                            bzlxKey = "ZBLX";
                        }else if("WAIXIANG".equals(bcfl)){
                            bzlxKey = "WXLX";
                        }else if("LIHE".equals(bcfl)){
                            bzlxKey = "LHLX";
                        }else if("BIAOQIAN".equals(bcfl)){
                            key = "BQCL";
                            bzlxKey = "BQLX";
                        }else if("SHUOMINGSHU".equals(bcfl)){
                            key = "BQCL";
                            bzlxKey = "SMSLX";
                        }else if("TIANCHONGCAILIAO".equals(bcfl)){
                            key = "TCCL";
                            bzlxKey = "TCCLLX";
                        }else if("FUZHUGONGJU".equals(bcfl)){
                            bzlxKey = "FZGJLX";
                            isBzfl = true;
                        }
                    }
                    if(obj!=null && obj.containsKey("baocaiDatas")){
                        JSONArray dataArr = obj.getJSONArray("baocaiDatas");
                        for(int i = 0;i<dataArr.size();i++){
                            Map<String, Object> mapInfo = new HashMap<String,Object>();
                            mapInfo.put("bzfl",DictUtils.getDictLabel("BCFL",String.valueOf(bcfl)));
                            mapInfo.put("bzflCode",bcfl);
                            JSONObject objValue = dataArr.getJSONObject(i);
                            mapInfo.put("bianma",objValue.getString("code"));
                            mapInfo.put("xtbj",objValue.getString("cankaojiage"));
                            mapInfo.put("type","0");
                            if(isBzfl){
                                mapInfo.put("bzclValue",objValue.getString("bzcl"));
                            }else{
                                if(objValue!=null && objValue.containsKey("bzcl")){
                                    Object bzcl = objValue.get("bzcl");
                                    String dictLabel = DictUtils.getDictLabel(key,String.valueOf(bzcl));
                                    mapInfo.put("bzclValue",dictLabel);
                                }else{
                                    mapInfo.put("bzclValue","");
                                }
                            }
                            if(objValue!=null && objValue.containsKey("bzlx")){
                                Object bzlx = objValue.get("bzlx");
                                String dictLabel = DictUtils.getDictLabel(bzlxKey,String.valueOf(bzlx));
                                mapInfo.put("bzlxValue",dictLabel);
                            }else{
                                mapInfo.put("bzlxValue","");
                            }
                            if(objValue!=null && objValue.containsKey("gongyi")){
                                Object gongyi = objValue.get("gongyi");
                                String dictLabel = DictUtils.getDictLabel("ZSGC",String.valueOf(gongyi));
                                mapInfo.put("gongyiValue",dictLabel);
                            }else{
                                mapInfo.put("gongyiValue","");
                            }
                            if(objValue!=null && objValue.containsKey("yicizhuangshi")){
                                Object yicizhuangshi = objValue.get("yicizhuangshi");
                                String dictLabel = DictUtils.getDictLabel("bc-zs",String.valueOf(yicizhuangshi));
                                mapInfo.put("yicizhuangshiValue",dictLabel);
                            }else{
                                mapInfo.put("yicizhuangshiValue","");
                            }
                            if(objValue!=null && objValue.containsKey("ercizhuangshi")){
                                Object ercizhuangshi = objValue.get("ercizhuangshi");
                                String dictLabel = DictUtils.getDictLabel("bc-zs",String.valueOf(ercizhuangshi));
                                mapInfo.put("ercizhuangshiValue",dictLabel);
                            }else{
                                mapInfo.put("ercizhuangshiValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzgg")){
                                Object bzgg = objValue.getString("bzgg");
                                mapInfo.put("bzggValue",bzgg);
                            }else{
                                mapInfo.put("bzggValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzccchang")){
                                Object bzccchang = objValue.getString("bzccchang");
                                mapInfo.put("bzccchangValue",bzccchang);
                            }else{
                                mapInfo.put("bzccchangValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzcckuan")){
                                Object bzcckuan = objValue.getString("bzcckuan");
                                mapInfo.put("bzcckuanValue",bzcckuan);
                            }else{
                                mapInfo.put("bzcckuanValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzccgao")){
                                Object bzccgao = objValue.getString("bzccgao");
                                mapInfo.put("bzccgaoValue",bzccgao);
                            }else{
                                mapInfo.put("bzccgaoValue","");
                            }
                            if(objValue!=null && objValue.containsKey("xinghao")){
                                Object xinghao = objValue.getString("xinghao");
                                mapInfo.put("xinghaoValue",xinghao);
                            }else{
                                mapInfo.put("xinghaoValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzm")){
                                Object bzm = objValue.getString("bzm");
                                mapInfo.put("bzmValue",bzm);
                            }else{
                                mapInfo.put("bzmValue","");
                            }
                            if(objValue!=null && objValue.containsKey("gongyingshang")){
                                Object gongyingshang = objValue.getString("gongyingshang");
                                mapInfo.put("gongyingshangValue",gongyingshang);
                            }else{
                                mapInfo.put("gongyingshangValue","");
                            }
                            if(objValue!=null && objValue.containsKey("attribute")){
                                Object attribute = objValue.getString("attribute");
                                mapInfo.put("attributeValue",attribute);
                            }else{
                                mapInfo.put("attributeValue","");
                            }
                            mapList.add(mapInfo);
                        }
                    }
                }
            }
        }
        return mapList;
    }

    @Override
    public List<Map<String, Object>> projectItemOfferDataListFinidshNew(ProjectItemOrder projectItemOrder) {
        List<Map<String, Object>> mapList = new ArrayList<Map<String,Object>>();
        List<Map<String,Object>> resultMap = projectItemOrderMapper.projectItemOfferDataListFinidshNew(projectItemOrder);
        for(Map<String,Object> result : resultMap){
            String confirmCode = result.get("confirmCode") +"";
            String itemConfirmCode = result.get("itemConfirmCode") + "";
            boolean isContinue = true;
            if(confirmCode.indexOf(",")!=-1){
                String [] codeArr  = confirmCode.split(",");
                for(String code : codeArr){
                    if(code.equals(itemConfirmCode)){
                        isContinue = false;
                    }
                }
            }else{
                if(confirmCode.equals(itemConfirmCode)){
                    isContinue = false;
                }
            }
            if(isContinue){
                continue;
            }
            if(result!=null && result.containsKey("fields")){
                Object fields = result.get("fields");
                Object itemFields = result.get("itemFields");
                if(StringUtils.isNotNull(fields) && StringUtils.isNotNull(itemFields)){
                    JSONObject obj = JSONObject.parseObject(String.valueOf(fields));
                    JSONObject objItem = JSONObject.parseObject(String.valueOf(itemFields));
                    String key =  "BZCL";
                    String bzlxKey = "BZLX";
                    boolean isBzfl = false;
                    String  bcfl = "";
                    boolean isProcess = true;
                    if (objItem!=null && objItem.containsKey("bcfl")){
                        bcfl = objItem.getString("bcfl");
                        if("HUAHE".equals(bcfl)){
                            bzlxKey = "HHLX";
                        }else if("ZHONGBAO".equals(bcfl)){
                            bzlxKey = "ZBLX";
                        }else if("WAIXIANG".equals(bcfl)){
                            bzlxKey = "WXLX";
                        }else if("LIHE".equals(bcfl)){
                            bzlxKey = "LHLX";
                        }else if("BIAOQIAN".equals(bcfl)){
                            key = "BQCL";
                            bzlxKey = "BQLX";
                        }else if("SHUOMINGSHU".equals(bcfl)){
                            key = "BQCL";
                            bzlxKey = "SMSLX";
                        }else if("TIANCHONGCAILIAO".equals(bcfl)){
                            key = "TCCL";
                            bzlxKey = "TCCLLX";
                        }else if("FUZHUGONGJU".equals(bcfl)){
                            bzlxKey = "FZGJLX";
                            isBzfl = true;
                        }else if("ALL".equals(bcfl)){
                            isProcess = false;
                            List<Map<String, Object>> baocaiDatasNBC = queryBcflAllDatas("NBC","baocaiDatasNBC",obj,confirmCode);
                            if(baocaiDatasNBC != null && baocaiDatasNBC.size()>0){
                                mapList.addAll(baocaiDatasNBC);
                            }
                            List<Map<String, Object>> baocaiDatasHUAHE = queryBcflAllDatas("baocaiDatasFUZHUGONGJU","baocaiDatasHUAHE",obj,confirmCode);
                            if(baocaiDatasHUAHE != null && baocaiDatasHUAHE.size()>0){
                                mapList.addAll(baocaiDatasHUAHE);
                            }
                            List<Map<String, Object>> baocaiDatasZHONGBAO = queryBcflAllDatas("ZHONGBAO","baocaiDatasZHONGBAO",obj,confirmCode);
                            if(baocaiDatasZHONGBAO != null && baocaiDatasZHONGBAO.size()>0){
                                mapList.addAll(baocaiDatasZHONGBAO);
                            }
                            List<Map<String, Object>> baocaiDatasWAIXIANG = queryBcflAllDatas("WAIXIANG","baocaiDatasWAIXIANG",obj,confirmCode);
                            if(baocaiDatasWAIXIANG != null && baocaiDatasWAIXIANG.size()>0){
                                mapList.addAll(baocaiDatasWAIXIANG);
                            }
                            List<Map<String, Object>> baocaiDatasLIHE = queryBcflAllDatas("LIHE","baocaiDatasLIHE",obj,confirmCode);
                            if(baocaiDatasLIHE != null && baocaiDatasLIHE.size()>0){
                                mapList.addAll(baocaiDatasLIHE);
                            }
                            List<Map<String, Object>> baocaiDatasBIAOQIAN = queryBcflAllDatas("BIAOQIAN","baocaiDatasBIAOQIAN",obj,confirmCode);
                            if(baocaiDatasBIAOQIAN != null && baocaiDatasBIAOQIAN.size()>0){
                                mapList.addAll(baocaiDatasBIAOQIAN);
                            }
                            List<Map<String, Object>> baocaiDatasSHUOMINGSHU = queryBcflAllDatas("SHUOMINGSHU","baocaiDatasSHUOMINGSHU",obj,confirmCode);
                            if(baocaiDatasSHUOMINGSHU != null && baocaiDatasSHUOMINGSHU.size()>0){
                                mapList.addAll(baocaiDatasSHUOMINGSHU);
                            }
                            List<Map<String, Object>> baocaiDatasTIANCHONGCAILIAO = queryBcflAllDatas("TIANCHONGCAILIAO","baocaiDatasTIANCHONGCAILIAO",obj,confirmCode);
                            if(baocaiDatasTIANCHONGCAILIAO != null && baocaiDatasTIANCHONGCAILIAO.size()>0){
                                mapList.addAll(baocaiDatasTIANCHONGCAILIAO);
                            }
                            List<Map<String, Object>> baocaiDatasFUZHUGONGJU = queryBcflAllDatas("FUZHUGONGJU","baocaiDatasFUZHUGONGJU",obj,confirmCode);
                            if(baocaiDatasFUZHUGONGJU != null && baocaiDatasFUZHUGONGJU.size()>0){
                                mapList.addAll(baocaiDatasFUZHUGONGJU);
                            }
                        }
                    }
                    if(isProcess && obj!=null && obj.containsKey("baocaiDatas")){
                        JSONArray dataArr = obj.getJSONArray("baocaiDatas");
                        for(int i = 0;i<dataArr.size();i++){
                            Map<String, Object> mapInfo = new HashMap<String,Object>();
                            mapInfo.put("bzfl",DictUtils.getDictLabel("BCFL",String.valueOf(bcfl)));
                            mapInfo.put("bzflCode",bcfl);
                            JSONObject objValue = dataArr.getJSONObject(i);
                            mapInfo.put("bianma",objValue.getString("code"));
                            mapInfo.put("xtbj",objValue.getString("cankaojiage"));
                            mapInfo.put("type",DictUtils.getDictLabel("BCFL",String.valueOf(bcfl))+"("+confirmCode+")");
                            if(isBzfl){
                                mapInfo.put("bzclValue",objValue.getString("bzcl"));
                            }else{
                                if(objValue!=null && objValue.containsKey("bzcl")){
                                    Object bzcl = objValue.get("bzcl");
                                    String dictLabel = DictUtils.getDictLabel(key,String.valueOf(bzcl));
                                    mapInfo.put("bzclValue",dictLabel);
                                }else{
                                    mapInfo.put("bzclValue","");
                                }
                            }
                            if(objValue!=null && objValue.containsKey("bzlx")){
                                Object bzlx = objValue.get("bzlx");
                                String dictLabel = DictUtils.getDictLabel(bzlxKey,String.valueOf(bzlx));
                                mapInfo.put("bzlxValue",dictLabel);
                            }else{
                                mapInfo.put("bzlxValue","");
                            }
                            if(objValue!=null && objValue.containsKey("gongyi")){
                                Object gongyi = objValue.get("gongyi");
                                String dictLabel = DictUtils.getDictLabel("ZSGC",String.valueOf(gongyi));
                                mapInfo.put("gongyiValue",dictLabel);
                            }else{
                                mapInfo.put("gongyiValue","");
                            }
                            if(objValue!=null && objValue.containsKey("yicizhuangshi")){
                                mapInfo.put("yicizhuangshiValue",objValue.getString("yicizhuangshi"));
                            }else{
                                mapInfo.put("yicizhuangshiValue","");
                            }
                            if(objValue!=null && objValue.containsKey("ercizhuangshi")){
                                mapInfo.put("ercizhuangshiValue",objValue.getString("ercizhuangshi"));
                            }else{
                                mapInfo.put("ercizhuangshiValue","");
                            }

                            if(objValue!=null && objValue.containsKey("bzgg")){
                                Object bzgg = objValue.getString("bzgg");
                                mapInfo.put("bzggValue",bzgg);
                            }else{
                                mapInfo.put("bzggValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzccchang")){
                                Object bzccchang = objValue.getString("bzccchang");
                                mapInfo.put("bzccchangValue",bzccchang);
                            }else{
                                mapInfo.put("bzccchangValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzcckuan")){
                                Object bzcckuan = objValue.getString("bzcckuan");
                                mapInfo.put("bzcckuanValue",bzcckuan);
                            }else{
                                mapInfo.put("bzcckuanValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzccgao")){
                                Object bzccgao = objValue.getString("bzccgao");
                                mapInfo.put("bzccgaoValue",bzccgao);
                            }else{
                                mapInfo.put("bzccgaoValue","");
                            }
                            if(objValue!=null && objValue.containsKey("xinghao")){
                                Object xinghao = objValue.getString("xinghao");
                                mapInfo.put("xinghaoValue",xinghao);
                            }else{
                                mapInfo.put("xinghaoValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzm")){
                                Object bzm = objValue.getString("bzm");
                                mapInfo.put("bzmValue",bzm);
                            }else{
                                mapInfo.put("bzmValue","");
                            }
                            if(objValue!=null && objValue.containsKey("gongyingshang")){
                                Object gongyingshang = objValue.getString("gongyingshang");
                                mapInfo.put("gongyingshangValue",gongyingshang);
                            }else{
                                mapInfo.put("gongyingshangValue","");
                            }
                            mapList.add(mapInfo);
                        }
                    }
                }

            }
        }
        return mapList;
    }

    @Override
    public List<Map<String, Object>> projectItemOfferBcDataList(ProjectItemOrder projectItemOrder) {
        List<Map<String, Object>> mapList = new ArrayList<Map<String,Object>>();
        List<Map<String,Object>> resultMap = projectItemOrderMapper.projectItemOfferBcDataList(projectItemOrder);
        for(Map<String,Object> result : resultMap){
            if(result!=null && result.containsKey("fields")){
                Object fields = result.get("fields");
                if(StringUtils.isNotNull(fields)){
                    JSONObject obj = JSONObject.parseObject(String.valueOf(fields));
                    if(obj!=null && obj.containsKey("bzxxData")){
                        JSONArray dataArr = obj.getJSONArray("bzxxData");
                        for(int i = 0;i<dataArr.size();i++){
                            Map<String, Object> mapInfo = new HashMap<String,Object>();
                            JSONObject objValue = dataArr.getJSONObject(i);
                            String code = "";
                            String type = objValue.getString("type");
                            if(StringUtils.isNotNull(type)){
                                if(type.contains("BOM")){
                                    code = objValue.getString("bomcode");
                                }else{
                                    code = objValue.getString("bianma");
                                }
                            }
                            mapInfo.put("bianma",code);
                            mapInfo.put("type",type);
                            mapInfo.put("bzmValue",objValue.getString("bzm"));
                            mapInfo.put("yicizhuangshiValue",objValue.getString("yicizhuangshi"));
                            mapInfo.put("ercizhuangshiValue",objValue.getString("ercizhuangshi"));
                            mapInfo.put("bzclValue",objValue.getString("bzcl"));
                            mapInfo.put("bzggValue",objValue.getString("guige"));
                            mapInfo.put("chicun",objValue.getString("chicun"));
                            mapInfo.put("gongyi",objValue.getString("gongyi"));
                            mapInfo.put("yongliang",objValue.getString("yongliang"));
                            mapInfo.put("xtbj",objValue.getString("hzjg"));
                            mapInfo.put("moq",objValue.getString("moq"));
                            mapInfo.put("xinghaoValue",objValue.getString("xinghao"));
                            mapInfo.put("gongyingshangValue",objValue.getString("gongyingshang"));
                            mapInfo.put("attributeValue",objValue.getString("attribute"));
                            mapList.add(mapInfo);
                        }
                    }
                }

            }
        }
        return mapList;
    }

    @Override
    public List<Map<String, Object>> projectItemOfferDataListArrNew(ProjectItemOrder projectItemOrder) {
        List<Map<String, Object>> mapList = new ArrayList<Map<String,Object>>();
        List<Map<String,Object>> resultMap = projectItemOrderMapper.projectItemOfferDataListArrNew(projectItemOrder);
        for(Map<String,Object> result : resultMap){
            if(result!=null && result.containsKey("fields")){
                Object fields = result.get("fields");
                Object itemFields = result.get("itemFields");
                if(StringUtils.isNotNull(fields) && StringUtils.isNotNull(itemFields)){
                    Object auditFields = result.get("auditFields");
                    if(StringUtils.isNotNull(auditFields)){
                        fields = auditFields;
                    }
                    JSONObject obj = JSONObject.parseObject(String.valueOf(fields));
                    JSONObject objItem = JSONObject.parseObject(String.valueOf(itemFields));
                    String key =  "BZCL";
                    String bzlxKey = "BZLX";
                    boolean isBzfl = false;
                    String  bcfl = "";
                    Object confirmCode = result.get("confirmCode");
                    boolean isProcess = true;
                    if (objItem!=null && objItem.containsKey("bcfl")){
                        bcfl = objItem.getString("bcfl");
                        if("HUAHE".equals(bcfl)){
                            bzlxKey = "HHLX";
                        }else if("ZHONGBAO".equals(bcfl)){
                            bzlxKey = "ZBLX";
                        }else if("WAIXIANG".equals(bcfl)){
                            bzlxKey = "WXLX";
                        }else if("LIHE".equals(bcfl)){
                            bzlxKey = "LHLX";
                        }else if("BIAOQIAN".equals(bcfl)){
                            key = "BQCL";
                            bzlxKey = "BQLX";
                        }else if("SHUOMINGSHU".equals(bcfl)){
                            key = "BQCL";
                            bzlxKey = "SMSLX";
                        }else if("TIANCHONGCAILIAO".equals(bcfl)){
                            key = "TCCL";
                            bzlxKey = "TCCLLX";
                        }else if("FUZHUGONGJU".equals(bcfl)){
                            bzlxKey = "FZGJLX";
                            isBzfl = true;
                        }else if("ALL".equals(bcfl)){
                            isProcess = false;
                            List<Map<String, Object>> baocaiDatasNBC = queryBcflAllDatas("NBC","baocaiDatasNBC",obj,confirmCode);
                            if(baocaiDatasNBC != null && baocaiDatasNBC.size()>0){
                                mapList.addAll(baocaiDatasNBC);
                            }
                            List<Map<String, Object>> baocaiDatasHUAHE = queryBcflAllDatas("baocaiDatasFUZHUGONGJU","baocaiDatasHUAHE",obj,confirmCode);
                            if(baocaiDatasHUAHE != null && baocaiDatasHUAHE.size()>0){
                                mapList.addAll(baocaiDatasHUAHE);
                            }
                            List<Map<String, Object>> baocaiDatasZHONGBAO = queryBcflAllDatas("ZHONGBAO","baocaiDatasZHONGBAO",obj,confirmCode);
                            if(baocaiDatasZHONGBAO != null && baocaiDatasZHONGBAO.size()>0){
                                mapList.addAll(baocaiDatasZHONGBAO);
                            }
                            List<Map<String, Object>> baocaiDatasWAIXIANG = queryBcflAllDatas("WAIXIANG","baocaiDatasWAIXIANG",obj,confirmCode);
                            if(baocaiDatasWAIXIANG != null && baocaiDatasWAIXIANG.size()>0){
                                mapList.addAll(baocaiDatasWAIXIANG);
                            }
                            List<Map<String, Object>> baocaiDatasLIHE = queryBcflAllDatas("LIHE","baocaiDatasLIHE",obj,confirmCode);
                            if(baocaiDatasLIHE != null && baocaiDatasLIHE.size()>0){
                                mapList.addAll(baocaiDatasLIHE);
                            }
                            List<Map<String, Object>> baocaiDatasBIAOQIAN = queryBcflAllDatas("BIAOQIAN","baocaiDatasBIAOQIAN",obj,confirmCode);
                            if(baocaiDatasBIAOQIAN != null && baocaiDatasBIAOQIAN.size()>0){
                                mapList.addAll(baocaiDatasBIAOQIAN);
                            }
                            List<Map<String, Object>> baocaiDatasSHUOMINGSHU = queryBcflAllDatas("SHUOMINGSHU","baocaiDatasSHUOMINGSHU",obj,confirmCode);
                            if(baocaiDatasSHUOMINGSHU != null && baocaiDatasSHUOMINGSHU.size()>0){
                                mapList.addAll(baocaiDatasSHUOMINGSHU);
                            }
                            List<Map<String, Object>> baocaiDatasTIANCHONGCAILIAO = queryBcflAllDatas("TIANCHONGCAILIAO","baocaiDatasTIANCHONGCAILIAO",obj,confirmCode);
                            if(baocaiDatasTIANCHONGCAILIAO != null && baocaiDatasTIANCHONGCAILIAO.size()>0){
                                mapList.addAll(baocaiDatasTIANCHONGCAILIAO);
                            }
                            List<Map<String, Object>> baocaiDatasFUZHUGONGJU = queryBcflAllDatas("FUZHUGONGJU","baocaiDatasFUZHUGONGJU",obj,confirmCode);
                            if(baocaiDatasFUZHUGONGJU != null && baocaiDatasFUZHUGONGJU.size()>0){
                                mapList.addAll(baocaiDatasFUZHUGONGJU);
                            }
                        }
                    }
                    if(isProcess && obj!=null && obj.containsKey("baocaiDatas")){
                        JSONArray dataArr = obj.getJSONArray("baocaiDatas");
                        for(int i = 0;i<dataArr.size();i++){
                            Map<String, Object> mapInfo = new HashMap<String,Object>();
                            mapInfo.put("bzfl",DictUtils.getDictLabel("BCFL",String.valueOf(bcfl))+"("+confirmCode+")");
                            mapInfo.put("bzflCode",bcfl);
                            JSONObject objValue = dataArr.getJSONObject(i);
                            mapInfo.put("bianma",objValue.getString("code"));
                            mapInfo.put("xtbj",objValue.getString("cankaojiage"));
                            mapInfo.put("type",DictUtils.getDictLabel("BCFL",String.valueOf(bcfl))+"("+confirmCode+")");
                            mapInfo.put("bzclType",objValue.getString("bzcl"));
                            if(isBzfl){
                                mapInfo.put("bzclValue",objValue.getString("bzcl"));
                            }else{
                                if(objValue!=null && objValue.containsKey("bzcl")){
                                    Object bzcl = objValue.get("bzcl");
                                    String dictLabel = DictUtils.getDictLabel(key,String.valueOf(bzcl));
                                    mapInfo.put("bzclValue",dictLabel);
                                }else{
                                    mapInfo.put("bzclValue","");
                                }
                            }
                            if(objValue!=null && objValue.containsKey("bzlx")){
                                Object bzlx = objValue.get("bzlx");
                                String dictLabel = DictUtils.getDictLabel(bzlxKey,String.valueOf(bzlx));
                                mapInfo.put("bzlxValue",dictLabel);
                            }else{
                                mapInfo.put("bzlxValue","");
                            }
                            if(objValue!=null && objValue.containsKey("gongyi")){
                                Object gongyi = objValue.get("gongyi");
                                String dictLabel = DictUtils.getDictLabel("ZSGC",String.valueOf(gongyi));
                                mapInfo.put("gongyiValue",dictLabel);
                            }else{
                                mapInfo.put("gongyiValue","");
                            }

                            if(objValue!=null && objValue.containsKey("yicizhuangshi")){
                                Object yicizhuangshi = objValue.get("yicizhuangshi");
                                String dictLabel = DictUtils.getDictLabel("bc-zs",String.valueOf(yicizhuangshi));
                                mapInfo.put("yicizhuangshiValue",dictLabel);
                            }else{
                                mapInfo.put("yicizhuangshiValue","");
                            }
                            if(objValue!=null && objValue.containsKey("ercizhuangshi")){
                                Object ercizhuangshi = objValue.get("ercizhuangshi");
                                String dictLabel = DictUtils.getDictLabel("bc-zs",String.valueOf(ercizhuangshi));
                                mapInfo.put("ercizhuangshiValue",dictLabel);
                            }else{
                                mapInfo.put("ercizhuangshiValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzgg")){
                                Object bzgg = objValue.getString("bzgg");
                                mapInfo.put("bzggValue",bzgg);
                            }else{
                                mapInfo.put("bzggValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzccchang")){
                                Object bzccchang = objValue.getString("bzccchang");
                                mapInfo.put("bzccchangValue",bzccchang);
                            }else{
                                mapInfo.put("bzccchangValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzcckuan")){
                                Object bzcckuan = objValue.getString("bzcckuan");
                                mapInfo.put("bzcckuanValue",bzcckuan);
                            }else{
                                mapInfo.put("bzcckuanValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzccgao")){
                                Object bzccgao = objValue.getString("bzccgao");
                                mapInfo.put("bzccgaoValue",bzccgao);
                            }else{
                                mapInfo.put("bzccgaoValue","");
                            }
                            if(objValue!=null && objValue.containsKey("xinghao")){
                                Object xinghao = objValue.getString("xinghao");
                                mapInfo.put("xinghaoValue",xinghao);
                            }else{
                                mapInfo.put("xinghaoValue","");
                            }
                            if(objValue!=null && objValue.containsKey("bzm")){
                                Object bzm = objValue.getString("bzm");
                                mapInfo.put("bzmValue",bzm);
                            }else{
                                mapInfo.put("bzmValue","");
                            }
                            if(objValue!=null && objValue.containsKey("gongyingshang")){
                                Object gongyingshang = objValue.getString("gongyingshang");
                                mapInfo.put("gongyingshangValue",gongyingshang);
                            }else{
                                mapInfo.put("gongyingshangValue","");
                            }
                            if(objValue!=null && objValue.containsKey("attribute")){
                                Object attribute = objValue.getString("attribute");
                                mapInfo.put("attributeValue",attribute);
                            }else{
                                mapInfo.put("attributeValue","");
                            }
                            mapList.add(mapInfo);
                        }
                    }
                }

            }
        }
        return mapList;
    }

    private List<Map<String, Object>> queryBcflAllDatas(String bcfl,String datasKey,JSONObject obj,Object confirmCode){
        List<Map<String, Object>> mapList = new ArrayList<Map<String,Object>>();
        String key =  "BZCL";
        String bzlxKey = "BZLX";
        boolean isBzfl = false;
        if("HUAHE".equals(bcfl)){
            bzlxKey = "HHLX";
        }else if("ZHONGBAO".equals(bcfl)){
            bzlxKey = "ZBLX";
        }else if("WAIXIANG".equals(bcfl)){
            bzlxKey = "WXLX";
        }else if("LIHE".equals(bcfl)){
            bzlxKey = "LHLX";
        }else if("BIAOQIAN".equals(bcfl)){
            key = "BQCL";
            bzlxKey = "BQLX";
        }else if("SHUOMINGSHU".equals(bcfl)){
            key = "BQCL";
            bzlxKey = "SMSLX";
        }else if("TIANCHONGCAILIAO".equals(bcfl)){
            key = "TCCL";
            bzlxKey = "TCCLLX";
        }else if("FUZHUGONGJU".equals(bcfl)){
            bzlxKey = "FZGJLX";
            isBzfl = true;
        }
        if(obj!=null && obj.containsKey(datasKey)){
            JSONArray dataArr = obj.getJSONArray(datasKey);
            for(int i = 0;i<dataArr.size();i++){
                Map<String, Object> mapInfo = new HashMap<String,Object>();
                mapInfo.put("bzfl",DictUtils.getDictLabel("BCFL",String.valueOf(bcfl))+"("+confirmCode+")");
                mapInfo.put("bzflCode",bcfl);
                JSONObject objValue = dataArr.getJSONObject(i);
                mapInfo.put("bianma",objValue.getString("code"));
                mapInfo.put("xtbj",objValue.getString("cankaojiage"));
                mapInfo.put("type",DictUtils.getDictLabel("BCFL",String.valueOf(bcfl))+"("+confirmCode+")");
                if(isBzfl){
                    mapInfo.put("bzclValue",objValue.getString("bzcl"));
                }else{
                    if(objValue!=null && objValue.containsKey("bzcl")){
                        Object bzcl = objValue.get("bzcl");
                        String dictLabel = DictUtils.getDictLabel(key,String.valueOf(bzcl));
                        mapInfo.put("bzclValue",dictLabel);
                    }else{
                        mapInfo.put("bzclValue","");
                    }
                }
                if(objValue!=null && objValue.containsKey("bzlx")){
                    Object bzlx = objValue.get("bzlx");
                    String dictLabel = DictUtils.getDictLabel(bzlxKey,String.valueOf(bzlx));
                    mapInfo.put("bzlxValue",dictLabel);
                }else{
                    mapInfo.put("bzlxValue","");
                }
                if(objValue!=null && objValue.containsKey("gongyi")){
                    Object gongyi = objValue.get("gongyi");
                    String dictLabel = DictUtils.getDictLabel("ZSGC",String.valueOf(gongyi));
                    mapInfo.put("gongyiValue",dictLabel);
                }else{
                    mapInfo.put("gongyiValue","");
                }

                if(objValue!=null && objValue.containsKey("yicizhuangshi")){
                    Object yicizhuangshi = objValue.get("yicizhuangshi");
                    String dictLabel = DictUtils.getDictLabel("bc-zs",String.valueOf(yicizhuangshi));
                    mapInfo.put("yicizhuangshiValue",dictLabel);
                }else{
                    mapInfo.put("yicizhuangshiValue","");
                }
                if(objValue!=null && objValue.containsKey("ercizhuangshi")){
                    Object ercizhuangshi = objValue.get("ercizhuangshi");
                    String dictLabel = DictUtils.getDictLabel("bc-zs",String.valueOf(ercizhuangshi));
                    mapInfo.put("ercizhuangshiValue",dictLabel);
                }else{
                    mapInfo.put("ercizhuangshiValue","");
                }
                if(objValue!=null && objValue.containsKey("bzgg")){
                    Object bzgg = objValue.getString("bzgg");
                    mapInfo.put("bzggValue",bzgg);
                }else{
                    mapInfo.put("bzggValue","");
                }
                if(objValue!=null && objValue.containsKey("bzccchang")){
                    Object bzccchang = objValue.getString("bzccchang");
                    mapInfo.put("bzccchangValue",bzccchang);
                }else{
                    mapInfo.put("bzccchangValue","");
                }
                if(objValue!=null && objValue.containsKey("bzcckuan")){
                    Object bzcckuan = objValue.getString("bzcckuan");
                    mapInfo.put("bzcckuanValue",bzcckuan);
                }else{
                    mapInfo.put("bzcckuanValue","");
                }
                if(objValue!=null && objValue.containsKey("bzccgao")){
                    Object bzccgao = objValue.getString("bzccgao");
                    mapInfo.put("bzccgaoValue",bzccgao);
                }else{
                    mapInfo.put("bzccgaoValue","");
                }
                if(objValue!=null && objValue.containsKey("xinghao")){
                    Object xinghao = objValue.getString("xinghao");
                    mapInfo.put("xinghaoValue",xinghao);
                }else{
                    mapInfo.put("xinghaoValue","");
                }
                if(objValue!=null && objValue.containsKey("bzm")){
                    Object bzm = objValue.getString("bzm");
                    mapInfo.put("bzmValue",bzm);
                }else{
                    mapInfo.put("bzmValue","");
                }
                if(objValue!=null && objValue.containsKey("gongyingshang")){
                    Object gongyingshang = objValue.getString("gongyingshang");
                    mapInfo.put("gongyingshangValue",gongyingshang);
                }else{
                    mapInfo.put("gongyingshangValue","");
                }
                mapList.add(mapInfo);
            }
        }
        return mapList;
    }

    @Override
    public List<Map<String, Object>> projectItemOrderSckxxGwtDataList(ProjectItemOrder projectItemOrder) {
        String projectItemType = projectItemOrder.getProjectItemType();
        List<Map<String,Object>> mapsData = new ArrayList<Map<String,Object>>();
        List<Map<String, Object>> maps  = projectItemOrderMapper.projectItemOrderSckxxGwtDataList(projectItemOrder);
        if(maps!=null && maps.size()>0){
            for(Map<String,Object> map : maps){
                Map<String,Object> m = new HashMap<String,Object>();
                Object id = map.get("id");
                Object confirmCode = map.get("confirmCode");
                m.put("id",id);
                m.put("confirmCode",confirmCode);
                mapsData.add(m);
            }
        }
        return mapsData;
    }

    /**
     * 报价加工费
     * @param projectItemOrder
     * @return
     */
    @Override
    public List<Map<String, Object>> projectItemOfferJgfDataList(ProjectItemOrder projectItemOrder) {
        List<Map<String,Object>> mapsData = new ArrayList<Map<String,Object>>();
        List<Map<String, Object>> maps  = projectItemOrderMapper.projectItemOfferJgfDataList(projectItemOrder);
        if(maps!=null && maps.size()>0){
            for(Map<String,Object> map : maps){
                Object auditFields = map.get("auditFields");
                Object confirmCode = map.get("confirmCode");
                if(StringUtils.isNotNull(auditFields)){
                    JSONObject obj = JSONObject.parseObject(String.valueOf(auditFields));
                    String files = obj.containsKey("files")?obj.getString("files"):"";
                    String imgs = obj.containsKey("imgs")?obj.getString("imgs"):"";
                    if(obj.containsKey("jiagongfeis") && StringUtils.isNotNull(obj.getString("jiagongfeis"))){
                        String jiagongfeis = obj.getString("jiagongfeis");
                        JSONArray arr = obj.getJSONArray("jiagongfeis");
                        for(int i = 0;i<arr.size();i++){
                            JSONObject o = arr.getJSONObject(i);
                            Map<String, Object> m = new HashMap<String,Object>();
                            m.put("priceType",o.getString("priceType"));
                            m.put("peopleCount",o.getString("peopleCount"));
                            m.put("channeng",o.getString("channeng"));
                            m.put("type",confirmCode);
                            m.put("files",files);
                            m.put("imgs",imgs);
                            mapsData.add(m);
                        }
                    }
                }
            }
        }
        return mapsData;
    }

    /**
     * 反馈
     */
    @Override
    public void orderFeed() {
        List<ProjectItemOrder> projectItemOrderList = projectItemOrderMapper.queryProjectItemOrderDataList("PROJECT_NRW");
        if(projectItemOrderList!=null && projectItemOrderList.size()>0){
            for(ProjectItemOrder projectItemOrder : projectItemOrderList){
                ProjectItemOrderFeedback projectItemOrderFeedbackInfo = projectItemOrderMapper.selectProjectItemOrderFeedInfo(projectItemOrder);
                ProjectItemOrder newProjectItemOrder = new ProjectItemOrder();
                if(projectItemOrderFeedbackInfo!=null && StringUtils.isNotNull(projectItemOrderFeedbackInfo.getId())){
                    Date endTime = projectItemOrderFeedbackInfo.getCreateTime();
                    Date startTime = projectItemOrder.getCreateTime();
                    long days = DateUtils.differDays(startTime,endTime);
                    if(days<=14){
                        newProjectItemOrder.setFeedStatus(3);
                    }else{
                        newProjectItemOrder.setFeedStatus(2);
                    }
                }else{
                    Date endTime = DateUtils.getNowDate();
                    Date startTime = projectItemOrder.getCreateTime();
                    long days = DateUtils.differDays(startTime,endTime);
                    if(days<=14){
                        newProjectItemOrder.setFeedStatus(0);
                    }else{
                        newProjectItemOrder.setFeedStatus(1);
                    }
                }
                newProjectItemOrder.setId(projectItemOrder.getId());
//                newProjectItemOrder.setUpdateTime(DateUtils.getNowDate());
                projectItemOrderMapper.updateProjectItemOrder(newProjectItemOrder);
            }
        }
    }

    @Override
    public void offerFeed() {
        //成品报价
        List<ProjectItem> projectItemList = new ArrayList<ProjectItem>(); // projectItemMapper.queryProjectItemOffer();
        if(projectItemList!=null && projectItemList.size()>0){
            for(ProjectItem projectItem : projectItemList){
                String fields = projectItem.getFields();
                Long id = projectItem.getId();
                if(StringUtils.isNotNull(fields) && StringUtils.isNotNull(id)){
                    JSONObject obj = JSONObject.parseObject(fields);
                    String bjlx = obj.getString("bjlx");
                    ProjectItemOrder projectItemOrderInfo = projectItemOrderMapper.queryProjectItemOrderFeedData(id);
                    if(projectItemOrderInfo!=null && StringUtils.isNotNull(projectItemOrderInfo.getId())){
                        Date feedTime = projectItemOrderInfo.getFeedTime();
                        Date sjTime = projectItemOrderInfo.getSjTime();
                        Integer isFeed = projectItemOrderInfo.getIsFeed();
                        if(ProjectConstant.NRW_BJ_APPLY.equals(bjlx)){  //内容物报价
                            Project projectInfo = new Project();
                            projectInfo.setId(projectItemOrderInfo.getProjectId());
                            projectInfo.setOfferTimeNrw(projectItemOrderInfo.getSjTime());
                            if(isFeed==1){
                                Date endTime = feedTime;
                                Date startTime = sjTime;
                                long days = DateUtils.differDays(startTime,endTime);
                                if(days<=14){
                                    projectInfo.setOfferNrwFeed(3);
                                }else{
                                    projectInfo.setOfferNrwFeed(2);
                                }
                            }else{
                                Date endTime = DateUtils.getNowDate();
                                Date startTime = sjTime;
                                long days = DateUtils.differDays(startTime,endTime);
                                if(days<=14){
                                    projectInfo.setOfferNrwFeed(0);
                                }else{
                                    projectInfo.setOfferNrwFeed(1);
                                }
                            }
                            projectMapper.updateProjectOfferFeedInfo(projectInfo);
                        }else if(ProjectConstant.CHP_BJ_APPLY.equals(bjlx)){  //成品报价
                            Project projectInfo = new Project();
                            projectInfo.setId(projectItemOrderInfo.getProjectId());
                            projectInfo.setOfferTimeChp(projectItemOrderInfo.getSjTime());
                            if(isFeed==1){
                                Date endTime = feedTime;
                                Date startTime = sjTime;
                                long days = DateUtils.differDays(startTime,endTime);
                                if(days<=14){
                                    projectInfo.setOfferChpFeed(3);
                                }else{
                                    projectInfo.setOfferChpFeed(2);
                                }
                            }else{
                                Date endTime = DateUtils.getNowDate();
                                Date startTime = sjTime;
                                long days = DateUtils.differDays(startTime,endTime);
                                if(days<=14){
                                    projectInfo.setOfferChpFeed(0);
                                }else{
                                    projectInfo.setOfferChpFeed(1);
                                }
                            }
                            projectMapper.updateProjectOfferFeedInfo(projectInfo);
                        }
                    }
                }
            }
        }

        hrCostUtil.processResignataionsDataList();
    }

    @Override
    public void productionFeasibilityFeed() {
        List<ProjectItemOrder> projectItemOrderList = projectItemOrderMapper.queryProjectItemOrderDataList("PROJECT_SCKXXPG");
        if(projectItemOrderList!=null && projectItemOrderList.size()>0){
            for(ProjectItemOrder projectItemOrder : projectItemOrderList){
                ProjectItemOrderFeedback projectItemOrderFeedbackInfo = projectItemOrderMapper.selectProjectItemOrderFeedInfo(projectItemOrder);
                ProjectItemOrder newProjectItemOrder = new ProjectItemOrder();
                if(projectItemOrderFeedbackInfo!=null && StringUtils.isNotNull(projectItemOrderFeedbackInfo.getId())){
                    Date endTime = projectItemOrderFeedbackInfo.getCreateTime();
                    Date startTime = projectItemOrder.getCreateTime();
                    long days = DateUtils.differDays(startTime,endTime);
                    if(days<=7){
                        newProjectItemOrder.setFeedStatus(3);
                    }else{
                        newProjectItemOrder.setFeedStatus(2);
                    }
                }else{
                    Date endTime = DateUtils.getNowDate();
                    Date startTime = projectItemOrder.getCreateTime();
                    long days = DateUtils.differDays(startTime,endTime);
                    if(days<=7){
                        newProjectItemOrder.setFeedStatus(0);
                    }else{
                        newProjectItemOrder.setFeedStatus(1);
                    }
                }
                newProjectItemOrder.setId(projectItemOrder.getId());
//                newProjectItemOrder.setUpdateTime(DateUtils.getNowDate());
                projectItemOrderMapper.updateProjectItemOrder(newProjectItemOrder);
            }
        }
    }

    @Override
    public void mergeSjOrderData() {
        List<ProjectItemOrder> projectItemOrderList = projectItemOrderMapper.queryProjectMergeItemOrderDataList(ProjectConstant.SJBA_APPLY);
        if(projectItemOrderList!=null && projectItemOrderList.size()>0){
            for(ProjectItemOrder projectItemOrder : projectItemOrderList){
                String itemFields = projectItemOrder.getItemFields();
                String  fields = projectItemOrder.getFields();
                String type = projectItemOrder.getProjectItemType();
                Long itemId = projectItemOrder.getItemId();
                Long id = projectItemOrder.getId();

                ProjectItem projectItem = new ProjectItem();
                projectItem.setId(itemId);
                projectItem.setType(ProjectConstant.PROJECT_INSPECTION_AND_FILING);
                JSONObject obj = new JSONObject();
                obj.put("applyType","0");
                projectItem.setFields(obj.toString());
                projectItemMapper.updateProjectItem(projectItem);

                ProjectItemOrder projectItemOrderInfo = new ProjectItemOrder();
                projectItemOrderInfo.setId(id);
                if(StringUtils.isNotNull(fields)){
                    JSONObject fieldsObj = JSONObject.parseObject(fields);
                    if(fieldsObj!=null){
                        JSONObject newObj = new JSONObject();
                        newObj.put("sjData",fieldsObj.getJSONArray("sjData"));
                        fieldsObj.put("sjData", null);
                        newObj.put("songjianFields", fieldsObj);
                        newObj.put("beianFields",new JSONObject());
                        projectItemOrderInfo.setFields(newObj.toString());
                        projectItemOrderMapper.updateProjectItemOrder(projectItemOrderInfo);
                    }
                }
            }
        }
    }

    @Override
    public void mergeBaOrderData() {
        List<ProjectItemOrder> projectItemOrderList = projectItemOrderMapper.queryProjectMergeItemOrderDataList(ProjectConstant.BA_APPLY);
        if(projectItemOrderList!=null && projectItemOrderList.size()>0){
            for(ProjectItemOrder projectItemOrder : projectItemOrderList){
                String itemFields = projectItemOrder.getItemFields();
                String  fields = projectItemOrder.getFields();
                String type = projectItemOrder.getProjectItemType();
                Long itemId = projectItemOrder.getItemId();
                Long id = projectItemOrder.getId();

                if(StringUtils.isNotNull(itemFields)){
                    JSONObject obj = new JSONObject();
                    JSONObject itemObj = JSONObject.parseObject(itemFields);
                    String balb= itemObj.getString("balb");
                    if("0".equals(balb)){  //稿件/文案审核
                        obj.put("applyType","2");
                    }else if("1".equals(balb)){  //备案
                        obj.put("applyType","1");
                    }
                    ProjectItem projectItem = new ProjectItem();
                    projectItem.setId(itemId);
                    projectItem.setType(ProjectConstant.PROJECT_INSPECTION_AND_FILING);
                    projectItem.setFields(obj.toString());
                    projectItemMapper.updateProjectItem(projectItem);

                    ProjectItemOrder projectItemOrderInfo = new ProjectItemOrder();
                    projectItemOrderInfo.setId(id);
                    if(StringUtils.isNotNull(fields)){
                        JSONObject fieldsObj = JSONObject.parseObject(fields);
                        if(fieldsObj!=null){
                            JSONObject newObj = new JSONObject();
                            newObj.put("sjData",fieldsObj.getJSONArray("itemDataInfos"));
                            fieldsObj.put("itemDataInfos", null);
                            newObj.put("songjianFields", new JSONObject());
                            newObj.put("beianFields",fieldsObj);
                            projectItemOrderInfo.setFields(newObj.toString());
                            projectItemOrderMapper.updateProjectItemOrder(projectItemOrderInfo);
                        }
                    }
                }
            }
        }
    }

    @Override
    public void updatePrjectSjbaProgress() {
        Integer count = projectItemOrderMapper.queryProjectSjbaOrderCount();
        if(count>0){
            int size = 100;//每页查询记录数
            int page = (count % size) > 0 ? (count / size) + 1 : (count / size);
            page = page > 200 ? 200 : page;
            if(page > 0) {
                for (int p1 = 0; p1 < page; p1++) {
                    List<ProjectSjbaDataExport> dataList = projectItemOrderMapper.queryProjectSjbaOrderDataList((p1 * size),size);
                    if(dataList!=null && dataList.size()>0){
                        List<Long> orderIdList = new ArrayList<Long>();
                        for(ProjectSjbaDataExport projectSjbaDataExport : dataList){
                            orderIdList.add(projectSjbaDataExport.getId());
                            String status = projectSjbaDataExport.getStatus();
                            String fields = projectSjbaDataExport.getFields();
                            if(StringUtils.isNotNull(fields)){
                                JSONObject fieldsObj = JSONObject.parseObject(fields);
                                String applyType = fieldsObj.getString("applyType");
                                projectSjbaDataExport.setApplyType(applyType);
                            }
                            if(ProjectConstant.JXZ_STATUS.equals(status) || ProjectConstant.YWC_STATUS.equals(status)){  //进行中或已完成
                                List<TProjectExecution> executionList = projectExecutionMapper.queryProjectSjbaExecutionDataList(projectSjbaDataExport);
                                if(executionList!=null && executionList.size()>0){
                                    Long currentExecutionId = null;  //第一次初审执行ID
                                    String currentAuditRemark = null; //
                                    Date currentAuditTime = null; //
                                    Date currentCreateTime = null; //
                                    Integer currentStatus = null;
                                    for(TProjectExecution projectExecution : executionList){
                                        Integer step = projectExecution.getStep();
                                        if(StringUtils.isNull(currentExecutionId) && step==1){
                                            currentExecutionId =  projectExecution.getId();
                                            currentAuditRemark = projectExecution.getAuditRemark();
                                            currentAuditTime = projectExecution.getAuditTime();
                                            currentCreateTime = projectExecution.getCreateTime();
                                            currentStatus = projectExecution.getStatus();
                                            break;
                                        }
                                    }
                                    if(StringUtils.isNotNull(currentExecutionId)){ //初审
                                        projectSjbaDataExport.setFirstSubmitTime(currentCreateTime);
                                        projectSjbaDataExport.setFirstAuditTime(currentAuditTime);
                                        projectSjbaDataExport.setFirstAuditStatus(currentStatus);
                                        if(StringUtils.isNull(currentAuditTime)){
                                            currentAuditTime = new Date();
                                        }
                                        long days = DateUtils.getWorkDays(currentAuditTime,currentCreateTime);
                                        projectSjbaDataExport.setFirstHs(days);
                                    }
                                    //初审通过 存在复审
                                    if(StringUtils.isNotNull(currentExecutionId) && currentStatus!=null && currentStatus.intValue()==1){
                                        //研发
                                        for(TProjectExecution projectExecution : executionList){
                                            Integer step = projectExecution.getStep();
                                            Long executionId = projectExecution.getId();
                                            String mergeKey = projectExecution.getMergeKey();
                                            if(step==2 && "yanfa".equals(mergeKey) && currentExecutionId<executionId){
                                                String auditRemark = projectExecution.getAuditRemark();
                                                Date auditTime = projectExecution.getAuditTime();
                                                Date createTime = projectExecution.getCreateTime();
                                                Integer executionStatus = projectExecution.getStatus();
                                                projectSjbaDataExport.setYanfaSubmitTime(createTime);
                                                projectSjbaDataExport.setYanfaAuditTime(auditTime);
                                                projectSjbaDataExport.setYanfaAuditStatus(executionStatus);
                                                if(StringUtils.isNull(auditTime)){
                                                    auditTime = new Date();
                                                }
                                                long days = DateUtils.getWorkDays(auditTime,createTime);
                                                projectSjbaDataExport.setYanfaHs(days);
                                                String executionFields = projectExecution.getFields();
                                                JSONObject newObj = new JSONObject();
                                                if(StringUtils.isNotNull(executionFields)){
                                                    JSONObject obj = JSONObject.parseObject(executionFields);
                                                    String finishTime = obj.getString("inspectionAndFillConfirmTime");
                                                    List<Long> finishedTimeMillsThrid = new ArrayList<Long>();
                                                    if(StringUtils.isNotNull(finishTime)){
                                                        List<String> finishTimeList = StringUtils.str2List(finishTime,",",true,true);
                                                        if(finishTimeList!=null && finishTimeList.size()>0){
                                                             for(String time : finishTimeList){
                                                                 Date sjTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD,time);
                                                                 finishedTimeMillsThrid.add(sjTime.getTime());
                                                             }
                                                        }
                                                    }
                                                    Date inspectionAndFillConfirmTime =  new Date();
                                                    if(StringUtils.isNotNull(finishTime)){
                                                        if(finishedTimeMillsThrid!=null && finishedTimeMillsThrid.size()>0) {
                                                            Collections.sort(finishedTimeMillsThrid, Collections.reverseOrder());
                                                            inspectionAndFillConfirmTime = new Date(finishedTimeMillsThrid.get(0));
                                                        }
                                                    }
                                                    long days1 = DateUtils.getWorkDays(inspectionAndFillConfirmTime,createTime);
                                                    newObj.put("inspectionAndFillCode",obj.getString("inspectionAndFillCode"));

                                                    if(StringUtils.isNotNull(finishTime)){
                                                        newObj.put("inspectionAndFillConfirmTime",DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD,inspectionAndFillConfirmTime));
                                                    }else{
                                                        newObj.put("inspectionAndFillConfirmTime","");
                                                    }
                                                    newObj.put("inspectionAndFillHs",days1);
                                                }else{
                                                    long days1 = DateUtils.getWorkDays(new Date(),createTime);
                                                    newObj.put("inspectionAndFillHs",days1);
                                                }
                                                projectSjbaDataExport.setYanfaFields(newObj.toString());
                                                break;
                                            }
                                        }
                                        //研发支持
                                        for(TProjectExecution projectExecution : executionList){
                                            Integer step = projectExecution.getStep();
                                            Long executionId = projectExecution.getId();
                                            String mergeKey = projectExecution.getMergeKey();
                                            if(step==2 && "yanfazhichi".equals(mergeKey) && currentExecutionId<executionId){
                                                String auditRemark = projectExecution.getAuditRemark();
                                                Date auditTime = projectExecution.getAuditTime();
                                                Date createTime = projectExecution.getCreateTime();
                                                Integer executionStatus = projectExecution.getStatus();
                                                projectSjbaDataExport.setYanfazhichiSubmitTime(createTime);
                                                projectSjbaDataExport.setYanfazhichiAuditTime(auditTime);
                                                projectSjbaDataExport.setYanfazhichiAuditStatus(executionStatus);
                                                if(StringUtils.isNull(auditTime)){
                                                    auditTime = new Date();
                                                }
                                                long days = DateUtils.getWorkDays(auditTime,createTime);
                                                projectSjbaDataExport.setYanfazhichiHs(days);
                                                String executionFields = projectExecution.getFields();
                                                JSONObject newObj = new JSONObject();
                                                if(StringUtils.isNotNull(executionFields)){
                                                    JSONObject obj = JSONObject.parseObject(executionFields);
                                                    Date sampleSubmitConfirmTime = obj.getDate("sampleSubmitConfirmTime");
                                                    if(StringUtils.isNull(sampleSubmitConfirmTime)){
                                                        sampleSubmitConfirmTime = new Date();
                                                    }
                                                    long days1 = DateUtils.getWorkDays(sampleSubmitConfirmTime,createTime);
                                                    newObj.put("sampleSubmitCode",obj.getString("sampleSubmitCode"));
                                                    String finishTime = obj.getString("inspectionAndFillConfirmTime");
                                                    if(StringUtils.isNotNull(finishTime)){
                                                        newObj.put("sampleSubmitConfirmTime",finishTime);
                                                    }else{
                                                        newObj.put("sampleSubmitConfirmTime","");
                                                    }
                                                    newObj.put("sampleSubmitHs",days1);
                                                }else{
                                                    long days1 = DateUtils.getWorkDays(new Date(),createTime);
                                                    newObj.put("sampleSubmitHs",days1);
                                                }
                                                projectSjbaDataExport.setYanfazhichiFields(newObj.toString());
                                                break;
                                            }
                                        }
                                        for(TProjectExecution projectExecution : executionList){
                                            Integer step = projectExecution.getStep();
                                            Long executionId = projectExecution.getId();
                                            String mergeKey = projectExecution.getMergeKey();
                                            if(step==2 && "shengji".equals(mergeKey) && currentExecutionId<executionId){
                                                String auditRemark = projectExecution.getAuditRemark();
                                                Date auditTime = projectExecution.getAuditTime();
                                                Date createTime = projectExecution.getCreateTime();
                                                Integer executionStatus = projectExecution.getStatus();
                                                projectSjbaDataExport.setShengjiSubmitTime(createTime);
                                                projectSjbaDataExport.setShengjiAuditTime(auditTime);
                                                projectSjbaDataExport.setShengjiAuditStatus(executionStatus);
                                                if(StringUtils.isNull(auditTime)){
                                                    auditTime = new Date();
                                                }
                                                long days = DateUtils.getWorkDays(auditTime,createTime);
                                                projectSjbaDataExport.setShengjiHs(days);
                                                JSONObject newObj = new JSONObject();
                                                String executionFields = projectExecution.getFields();
                                                if(StringUtils.isNotNull(executionFields)){
                                                    JSONObject obj = JSONObject.parseObject(executionFields);
                                                    if(obj!=null){
                                                        String isFeasible = obj.getString("isFeasible");
                                                        String isFillingFeasible = obj.getString("isFillingFeasible");
                                                        String isFeasibleCode = obj.getString("isFeasibleCode");
                                                        String isFillingFeasibleCode = obj.getString("isFillingFeasibleCode");
                                                        String productionProcessCode = obj.getString("productionProcessCode");
                                                        newObj.put("isFeasible",isFeasible);
                                                        newObj.put("isFillingFeasible",isFillingFeasible);
                                                        newObj.put("isFeasibleCode",isFeasibleCode);
                                                        newObj.put("isFillingFeasibleCode",isFillingFeasibleCode);
                                                        newObj.put("productionProcessCode",productionProcessCode);
                                                        Date isFeasibleConfirmTime = obj.getDate("isFeasibleConfirmTime");
                                                        if(StringUtils.isNull(isFeasibleConfirmTime)){
                                                            isFeasibleConfirmTime = new Date();
                                                        }
                                                        long days1 = DateUtils.getWorkDays(isFeasibleConfirmTime,createTime);
                                                        String finishTime1 = obj.getString("isFeasibleConfirmTime");
                                                        if(StringUtils.isNotNull(finishTime1)){
                                                            newObj.put("isFeasibleConfirmTime",finishTime1);
                                                        }else{
                                                            newObj.put("isFeasibleConfirmTime","");
                                                        }
                                                        newObj.put("isFeasibleHs",days1);


                                                        Date isFillingFeasibleConfirmTime = obj.getDate("isFillingFeasibleConfirmTime");
                                                        if(StringUtils.isNull(isFillingFeasibleConfirmTime)){
                                                            isFillingFeasibleConfirmTime = new Date();
                                                        }
                                                        long days2 = DateUtils.getWorkDays(isFillingFeasibleConfirmTime,createTime);
                                                        String finishTime2 = obj.getString("isFillingFeasibleConfirmTime");
                                                        if(StringUtils.isNotNull(finishTime2)){
                                                            newObj.put("isFillingFeasibleConfirmTime",finishTime2);
                                                        }else{
                                                            newObj.put("isFillingFeasibleConfirmTime","");
                                                        }
                                                        newObj.put("isFillingFeasibleHs",days2);

                                                        Date productionProcessConfirmTime = obj.getDate("productionProcessConfirmTime");
                                                        if(StringUtils.isNull(productionProcessConfirmTime)){
                                                            productionProcessConfirmTime = new Date();
                                                        }
                                                        long days3 = DateUtils.getWorkDays(productionProcessConfirmTime,createTime);
                                                        String finishTime3 = obj.getString("productionProcessConfirmTime");
                                                        if(StringUtils.isNotNull(finishTime3)){
                                                            newObj.put("productionProcessConfirmTime",finishTime3);
                                                        }else{
                                                            newObj.put("productionProcessConfirmTime","");
                                                        }
                                                        newObj.put("productionProcessHs",days3);
                                                    }
                                                }else{
                                                    long days1 = DateUtils.getWorkDays(new Date(),createTime);
                                                    newObj.put("isFeasibleHs",days1);
                                                    newObj.put("isFillingFeasibleHs",days1);
                                                    newObj.put("productionProcessHs",days1);
                                                }
                                                projectSjbaDataExport.setShengjiFields(newObj.toString());
                                                break;
                                            }
                                        }
                                    }
                                    //执行
                                    if(StringUtils.isNotNull(currentExecutionId) && currentStatus!=null && currentStatus.intValue()==1){
                                        for(TProjectExecution projectExecution : executionList){
                                            Integer step = projectExecution.getStep();
                                            Long executionId = projectExecution.getId();
                                            if(step==3  && currentExecutionId<executionId){
                                                String auditRemark = projectExecution.getAuditRemark();
                                                Date auditTime = projectExecution.getAuditTime();
                                                Date createTime = projectExecution.getCreateTime();
                                                Integer executionStatus = projectExecution.getStatus();
                                                projectSjbaDataExport.setProcessSubmitTime(createTime);
                                                projectSjbaDataExport.setProcessAuditTime(auditTime);
                                                projectSjbaDataExport.setProcessAuditStatus(executionStatus);
                                                if(StringUtils.isNull(auditTime)){
                                                    auditTime = new Date();
                                                }
                                                long days = DateUtils.getWorkDays(auditTime,createTime);
                                                projectSjbaDataExport.setProcessHs(days);
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                            String orderStatus = projectSjbaDataExport.getStatus();
                            if(!ProjectConstant.JXZ_STATUS.equals(orderStatus)){
                                projectItemOrderMapper.updateProjectItemOrderSync(projectSjbaDataExport);
                            }
                        }
                        if(orderIdList!=null && orderIdList.size()>0){
                            projectItemOrderMapper.deleteProjectSjbaData(orderIdList);
                        }
                        projectItemOrderMapper.batchInsertProjectSjbaData(dataList);
                    }
                }
            }
        }
    }

    @Override
    public void syncProjectExecutionSoftLabNoInfo() {
        List<JSONObject> dataList = projectItemOrderMapper.queryProjectSjbaSecondOrderDataList();
        if(dataList!=null && dataList.size()>0){
            for(JSONObject obj : dataList){
                Long id = obj.getLong("id");
                String fields = obj.getString("fields");
                String projectNo = obj.getString("projectNo");
                String beforeProjectNo = obj.getString("beforeProjectNo");
                if(StringUtils.isNotNull(fields) && StringUtils.isNotNull(id) && StringUtils.isNotNull(projectNo)){
                    Map<String,Object> params_ = new HashMap<String,Object>();
                    params_.put("projectNo",projectNo);
                    if(StringUtils.isNotNull(beforeProjectNo) && beforeProjectNo.indexOf("-")>0 && beforeProjectNo.startsWith("P")){
                        beforeProjectNo = beforeProjectNo.substring(0,beforeProjectNo.lastIndexOf("-"));
                    }
                    params_.put("beforeProjectNo",beforeProjectNo);
                    List<JSONObject> formulaDataList =formulaMapper.queryFormulaBeianDataListNew(params_);
                    StringBuffer labNoBuff = new StringBuffer();
                    StringBuffer baTimeBuff = new StringBuffer();
                    JSONObject dataObj = JSONObject.parseObject(fields);
                    if(dataObj!=null && dataObj.containsKey("sjData")){
                        JSONArray sjDataList = dataObj.getJSONArray("sjData");
                        if(sjDataList!=null && sjDataList.size()>0){
                            boolean isProcess = true;
                            for(int i = 0;i<sjDataList.size();i++){
                                JSONObject sjObj = sjDataList.getJSONObject(i);
                                String sysbh = sjObj.getString("sysbh");
                                boolean isExists = false;
                                if(StringUtils.isNotNull(sysbh)){
                                    for(JSONObject labObj : formulaDataList){
                                        String laboratoryCode  = labObj.getString("laboratoryCode");
                                        String baTime =  labObj.getString("baTime");
                                        if(sysbh.equals(laboratoryCode)){
                                            labNoBuff.append(",").append(laboratoryCode);
                                            baTimeBuff.append(",").append(baTime);
                                            isExists = true;
                                        }
                                    }
                                }
                                if(!isExists){
                                    isProcess = false;
                                }
                            }
                            if(isProcess){  //表示以获取
                                //进行自动确认
                                List<JSONObject> objDataList =projectExecutionMapper.queryProjectSjbaPassExecutionData(id);
                                if(objDataList!=null && objDataList.size()>0){
                                    for(JSONObject objData : objDataList){
                                        Long executionId =  objData.getLong("id");
                                        if(StringUtils.isNotNull(executionId) && labNoBuff!=null && labNoBuff.toString().length()>0){
                                            Map<String,Object> params = new HashMap<String,Object>();
                                            params.put("executionId",executionId);
                                            JSONObject executionObj = new JSONObject();
                                            executionObj.put("inspectionAndFillCode",labNoBuff.toString().substring(1));
                                            executionObj.put("inspectionAndFillConfirmTime",baTimeBuff.toString().substring(1));
                                            params.put("fields",executionObj.toString());
                                            params.put("userId",1);
                                            projectExecutionMapper.updateProjectExecutionSjbaInfo(params);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public void passProjectSjbaExecutionData() {
        List<JSONObject> dataList = projectItemOrderMapper.queryProjectSjbaSecondOrderDataList();
        if(dataList!=null && dataList.size()>0) {
            for (JSONObject data : dataList) {
                Long projectOrderId = data.getLong("id");
                List<TProjectExecution> executionList = projectExecutionMapper.queryProjectExecutionSjbaDataList(projectOrderId);
                if(executionList!=null && executionList.size()>0){
                    boolean isProcessFinished = true;
                    JSONArray array = new JSONArray();
                    for(TProjectExecution execution : executionList){
                        //审核通过
                        if(StringUtils.isNotNull(execution.getStatus()) && execution.getStatus().intValue()==1){
                            JSONObject obj = new JSONObject();
                            obj.put("id",execution.getId());
                            obj.put("status",execution.getStatus());
                            obj.put("isMerge",execution.getIsMerge());
                            obj.put("fields",execution.getFields());
                            obj.put("mergeKey",execution.getMergeKey());
                            obj.put("deptName",execution.getDeptName());
                            obj.put("auditRemark",execution.getAuditRemark());
                            obj.put("auditTime",DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD,execution.getAuditTime()));
                            array.add(obj);
                        }else{
                            isProcessFinished = false;
                            String mergeKey = execution.getMergeKey();
                            String fields = execution.getFields();
                            if(ProjectConstant.PROJECT_SJBA_SHENGJI.equals(mergeKey) && StringUtils.isNotNull(fields)){
//                                JSONObject obj  = JSONObject.parseObject(fields);
//                                if(obj!=null && obj.containsKey("isFeasible")){
//                                    String isFeasible = obj.getString("isFeasible");
//                                    String isFeasibleCode = obj.getString("isFeasibleCode");
//                                    String isFeasibleConfirmTime = obj.getString("isFeasibleConfirmTime");
//                                    //&& StringUtils.isNotNull(isFeasibleCode)
//                                    //&& StringUtils.isNotNull(isFeasibleConfirmTime)
//                                    if(StringUtils.isNotNull(isFeasible)){
//                                        if("0".equals(isFeasible)){
//
//                                        }else{
//                                            isProcessFinished = false;
//                                        }
//                                    }else{
//                                        isProcessFinished = false;
//                                    }
//                                }else{
//                                    isProcessFinished = false;
//                                }
                                //2023.04.19 去除配置可行性限制执行
                            }else if(ProjectConstant.PROJECT_SJBA_YANFAZHICHI.equals(mergeKey)){
                                 //2023.04.17 更改为配置可行性OK  研发释放 既到送检备案执行步骤

                            }else{
                                //isProcessFinished = false;
                            }
                        }
                    }
                    //更新订单字段数据
                    ProjectItemOrder projectItemOrderInfo = new ProjectItemOrder();
                    projectItemOrderInfo.setId(projectOrderId);
                    projectItemOrderInfo.setMergeFields(array.toString());
                    if(isProcessFinished){
                        projectItemOrderInfo.setShStatus1(1);
                    }
                    //projectItemOrderMapper.updateProjectItemOrderMergeOrderFields(projectItemOrderInfo);
                    if(isProcessFinished){  //审核完成进行下一步操作
                        TProjectExecution tProjectExecution = new TProjectExecution();
                        tProjectExecution.setProjectItemCode(ProjectConstant.PROJECT_INSPECTION_AND_FILING);
                        tProjectExecution.setProjectOrderId(projectOrderId);
                        tProjectExecution.setStep(2);
                        tProjectExecution.setStatus(1);
                        tProjectExecution.setProcessUserId(1L);
                        tProjectExecution.setId(-1L);
                        tProjectExecution.setIsProcess("是");  //表示已操作
                        projectExecutionService.updateTProjectExecution(tProjectExecution);
                    }
                }
            }
        }
    }

    @Override
    @Transactional
    public void saveBcReply(ProjectItemOrder projectItemOrder) {
        String status = projectItemOrder.getStatus();
        projectItemOrder.setUpdateBy(SecurityUtils.getUsername());
        projectItemOrder.setUpdateTime(DateUtils.getNowDate());
        projectItemOrder.setBcReplyTime(DateUtils.getNowDate());
        projectItemOrder.setBcReplyUser(SecurityUtils.getUsername());

        List<ProjectOrderBcReply> bcList = projectItemOrder.getProjectOrderBcReplyList();
        if(CollUtil.isNotEmpty(bcList)) {
            projectBcService.saveOrderBcReplyList(projectItemOrder,bcList);
        }


        String projectItemType = projectItemOrder.getProjectItemType();
        if(ProjectConstant.CHP_BJ_APPLY.equals(projectItemType)){
            Map<String,Object> params = new HashMap<String,Object>();
            params.put("itemOrderId",projectItemOrder.getId());
            params.put("mergeKey","packagingMaterialDevelopment");
            //获取执行流程操作
            TProjectExecution execution = projectItemOrderMapper.queryProjectItemOrderExecutionDataInfo(params);
            if(execution!=null && StringUtils.isNotNull(execution.getId())){
                //审核确认操作
                TProjectExecution projectExecution = new TProjectExecution();
                projectExecution.setId(execution.getId());
                projectExecution.setProjectOrderId(projectItemOrder.getId());
                projectExecution.setProjectItemCode(ProjectConstant.CHP_BJ_APPLY);
                projectExecution.setCurrentStep("复审");
                projectExecution.setItemType("PROJECT_OFFER");
                projectExecution.setMergeKey(params.get("mergeKey").toString());
                projectExecution.setStep(2);
                projectExecution.setConfirmCode(execution.getItemOrderCode());
                projectExecution.setStatus(Integer.valueOf(status));
                projectExecution.setAuditRemark(projectItemOrder.getAuditRemark());
                projectExecution.setSjTime(DateUtils.getNowDate());
                JSONObject fields = new JSONObject();
                fields.put("files","");
                fields.put("filesChushen","");
                fields.put("filesFushen",projectItemOrder.getFilesFushen());
                fields.put("filesProcess","");
                fields.put("filesApply","");
                fields.put("imgs",projectItemOrder.getImgs());

                projectExecution.setFields(fields.toJSONString());

                projectItemOrder.setYjTime(DateUtils.addDays(DateUtils.getNowDate(),1));
                projectItemOrder.setFirstYjTime(DateUtils.addDays(DateUtils.getNowDate(),1));
                tProjectExecutionService.updateTProjectExecution(projectExecution);

                new Thread(){
                    public void run(){
                        try {
                            Thread.sleep(10000);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        //更新价格
                        hrCostUtil.generateProjectOfferGrossProfit(execution.getItemOrderCode());
                    }
                }.start();
            }else{
                //审核确认操作
                TProjectExecution projectExecution = new TProjectExecution();
                projectExecution.setId(projectItemOrder.getExecutionId());
                projectExecution.setConfirmCode(projectItemOrder.getArchiveCode());
                projectExecution.setAuditRemark(projectItemOrder.getAuditRemark());
                JSONObject fields = new JSONObject();
                fields.put("files","");
                fields.put("filesChushen","");
                fields.put("filesFushen",projectItemOrder.getFilesFushen());
                fields.put("filesProcess","");
                fields.put("filesApply","");
                fields.put("imgs",projectItemOrder.getImgs());
                projectExecution.setFields(fields.toJSONString());
                projectExecutionMapper.updateTProjectExecution(projectExecution);
                new Thread(){
                    public void run(){
                        try {
                            Thread.sleep(10000);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        //更新价格
                        hrCostUtil.generateProjectOfferGrossProfit(projectItemOrder.getCode());
                    }
                }.start();
            }
        }else if(ProjectConstant.RQ_BC_APPLY.equals(projectItemType)){
            Map<String,Object> params = new HashMap<String,Object>();
            params.put("itemOrderId",projectItemOrder.getId());
            params.put("step",2);
            //获取执行流程操作
            TProjectExecution execution = projectItemOrderMapper.queryProjectItemOrderExecutionOtherDataInfo(params);
            if(execution!=null && StringUtils.isNotNull(execution.getId())){
                //审核确认操作
                TProjectExecution projectExecution = new TProjectExecution();
                projectExecution.setId(execution.getId());
                projectExecution.setProjectOrderId(projectItemOrder.getId());
                projectExecution.setProjectItemCode(ProjectConstant.RQ_BC_APPLY);
                projectExecution.setCurrentStep("复审");
                projectExecution.setItemType("PROJECT_OFFER");
                projectExecution.setStep(2);
                projectExecution.setConfirmCode(execution.getItemOrderCode());
                projectExecution.setStatus(Integer.valueOf(status));
                projectExecution.setAuditRemark(projectItemOrder.getAuditRemark());
                projectExecution.setSjTime(DateUtils.getNowDate());
                JSONObject fields = new JSONObject();
                fields.put("files","");
                fields.put("filesChushen","");
                fields.put("filesFushen",projectItemOrder.getFilesFushen());
                fields.put("filesProcess","");
                fields.put("filesApply","");
                fields.put("imgs",projectItemOrder.getImgs());

                projectExecution.setFields(fields.toJSONString());

                projectItemOrder.setYjTime(DateUtils.addDays(DateUtils.getNowDate(),1));
                projectItemOrder.setFirstYjTime(DateUtils.addDays(DateUtils.getNowDate(),1));
                tProjectExecutionService.updateTProjectExecution(projectExecution);
            }
        }
        projectItemOrder.setStatus(null);
        projectItemOrder.setImgs(null);
        projectItemOrderMapper.updateProjectItemOrder(projectItemOrder);
    }

    @Override
    @Transactional
    public void saveGongyiReply(ProjectItemOrder projectItemOrder) {
        String status = projectItemOrder.getStatus();

        projectItemOrder.setUpdateBy(SecurityUtils.getUsername());
        projectItemOrder.setUpdateTime(DateUtils.getNowDate());
        projectItemOrder.setGongyiReplyTime(DateUtils.getNowDate());
        projectItemOrder.setGongyiReplyUser(SecurityUtils.getUsername());
        Map<String,Object> params = new HashMap<String,Object>();
        params.put("itemOrderId",projectItemOrder.getId());
        params.put("mergeKey","technology");
        TProjectExecution execution = projectItemOrderMapper.queryProjectItemOrderExecutionDataInfo(params);
        if(execution!=null && StringUtils.isNotNull(execution.getId())){
            //审核确认操作
            TProjectExecution projectExecution = new TProjectExecution();
            projectExecution.setId(execution.getId());
            projectExecution.setProjectOrderId(projectItemOrder.getId());
            projectExecution.setProjectItemCode("PROJECT_CHPBJ");
            projectExecution.setCurrentStep("复审");
            projectExecution.setItemType("PROJECT_OFFER");
            projectExecution.setMergeKey(params.get("mergeKey").toString());
            projectExecution.setStep(2);
            projectExecution.setConfirmCode(projectItemOrder.getArchiveCode());
            projectExecution.setStatus(Integer.valueOf(status));
            projectExecution.setAuditRemark(projectItemOrder.getAuditRemark());

            JSONObject fields = new JSONObject();
            fields.put("files","");
            fields.put("filesChushen","");
            fields.put("filesFushen",projectItemOrder.getFilesFushen());
            fields.put("filesProcess","");
            fields.put("filesApply","");
            fields.put("imgs",projectItemOrder.getImgs());

            projectExecution.setFields(fields.toJSONString());
            projectExecution.setSjTime(DateUtils.getNowDate());
            projectItemOrder.setYjTime(DateUtils.addDays(DateUtils.getNowDate(),1));
            projectItemOrder.setFirstYjTime(DateUtils.addDays(DateUtils.getNowDate(),1));
            tProjectExecutionService.updateTProjectExecution(projectExecution);
            new Thread(){
                public void run(){
                    try {
                        Thread.sleep(10000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    //更新价格
                    hrCostUtil.generateProjectOfferGrossProfit(execution.getItemOrderCode());
                }
            }.start();
        }else{
            //审核确认操作
            TProjectExecution projectExecution = new TProjectExecution();
            projectExecution.setId(projectItemOrder.getExecutionId());
            projectExecution.setConfirmCode(projectItemOrder.getArchiveCode());
            projectExecution.setAuditRemark(projectItemOrder.getAuditRemark());
            JSONObject fields = new JSONObject();
            fields.put("files","");
            fields.put("filesChushen","");
            fields.put("filesFushen",projectItemOrder.getFilesFushen());
            fields.put("filesProcess","");
            fields.put("filesApply","");
            fields.put("imgs",projectItemOrder.getImgs());
            projectExecution.setFields(fields.toJSONString());
            projectExecutionMapper.updateTProjectExecution(projectExecution);
            new Thread(){
                public void run(){
                    try {
                        Thread.sleep(10000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    //更新价格
                    hrCostUtil.generateProjectOfferGrossProfit(projectItemOrder.getCode());
                }
            }.start();
        }
        projectItemOrder.setStatus(null);
        projectItemOrder.setImgs(null);
        projectItemOrderMapper.updateProjectItemOrder(projectItemOrder);
    }

    @Override
    public List<ProjectItemOrder> queryProjectItemOrderOfferDataList(ProjectItemOrder projectItemOrder) {
        List<ProjectItemOrder> dataList = projectItemOrderMapper.queryProjectItemOrderOfferDataList(projectItemOrder);
        if(dataList!=null && dataList.size()>0){
            for(ProjectItemOrder orderInfo  : dataList){
                Long projectOrderId = orderInfo.getId();
                if(StringUtils.isNotNull(projectOrderId)){
                    TProjectExecution params = new TProjectExecution();
                    params.setProcessUserId(SecurityUtils.getUserId());
                    params.setProjectOrderId(projectOrderId);
                    List<TProjectExecution> executionDataList = projectExecutionMapper.queryProjectExecutionOrderInfo(params);
                    TProjectExecution dataObj = ProjectExecutionUtil.processProjectExecution(executionDataList);
                    if(dataObj!=null){
                        orderInfo.setExecutionStatus(dataObj.getStatus());
                        orderInfo.setExecutionId(dataObj.getId());
                        orderInfo.setIsAudit(dataObj.getIsAudit());
                        orderInfo.setAuditTime(dataObj.getAuditTime());
                    }
                }
            }
        }
        return dataList;
    }

    @Override
    public List<ProjectOfferOrder> queryProjectOfferFormulaDataList(Long id) {
        List<ProjectOfferOrder> dataList = projectItemOrderMapper.queryProjectOfferFormulaDataList(id);
        if(dataList!=null && dataList.size()>0){
           for(ProjectOfferOrder projectOfferOrder : dataList){
               processProjeftOfferOrder(projectOfferOrder);
           }
        }
        return dataList;
    }
    @Override
    public List<ProjectOfferOrder> queryProjectOfferChangeRecordDataList(Long id) {
        List<ProjectOfferOrder> dataList = projectItemOrderMapper.queryProjectOfferChangeRecordDataList(id);
        if(dataList!=null && dataList.size()>0){
           for(ProjectOfferOrder projectOfferOrder : dataList){
               processProjeftOfferOrder(projectOfferOrder);
           }
        }
        return dataList;
    }
    @Override
    public List<ProjectOfferOrder> queryMultiProjectOfferChangeRecordDataList(ProjectOfferOrder params) {
        List<ProjectOfferOrder> dataList = projectItemOrderMapper.queryMultiProjectOfferChangeRecordDataList(params);
        if(dataList!=null && dataList.size()>0){
           for(ProjectOfferOrder projectOfferOrder : dataList){
               processProjeftOfferOrder(projectOfferOrder);
           }
        }
        return dataList;
    }
    @Override
    public List<ProjectOfferOrder> queryMultiProjectOfferFormulaDetailsDataList(ProjectOfferOrder params) {
        List<ProjectOfferOrder> dataList = projectItemOrderMapper.queryMultiProjectOfferFormulaDetailsDataList(params);
        if(dataList!=null && dataList.size()>0){
           for(ProjectOfferOrder projectOfferOrder : dataList){
               processProjeftOfferOrder(projectOfferOrder);
           }
        }
        return dataList;
    }
    @Override
    public List<ProjectOfferOrder> queryProjectOfferFormulaDataDetailsList(Long id) {
        List<ProjectOfferOrder> dataList = projectItemOrderMapper.queryProjectOfferFormulaDataDetailsList(id);
        if(dataList!=null && dataList.size()>0){
           for(ProjectOfferOrder projectOfferOrder : dataList){
               processProjeftOfferOrder(projectOfferOrder);
           }
        }
        return dataList;
    }

    @Override
    public List<ProjectBc> queryProjectOfferPackagingMaterialDataList(Long id) {
        List<ProjectBc> dataList = projectItemOrderMapper.queryProjectOfferPackagingMaterialDataList(id);
        if(dataList!=null && dataList.size()>0){
            for(ProjectBc projectBc : dataList){
                String attribute =projectBc.getAttribute();
                String opType = projectBc.getOpType();
                if(ErpDataReqUtil.isCustomer(attribute,opType)){
                    projectBc.setPrice(WagesUtils.bigDecimal("0"));
                    projectBc.setLossNum1(WagesUtils.bigDecimal("0"));
                    projectBc.setLossNum2(WagesUtils.bigDecimal("0"));
                }else{
                    //获取标准损耗率
                    String type = projectBc.getType();  //包材类别
                    String zrqType = projectBc.getZrqType();  //主包类别
                    String searchType = "";
                    if("0".equals(type)){  //主包材
                        searchType = DictUtils.getDictLabel("bc-zrq",zrqType);
                    }else{  //包材类别
                        searchType = DictUtils.getDictLabel("project_bc_type",type);
                    }
                    if(StringUtils.isNotNull(searchType)){
                        OrderStandardLoss params = new OrderStandardLoss();
                        params.setType("104");
                        params.setProjectNameType(searchType);
                        List<OrderStandardLoss> orderStandardLossList = orderStandardLossMapper.selectOrderStandardLossList(params);
                        if(orderStandardLossList!=null && orderStandardLossList.size()>0){
                            Collections.sort(orderStandardLossList);
                            OrderStandardLoss orderStandardLoss = orderStandardLossList.get(0);
                            projectBc.setLossNum1(orderStandardLoss.getLossNum1());
                            projectBc.setLossNum2(orderStandardLoss.getLossNum2());
                        }
                    }
                }
            }
        }
        return dataList;
    }

    @Override
    public JSONObject queryFormulaProductName(ProjectItemOrder projectItemOrder) {
        if(StringUtils.isNotNull(projectItemOrder.getConfirmCode())){
            projectItemOrder.setConfirmCode(projectItemOrder.getConfirmCode());
        }
        JSONObject obj = softwareDevelopingFormulaMapper.queryFormulaProductName(projectItemOrder);
        if(obj!=null){
            String productName = obj.getString("productName");
            String itemNameText = obj.getString("itemNameText");
            if(StringUtils.isNotNull(itemNameText)){
                productName = productName + "("+itemNameText+")";
            }
            obj.put("productName",productName);
        }
        return obj;
    }

    @Override
    public JSONObject getMultiPackagingMaterialDatas(ProjectOfferOrder projectOfferOrder) {
        JSONObject returnObj = new JSONObject();
        ProjectOfferOrder dataResult = projectItemOrderMapper.queryProjectOfferPackagingMaterialDataObj(projectOfferOrder);
        boolean isProcess = true;
        Double reqNum = null;
        BigDecimal spddrql= projectOfferOrder.getOrderNum();
        String productTypes = "";
        if(StringUtils.isNotNull(spddrql)){
            reqNum = spddrql.doubleValue();
        }
        Set<Integer> zrqTypeSet = new HashSet<Integer>();
        Set<Integer> fpqTypeSet = new HashSet<Integer>();
        Set<String> bcIdList = new HashSet<String>();
        String formulaDataList = dataResult.getFormulaDataList();
        if(StringUtils.isNotNull(formulaDataList)){
            JSONArray formulaDataArray = JSONArray.parseArray(formulaDataList);
            for(int i = 0;i<formulaDataArray.size();i++){
                JSONObject formulaObj = formulaDataArray.getJSONObject(i);
                if(formulaObj!=null && formulaObj.containsKey("mainBc")){
                    JSONArray mainBcArray = formulaObj.getJSONArray("mainBc");
                    if(mainBcArray!=null && mainBcArray.size()>0){
                        for(int k = 0;k<mainBcArray.size();k++){
                            JSONObject bcObj = mainBcArray.getJSONObject(k);
                            String allocator = bcObj.getString("allocator");
                            Integer zrqType = bcObj.getInteger("zrqType");
                            String id = bcObj.getString("id");
                            if(StringUtils.isNotNull(allocator) && ErpDataReqUtil.isInteger(allocator) && Double.valueOf(allocator)>0){
                                fpqTypeSet.add(Integer.valueOf(allocator));
                            }else if(StringUtils.isNotNull(zrqType)){
                                zrqTypeSet.add(zrqType);
                            }
                            bcIdList.add(id);
                        }
                    }
                }
            }
        }
        JSONArray bcArray = new JSONArray();
        if(dataResult!=null && StringUtils.isNotNull(dataResult.getPackagingMaterialDatas())){
            String packagingMaterialDatas = dataResult.getPackagingMaterialDatas();
            if(JSONArray.isValidArray(packagingMaterialDatas)){
                ProjectItemOrder projectItemOrderInfo =  projectItemOrderMapper.selectProjectItemOrderProductTabsById(projectOfferOrder.getProjectOrderId());
                if(projectItemOrderInfo!=null && StringUtils.isNotNull(projectItemOrderInfo.getProductType())) {
                    productTypes = projectItemOrderInfo.getProductType();
                }
                bcArray = JSONArray.parseArray(packagingMaterialDatas);
                isProcess = false;
            }
        }
        //判断烟包膜 中包 外箱
        ProjectOfferBcPrice projectOfferBcPrice = projectOfferBcPriceMapper.queryProjectOfferBcPriceDataInfo();
        boolean isBcPrice = true;
//        if(projectOfferBcPrice!=null && StringUtils.isNotNull(projectOfferBcPrice.getId())){
//            isBcPrice = true;
//        }
        String productCode = projectOfferOrder.getProductCode();

        if(isProcess && StringUtils.isNotNull(productCode)){
           ProjectItemOrder projectItemOrderInfo =  projectItemOrderMapper.selectProjectItemOrderProductTabsById(projectOfferOrder.getProjectOrderId());
           if(projectItemOrderInfo!=null && StringUtils.isNotNull(projectItemOrderInfo.getProductTabs())){
               productTypes = projectItemOrderInfo.getProductType();
               String productTabs = projectItemOrderInfo.getProductTabs();
               if(JSONArray.isValidArray(productTabs)){
                   JSONArray productArray = JSONArray.parseArray(productTabs);
                   if(productArray!=null && productArray.size()>0){
                       for(int i = 0;i<productArray.size();i++){
                           JSONObject productObj = productArray.getJSONObject(i);
                           String md003 = productObj.getString("id");
                           if(productCode.equals(md003)){
                               bcArray = productObj.getJSONArray("bcArray");
                           }
                       }
                   }
               }
           }
        }
        boolean isZb = false;
        boolean isWx = false;
        for(int i = 0;i<bcArray.size();i++){
            JSONObject obj = bcArray.getJSONObject(i);
            String type = obj.getString("type");
            if("2".equals(type)){
                isZb = true;
            }
            if("3".equals(type)){
                isWx = true;
            }
        }
        if(isProcess && bcArray!=null && bcArray.size()>0){
            for(int j = 0;j<bcArray.size();j++){
                JSONObject obj = bcArray.getJSONObject(j);
                String attribute = obj.getString("attribute");
                String mb008 = obj.getString("mb008");
                if(StringUtils.isNotNull(mb008)){
                    attribute = mb008;
                }else{
                    attribute = ErpDataReqUtil.getAttribute(attribute);
                }
                String opType = obj.getString("opType");
                String labNo = obj.getString("code");
                String lossPrice = obj.getString("lossPrice");
                String dosage = obj.getString("consumption");
                String base = obj.getString("md007");
                //String orderPrice_ = obj.getString("orderPrice");
                String isAdd = obj.getString("isAdd");
                isAdd = StringUtils.convertDefaultValue(isAdd,"0");
                obj.put("projectOrderId",projectOfferOrder.getProjectOrderId());
                JSONObject packagingMaterialPriceObj = projectMapper.queryProjectOfferPackagingPriceInfo(obj);
                String price = "";
                String orderPrice = "";
                String priceArray = "";
                String files = "";
                String priceDesc = "";
                if(packagingMaterialPriceObj!=null){
                    price = packagingMaterialPriceObj.getString("price");
                    priceArray= packagingMaterialPriceObj.getString("priceArray");
                    String type= packagingMaterialPriceObj.getString("type");
                    String zrqType= packagingMaterialPriceObj.getString("zrqType");
                    obj.put("type",type);
                    obj.put("zrqType",zrqType);
                    files= packagingMaterialPriceObj.getString("files");
                    priceDesc= packagingMaterialPriceObj.getString("priceDesc");
                }
                BigDecimal bomNum = WagesUtils.bigDecimal("1");
                BigDecimal floatLossNum = WagesUtils.bigDecimal("3");
                if(!ErpDataReqUtil.isCustomer(attribute,opType)){
                    if(StringUtils.isNotNull(dosage) && StringUtils.isNotNull(base)
                            && ErpDataReqUtil.isInteger(dosage)
                            && ErpDataReqUtil.isInteger(base)){
                        bomNum = WagesUtils.divide(WagesUtils.bigDecimal(dosage,6),WagesUtils.bigDecimal(base,6),6,BigDecimal.ROUND_HALF_UP);
                    }
                    Map<String,Object> params = new HashMap<String,Object>();
                    String type = obj.getString("type");
                    String zrqType = obj.getString("zrqType");
                    String name = obj.getString("name");
                    if(StringUtils.isNotNull(obj.getString("type"))){
                        //获取损耗数据
                        if("0".equals(type)){
                            zrqType = StringUtils.convertDefaultValue(zrqType,"-1");
                            params.put("zrqType",zrqType);
                        }else{
                            params.put("type",type);
                        }
                        params.put("orderNum",WagesUtils.bigDecimal(projectOfferOrder.getOrderNum()).multiply(bomNum));
                        ProjectOfferBcLossItem projectOfferBcLossItem =  projectOfferBcLossMapper.queryProjectOfferBcLossItemDataInfo(params);
                        if(projectOfferBcLossItem!=null && projectOfferBcLossItem.getFloatLossNum()!=null && projectOfferBcLossItem.getFloatLossNum().doubleValue()>0 && StringUtils.isNotNull(projectOfferBcLossItem.getId())){
                            floatLossNum =  projectOfferBcLossItem.getFloatLossNum();
                        }
                    }
                    if(StringUtils.isNotNull(price) && ErpDataReqUtil.isInteger(price)){
                        if(bomNum!=null && bomNum.doubleValue()>0 && ErpDataReqUtil.isInteger(price)){
                            price = bomNum.multiply(WagesUtils.bigDecimal(price,6)).toPlainString();
                        }
//                        if("2".equals(type) || "3".equals(type) || "10".equals(type)){  //中包 外箱  烟包膜/收缩膜
//                            boolean isFlag = true;
//                            JSONObject priceObj = ProjectOfferUtil.getBcPriceNewObj(productTypes,type,name,isZb,isWx,base);
//                            if(priceObj!=null && priceObj.containsKey("price")){
//                                BigDecimal returnPrice = priceObj.getBigDecimal("price");
//                                price = returnPrice.toPlainString();
//                            }
//                        }
                    }else{
                        if("2".equals(type) || "3".equals(type) || "10".equals(type)){  //中包 外箱  烟包膜/收缩膜
                            boolean isFlag = true;
                            JSONObject priceObj = ProjectOfferUtil.getBcPriceNewObj(productTypes,type,name,isZb,isWx,base);
                            if(priceObj!=null && priceObj.containsKey("price")){
                                BigDecimal returnPrice = priceObj.getBigDecimal("price");
                                if(StringUtils.isNotNull(returnPrice) && returnPrice.doubleValue()>0){
                                    price = returnPrice.toPlainString();
                                }
                            }
                        }
                    }
                    if(StringUtils.isNotNull(price) && StringUtils.isNotNull(floatLossNum) && floatLossNum.doubleValue()>0){
                        BigDecimal lossRate = floatLossNum.divide(WagesUtils.bigDecimal("100"));
                        lossPrice = WagesUtils.bigDecimal(WagesUtils.bigDecimal(price,6).multiply(lossRate),6).toPlainString();
                    }
                    BigDecimal totalNum = WagesUtils.bigDecimal("0");
                    if(StringUtils.isNotNull(bomNum) && StringUtils.isNotNull(reqNum)){
                        totalNum = bomNum.multiply(WagesUtils.bigDecimal(reqNum));
                    }
                    if(StringUtils.isNotNull(priceArray)){
                        JSONArray array = JSONArray.parseArray(priceArray);
                        if(array!=null && array.size()>0){
                            JSONObject priceObj = ProjectExecutionUtil.queryPackagingMaterialPrice(array,totalNum,bomNum);
                            if(priceObj!=null){
                                orderPrice = priceObj.getString("orderPrice");
                            }
                        }
                    }
                }else{
                    price = "0";
                    orderPrice = "0";
                }
                Integer isMulti = 0;
                if(isWx && isZb){
                    isMulti = 2;
                }else if(isWx){
                    isMulti = 1;
                }
                obj.put("price",price);
                obj.put("orderPrice",orderPrice);
                obj.put("lossRate",floatLossNum);
                obj.put("lossPrice",lossPrice);
                obj.put("priceArray",priceArray);
                obj.put("files",files);
                obj.put("priceDesc",priceDesc);
                obj.put("isMulti",isMulti);
            }
        }
        String packagingMaterialDatasType = dataResult.getPackagingMaterialDatasType();
        JSONArray bcTypePrice = ProjectExecutionUtil.queryBcTypePrice(bcArray,"0",packagingMaterialDatasType,bcIdList,projectOfferOrder.getFromType());
        JSONArray bcTypesData = ProjectExecutionUtil.queryBcTypePriceNew(bcArray,"0",packagingMaterialDatasType,zrqTypeSet,fpqTypeSet,bcIdList);
        returnObj.put("packagingMaterialDatas",bcArray);
        returnObj.put("bcTypePrice",bcTypePrice);
        returnObj.put("bcTypesData",bcTypesData);
        return returnObj;
    }

    @Override
    public JSONObject processPackagingMaterialTypePriceInfo(ProjectOfferOrder projectOfferOrder) {
        JSONObject returnObj = new JSONObject();
        if(projectOfferOrder!=null && StringUtils.isNotNull(projectOfferOrder.getPackagingMaterialDatas())){
            String packagingMaterialDatas = projectOfferOrder.getPackagingMaterialDatas();
            if(JSONArray.isValidArray(packagingMaterialDatas)){
                ProjectOfferOrder dataResult = projectItemOrderMapper.queryProjectOfferPackagingMaterialDataObj(projectOfferOrder);
                JSONArray array = JSONArray.parseArray(packagingMaterialDatas);
                JSONArray bcTypePrice = ProjectExecutionUtil.queryBcTypePrice(array,"1",dataResult.getPackagingMaterialDatasType(),new HashSet<String>(),"2");
                returnObj.put("bcTypePrice",bcTypePrice);
            }
        }
        return returnObj;
    }

    @Transactional
    @Override
    public void deleteProjectItemOrderByIds(Long[] ids) {
        projectItemOrderMapper.deleteProjectItemOrderByIds(ids);
        for (Long id : ids) {
            ProjectItemOrder order = projectItemOrderMapper.selectProjectItemOrderById(id);
            productService.updateProductDkNums(order.getItemName(),order.getProjectId());
        }
    }

    /**
     * 获取打样单信息
     * @param projectOrderId
     * @return
     */
    @Override
    public JSONObject autoScheduleSampleOrder(Long projectOrderId,Integer projectType,Long userId) {
        JSONObject returnObj = new JSONObject();
        returnObj.put("status","1");
        try {
            projectType = StringUtils.convertDefaultValue(projectType,0);
            JSONObject dataObj = new JSONObject();
            dataObj.put("projectType",projectType);
            if(projectType.intValue()==1){
                dataObj = projectItemOrderMapper.queryAutoScheduleSampleBeforeOrder(projectOrderId);
            }else if(projectType.intValue()==2){
                dataObj = projectItemOrderMapper.queryAutoScheduleSampleOrderSoftware(projectOrderId);
            }else{
                dataObj = projectItemOrderMapper.queryAutoScheduleSampleOrder(projectOrderId);
            }
            //保存成本数据
            JSONObject costObj = new JSONObject();
            if (dataObj != null) {
                returnObj.put("status","2");
                String fields = dataObj.getString("fields");
                if(StringUtils.isNotNull(fields)){
                    JSONObject fieldsObj = JSONObject.parseObject(fields);
                    if(fieldsObj!=null && fieldsObj.containsKey("dylb")){
                        String dylb = fieldsObj.getString("dylb");
                        dataObj.put("dylb",dylb);
                        String dylbLabel = DictUtils.getDictLabel("project_nrw_dylb",dylb);
                        dataObj.put("dylbLabel",dylbLabel);
                    }
                    if(fieldsObj!=null && fieldsObj.containsKey("nrwObj")){
                        JSONObject nrwObj = fieldsObj.getJSONObject("nrwObj");
                        if(nrwObj!=null && nrwObj.containsKey("pfPrice")){
                            String pfPrice = nrwObj.getString("pfPrice");
                            if(StringUtils.isNotNull(pfPrice) && ErpDataReqUtil.isInteger(pfPrice)){
                                costObj.put("pfPrice", WagesUtils.bigDecimal(pfPrice,6));
                            }
                        }
                    }
                }
                //获取价格
                String productType = dataObj.getString("productType");
                if(StringUtils.isNotNull(productType)){
                    List<Integer> category = StringUtils.stringToIntegerList(productType);
                    dataObj.put("categoryId",category.get(category.size()-1));
                }
                String serviceMode = dataObj.getString("serviceMode");
                serviceMode = StringUtils.convertDefaultValue(serviceMode,"BVIP");
                String checkType = "2";
                if(StringUtils.isNotNull(serviceMode)){
                    checkType = "1";
                }
                BigDecimal days = projectDydUtil.getDays(checkType,serviceMode,dataObj.getString("dylb"),projectOrderId);
                dataObj.put("days",days);
                dataObj.put("checkType",checkType);
                dataObj.put("userId",userId);
                dataObj.put("projectOrderId",projectOrderId);


                String dylx = "0";
                String preFieldsObj =  dataObj.getString("itemFields");
                if(StringUtils.isNotNull(preFieldsObj)){
                    JSONObject fieldsObj = JSONObject.parseObject(preFieldsObj);
                    if(fieldsObj!=null && fieldsObj.containsKey("dylx")){
                        String  dylxVal = fieldsObj.getString("dylx");
                        if(StringUtils.isNotNull(dylxVal)){
                            dylx = dylxVal;
                        }
                    }
                }
                String dylbVal = dataObj.getString("dylb");
                if("1".equals(dylx)){
                    dylbVal = "0";
                }
                // 获取标准工时
                BigDecimal standardHours = categoryService.selectSampleTimeByCategoryAndLevel(dataObj.getLong("categoryId"), dylbVal);
                dataObj.put("standardHours",standardHours);
                dataObj.put("dylx",dylx);
                returnObj = flowTools.autoScheduleSampleOrder(dataObj);
                final JSONObject dataObjNew = dataObj;
                //处理订单 价格信息
                new Thread(){
                    public void run(){
                        try {
                            Thread.sleep(6000);
                            processAutoOrderPriceInfo(costObj,dataObjNew,projectOrderId,days);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                }.start();
            }else{
                returnObj.put("msg","打样单未找到!");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return returnObj;
    }


    /**
     * 获取打样单信息
     * @param projectItemOrder
     * @return
     */
    @Override
    public JSONObject autoScheduleSampleOrder(ProjectItemOrder projectItemOrder) {
        JSONObject returnObj = new JSONObject();
        returnObj.put("status","1");
        try {
            Integer projectType = projectItemOrder.getProjectType();
            Long projectOrderId = projectItemOrder.getId();
            projectType = StringUtils.convertDefaultValue(projectType,0);
            JSONObject dataObj = new JSONObject();
            dataObj.put("projectType",projectType);
            if(projectType.intValue()==1){
                dataObj = projectItemOrderMapper.queryAutoScheduleSampleBeforeOrder(projectOrderId);
            }else if(projectType.intValue()==2){
                dataObj = projectItemOrderMapper.queryAutoScheduleSampleOrderSoftware(projectOrderId);
            }else{
                dataObj = projectItemOrderMapper.queryAutoScheduleSampleOrder(projectOrderId);
            }
            dataObj.put("fields",projectItemOrder.getFields());
            dataObj.put("productType",projectItemOrder.getCategoryText());
            dataObj.put("code",projectItemOrder.getCode());
            dataObj.put("projectOrderId",projectOrderId);
            dataObj.put("remark",projectItemOrder.getRemark());
             //保存成本数据
            JSONObject costObj = new JSONObject();
            if (dataObj != null) {
                returnObj.put("status","2");
                String fields = dataObj.getString("fields");
                if(StringUtils.isNotNull(fields)){
                    JSONObject fieldsObj = JSONObject.parseObject(fields);
                    if(fieldsObj!=null && fieldsObj.containsKey("dylb")){
                        String dylb = fieldsObj.getString("dylb");
                        dataObj.put("dylb",dylb);
                        String dylbLabel = DictUtils.getDictLabel("project_nrw_dylb",dylb);
                        dataObj.put("dylbLabel",dylbLabel);
                    }
                    if(fieldsObj!=null && fieldsObj.containsKey("nrwObj")){
                        JSONObject nrwObj = fieldsObj.getJSONObject("nrwObj");
                        if(nrwObj!=null && nrwObj.containsKey("pfPrice")){
                            String pfPrice = nrwObj.getString("pfPrice");
                            if(StringUtils.isNotNull(pfPrice) && ErpDataReqUtil.isInteger(pfPrice)){
                                costObj.put("pfPrice", WagesUtils.bigDecimal(pfPrice,6));
                            }
                        }
                    }
                }
                //获取价格
                String productType = dataObj.getString("productType");
                if(StringUtils.isNotNull(productType)){
                    List<Integer> category = StringUtils.stringToIntegerList(productType);
                    dataObj.put("categoryId",category.get(category.size()-1));
                }
                String serviceMode = dataObj.getString("serviceMode");
                serviceMode = StringUtils.convertDefaultValue(serviceMode,"BVIP");
                String checkType = "2";
                if(StringUtils.isNotNull(serviceMode)){
                    checkType = "1";
                }
                BigDecimal days = projectDydUtil.getDays(checkType,serviceMode,dataObj.getString("dylb"),projectOrderId);
                dataObj.put("days",days);
                dataObj.put("checkType",checkType);
                dataObj.put("userId",projectItemOrder.getUserId());
                dataObj.put("projectOrderId",projectOrderId);


                String dylx = "0";
                String preFieldsObj =  dataObj.getString("itemFields");
                if(StringUtils.isNotNull(preFieldsObj)){
                    JSONObject fieldsObj = JSONObject.parseObject(preFieldsObj);
                    if(fieldsObj!=null && fieldsObj.containsKey("dylx")){
                        String  dylxVal = fieldsObj.getString("dylx");
                        if(StringUtils.isNotNull(dylxVal)){
                            dylx = dylxVal;
                        }
                    }
                }
                String dylbVal = dataObj.getString("dylb");
                if("1".equals(dylx)){
                    dylbVal = "0";
                }
                // 获取标准工时
                BigDecimal standardHours = categoryService.selectSampleTimeByCategoryAndLevel(dataObj.getLong("categoryId"), dylbVal);
                dataObj.put("standardHours",standardHours);
                dataObj.put("dylx",dylx);
                returnObj = flowTools.autoScheduleSampleOrder(dataObj);
                final JSONObject dataObjNew = dataObj;
                //处理订单 价格信息
                new Thread(){
                    public void run(){
                        try {
                            Thread.sleep(6000);
                            processAutoOrderPriceInfo(costObj,dataObjNew,projectOrderId,days);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                }.start();
            }else{
                returnObj.put("msg","打样单未找到!");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return returnObj;
    }

    /**
     * 处理打样单价格
     * @param costObj
     * @param dataObj
     * @param projectOrderId
     * @param days
     */
    private void processAutoOrderPriceInfo(JSONObject costObj,JSONObject dataObj,Long projectOrderId,BigDecimal days){
        //获取净含量
        JSONObject returnObj = new JSONObject();  //获取数据
        Integer projectType = dataObj.getInteger("projectType");
        projectType = StringUtils.convertDefaultValue(projectType,0);
        if(projectType.intValue()==1){  //立项前
            BeforeProject beforeProject = new BeforeProject();
            beforeProject.setProjectProductIds(dataObj.getString("projectProductId"));
            beforeProject.setProductKey(dataObj.getString("itemName"));
            if(StringUtils.isNotNull(beforeProject.getProjectProductIds())){
                List<JSONObject> dataList = beforeProjectService.queryBeforeProjectProductShowDataList(beforeProject);
                if(dataList!=null && dataList.size()>0){
                    returnObj = dataList.get(0);
                }
            }
        }else{  //进行中
            BeforeProject beforeProject = new BeforeProject();
            beforeProject.setId(dataObj.getLong("projectId"));
            beforeProject.setProductKey(dataObj.getString("itemName"));
            if(StringUtils.isNotNull(beforeProject.getId())){
                List<JSONObject> dataList = beforeProjectService.queryProjectShowDataList(beforeProject);
                if(dataList!=null && dataList.size()>0){
                    returnObj = dataList.get(0);
                }
            }
        }
        String pfPrice = costObj.getString("pfPrice");
        List<String> specList = new ArrayList<String>();
        if(returnObj!=null && returnObj.containsKey("id")) {
            Long id = returnObj.getLong("dataId");
            List<JSONObject> dataList = beforeProjectService.queryProjectProductItemDataList(id);
            if(dataList!=null && dataList.size()>0){
                for(JSONObject itemObj : dataList){
                     if(itemObj!=null && itemObj.containsKey("spec")){
                        String spec = itemObj.getString("spec");
                         specList.add(spec);
                    }
                }
            }
        }
        List<String> showPrice = new ArrayList<String>();
        if(specList!=null && specList.size()>0){
            for(int i = 0;i<specList.size();i++){
                String spec = specList.get(i);
                if(StringUtils.isNotNull(spec)){
                    if(!("/".equals(spec) || "\\".equals(spec) || "-".equals(spec) || "_".equals(spec))){
                        spec = StringUtils.replaceSplit(spec);  //统一分隔符
                        List<String> specList_ = StringUtils.str2List(spec,"&",true,true);
                        if(specList_!=null && specList_.size()>0){
                            for(String spec_ : specList_){
                               String netContent = StringUtils.processSpec(spec_);
                               if(StringUtils.isNotNull(netContent) && ErpDataReqUtil.isInteger(netContent)
                                 && StringUtils.isNotNull(pfPrice) && ErpDataReqUtil.isInteger(pfPrice)
                                 && WagesUtils.bigDecimal(netContent,6).doubleValue()>0
                                 && WagesUtils.bigDecimal(pfPrice,6).doubleValue()>0){
                                    BigDecimal pfPrice_ = WagesUtils.bigDecimal(pfPrice,6);
                                    BigDecimal netContent_ = WagesUtils.bigDecimal(netContent,6);
                                    String profit = getProfit(netContent_,pfPrice_);
                                    if(StringUtils.isNotNull(profit) && ErpDataReqUtil.isInteger(profit)){
                                        showPrice.add(spec_+"成本:"+profit);
                                    }
                               }
                            }
                        }
                    }
                }
            }
        }
        ProjectItemOrder projectItemOrder = new ProjectItemOrder();
        projectItemOrder.setId(projectOrderId);
        projectItemOrder.setYjTime(DateUtils.addDays(DateUtils.getNowDate(),days.intValue()));
        projectItemOrder.setFirstYjTime(DateUtils.addDays(DateUtils.getNowDate(),days.intValue()));
        if(showPrice!=null && showPrice.size()>0) {
            projectItemOrder.setSamplePrice(StringUtils.join(showPrice,","));
        }
        projectItemOrderMapper.updateProjectItemOrderZdy(projectItemOrder);
    }


    /**
     * 计算利润
     * @param netContent
     * @param pfPrice
     * @return
     */
    private String getProfit(BigDecimal netContent,BigDecimal pfPrice) {
        String profit = "";
        BigDecimal profitValue = WagesUtils.bigDecimal("3.5");
        if(netContent.doubleValue()<10){
            profitValue = WagesUtils.bigDecimal("1.2");
        }else if(netContent.doubleValue()<50){
            profitValue = WagesUtils.bigDecimal("1.5");
        }else if(netContent.doubleValue()<100){
            profitValue = WagesUtils.bigDecimal("1.7");
        }else if(netContent.doubleValue()<200){
            profitValue = WagesUtils.bigDecimal("2");
        }else if(netContent.doubleValue()<500){
            profitValue = WagesUtils.bigDecimal("2.5");
        }else{
            profitValue = WagesUtils.bigDecimal("3.5");
        }

        BigDecimal r1 = WagesUtils.divide(profitValue,netContent,6,BigDecimal.ROUND_HALF_UP);
        BigDecimal r2 = r1.multiply(WagesUtils.bigDecimal("1000"));
        BigDecimal r3 = pfPrice.subtract(r2);
        if(r3!=null && r3.doubleValue()>0){
            profit = WagesUtils.bigDecimal(r3,6).stripTrailingZeros().toString();
        }
        return profit;
    }

    /**
     * 处理价格信息
     * @param projectOfferOrder
     */
    private void processProjeftOfferOrder(ProjectOfferOrder projectOfferOrder){
        String gzl = projectOfferOrder.getGzl();
        BigDecimal moqPrice = projectOfferOrder.getMoqPrice();
        BigDecimal orderPrice = projectOfferOrder.getOrderPrice();
        projectOfferOrder.setMoqPriceKg(moqPrice);
        projectOfferOrder.setOrderPriceKg(orderPrice);
        JSONArray formulaArray = new JSONArray();
        BigDecimal totalLoss = WagesUtils.bigDecimal("0");
        BigDecimal outKgPrice = WagesUtils.bigDecimal("0");
        BigDecimal outKgPricePcs = WagesUtils.bigDecimal("0");
        BigDecimal totalCostPricePcs = WagesUtils.bigDecimal("0");
        String formulaDataList = projectOfferOrder.getFormulaDataList();
        BigDecimal bomNum = WagesUtils.bigDecimal("1");
        if(StringUtils.isNotNull(formulaDataList) && JSONArray.isValidArray(formulaDataList)){
            JSONArray formulaDataArray = JSONArray.parseArray(formulaDataList);
            if(formulaDataArray!=null && formulaDataArray.size()>0){
                for(int i = 0;i<formulaDataArray.size();i++){
                    JSONObject formulaObj = formulaDataArray.getJSONObject(i);
                    String bsJhl = formulaObj.getString("bsJhl");
                    String formulaGzl = formulaObj.getString("gzl");
                    String gzlUnit = formulaObj.getString("gzlUnit");
                    String labNo = formulaObj.getString("labNo");
                    String formulaType = formulaObj.getString("formulaType");
                    BigDecimal formulaOrderPriceKg = formulaObj.getBigDecimal("orderPrice");
                    BigDecimal formulaMoqPriceKg = formulaObj.getBigDecimal("moqPrice");
                    BigDecimal totalLosssNum = formulaObj.getBigDecimal("totalLosssNum");
                    BigDecimal parentBomNum = formulaObj.getBigDecimal("parentBomNum");
                    parentBomNum = StringUtils.convertDefaultValue(parentBomNum,WagesUtils.bigDecimal("1"));
                    //配方类别
                    String category = formulaObj.getString("category");
                    BigDecimal outKgMoney = WagesUtils.bigDecimal("0");  //单个对外报价
                    BigDecimal outKgMoneyPcs = WagesUtils.bigDecimal("0");  //单个对外报价
                    //含损耗
                    BigDecimal orderNum = projectOfferOrder.getOrderNum();
                    BigDecimal singleLossMoney = WagesUtils.bigDecimal("0");
                    if(totalLosssNum!=null && totalLosssNum.doubleValue()>0){
                        BigDecimal totalLossMoney = totalLosssNum.multiply(formulaMoqPriceKg);
                        singleLossMoney = WagesUtils.divide(totalLossMoney,orderNum,6,BigDecimal.ROUND_HALF_UP);
                        totalLoss = totalLoss.add(singleLossMoney);
                    }
                    BigDecimal formulaMoqPricePcs = WagesUtils.bigDecimal(formulaMoqPriceKg,6).multiply(WagesUtils.bigDecimal(formulaGzl,6)).divide(WagesUtils.bigDecimal("1000"));
                    BigDecimal formulaOrderPricePcs = WagesUtils.bigDecimal(formulaOrderPriceKg,6).multiply(WagesUtils.bigDecimal(formulaGzl,6)).divide(WagesUtils.bigDecimal("1000"));

                    formulaMoqPricePcs = formulaMoqPricePcs.add(singleLossMoney);
                    totalCostPricePcs  = totalCostPricePcs.add(parentBomNum.multiply(formulaMoqPricePcs));
                    formulaOrderPricePcs = formulaOrderPricePcs.add(singleLossMoney);
                    if(formulaObj!=null && StringUtils.isNotNull(formulaObj.getBigDecimal("outKgMoney"))
                    && formulaObj.getBigDecimal("outKgMoney").doubleValue()>=0){
                        BigDecimal outKgMoney_ = formulaObj.getBigDecimal("outKgMoney");
                        BigDecimal outKgMoneyPcs_ = formulaObj.getBigDecimal("outKgMoneyPcs");
                        //换算为KG单价成本 加上损耗
                        outKgMoney = outKgMoney_;
                        outKgMoneyPcs =outKgMoneyPcs_;
                        outKgPrice = outKgPrice.add(outKgMoney);  //汇总对外报价
                        outKgPricePcs = outKgPricePcs.add(outKgMoneyPcs);  //汇总对外报价 单片
                    }else{
                        //获取过内容报价
                        JSONObject priceObj = projectItemOrderMapper.queryProjectOfferNrwHistoryDataInfo(labNo);
                        if (priceObj != null && priceObj.containsKey("price") && StringUtils.isNotNull(priceObj.getString("price"))) {
                            BigDecimal outPrice = priceObj.getBigDecimal("price");
                            Integer rateType = priceObj.getInteger("rateType");
                            if(rateType!=null && rateType.intValue()==1){
                                outPrice = WagesUtils.divide(outPrice,WagesUtils.bigDecimal("1.13"),2,BigDecimal.ROUND_HALF_UP);
                            }
                            //换算为KG单价成本 加上损耗
                            outKgMoney = outPrice;
                            outKgMoneyPcs = outKgMoney.multiply(WagesUtils.bigDecimal(formulaGzl,6)).divide(WagesUtils.bigDecimal("1000"));
                            outKgMoneyPcs = parentBomNum.multiply(outKgMoneyPcs);
                            outKgPrice = outKgPrice.add(outKgMoney);  //汇总对外报价
                            outKgPricePcs = outKgPricePcs.add(outKgMoneyPcs);  //汇总对外报价 单片
                        }else{
                            JSONObject customerObj = queryCustomerPrice(labNo,projectOfferOrder.getProductTabs());
                            Integer stauts =  customerObj.getInteger("status");
                            if(stauts==2){
                               BigDecimal price =  customerObj.getBigDecimal("price");
                                //换算为KG单价成本 加上损耗
                                outKgMoney = price;
                                outKgMoneyPcs = outKgMoney.multiply(WagesUtils.bigDecimal(formulaGzl,6)).divide(WagesUtils.bigDecimal("1000"));
                                outKgMoneyPcs = parentBomNum.multiply(outKgMoneyPcs);
                                outKgPrice = outKgPrice.add(outKgMoney);  //汇总对外报价
                                outKgPricePcs = outKgPricePcs.add(outKgMoneyPcs);  //汇总对外报价 单片
                            }else{
                                //获取利润率
                                Map<String,Object> params = new HashMap<String,Object>();
                                params.put("dataId",category);
                                params.put("gzl",WagesUtils.bigDecimal(formulaGzl,6));
                                ProjectOfferNrwProfitItem projectOfferNrwProfitItem = projectOfferNrwProfitMapper.queryProjectOfferNrwProfitItemDataInfo(params);
                                if(projectOfferNrwProfitItem!=null && StringUtils.isNotNull(projectOfferNrwProfitItem.getId())){
                                    BigDecimal profitRate = projectOfferNrwProfitItem.getProfitRate();
                                    if(profitRate!=null){
                                        profitRate = profitRate.multiply(WagesUtils.bigDecimal("100"));
                                        //换算为KG单价成本 加上损耗
                                        BigDecimal totalMoneyKg =  WagesUtils.divide(formulaMoqPricePcs,WagesUtils.bigDecimal(formulaGzl,6),6,BigDecimal.ROUND_HALF_UP).multiply(WagesUtils.bigDecimal("1000"));
                                        BigDecimal profit = totalMoneyKg.multiply(profitRate).divide(WagesUtils.bigDecimal("100"));
                                        outKgMoney = WagesUtils.bigDecimal(profit.add(totalMoneyKg),2);
                                        outKgMoneyPcs = outKgMoney.multiply(WagesUtils.bigDecimal(formulaGzl,6)).divide(WagesUtils.bigDecimal("1000"));
                                        outKgMoneyPcs = parentBomNum.multiply(outKgMoneyPcs);
                                        outKgPrice = outKgPrice.add(outKgMoney);  //汇总对外报价
                                        outKgPricePcs = outKgPricePcs.add(outKgMoneyPcs);  //汇总对外报价 单片
                                    }
                                }
                            }
                        }
                    }
                    formulaObj.put("outKgMoney",WagesUtils.bigDecimal(outKgMoney));
                    formulaObj.put("outKgMoneyPcs",WagesUtils.bigDecimal(outKgMoneyPcs,2));
                    formulaObj.put("formulaMoqPriceLoss",singleLossMoney);
                    formulaObj.put("formulaMoqPriceKg",WagesUtils.bigDecimal(formulaMoqPriceKg,5));
                    formulaObj.put("formulaMoqPricePcs",WagesUtils.bigDecimal(formulaMoqPricePcs,5));
                    formulaObj.put("formulaOrderPricePcs",WagesUtils.bigDecimal(formulaOrderPricePcs,5));
                    formulaObj.put("singleLossMoney",singleLossMoney);
                    formulaObj.put("formulaType",formulaType);
                    formulaObj.put("bsJhl",bsJhl);
                    formulaObj.put("parentBomNum",parentBomNum);
                    formulaObj.put("formulaGzl",formulaGzl);
                    formulaObj.put("gzlUnit",gzlUnit);
                    formulaObj.put("labNo",labNo);
                    formulaObj.put("formulaOrderPriceKg",formulaOrderPriceKg);
                    formulaArray.add(formulaObj);
                }
            }
        }

//        if(bomList!=null && bomList.size()>0){
//            Collections.sort(bomList);
//            bomNum = bomList.get(0);
//        }

        projectOfferOrder.setFormulaDataList(formulaArray.toJSONString());

        projectOfferOrder.setBomNum(bomNum);

        if(StringUtils.isNull(projectOfferOrder.getOutKgPrice()) || projectOfferOrder.getOutKgPrice().doubleValue()==0){
            if(outKgPrice!=null && outKgPrice.doubleValue()>0){
                projectOfferOrder.setOutKgPrice(WagesUtils.bigDecimal(outKgPrice));
            }else{
                projectOfferOrder.setOutKgPrice(WagesUtils.bigDecimal("0"));
            }
        }
        projectOfferOrder.setOutKgRatePrice(WagesUtils.getRateNumber(projectOfferOrder.getOutKgPrice(),WagesUtils.RATE_PRICE));

        if(StringUtils.isNull(projectOfferOrder.getOutSinglePrice()) || projectOfferOrder.getOutSinglePrice().doubleValue()==0){
            if(outKgPricePcs!=null && outKgPricePcs.doubleValue()>0){
                projectOfferOrder.setOutSinglePrice(WagesUtils.bigDecimal(outKgPricePcs));
            }else{
                if(StringUtils.isNotNull(projectOfferOrder.getOutKgPrice()) && StringUtils.isNotNull(gzl)){
                    BigDecimal price = projectOfferOrder.getOutKgPrice().multiply(WagesUtils.bigDecimal(gzl,6)).divide(WagesUtils.bigDecimal("1000"));
                    price = price.multiply(bomNum);
                    projectOfferOrder.setOutSinglePrice(price);
                }else{
                    projectOfferOrder.setOutSinglePrice(WagesUtils.bigDecimal("0"));
                }
            }
        }

        if(StringUtils.isNotNull(gzl) && ErpDataReqUtil.isInteger(gzl) && StringUtils.isNotNull(moqPrice)){
            moqPrice = moqPrice.multiply(WagesUtils.bigDecimal(gzl,6)).divide(WagesUtils.bigDecimal("1000"));
            orderPrice = orderPrice.multiply(WagesUtils.bigDecimal(gzl,6)).divide(WagesUtils.bigDecimal("1000"));
            projectOfferOrder.setMoqPrice(moqPrice);
            projectOfferOrder.setOrderPrice(orderPrice);
        }

        //损耗金额
        if(StringUtils.isNull(projectOfferOrder.getLossPrice())){
            projectOfferOrder.setLossPrice(totalLoss);
        }

        if(StringUtils.isNull(projectOfferOrder.getOutSinglePrice())){
            projectOfferOrder.setOutSinglePrice(WagesUtils.bigDecimal("0"));
        }
        if(StringUtils.isNotNull(projectOfferOrder.getMoqPrice())){
            BigDecimal singlePrice = projectOfferOrder.getMoqPrice().add(projectOfferOrder.getLossPrice());
            singlePrice = singlePrice.multiply(bomNum);
            projectOfferOrder.setSinglePrice(WagesUtils.bigDecimalNew(singlePrice,6));
            projectOfferOrder.setSingleRatePrice(WagesUtils.getRateNumber(singlePrice,WagesUtils.RATE_PRICE));
        }

        if(StringUtils.isNotNull(projectOfferOrder.getOutSinglePrice())){
            projectOfferOrder.setOutSingleRatePrice(WagesUtils.getRateNumber(projectOfferOrder.getOutSinglePrice(),WagesUtils.RATE_PRICE));
        }

        projectOfferOrder.setTotalCostPricePcs(totalCostPricePcs);
        if(StringUtils.isNotNull(totalCostPricePcs) && StringUtils.isNotNull(projectOfferOrder.getSinglePrice()) && StringUtils.isNotNull(projectOfferOrder.getOutSinglePrice())){
            BigDecimal diffSinglePrice = projectOfferOrder.getOutSinglePrice().subtract(projectOfferOrder.getSinglePrice());
            if(totalCostPricePcs!=null && totalCostPricePcs.doubleValue()>0){
                diffSinglePrice = projectOfferOrder.getOutSinglePrice().subtract(totalCostPricePcs);
            }
            projectOfferOrder.setDiffSinglePrice(diffSinglePrice);
            projectOfferOrder.setDiffSingleRatePrice(WagesUtils.getRateNumber(diffSinglePrice,WagesUtils.RATE_PRICE));
            BigDecimal profitRate =  WagesUtils.divide(diffSinglePrice,projectOfferOrder.getOutSinglePrice(),4,BigDecimal.ROUND_HALF_UP);
            profitRate = profitRate.multiply(WagesUtils.bigDecimal("100"));
            projectOfferOrder.setProfitRate(profitRate);
        }


        if(StringUtils.isNull(projectOfferOrder.getLossPriceBc())){
            projectOfferOrder.setLossPriceBc(WagesUtils.bigDecimal("0"));
        }
        if(StringUtils.isNull(projectOfferOrder.getOutSinglePriceBc())){
            projectOfferOrder.setOutSinglePriceBc(WagesUtils.bigDecimal("0"));
        }
        if(StringUtils.isNotNull(projectOfferOrder.getMoqPriceBc())){
            BigDecimal singlePrice = projectOfferOrder.getMoqPriceBc().add(projectOfferOrder.getLossPriceBc());
            projectOfferOrder.setSinglePriceBc(WagesUtils.bigDecimalNew(singlePrice,6));
            projectOfferOrder.setSingleRatePriceBc(WagesUtils.getRateNumber(singlePrice,WagesUtils.RATE_PRICE));
        }

        if(StringUtils.isNull(projectOfferOrder.getBcRate())){
            projectOfferOrder.setBcRate(WagesUtils.bigDecimal("13"));
        }

        if(StringUtils.isNotNull(projectOfferOrder.getOutSinglePriceBc())){
            if(StringUtils.isNotNull(projectOfferOrder.getBcRate()) && projectOfferOrder.getBcRate().doubleValue()==13){
                projectOfferOrder.setOutSingleRatePriceBc(WagesUtils.getRateNumber(projectOfferOrder.getOutSinglePriceBc(),WagesUtils.RATE_PRICE,3));
            }else{
                projectOfferOrder.setOutSingleRatePriceBc(WagesUtils.bigDecimal(projectOfferOrder.getOutSinglePriceBc(),3));
            }
        }

        if(StringUtils.isNotNull(projectOfferOrder.getSinglePriceBc()) && StringUtils.isNotNull(projectOfferOrder.getOutSinglePriceBc())){
            BigDecimal diffSinglePrice = projectOfferOrder.getOutSinglePriceBc().subtract(projectOfferOrder.getSinglePriceBc());
            projectOfferOrder.setDiffSinglePriceBc(diffSinglePrice);
            projectOfferOrder.setDiffSingleRatePriceBc(WagesUtils.getRateNumber(diffSinglePrice,WagesUtils.RATE_PRICE));
            BigDecimal profitRate =  WagesUtils.divide(diffSinglePrice,projectOfferOrder.getOutSinglePriceBc(),4,BigDecimal.ROUND_HALF_UP);
            profitRate = profitRate.multiply(WagesUtils.bigDecimal("100"));
            projectOfferOrder.setProfitRateBc(profitRate);
        }


        if(StringUtils.isNull(projectOfferOrder.getFillPrice())){
            projectOfferOrder.setFillPrice(WagesUtils.bigDecimal("0"));
        }


        BigDecimal hourCostSinglePrice = projectOfferOrder.getHourCostSinglePrice();
        if(StringUtils.isNotNull(projectOfferOrder.getFillCostPrice()) && StringUtils.isNotNull(hourCostSinglePrice) && ProjectOfferUtil.STANDR_HOUR_PRICE.doubleValue()!=hourCostSinglePrice.doubleValue()){
            BigDecimal hourRate = WagesUtils.divide(hourCostSinglePrice,ProjectOfferUtil.STANDR_HOUR_PRICE,4,BigDecimal.ROUND_HALF_UP);
            BigDecimal fillCostPrice = projectOfferOrder.getFillCostPrice().multiply(hourRate);
            projectOfferOrder.setFillPrice(WagesUtils.bigDecimal(fillCostPrice,4));
        }

        projectOfferOrder.setFillRatePrice(WagesUtils.getRateNumber(projectOfferOrder.getFillPrice(),WagesUtils.RATE_PRICE));



        if(StringUtils.isNull(projectOfferOrder.getFillOutPrice())){
            projectOfferOrder.setFillOutPrice(WagesUtils.bigDecimal("0"));
        }

        projectOfferOrder.setFillOutRatePrice(WagesUtils.getRateNumber(projectOfferOrder.getFillOutPrice(),WagesUtils.RATE_PRICE));

        if(StringUtils.isNotNull(projectOfferOrder.getFillPrice()) && StringUtils.isNotNull(projectOfferOrder.getFillOutPrice())){
            BigDecimal diffSinglePrice = projectOfferOrder.getFillOutPrice().subtract(projectOfferOrder.getFillPrice());
            projectOfferOrder.setDiffSinglePriceFill(diffSinglePrice);
            projectOfferOrder.setDiffSingleRatePriceFill(WagesUtils.getRateNumber(diffSinglePrice,WagesUtils.RATE_PRICE));
            BigDecimal profitRate =  WagesUtils.divide(diffSinglePrice,projectOfferOrder.getFillOutPrice(),4,BigDecimal.ROUND_HALF_UP);
            profitRate = profitRate.multiply(WagesUtils.bigDecimal("100"));
            projectOfferOrder.setProfitRateFill(profitRate);
        }


        if(StringUtils.isNull(projectOfferOrder.getFreightMoney())){
            projectOfferOrder.setFreightMoney(WagesUtils.bigDecimal("0"));
        }

        BigDecimal price = WagesUtils.bigDecimal(projectOfferOrder.getSinglePrice(),6).add(WagesUtils.bigDecimal(projectOfferOrder.getSinglePriceBc(),6))
                .add(WagesUtils.bigDecimal(projectOfferOrder.getFillPrice()));

        if(totalCostPricePcs!=null && totalCostPricePcs.doubleValue()>0){
            price = WagesUtils.bigDecimal(totalCostPricePcs,6).add(WagesUtils.bigDecimal(projectOfferOrder.getSinglePriceBc(),6))
                    .add(WagesUtils.bigDecimal(projectOfferOrder.getFillPrice()));
        }

        BigDecimal ratePrice = WagesUtils.getRateNumber(price,WagesUtils.RATE_PRICE);

        BigDecimal outPrice = WagesUtils.bigDecimal(projectOfferOrder.getOutSinglePrice(),6).add(WagesUtils.bigDecimal(projectOfferOrder.getOutSinglePriceBc(),6))
                .add(WagesUtils.bigDecimal(projectOfferOrder.getFillOutPrice(),6))
                .add(WagesUtils.bigDecimal(projectOfferOrder.getFreightMoney(),6));

        if(StringUtils.isNull(projectOfferOrder.getGoodsRate())){
            projectOfferOrder.setGoodsRate(WagesUtils.bigDecimal("13"));
        }

        BigDecimal goodsRate = projectOfferOrder.getGoodsRate();

        //判断是否改过税率
        BigDecimal newRate = WagesUtils.divide(goodsRate,WagesUtils.bigDecimal("100"),2,BigDecimal.ROUND_HALF_UP);
        BigDecimal newRate_ = WagesUtils.bigDecimal("1").add(newRate);

        BigDecimal outRatePrice = WagesUtils.getRateNumber(outPrice,newRate_);

        BigDecimal diffPrice = outPrice.subtract(price);
        BigDecimal diffRatePrice = WagesUtils.getRateNumber(diffPrice,WagesUtils.RATE_PRICE);

        BigDecimal goodsProfitRate = WagesUtils.divide(diffPrice,outPrice,4,BigDecimal.ROUND_HALF_UP);
        goodsProfitRate = goodsProfitRate.multiply(WagesUtils.bigDecimal("100"));

        projectOfferOrder.setPrice(price);
        projectOfferOrder.setRatePrice(ratePrice);
        projectOfferOrder.setOutPrice(outPrice);
        projectOfferOrder.setOutRatePrice(outRatePrice);
        projectOfferOrder.setDiffPrice(diffPrice);
        projectOfferOrder.setDiffRatePrice(diffRatePrice);
        projectOfferOrder.setGoodsProfitRate(goodsProfitRate);


        BigDecimal outSinglePrice = projectOfferOrder.getOutSinglePrice();
        BigDecimal fillPrice = projectOfferOrder.getFillPrice();
        //销售提成 料体单价*0.03 + 加工费*0.03
        BigDecimal salesCommission = WagesUtils.bigDecimal("0");
        //税费    料体盈利*0.143+加工费*0.143+包材价格*0.13
        //税费    改版 未税单价*0.13
        BigDecimal rateFee = WagesUtils.bigDecimal("0");
        rateFee = outPrice.multiply(WagesUtils.bigDecimal("0.13"));
        //成本合计 料体成本+包材价格+加工费成本+销售提成+咨询费+税费十运费
        BigDecimal totalCostMoney = WagesUtils.bigDecimal("0");
        if(StringUtils.isNotNull(outSinglePrice)){
            salesCommission = salesCommission.add(outSinglePrice.multiply(WagesUtils.bigDecimal("0.03")));
        }
        if(StringUtils.isNotNull(fillPrice)){
            salesCommission = salesCommission.add(fillPrice.multiply(WagesUtils.bigDecimal("0.03")));
            //rateFee = rateFee.add(fillPrice.multiply(WagesUtils.bigDecimal("0.143")));
            totalCostMoney = totalCostMoney.add(fillPrice);
        }
        projectOfferOrder.setSalesCommission(salesCommission);


        //税费
        BigDecimal diffSinglePrice = projectOfferOrder.getDiffSinglePrice();  //料体盈利
        BigDecimal moqPriceBc = projectOfferOrder.getMoqPriceBc(); //包材价格

        if(StringUtils.isNotNull(diffSinglePrice)){
             //rateFee = rateFee.add(diffSinglePrice.multiply(WagesUtils.bigDecimal("0.143")));
        }
        if(StringUtils.isNotNull(moqPriceBc)){
             //rateFee = rateFee.add(moqPriceBc.multiply(WagesUtils.bigDecimal("0.13")));
             totalCostMoney = totalCostMoney.add(moqPriceBc);
        }

        projectOfferOrder.setRateFee(rateFee);

        //成本合计
        BigDecimal singlePrice = projectOfferOrder.getSinglePrice();  //料体成本
        if(totalCostPricePcs!=null && totalCostPricePcs.doubleValue()>0){
            singlePrice = totalCostPricePcs;
        }
        if(StringUtils.isNotNull(singlePrice)){
             totalCostMoney = totalCostMoney.add(singlePrice);
        }

        if(StringUtils.isNull(projectOfferOrder.getConsultingFee())){
            projectOfferOrder.setConsultingFee(WagesUtils.bigDecimal("0"));
        }

        totalCostMoney = totalCostMoney.add(salesCommission);
        totalCostMoney = totalCostMoney.add(rateFee);
        totalCostMoney = totalCostMoney.add(projectOfferOrder.getFreightMoney());
        totalCostMoney = totalCostMoney.add(projectOfferOrder.getConsultingFee());
        projectOfferOrder.setTotalCostMoney(totalCostMoney);

        //总盈利  含税单价-成本合计
        BigDecimal totalProfit = WagesUtils.bigDecimal("0");

        totalProfit = outRatePrice.subtract(totalCostMoney);

        projectOfferOrder.setTotalProfit(totalProfit);
    }


    /**
     * 获取客户接受价格
     * @param labCode
     * @param productTabs
     * @return
     */
    private JSONObject queryCustomerPrice(String labCode,String productTabs){
        JSONObject returnObj = new JSONObject();
        BigDecimal price = WagesUtils.bigDecimal("0");
        Integer status = 1;
        if(StringUtils.isNotNull(labCode) && StringUtils.isNotNull(productTabs)){
            JSONArray array = JSONArray.parseArray(productTabs);
            if(array!=null && array.size()>0){
                for(int i = 0;i<array.size();i++){
                    JSONObject obj = array.getJSONObject(i);
                    if(obj.containsKey("bcpArray")){
                        JSONArray bcpArray = obj.getJSONArray("bcpArray");
                        if (bcpArray != null && bcpArray.size() > 0) {
                            for(int j = 0;j<bcpArray.size();j++){
                                JSONObject bcpObj = bcpArray.getJSONObject(j);
                                if(bcpObj!=null && bcpObj.containsKey("customerPrice")){
                                    String customerPrice = bcpObj.getString("customerPrice");
                                    String confirmCode = bcpObj.getString("confirmCode");
                                    if(labCode.equals(confirmCode) && StringUtils.isNotNull(customerPrice) && ErpDataReqUtil.isInteger(customerPrice)){
                                        price = WagesUtils.bigDecimal(customerPrice,6);
                                        status = 2;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        returnObj.put("status",status);
        returnObj.put("price",price);
        return  returnObj;
    }

    /**
     * 更新项目订单样品报价
     * @param projectItemOrderId 项目订单ID
     * @param samplePrice 样品报价
     * @return 更新结果
     */
    @Override
    @Transactional
    public int updateProjectItemOrderSamplePrice(Long projectItemOrderId, String samplePrice) {
        // 查询订单是否存在
        ProjectItemOrder existingOrder = projectItemOrderMapper.selectProjectItemOrderById(projectItemOrderId);
        if (existingOrder == null) {
            throw new RuntimeException("项目订单不存在，ID: " + projectItemOrderId);
        }
        // 执行更新
        int result = projectItemOrderMapper.updateProjectItemOrderSamplePrice(projectItemOrderId, samplePrice);
        return result;
    }

}
