package com.ruoyi.software.service.impl;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.software.domain.EngineerWorkRecord;
import com.ruoyi.software.mapper.EngineerWorkRecordMapper;
import com.ruoyi.software.service.IEngineerWorkRecordService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 工程师工作记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
public class EngineerWorkRecordServiceImpl implements IEngineerWorkRecordService 
{
    @Resource
    private EngineerWorkRecordMapper engineerWorkRecordMapper;

    @Resource
    private ISysUserService userService;

    @Override
    public void insertEngineerWorkRecord(EngineerWorkRecord engineerWorkRecord)
    {
        engineerWorkRecord.setCreateTime(DateUtils.getNowDate());
        engineerWorkRecord.setCreateBy(SecurityUtils.getUsername());
        engineerWorkRecordMapper.insertEngineerWorkRecord(engineerWorkRecord);
    }

    @Override
    public void updateEngineerWorkRecord(EngineerWorkRecord engineerWorkRecord)
    {
        // 1. 检查记录是否存在
        EngineerWorkRecord existingRecord = engineerWorkRecordMapper.selectEngineerWorkRecordById(engineerWorkRecord.getId());
        if (existingRecord == null) {
            throw new ServiceException("工作记录不存在");
        }

        // 2. 处理工时变更
        if (engineerWorkRecord.getWorkHours() != null) {
            BigDecimal workHoursDiff = engineerWorkRecord.getWorkHours().subtract(existingRecord.getWorkHours());
            
            if (workHoursDiff.compareTo(BigDecimal.ZERO) > 0) {
                // 2.1 增加标准工时的情况
                // 如果原来没有剩余工时，设置为0
                if (existingRecord.getRemainingHours() == null) {
                    existingRecord.setRemainingHours(BigDecimal.ZERO);
                }
                // 增加相应的可用工时
                engineerWorkRecord.setRemainingHours(existingRecord.getRemainingHours().add(workHoursDiff));
            } else if (workHoursDiff.compareTo(BigDecimal.ZERO) < 0) {
                // 2.2 减少标准工时的情况
                // 计算已使用的工时
                BigDecimal usedHours = existingRecord.getWorkHours().subtract(existingRecord.getRemainingHours());
                // 新的标准工时必须大于等于已使用的工时
                if (engineerWorkRecord.getWorkHours().compareTo(usedHours) < 0) {
                    throw new ServiceException("无法减少标准工时：新的工时小于已使用的工时");
                }
                // 设置新的剩余工时
                engineerWorkRecord.setRemainingHours(engineerWorkRecord.getWorkHours().subtract(usedHours));
            } else {
                // 工时没有变化，保持原有的剩余工时
                engineerWorkRecord.setRemainingHours(existingRecord.getRemainingHours());
            }
        }

        // 3. 更新记录
        engineerWorkRecord.setUpdateTime(DateUtils.getNowDate());
        engineerWorkRecord.setUpdateBy(SecurityUtils.getUsername());
        engineerWorkRecordMapper.updateEngineerWorkRecord(engineerWorkRecord);
    }

    /**
     * 根据难度等级和品类ID获取对应的工程师列表
     *  如果对应等级没有可用工程师，就查询更高等级的工程师
     *  如果没有更高等级的工程师，就将本组别内的最高等级的工程师返回
     *
     * @param difficultyLevelId 难度等级
     * @param categoryId 品类ID
     * @param projectOrderId 项目ID
     * @return 工程师列表
     */
    @Override
    public List<SysUser> getEngineersByDifficultyLevel(String difficultyLevelId, Long categoryId, Long projectOrderId)
    {
        // 1. 获取指定类别下的可用工程师列表
        List<SysUser> engineers = userService.selectEngineersByCategoryId(categoryId, projectOrderId);
        if (engineers == null || engineers.isEmpty()) {
            return Collections.emptyList();
        }

        // 2. 确定当前难度等级的级别
        int currentLevel = getDifficultyLevelCategory(difficultyLevelId);
        if (currentLevel == -1) {
            return Collections.emptyList(); // 无效难度等级
        }

        // 3. 从当前等级开始，逐级向上查找可用工程师
        for (int level = currentLevel; level <= 3; level++) {
            List<SysUser> filteredEngineers = filterEngineersByLevel(engineers, level);
            if (!filteredEngineers.isEmpty()) {
                return filteredEngineers; // 找到可用工程师，直接返回
            }
        }

        // 4. 如果所有等级都没有可用工程师，返回本组别内的最高等级工程师
        return getHighestLevelEngineersInGroup(engineers);
    }

    /**
     * 根据难度等级字符串确定等级分类
     * @param difficultyLevel 难度等级字符串
     * @return 等级分类：1-等级1，2-等级2，3-等级3，-1-无效等级
     */
    private int getDifficultyLevelCategory(String difficultyLevel) {
        switch (difficultyLevel) {
            case "8":
            case "5":
            case "2":
                return 3; // 等级3（最高）
            case "7":
            case "4":
            case "1":
                return 2; // 等级2（中等）
            case "6":
            case "3":
            case "0":
                return 1; // 等级1（最低）
            default:
                return -1; // 无效难度等级
        }
    }

    /**
     * 根据等级分类过滤工程师
     * @param engineers 工程师列表
     * @param level 等级分类：1-等级1，2-等级2，3-等级3
     * @return 符合条件的工程师列表
     */
    private List<SysUser> filterEngineersByLevel(List<SysUser> engineers, int level) {
        List<SysUser> filteredEngineers = new ArrayList<>();

        for (SysUser engineer : engineers) {
            String rank = engineer.getRank();
            // 跳过无效职级格式
            if (rank == null || rank.length() < 2 || !rank.startsWith("P")) {
                continue;
            }

            // 解析职级数字部分
            int rankValue = Integer.parseInt(rank.substring(1));

            // 根据等级分类进行匹配
            switch (level) {
                case 3:
                    // 等级3：P4级以上
                    if (rankValue >= 4) {
                        filteredEngineers.add(engineer);
                    }
                    break;
                case 2:
                    // 等级2：P1~P3
                    if (rankValue >= 1 && rankValue <= 3) {
                        filteredEngineers.add(engineer);
                    }
                    break;
                case 1:
                    // 等级1：仅P1
                    if (rankValue == 1) {
                        filteredEngineers.add(engineer);
                    }
                    break;
            }
        }

        return filteredEngineers;
    }

    /**
     * 获取本组别内的最高等级工程师
     * @param engineers 工程师列表
     * @return 最高等级的工程师列表
     */
    private List<SysUser> getHighestLevelEngineersInGroup(List<SysUser> engineers) {
        if (engineers == null || engineers.isEmpty()) {
            return Collections.emptyList();
        }

        // 找出所有工程师中的最高职级
        int maxRankValue = 0;
        for (SysUser engineer : engineers) {
            String rank = engineer.getRank();
            // 跳过无效职级格式
            if (rank == null || rank.length() < 2 || !rank.startsWith("P")) {
                continue;
            }

            try {
                // 解析职级数字部分
                int rankValue = Integer.parseInt(rank.substring(1));
                if (rankValue > maxRankValue) {
                    maxRankValue = rankValue;
                }
            } catch (NumberFormatException e) {
                // 忽略无法解析的职级
                continue;
            }
        }

        // 如果没有找到有效的职级，返回空列表
        if (maxRankValue == 0) {
            return Collections.emptyList();
        }

        // 返回所有最高职级的工程师
        List<SysUser> highestLevelEngineers = new ArrayList<>();
        for (SysUser engineer : engineers) {
            String rank = engineer.getRank();
            if (rank != null && rank.length() >= 2 && rank.startsWith("P")) {
                try {
                    int rankValue = Integer.parseInt(rank.substring(1));
                    if (rankValue == maxRankValue) {
                        highestLevelEngineers.add(engineer);
                    }
                } catch (NumberFormatException e) {
                    // 忽略无法解析的职级
                    continue;
                }
            }
        }

        return highestLevelEngineers;
    }

    @Override
    public boolean deductWorkHours(Long userId, String workDate, BigDecimal hours) {
        if (userId == null || workDate == null || hours == null || hours.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        int result = engineerWorkRecordMapper.deductWorkHours(userId, workDate, hours);
        return result > 0;
    }


    @Override
    public boolean restoreWorkHours(Long userId, String workDate, BigDecimal hours) {
        if (userId == null || workDate == null || hours == null || hours.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        BigDecimal remainingHoursToRestore = hours;
        // 获取按工作日期升序的工作记录
        List<EngineerWorkRecord> workRecords = engineerWorkRecordMapper.selectWorkRecordsForRestore(userId, workDate);
        for (EngineerWorkRecord record : workRecords) {
            BigDecimal availableToRestore = record.getWorkHours().subtract(record.getRemainingHours());
            if (availableToRestore.compareTo(BigDecimal.ZERO) <= 0) {
                continue; // 当前日期无法退回工时，继续下一个日期
            }
            if (remainingHoursToRestore.compareTo(availableToRestore) <= 0) {
                // 当前记录可以完全退回剩余工时
                engineerWorkRecordMapper.updateRemainingHours(record.getId(), remainingHoursToRestore);
                remainingHoursToRestore = BigDecimal.ZERO;
                break;
            } else {
                // 当前记录只能部分退回工时
                engineerWorkRecordMapper.updateRemainingHours(record.getId(), availableToRestore);
                remainingHoursToRestore = remainingHoursToRestore.subtract(availableToRestore);
            }
        }
        if (remainingHoursToRestore.compareTo(BigDecimal.ZERO) > 0) {
            throw new ServiceException("用户：" + userId + "，没有足够的工时记录来退回指定的工时。");
        }
        return true;
    }

    @Override
    public Date findNextAvailableWorkDate(Long userId, String startDate, BigDecimal requiredHours, int range) {
        if (userId == null || startDate == null || requiredHours == null || requiredHours.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        return engineerWorkRecordMapper.findNextAvailableWorkDate(userId, startDate, requiredHours, range);
    }

    @Override
    public List<EngineerWorkRecord> getEngineerWorkTime(Long userId, int days) {
        // 获取今天的日期
        Date today = new Date();

        // 计算结束日期（今天 + days 天）
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(today);
        calendar.add(Calendar.DAY_OF_YEAR, days);
        Date endDate = calendar.getTime();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        // 调用Mapper查询
        return engineerWorkRecordMapper.selectEngineerWorkTime(userId, formatter.format(today),formatter.format(endDate));
    }

    /**
     * 获取指定工程的最近一周的工时情况
     * @param userId 工程师
     * @return
     */
    @Override
    public Map<String, Object> weeklyByUserId(Long userId) {
        // 获取今天的日期
        Date today = new Date();

        // 计算开始日期（7天前）
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(today);
        calendar.add(Calendar.DAY_OF_YEAR, -7);
        Date startDate = calendar.getTime();

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        // 调用Mapper查询
        List<EngineerWorkRecord> records = engineerWorkRecordMapper.selectEngineerWorkTime(userId, formatter.format(startDate), formatter.format(today));

        // 生成连续7天的日期数组（从7天前到今天）
        List<String> dateList = new ArrayList<>();
        double[] workHoursArray = new double[7];
        double[] usedHoursArray = new double[7];
        double[] remainingHoursArray = new double[7];

        // 初始化数组（默认值0.0）
        Arrays.fill(workHoursArray, 0.0);
        Arrays.fill(usedHoursArray, 0.0);
        Arrays.fill(remainingHoursArray, 0.0);

        // 生成日期列表并建立日期到索引的映射
        Map<String, Integer> dateIndexMap = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        Calendar cal = Calendar.getInstance();
        cal.setTime(startDate);

        for (int i = 0; i < 7; i++) {
            String dateStr = sdf.format(cal.getTime());
            dateList.add(dateStr);
            dateIndexMap.put(dateStr, i);
            cal.add(Calendar.DAY_OF_YEAR, 1);
        }

        // 处理查询结果
        for (EngineerWorkRecord record : records) {
            String recordDate = sdf.format(record.getWorkDate());
            Integer index = dateIndexMap.get(recordDate);

            if (index != null) {
                // 处理可能为空的BigDecimal值
                BigDecimal workHours = record.getWorkHours() != null ? record.getWorkHours() : BigDecimal.ZERO;
                BigDecimal remainingHours = record.getRemainingHours() != null ? record.getRemainingHours() : BigDecimal.ZERO;

                workHoursArray[index] = workHours.doubleValue();
                remainingHoursArray[index] = remainingHours.doubleValue();

                // 计算已用工时 = 可用工时 - 剩余工时
                BigDecimal usedHours = workHours.subtract(remainingHours);
                usedHoursArray[index] = usedHours.doubleValue();
            }
        }

        // 构建响应对象
        Map<String, Object> result = new HashMap<>();
        result.put("dates", dateList);
        result.put("workHours", workHoursArray);
        result.put("usedHours", usedHoursArray);
        result.put("remainingHours", remainingHoursArray);

        return result;
    }

    /**
     * 获取工程师协助人
     * 1. 打样单的难度高于当前工程师的职级
     * 2. 当前用户所在的组别下is_engineer字段为1的用户工程师作为协助人，
     *
     * @param userId 用户ID
     * @param categoryId 产品类别ID
     * @param difficultyLevelId 难度等级
     * @return 协助人用户信息，如果没有符合条件的协助人则返回null
     */
    @Override
    public SysUser getEngineerAssistant(Long userId, Long categoryId, String difficultyLevelId) {
        if (userId == null || categoryId == null || difficultyLevelId == null) {
            return null;
        }

        // 1. 获取当前用户信息
        SysUser currentUser = userService.selectUserById(userId);
        if (currentUser == null || currentUser.getDeptId() == null) {
            return null;
        }

        // 2. 获取当前用户的职级
        String currentUserRank = currentUser.getRank();
        if (currentUserRank == null || !currentUserRank.startsWith("P")) {
            return null;
        }

        // 3. 解析当前用户职级数字
        int currentRankValue;
        try {
            currentRankValue = Integer.parseInt(currentUserRank.substring(1));
        } catch (NumberFormatException e) {
            return null;
        }

        // 4. 确定打样单难度等级
        int difficultyLevel = getDifficultyLevelCategory(difficultyLevelId);
        if (difficultyLevel == -1) {
            return null;
        }

        // 5. 判断打样单难度是否高于当前工程师职级: 等级3需要P4+，等级2需要P1-P3，等级1需要P1
        boolean needAssistant = false;
        switch (difficultyLevel) {
            case 3:
                // 等级3（最高难度）：需要P4+，如果当前用户低于P4则需要辅助
                needAssistant = currentRankValue < 4;
                break;
            case 2:
                // 等级2（中等难度）：需要P1-P3，如果当前用户低于P1则需要辅助
                needAssistant = currentRankValue < 1;
                break;
            case 1:
                // 等级1（最低难度）：需要P1，如果当前用户低于P1则需要辅助
                needAssistant = currentRankValue < 1;
                break;
        }

        // 6. 如果不需要协助人，直接返回null
        if (!needAssistant) {
            return null;
        }

        // 7. 查询当前用户所在组别下的工程师用户（is_user=0）
        List<SysUser> departmentEngineers = engineerWorkRecordMapper.selectEngineersByDeptId(
            currentUser.getDeptId(), userId);

        if (departmentEngineers == null || departmentEngineers.isEmpty()) {
            return null;
        }

        // 8. 根据难度等级筛选符合条件的辅助工程师
        List<SysUser> qualifiedAssistants = new ArrayList<>();
        for (SysUser engineer : departmentEngineers) {
            String engineerRank = engineer.getRank();
            if (engineerRank == null || !engineerRank.startsWith("P")) {
                continue;
            }

            try {
                int engineerRankValue = Integer.parseInt(engineerRank.substring(1));

                // 根据难度等级判断是否符合条件
                boolean isQualified = false;
                switch (difficultyLevel) {
                    case 3:
                        // 等级3：需要P4+的工程师作为辅助
                        isQualified = engineerRankValue >= 4;
                        break;
                    case 2:
                        // 等级2：需要P1+的工程师作为辅助
                        isQualified = engineerRankValue >= 1;
                        break;
                    case 1:
                        // 等级1：需要P1+的工程师作为辅助
                        isQualified = engineerRankValue >= 1;
                        break;
                }

                if (isQualified) {
                    qualifiedAssistants.add(engineer);
                }
            } catch (NumberFormatException e) {
                // 忽略无法解析的职级
                continue;
            }
        }

        // 9. 返回职级最高的辅助工程师，如果有多个同等级则返回第一个
        if (!qualifiedAssistants.isEmpty()) {
            // 按职级降序排序，职级高的在前
            qualifiedAssistants.sort((a, b) -> {
                try {
                    int rankA = Integer.parseInt(a.getRank().substring(1));
                    int rankB = Integer.parseInt(b.getRank().substring(1));
                    return Integer.compare(rankB, rankA); // 降序
                } catch (NumberFormatException e) {
                    return 0;
                }
            });
            return qualifiedAssistants.get(0);
        }

        return null;
    }

}
