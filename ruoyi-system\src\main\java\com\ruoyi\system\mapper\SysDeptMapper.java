package com.ruoyi.system.mapper;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.domain.entity.SysDept;
import org.apache.ibatis.annotations.Param;

import java.util.Dictionary;
import java.util.List;
import java.util.Map;

/**
 * 部门管理 数据层
 *
 * <AUTHOR>
 */
public interface SysDeptMapper
{

    public List<SysDept> selectDeptList(SysDept dept);

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @param deptCheckStrictly 部门树选择项是否关联显示
     * @return 选中部门列表
     */
    public List<Integer> selectDeptListByRoleId(@Param("roleId") Long roleId, @Param("deptCheckStrictly") boolean deptCheckStrictly);

    public SysDept selectDeptById(Long deptId);

    SysDept selectDeptByNo(String deptNo);

    /**
     * 根据ID查询所有子部门
     *
     * @param deptId 部门ID
     * @return 部门列表
     */
    public List<SysDept> selectChildrenDeptById(Long deptId);

    /**
     * 根据ID列表查询所有子部门
     *
     * @param deptIds 部门ID列表
     * @return 部门列表
     */
    public List<SysDept> selectChildrenDeptByIds(Long[] deptIds);

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    public int selectNormalChildrenDeptById(Long deptId);

    /**
     * 是否存在子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public int hasChildByDeptId(Long deptId);

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public int checkDeptExistUser(Long deptId);

    /**
     * 校验部门名称是否唯一
     *
     * @param deptName 部门名称
     * @param parentId 父部门ID
     * @return 结果
     */
    public SysDept checkDeptNameUnique(@Param("deptName") String deptName, @Param("parentId") Long parentId);

    public int insertDept(SysDept dept);

    public int updateDept(SysDept dept);

    /**
     * 修改所在部门正常状态
     *
     * @param deptIds 部门ID组
     */
    public void updateDeptStatusNormal(Long[] deptIds);

    /**
     * 修改子元素关系
     *
     * @param depts 子元素
     * @return 结果
     */
    public int updateDeptChildren(@Param("depts") List<SysDept> depts);

    public List<SysDept> selectDeptByParentId(Long parentId);

    public int deleteDeptById(Long deptId);

    List<String> queryProjectItem(Long deptId);

    void deleteDeptProjectItemInfo(Long deptId);

    void batchInsertDeptProjectItemInfo(List<Map<String, Object>> mapList);

    public Long queryDeptIdByDeptName(String deptName);

    List<SysDept> selectDeptListByIds(@Param("deptIds") List<Long> deptIds);

    List<Long> selectTopDeptIds();

    List<SysDept> selectNewWorkDept();

    JSONObject selectUserDeptInfo(Long userId);

    public List<SysDept> selectDeptListFy(SysDept dept);
}
