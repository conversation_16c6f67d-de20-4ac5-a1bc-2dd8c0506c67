package com.ruoyi.software.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.util.StringUtil;
import com.ruoyi.ai.service.FlowTools;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.service.IErpService;
import com.ruoyi.common.util.ErpDataReqUtil;
import com.ruoyi.common.utils.*;
import com.ruoyi.common.vo.AiDto;
import com.ruoyi.hr.domain.TAttendanceBo;
import com.ruoyi.hr.domain.TLegalHolidays;
import com.ruoyi.hr.mapper.SysHrUserMapper;
import com.ruoyi.hr.service.ITLegalHolidaysService;
import com.ruoyi.legal.domain.LegalIcp;
import com.ruoyi.legal.mapper.LegalIcpMapper;
import com.ruoyi.mes.domain.MesToken;
import com.ruoyi.mes.mapper.MesTokenMapper;
import com.ruoyi.project.service.IProjectService;
import com.ruoyi.project.service.IProjectSoftFormulaService;
import com.ruoyi.rd.domain.RdIngredientsStandard;
import com.ruoyi.rd.domain.RdVcrp;
import com.ruoyi.rd.domain.RdYsty;
import com.ruoyi.rd.domain.Stability;
import com.ruoyi.rd.mapper.RdIngredientsMapper;
import com.ruoyi.rd.mapper.RdIngredientsStandardMapper;
import com.ruoyi.rd.mapper.RdYstyMapper;
import com.ruoyi.rd.mapper.StabilityMapper;
import com.ruoyi.software.domain.*;
import com.ruoyi.software.mapper.*;
import com.ruoyi.software.service.ISoftwareDevelopingFormulaService;
import com.ruoyi.software.service.ISoftwareService;
import com.ruoyi.software.util.*;
import com.ruoyi.system.mapper.SysDictDataMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.work.domain.MessageParams;
import com.ruoyi.work.service.MessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 研发配方Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-20
 */
@Service
public class SoftwareDevelopingFormulaServiceImpl implements ISoftwareDevelopingFormulaService
{
    @Resource
    private SoftwareDevelopingFormulaMapper softwareDevelopingFormulaMapper;
    @Resource
    private SoftwareMaterialFormulaMapper softwareMaterialFormulaMapper;
    @Resource
    private StabilityMapper stabilityMapper;
    @Resource
    private SoftwareMaterialProducerMapper softwareMaterialProducerMapper;
    @Resource
    private SoftwareMaterialProducerAgentMapper softwareMaterialProducerAgentMapper;
    @Resource
    private IProjectSoftFormulaService projectSoftFormulaService;
    @Resource
    private SoftwareMaterialMapper softwareMaterialMapper;
    @Resource
    private SoftwareMaterialFormulaSpecFfbzMapper softwareMaterialFormulaSpecFfbzMapper;
    @Resource
    private ISoftwareService softwareService;
    @Resource
    private RdIngredientsMapper rdIngredientsMapper;
    @Resource
    private IProjectService projectService;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private SoftwareFormulaReleaseMapper softwareFormulaReleaseMapper;
    @Resource
    private SysHrUserMapper sysHrUserMapper;
    @Resource
    private RdIngredientsStandardMapper rdIngredientsStandardMapper;
    @Resource
    private RdYstyMapper rdYstyMapper;
    @Resource
    private ReferenceDataUtil referenceDataUtil;
    @Resource
    private IErpService erpService;
    @Resource
    private MessageService messageService;
    @Resource
    private FormulaMapper formulaMapper;
    @Resource
    private LegalIcpMapper legalIcpMapper;
    @Resource
    private SoftwareMapper softwareMapper;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private SysDictDataMapper sysDictDataMapper;
    @Resource
    private MesTokenMapper mesTokenMapper;
    @Resource
    private EngineerWorkRecordMapper engineerWorkRecordMapper;

    @Resource
    private ITLegalHolidaysService tLegalHolidaysService;

    @Resource
    private FlowTools flowTools;

    @Override
    public void insertSoftwareDevelopingFormula(SoftwareDevelopingFormula softwareDevelopingFormula)
    {
        softwareDevelopingFormula.setOperator(SecurityUtils.getUsername());
        softwareDevelopingFormula.setCreatedTime(DateUtils.getNowDate());
        softwareDevelopingFormulaMapper.insertSoftwareDevelopingFormula(softwareDevelopingFormula);
    }

    @Override
    public void updateSoftwareDevelopingFormula(SoftwareDevelopingFormula softwareDevelopingFormula)
    {
        softwareDevelopingFormula.setLastModifiedTime(DateUtils.getNowDate());
        softwareDevelopingFormula.setOperator(SecurityUtils.getUsername());
        softwareDevelopingFormulaMapper.updateSoftwareDevelopingFormula(softwareDevelopingFormula);
    }

    @Override
    public int insertOrUpdateSoftwareDevelopingFormula(SoftwareDevelopingFormula softwareDevelopingFormula)
    {
        int reuslt = 0;
//        if(StringUtils.isNotNull(softwareDevelopingFormula.getLaboratoryCode())){
//            String labNo = softwareDevelopingFormula.getLaboratoryCode();
//            softwareDevelopingFormula.setLaboratoryCode(StringUtils.repalceKuohao(labNo));
//        }
        Integer count = softwareDevelopingFormulaMapper.queryFormulaLabCount(softwareDevelopingFormula);
        if (count > 0 && softwareDevelopingFormula.getIsDraft().intValue()==0) {
            reuslt = 2;
        }else{
            String formulaMaterialDatas = softwareDevelopingFormula.getFormulaMaterialDatas();
            List<Integer> addMaterialIdList = new ArrayList<Integer>();
            if(StringUtils.isNotNull(formulaMaterialDatas)) {
                JSONArray array = JSONArray.parseArray(formulaMaterialDatas);
                Integer isCalc = 1;
                BigDecimal formulaPrice = WagesUtils.bigDecimal("0");
                BigDecimal totalPercent = WagesUtils.bigDecimal("0");
                for (int i = 0; i < array.size(); i++) {
                    JSONObject obj = array.getJSONObject(i);
                    BigDecimal price = obj.getBigDecimal("price");
                    String percentage = obj.getString("percentage");
                    Integer materialId = obj.getInteger("materialId");
                    Integer type = obj.getInteger("type");
                    type = StringUtils.convertDefaultValue(type,0);
                    if(type==0){
                        addMaterialIdList.add(materialId);
                    }
                    if (StringUtils.isNull(price)) {
                        isCalc = 0;
                    }else{
                        if(StringUtils.isNull(percentage)){
                            return 3;  //缺失原料比例
                        }else{
                            if(percentage.startsWith(".")){
                                percentage = "0"+percentage;
                            }
                            BigDecimal newPercentage = WagesUtils.bigDecimal(percentage,10);
                            BigDecimal totalPrice = price.multiply(newPercentage).divide(new BigDecimal("100"));
                            formulaPrice = formulaPrice.add(totalPrice);
                            totalPercent = totalPercent.add(newPercentage);
                        }
                    }
                }
                if(totalPercent.doubleValue()!=100 && softwareDevelopingFormula.getIsDraft().intValue()==0){
                    return 4;  //原料比例不等于100
                }
                softwareDevelopingFormula.setPrice(formulaPrice.setScale(2,BigDecimal.ROUND_HALF_UP));
                softwareDevelopingFormula.setIsCalc(isCalc);
            }
            String materialIds = softwareDevelopingFormula.getAddTips();
            boolean isQuery = false;
            if (StringUtils.isNotNull(materialIds) && addMaterialIdList!=null && addMaterialIdList.size()>0) {
                List<Integer> idList = StringUtils.stringToIntegerList(materialIds);
                for (Integer id : addMaterialIdList) {
                    if (idList.contains(id)) {
                        isQuery = true;
                    }
                }
                if(isQuery && addMaterialIdList!=null && addMaterialIdList.size()>0){
                    Integer result = softwareMaterialMapper.queryFormulaMaterialStatusData(addMaterialIdList);
                    if(result>0 && softwareDevelopingFormula.getIsDraft().intValue()==0){
                        return 5;
                    }
                }
            }
            boolean isEdit = false;
            if(softwareDevelopingFormula!=null && StringUtils.isNotNull(softwareDevelopingFormula.getId()) && softwareDevelopingFormula.getId()>0){
                isEdit = true;
            }else{
                //获取今年配方数
                Integer formulaCount = softwareDevelopingFormulaMapper.queryFormulaCount();
                formulaCount = StringUtils.convertDefaultValue(formulaCount, 0);
                formulaCount = formulaCount + 1;
                String code = DateUtils.dateTime(DateUtils.getNowDate(),DateUtils.YY) + StringUtils.addLeftToEqualLength(String.valueOf(formulaCount),8);
                softwareDevelopingFormula.setFormulaCode(code);
            }
            if(StringUtils.isNotNull(softwareDevelopingFormula.getCategoryText())){
                String categoryText = softwareDevelopingFormula.getCategoryText();
                List<Integer> category = StringUtils.stringToIntegerList(categoryText);
                softwareDevelopingFormula.setCategory(category.get(category.size()-1));
            }
            if(StringUtils.isNotNull(softwareDevelopingFormula.getCirText())){
                String cirText = softwareDevelopingFormula.getCirText();
                List<Integer> cir = StringUtils.stringToIntegerList(cirText);
                softwareDevelopingFormula.setCirId(cir.get(cir.size()-1));
            }
            if(StringUtils.isNotNull(softwareDevelopingFormula.getDuliText())){
                String duliText = softwareDevelopingFormula.getDuliText();
                List<Integer> duli = StringUtils.stringToIntegerList(duliText);
                softwareDevelopingFormula.setDuliId(duli.get(duli.size()-1));
            }
            if(isEdit){
                softwareDevelopingFormula.setLastModifiedTime(DateUtils.getNowDate());
                softwareDevelopingFormula.setOperator(SecurityUtils.getUsername());
                reuslt = softwareDevelopingFormulaMapper.updateSoftwareDevelopingFormula(softwareDevelopingFormula);
            }else{
                //保存数据 获取当前人所在一级部门
                Long deptId = sysHrUserMapper.queryUserFormulaRoleDeptId(SecurityUtils.getUserId());
                softwareDevelopingFormula.setDeptId(deptId);
                softwareDevelopingFormula.setOrganizationId(deptId);
                softwareDevelopingFormula.setOperator(SecurityUtils.getUsername());
                softwareDevelopingFormula.setCreatedTime(DateUtils.getNowDate());



                //是否复制配方
                if(StringUtils.isNotNull(softwareDevelopingFormula.getCopyFormulaId())){
                    SoftwareDevelopingFormula dataInfo = softwareDevelopingFormulaMapper.selectSoftwareDevelopingFormulaById(softwareDevelopingFormula.getCopyFormulaId());
                    if(dataInfo!=null){
                        softwareDevelopingFormula.setExecNumber(dataInfo.getExecNumber());
                        softwareDevelopingFormula.setExecNumberId(dataInfo.getExecNumberId());
                        softwareDevelopingFormula.setCurrentTemplateId(dataInfo.getCurrentTemplateId());
                        if(StringUtils.isNotNull(dataInfo.getGongyijianshu())){
                            softwareDevelopingFormula.setGongyijianshu(dataInfo.getGongyijianshu());
                        }
                        if(StringUtils.isNotNull(dataInfo.getJcxmJson())){
                            softwareDevelopingFormula.setJcxmJson(dataInfo.getJcxmJson());
                        }
                    }
                }

                reuslt = softwareDevelopingFormulaMapper.insertSoftwareDevelopingFormula(softwareDevelopingFormula);

                //是否复制配方
                if(StringUtils.isNotNull(softwareDevelopingFormula.getCopyFormulaId())){
                    //保存复制的半成品模板
                    softwareDevelopingFormulaMapper.insertFormulaBcpTemplateData(softwareDevelopingFormula);
                }


                Map<String,Object> params = new HashMap<String,Object>();
                params.put("formulaId",softwareDevelopingFormula.getId());
                params.put("deptId",deptId);
                params.put("shareDeptId",null);
                params.put("type",1);
                params.put("userName",SecurityUtils.getUsername());
                softwareDevelopingFormulaMapper.saveFormulaDeptDataInfo(params);

            }
            if(StringUtils.isNotNull(formulaMaterialDatas)){
                JSONArray array = JSONArray.parseArray(formulaMaterialDatas);
                List<SoftwareMaterialFormula> dataList = new ArrayList<SoftwareMaterialFormula>();
                List<SoftwareMaterialFormula> oldDataList = new ArrayList<SoftwareMaterialFormula>();
                for(int i = 0;i<array.size();i++){
                    JSONObject obj = array.getJSONObject(i);
                    SoftwareMaterialFormula softwareMaterialFormula = new SoftwareMaterialFormula();
                    softwareMaterialFormula = JSONObject.parseObject(obj.toJSONString(),SoftwareMaterialFormula.class);
                    String percentage = softwareMaterialFormula.getPercentage();
                    if(StringUtils.isNotNull(percentage) && percentage.startsWith(".")){
                        percentage = "0" + percentage;
                        softwareMaterialFormula.setPercentage(percentage);
                    }
                    softwareMaterialFormula.setFormulaId(softwareDevelopingFormula.getId());
                    softwareMaterialFormula.setOperator(SecurityUtils.getUsername());
                    dataList.add(softwareMaterialFormula);
                }
                if(dataList!=null && dataList.size()>0){
                    //删除配方记录
                    if(isEdit){
                        List<SoftwareMaterialFormula> newFormulaDataList = new ArrayList<SoftwareMaterialFormula>();
                        oldDataList = softwareMaterialFormulaMapper.queryMaterialFormulaList(softwareDevelopingFormula.getId());
                        List<Long> newIdList = new ArrayList<Long>();
                        List<Long> oldIdList = new ArrayList<Long>();
                        for(SoftwareMaterialFormula sf : oldDataList){
                            oldIdList.add(sf.getId());
                        }
                        for(SoftwareMaterialFormula sf : dataList){
                            if(StringUtils.isNotNull(sf.getId())){
                                newIdList.add(sf.getId());
                                sf.setLastModifiedTime(DateUtils.getNowDate());
                                softwareMaterialFormulaMapper.updateSoftwareMaterialFormula(sf);
                            }else{
                                newFormulaDataList.add(sf);
                            }
                        }
                        List<Long> deleteIdList = new ArrayList<Long>();
                        for(Long fId : oldIdList){
                            if(!newIdList.contains(fId)){
                                deleteIdList.add(fId);
                            }
                        }
                        if(deleteIdList!=null && deleteIdList.size()>0){
                            softwareMaterialFormulaMapper.deleteMaterialFormulaDatas(deleteIdList);
                        }
                        if(newFormulaDataList!=null && newFormulaDataList.size()>0){
                            softwareMaterialFormulaMapper.batchInsertFormulaMaterialData(newFormulaDataList);
                        }
                    }else{
                        softwareMaterialFormulaMapper.batchInsertFormulaMaterialData(dataList);
                    }
                }
                proccessModifiedLog(oldDataList,dataList,SecurityUtils.getUsername());
            }

            Integer isDraft = softwareDevelopingFormula.getIsDraft();
            if(isDraft!=null && isDraft.intValue()==0 && StringUtils.isNotNull(softwareDevelopingFormula.getLaboratoryCode())){
                new Thread(){
                   public void run(){
                       //同步研发软件数据
                       JSONObject paramsObj = new JSONObject();
                       JSONObject obj = new JSONObject();
                       obj.put("code", softwareDevelopingFormula.getFormulaCode());
                       obj.put("labNo", softwareDevelopingFormula.getLaboratoryCode());
                       obj.put("operName", SecurityUtils.getUsername());
                       obj.put("createdTime",DateUtils.dateTime(DateUtils.getNowDate(),DateUtils.YYYY_MM_DD_HH_MM_SS));
                       paramsObj.put("formualInfo",obj);
                       projectSoftFormulaService.queryProjectFormulaDetailInfo(paramsObj);
                       //是否包含日本原料
                       softwareDevelopingFormulaMapper.updateFormulaMaterialData(softwareDevelopingFormula.getId());
                       //是否含有周期超过30天原料
                       softwareDevelopingFormulaMapper.updateFormulaMaterialCycleData(softwareDevelopingFormula.getId());
                   }
                }.start();
            }
        }
        new Thread(){
            public void run(){
                try {
                    Thread.sleep(5500);
                    processSoftwareDevelopingFormulaDetailById(softwareDevelopingFormula.getId());
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }

            }
        }.start();
        return reuslt;
     }

    @Override
    public List<JSONObject> queryFormulaMaterialData(Long formulaId) {
        List<JSONObject> list = softwareMaterialFormulaMapper.queryFormulaMaterialData(formulaId);
        if(list!=null && list.size()>0){
            for(JSONObject obj : list) {
                if(!(obj.containsKey("key") && StringUtils.isNotNull(obj.getString("key")))){
                    obj.put("key", UUID.randomUUID().toString().replaceAll("-",""));
                }
                Integer type = obj.getInteger("type");
                Integer isUse = obj.getInteger("isUse");
                type = StringUtils.convertDefaultValue(type,0);
                isUse = StringUtils.convertDefaultValue(isUse,0);
                obj.put("isUse",isUse);
                obj.put("isRelation","0");
                List<JSONObject> materialArray = new ArrayList<JSONObject>();
                if(type==1){
                    obj.put("materialCode",obj.getString("laboratoryCode"));
                    obj.put("price",obj.getString("price1"));
                    String itemNameText = obj.getString("itemNameText");
                    if(StringUtils.isNotNull(itemNameText)){
                        obj.put("materialGoodsName",obj.getString("productName")+"("+itemNameText+")");
                    }else{
                        obj.put("materialGoodsName",obj.getString("productName"));
                    }
                }else{
                    String designatedUse = obj.getString("designatedUse");
                    if("指定".equals(designatedUse)){
                        Long materialId = obj.getLong("materialId");
                        if(materialId!=null && materialId>0){
                            materialArray = softwareDevelopingFormulaMapper.queryFormulaAppointMaterialDataList(materialId);
                        }
                    }else{
                        obj.put("appointCode","");
                    }
                    SoftwareMaterial softwareMaterial = new SoftwareMaterial();
                    softwareMaterial.setMaterialCode(obj.getString("materialCode"));
                    //查询是否存在可替换原料
                    List<String> relationMaterialCodeList = softwareMaterialMapper.queryRelationMaterialDataInfo(softwareMaterial);
                    if(relationMaterialCodeList!=null && relationMaterialCodeList.size()>0){
                        obj.put("isRelation","1");
                        obj.put("relationCode",StringUtils.join(relationMaterialCodeList,"、"));
                    }
                }
                obj.put("materialCodes",materialArray);
                Integer isImport = obj.getInteger("isImport");
                String importCountry = obj.getString("importCountry");
                if(StringUtils.isNotNull(isImport) && isImport.intValue()==1){
                    obj.put("isImport",1);
                    obj.put("importCountry",importCountry);
                }else{
                    obj.put("isImport",0);
                    obj.put("importCountry","");
                }
            }
        }
        Collections.sort(list, (o1, o2) -> {
            String reg = "[^\\d]";
            String t1 = o1.getString("subItem");
            t1 = StringUtils.convertDefaultValue(t1,"/");
            String t2 = o2.getString("subItem");
            t2 = StringUtils.convertDefaultValue(t2,"/");
            try {
                int first = Integer.parseInt(StringUtils.convertDefaultValue(t1.replaceAll(reg, ""), "-1"))+t1.getBytes()[0]*100;
                int second = Integer.parseInt(StringUtils.convertDefaultValue(t2.replaceAll(reg, ""), "-1"))+t2.getBytes()[0]*100;
                if (first > second) {
                    return 1;
                } else if (first < second) {
                    return -1;
                } else {
                    return 0;
                }
            } catch (NumberFormatException e) {
                System.out.println(e.getMessage());
                return 0;
            }catch (Exception e) {
                System.out.println(e.getMessage());
                return 0;
            }
        });
        return list;
    }

    @Override
    public JSONObject queryFormulaMaterialObjData(Long id) {
        JSONObject returnObj = new JSONObject();
        List<JSONObject> list = softwareMaterialFormulaMapper.queryFormulaMaterialData(id);
        StringBuffer materialIdBuff = new StringBuffer();
        String tipsStr = "";
        if(list!=null && list.size()>0){
            for(JSONObject obj : list) {
                if(!(obj.containsKey("key") && StringUtils.isNotNull(obj.getString("key")))){
                    obj.put("key", UUID.randomUUID().toString().replaceAll("-",""));
                }
                Integer type = obj.getInteger("type");
                type = StringUtils.convertDefaultValue(type,0);
                List<JSONObject> materialArray = new ArrayList<JSONObject>();
                obj.put("isRelation","0");
                if(type==1){
                    obj.put("materialCode",obj.getString("laboratoryCode"));
                    obj.put("price",obj.getString("price1"));
                    String itemNameText = obj.getString("itemNameText");
                    if(StringUtils.isNotNull(itemNameText)){
                        obj.put("materialGoodsName",obj.getString("productName")+"("+itemNameText+")");
                    }else{
                        obj.put("materialGoodsName",obj.getString("productName"));
                    }
                }else{
                    Integer status = obj.getInteger("status");
                    status = StringUtils.convertDefaultValue(status, 0);
                    if(status==1){
                        materialIdBuff.append(",").append(obj.getLong("materialId"));
                    }
                    if(status==2){
                        materialIdBuff.append(",").append(obj.getLong("materialId"));
                    }
                    String designatedUse = obj.getString("designatedUse");
                    if("指定".equals(designatedUse)){
                        Long materialId = obj.getLong("materialId");
                        if(materialId!=null && materialId>0){
                            materialArray = softwareDevelopingFormulaMapper.queryFormulaAppointMaterialDataList(materialId);
                        }
                    }else{
                        obj.put("appointCode","");
                    }
                    SoftwareMaterial softwareMaterial = new SoftwareMaterial();
                    softwareMaterial.setMaterialCode(obj.getString("materialCode"));
                    //查询是否存在可替换原料
                    List<String> relationMaterialCodeList = softwareMaterialMapper.queryRelationMaterialDataInfo(softwareMaterial);
                    if(relationMaterialCodeList!=null && relationMaterialCodeList.size()>0){
                        obj.put("isRelation","1");
                        obj.put("relationCode",StringUtils.join(relationMaterialCodeList,"、"));
                    }
                }
                obj.put("materialCodes",materialArray);
                Integer isImport = obj.getInteger("isImport");
                String importCountry = obj.getString("importCountry");
                if(StringUtils.isNotNull(isImport) && isImport.intValue()==1){
                    obj.put("isImport",1);
                    obj.put("importCountry",importCountry);
                }else{
                    obj.put("isImport",0);
                    obj.put("importCountry","");
                }
            }
        }
        Collections.sort(list, (o1, o2) -> {
            String reg = "[^\\d]";
            String t1 = o1.getString("subItem");
            t1 = StringUtils.convertDefaultValue(t1,"/");
            String t2 = o2.getString("subItem");
            t2 = StringUtils.convertDefaultValue(t2,"/");
            try {
                int first = Integer.parseInt(StringUtils.convertDefaultValue(t1.replaceAll(reg, ""), "-1"))+t1.getBytes()[0]*100;
                int second = Integer.parseInt(StringUtils.convertDefaultValue(t2.replaceAll(reg, ""), "-1"))+t2.getBytes()[0]*100;
                if (first > second) {
                    return 1;
                } else if (first < second) {
                    return -1;
                } else {
                    return 0;
                }
            } catch (NumberFormatException e) {
                System.out.println(e.getMessage());
                return 0;
            }catch (Exception e) {
                System.out.println(e.getMessage());
                return 0;
            }
        });
        returnObj.put("dataList",list);
        if(StringUtils.isNotNull(materialIdBuff)){
            tipsStr = materialIdBuff.substring(1);
        }
        returnObj.put("tips",tipsStr);
        return returnObj;
    }

    @Override
    public SoftwareDevelopingFormula selectSoftwareDevelopingFormulaDetailById(Long id) {
        SoftwareDevelopingFormula softwareDevelopingFormula = softwareDevelopingFormulaMapper.selectSoftwareDevelopingFormulaById(id);
        Integer isResult = 0; // 是否含有待评估
        Integer isGt = 0; // 是否含有超过历史使用量
        Integer isCiji = 0; // 是否含有刺激
        Integer isZhimin = 0; // 是否含有致敏
        List<JSONObject> essenceList = new ArrayList<JSONObject>();
        //是否儿童产品
        boolean isChildrean = false;
        String syrq = softwareDevelopingFormula.getSyrq();
        Integer isContainsEssence = 0;  //是否含有香精
        BigDecimal totalFragrancePercent = WagesUtils.bigDecimal("0");
        if(StringUtils.isNotNull(syrq) && (syrq.contains("1") || syrq.contains("2"))){
            isChildrean = true;
        }
        //获取配方对应VCPR CODE
        RdVcrp rdVcrpInfo = rdIngredientsStandardMapper.queryFormualVcrpDataInfo(softwareDevelopingFormula.getDuliId());
        boolean isVcrpCode = false;
        if(rdVcrpInfo!=null && StringUtils.isNotNull(rdVcrpInfo.getCode())){
            isVcrpCode = true;
        }
        Set<String> essenceSet = new HashSet<String>(); //香精原料
        List<Map<String, Object>> formulaIdList = new ArrayList<Map<String, Object>>();
        List<JSONObject> formulaMaterialDatas = queryFormulaMaterialData(id);
        if(formulaMaterialDatas!=null && formulaMaterialDatas.size()>0){
            for(JSONObject obj : formulaMaterialDatas){
                Integer type = obj.getInteger("type");
                type = StringUtils.convertDefaultValue(type,0);
                if(type==1){
                    Map<String, Object> formulaIdMap = new HashMap<String,Object>();
                    formulaIdMap.put("formulaId", obj.getLong("materialId"));
                    formulaIdMap.put("percentage",obj.getString("percentage"));
                    formulaIdList.add(formulaIdMap);
                }
            }
            softwareDevelopingFormula.setFormulaMaterialDatas(JSONArray.toJSONString(formulaMaterialDatas));
        }

        //1 驻留  2 淋洗
        String pflx = softwareDevelopingFormula.getPflx();
        //获取成分组分数据
        {
            //获取配方表,组分表数据
            List<Map<String, Object>> dataList = softwareDevelopingFormulaMapper.queryIngredientExcelList(id);
            //特殊原料信息
            List<Map<String, Object>> specMaterialData = new ArrayList<Map<String, Object>>();
            //停止原料
            List<String> disableMaterials = new ArrayList<String>();
            //替换原料
            List<String> repalceMaterials = new ArrayList<String>();
            ComponentUtil tipsMapUtil = new ComponentUtil();
            //获取色淀 纳米级极信息
            Map<String,Object> materialsMap = new HashMap<String,Object>();
            Set<String> materialSet = new HashSet<String>();
            if (formulaIdList != null && formulaIdList.size() > 0) {
                List<Map<String, Object>> newDataList = softwareDevelopingFormulaMapper.queryIngredientNewExcelList(formulaIdList);
                if (newDataList != null && newDataList.size() > 0) {
                    for (Map<String, Object> map : newDataList) {
                        Object oldFormulaID = map.get("formulaId");
                        Object oldPercentage = map.get("percentage");
                        if (StringUtils.isNull(oldPercentage)) {
                            oldPercentage = 0;
                        }
                        for (Map<String, Object> idMap : formulaIdList) {
                            Object formulaId = idMap.get("formulaId");
                            Object percentage = idMap.get("percentage");
                            if (StringUtils.isNull(percentage)) {
                                percentage = 0;
                            }
                            if (String.valueOf(formulaId).equals(String.valueOf(oldFormulaID))) {
                                BigDecimal res = new BigDecimal(String.valueOf(percentage))
                                        .multiply(new BigDecimal(String.valueOf(oldPercentage)))
                                        .divide(new BigDecimal(100));
                                map.put("percentage", res);
                            }
                        }
                    }
                }
                if (dataList != null && dataList.size() > 0) {
                    if (newDataList != null && newDataList.size() > 0) {
                        dataList.addAll(newDataList);
                    }
                } else {
                    if (newDataList != null && newDataList.size() > 0) {
                        dataList = newDataList;
                    }
                }
            }
            if (dataList != null && dataList.size() > 0) {
                SortUtil.listSort(dataList, "percentage", true);
            }
            BigDecimal totalPercert = new BigDecimal("0");
            BigDecimal sjTotalPercet = new BigDecimal("0");
            for (Map<String, Object> data : dataList) {
                Object percentage = data.get("percentage");
                totalPercert = totalPercert.add(new BigDecimal(String.valueOf(percentage)));
                Object inciName = data.get("inciName");
                Object inciNameEng = data.get("inciNameEng");
                Object proportion = data.get("proportion");
                Object typeical = data.get("typeical");
                Object sjProportion = data.get("sjProportion");
                List<Symd> SELECT_SYMD_DATA_LIST = new ArrayList<Symd>();
                List<String> symdDataList = new ArrayList<String>();
                Object symdInfo = data.get("symdInfo");
                if (StringUtils.isNotNull(symdInfo)) {
                    symdDataList = StringUtils.str2List(String.valueOf(symdInfo), "~",true,true);
                }
                data.put("symdDataList", symdDataList);
                data.put("SYMD_DATA_LIST", SELECT_SYMD_DATA_LIST);
                Object inicRemark = data.get("inicRemark");
                Object inicNmjyl = data.get("inicNmjyl");
                Object inicYzwsybw = data.get("inicYzwsybw");
                Object inicBxjmcjyl = data.get("inicBxjmcjyl");
                Object inicLbyl = data.get("inicLbyl");
                String val = "";
                StringBuffer sb = new StringBuffer();
                if (StringUtils.isNotNull(inicRemark)) {
                    List<Sdzl> SDZL_DATA_LIST = new MaterialConstansUtil().SDZL_DATA_LIST;
                    for (Sdzl sdzl : SDZL_DATA_LIST) {
                        if (sdzl.getId() == Integer.valueOf(inicRemark + "")) {
                            val = DictUtils.getDictLabel("SOFTWARE_SDZL",String.valueOf(inicRemark));
                            sb.append(val);
                            break;
                        }
                    }
                }
                if (StringUtils.isNotNull(inicNmjyl)) {
                    if ("是".equals(inicNmjyl)) {
                        sb.append("<br />").append("纳米级");
                    }
                }
                if (StringUtils.isNotNull(inicYzwsybw)) {
                    sb.append("<br />").append("原植物使用部位").append(inicYzwsybw);
                }
                if (StringUtils.isNotNull(inicBxjmcjyl)) {
                    sb.append("<br />").append("变性剂").append(inicBxjmcjyl);
                }
                if (StringUtils.isNotNull(inicLbyl)) {
                    sb.append("<br />").append("类别原料").append(inicLbyl);
                }
                data.put("inicRemark", sb.toString());
                List<Map<String, Object>> inicDataList = new ArrayList<Map<String, Object>>();
                if (StringUtils.isNotNull(inciName)) {
                    if (inciName.toString().indexOf("~") > -1) {
                        String[] dataArr = inciName.toString().split("~");
                        String[] dataEngArr = inciNameEng.toString().split("~");
                        String[] dataProportionArr = proportion.toString().split("~");
                        String[] typeicalArr;
                        if(StringUtils.isNotNull(typeical)){
                            typeicalArr = typeical.toString().split("~");
                        }else{
                            typeicalArr = new String[dataProportionArr.length];
                        }
                        String[] dataSjProportionArr = StringUtils.isNull(sjProportion) ? new String[0]
                                : sjProportion.toString().split("~");
                        if (dataArr != null && dataArr.length > 0) {
                            for (int i = 0; i < dataArr.length; i++) {
                                Map<String, Object> mapData = new HashMap<String, Object>();
                                if(StringUtils.isNotNull(dataArr[i])){
                                    materialSet.add(dataArr[i]);
                                }
                                mapData.put("inciName", dataArr[i]);
                                mapData.put("inciNameEng", dataEngArr[i]);
                                mapData.put("typeical", typeicalArr[i]);
                                String minPro = "";
                                String proportionSingle = dataProportionArr[i];
                                String typeicalSingle = typeicalArr[i];
                                String sjProportionSingle = dataSjProportionArr.length > i ? dataSjProportionArr[i]
                                        : "/";
                                if (!StringUtil.isEmpty(proportionSingle) && proportionSingle.indexOf("-") > 0) {
                                    FormulaDataUtil formulaDateUtil = new FormulaDataUtil();
                                    minPro = formulaDateUtil.getLeftNums(proportionSingle);
                                    BigDecimal returnNum = formulaDateUtil.getMiddleNums(proportionSingle);
                                    if (returnNum.doubleValue() > 0) {
                                        if(StringUtils.isNotNull(typeicalSingle) && ErpDataReqUtil.isInteger(typeicalSingle.toString())){
                                            proportionSingle = typeicalSingle.toString();
                                        }else{
                                            proportionSingle = returnNum.toString();
                                        }
                                    }
                                }
                                mapData.put("materialProportion", proportionSingle);
                                mapData.put("proportionSingle", proportionSingle);
                                String proportionVal = ComponentUtil.processCalcPercent(proportionSingle, percentage + "");
                                sjTotalPercet = sjTotalPercet.add(new BigDecimal(proportionVal));
                                mapData.put("proportion", proportionVal);

                                if (!StringUtil.isEmpty(sjProportionSingle) && sjProportionSingle.indexOf("-") > 0) {
                                    FormulaDataUtil formulaDateUtil = new FormulaDataUtil();
                                    BigDecimal returnNum = formulaDateUtil.getMiddleNums(sjProportionSingle);
                                    if (returnNum.doubleValue() > 0) {
                                        sjProportionSingle = returnNum.toString();
                                    }
                                }
                                if ((StringUtil.isEmpty(sjProportionSingle) || "/".equals(sjProportionSingle)
                                        || "\\".equals(sjProportionSingle)) && !StringUtil.isEmpty(minPro)) {
                                    sjProportionSingle = minPro;
                                }
                                mapData.put("sjProportionSingle", sjProportionSingle);
                                mapData.put("sjProportion", ComponentUtil.processCalcPercent2(sjProportionSingle, percentage + ""));
                                inicDataList.add(mapData);
                            }
                        }
                    } else {
                        Map<String, Object> mapData = new HashMap<String, Object>();
                        if(StringUtils.isNotNull(inciName)){
                            materialSet.add(inciName.toString());
                        }
                        mapData.put("inciName", inciName);
                        mapData.put("inciNameEng", inciNameEng);
                        String proportionSingle = proportion.toString();
                        String minPro = "";
                        String sjPoportionSingle = StringUtils.isNull(sjProportion) ? "/" : sjProportion.toString();
                        if (!StringUtil.isEmpty(proportionSingle) && proportionSingle.indexOf("-") > 0) {
                            FormulaDataUtil formulaDateUtil = new FormulaDataUtil();
                            BigDecimal returnNum = formulaDateUtil.getMiddleNums(proportionSingle);
                            minPro = formulaDateUtil.getLeftNums(proportionSingle);
                            if (returnNum.doubleValue() > 0) {
                                if(StringUtils.isNotNull(typeical) && ErpDataReqUtil.isInteger(typeical.toString())){
                                    proportionSingle = typeical.toString();
                                }else{
                                    proportionSingle = returnNum.toString();
                                }
                            }
                        }
                        mapData.put("proportionSingle", proportionSingle);
                        mapData.put("materialProportion", proportionSingle);
                        mapData.put("typeical", typeical);
                        String proportionVal = ComponentUtil.processCalcPercent(proportionSingle, percentage + "");
                        sjTotalPercet = sjTotalPercet.add(new BigDecimal(proportionVal));
                        mapData.put("proportion", proportionVal);
                        if (!StringUtil.isEmpty(sjPoportionSingle) && sjPoportionSingle.indexOf("-") > 0) {
                            FormulaDataUtil formulaDateUtil = new FormulaDataUtil();
                            BigDecimal returnNum = formulaDateUtil.getMiddleNums(sjPoportionSingle);
                            if (returnNum.doubleValue() > 0) {
                                sjPoportionSingle = returnNum.toString();
                            }
                        }
                        if ((StringUtil.isEmpty(sjPoportionSingle) || "/".equals(sjPoportionSingle)
                                || "\\".equals(sjPoportionSingle)) && !StringUtil.isEmpty(minPro)) {
                            sjPoportionSingle = minPro;
                        }
                        mapData.put("sjProportionSingle", sjPoportionSingle);
                        mapData.put("sjProportion", ComponentUtil.processCalcPercent2(sjPoportionSingle, percentage + ""));
                        inicDataList.add(mapData);
                    }
                }
                data.put("inicDataList", inicDataList);
            }

            //获取原料信息
            for(String inciNameStr : materialSet){
                String result = tipsMapUtil.queryMaterialInciResult(inciNameStr,dataList);
                if(StringUtils.isNotNull(result)){
                    materialsMap.put(inciNameStr,result);
                }
            }
            //组分信息
            List<ComponentUtil> allList = new ArrayList<ComponentUtil>();
            // 获取特殊原料替换信息
            List<Map<String, Object>> tipsMaterialFormulaDataList = softwareDevelopingFormulaMapper
                    .queryMaterialFormulaSpecialInfo(id);
            Set<String> compositionalDatas = new HashSet<String>();
            for (int i = 0; i < dataList.size(); i++) {
                String name = (String) dataList.get(i).get("inciName");
                String percentage = (String) (dataList.get(i).get("percentage") + "");
                String inciNameEng = (String) dataList.get(i).get("inciNameEng");
                String proportion = (String) dataList.get(i).get("proportion");
                Object typeical =  dataList.get(i).get("typeical");
                Object status = dataList.get(i).get("status");
                String inciChiName[] = name.split("~");
                String inciEngName[] = inciNameEng.split("~");
                String proArr[] = proportion.split("~");
                String[] typeicalArr;
                if(StringUtils.isNotNull(typeical)){
                    typeicalArr = typeical.toString().split("~");
                }else{
                    typeicalArr = new String[proArr.length];
                }
                List<ComponentUtil> list_ = new ArrayList<ComponentUtil>();
                Object mid = dataList.get(i).get("mid");
                Object isEditIfra = dataList.get(i).get("isEditIfra");
                Object materialCode = dataList.get(i).get("materialCode");
                for (int j = 0; j < inciChiName.length; j++) {
                    String inciChiNameTips = inciChiName[j];
                    compositionalDatas.add(inciChiNameTips);
                    String tipsMaterialInciName = "";
                    Integer tips = tipsMapUtil.isTipsFun(inciChiNameTips);
                    if (tips != null && tips.intValue() == 1) {
                        Map<String, Object> tipsMap = new HashMap<String, Object>();
                        tipsMap.put("mfId", dataList.get(i).get("mfId"));
                        tipsMap.put("materialId", dataList.get(i).get("mid"));
                        tipsMap.put("materialCode", dataList.get(i).get("materialCode"));
                        tipsMap.put("inciName", inciChiName[j]);
                        tipsMap.put("formulaId", dataList.get(i).get("formulaId"));
                        tipsMap.put("formulaCode", dataList.get(i).get("formulaCode"));
                        tipsMaterialInciName = tipsMapUtil.getReplaceTipsMaterialName(tipsMap,
                                tipsMaterialFormulaDataList);
                        tipsMap.put("replaceInciName", tipsMaterialInciName);
                        specMaterialData.add(tipsMap);
                    }
                    if ("1".equals(String.valueOf(status))) {
                        disableMaterials.add(inciChiName[j]);
                    }
                    if ("2".equals(String.valueOf(status))) {
                        repalceMaterials.add(inciChiName[j]);
                    }
                    ComponentUtil componentUtil = new ComponentUtil();
                    if (StringUtil.isEmpty(tipsMaterialInciName)) {
                        componentUtil.setChiName(inciChiName[j]);
                        componentUtil.setEngName(inciEngName[j]);
                        tipsMaterialInciName = inciChiName[j];
                    } else {
                        componentUtil.setChiName(tipsMaterialInciName);
                        componentUtil.setEngName(componentUtil.getTipsMaterialEngName(tipsMaterialInciName));
                    }
                    String proStr = proArr[j];
                    String typeicalSingle = typeicalArr[j];
                    Double rate = 0D;
                    if (!StringUtil.isEmpty(proStr) && proStr.matches("\\d{1,20}\\.?\\d{0,20}")) {
                        if (StringUtil.isEmpty(percentage)) {
                            percentage = "1";
                        }
                        percentage = percentage.trim();
                        BigDecimal per = new BigDecimal(percentage);
                        BigDecimal result1 = per.multiply(new BigDecimal(proStr)).divide(new BigDecimal(100));
                        rate = result1.doubleValue();
                        componentUtil.setPercert(result1);
                    } else {
                        if (StringUtil.isEmpty(percentage)) {
                            percentage = "1";
                        }
                        percentage = percentage.trim();
                        BigDecimal per = new BigDecimal(percentage);
                        BigDecimal result1 = new BigDecimal(percentage);
                        rate = Double.valueOf(percentage);
                        if (!StringUtil.isEmpty(proStr) && proStr.indexOf("-") > 0) {
                            FormulaDataUtil formulaDataUtil = new FormulaDataUtil();
                            BigDecimal returnNum = formulaDataUtil.getMiddleNums(proStr);
                            if (returnNum.doubleValue() > 0) {
                                if(StringUtils.isNotNull(typeicalSingle) && ErpDataReqUtil.isInteger(typeicalSingle.toString())) {
                                    result1 = per.multiply(WagesUtils.bigDecimal(typeicalSingle,6)).divide(new BigDecimal(100));
                                    rate = result1.doubleValue();
                                }else{
                                    result1 = per.multiply(returnNum).divide(new BigDecimal(100));
                                    rate = result1.doubleValue();
                                }
                            }
                        }
                        componentUtil.setPercert(result1);
                    }
                    //判断是否为香精  判断比例是否合格
                    String chiName = componentUtil.getChiName();
                    if(ReferenceDataUtil.isEssence(chiName)){
                        isContainsEssence = 1;
                        JSONObject essenceObj = new JSONObject();
                        essenceObj.put("name",chiName);
                        essenceObj.put("status","2"); //默认不合规
                        if((isVcrpCode ||isChildrean) && StringUtils.isNotNull(isEditIfra) &&  "1".equals(isEditIfra.toString())){
                            if(isChildrean){
                                Map<String,Object> vcrpParams = new HashMap<String,Object>();
                                vcrpParams.put("mid",mid);
                                vcrpParams.put("vcrpCode",FormulaDataUtil.CHILDREN_VCRP);
                                JSONObject ifraData = softwareMaterialMapper.querySoftwareMaterialIfraDataListByChildrean(vcrpParams);
                                if(ifraData!=null && ifraData.containsKey("maximum")){
                                    BigDecimal maximum = ifraData.getBigDecimal("maximum");
                                    if(componentUtil.getPercert().doubleValue()<=maximum.doubleValue()){
                                        essenceObj.put("status","1");
                                    }else{
                                        essenceObj.put("status","3");
                                        essenceSet.add(String.valueOf(materialCode));
                                    }
                                }else{
                                    essenceSet.add(String.valueOf(materialCode));
                                }
                            }else{
                                Map<String,Object> vcrpParams = new HashMap<String,Object>();
                                vcrpParams.put("mid",mid);
                                vcrpParams.put("vcrpCode",rdVcrpInfo.getCode());
                                List<JSONObject> ifraData = softwareMaterialMapper.querySoftwareMaterialIfraDataListByVcrpCode(vcrpParams);
                                if(ifraData!=null && ifraData.size()==1){
                                    JSONObject ifraObj = ifraData.get(0);
                                    BigDecimal maximum = ifraObj.getBigDecimal("maximum");
                                    if(maximum!=null && componentUtil.getPercert().doubleValue()<=maximum.doubleValue()){
                                        essenceObj.put("status","1");
                                    }else{
                                        essenceObj.put("status","3");
                                    }
                                }
                            }
                        }else{
                            essenceSet.add(String.valueOf(materialCode));
                        }
                        essenceList.add(essenceObj);

                        //香精或香料， 26致敏香料组分
                        if(isChildrean){
                            SoftwareMaterial softwareMaterial = new SoftwareMaterial();
                            softwareMaterial.setId(Long.valueOf(mid.toString()));
                            List<JSONObject> materialFragranceDataList = softwareMaterialMapper.queryMaterialFragranceDataList(softwareMaterial);
                            if(materialFragranceDataList!=null && materialFragranceDataList.size()>0){
                                BigDecimal totalPer = WagesUtils.bigDecimal("0");
                                for(JSONObject obj : materialFragranceDataList){
                                    if(obj!=null && obj.containsKey("percent")){
                                        String percent = obj.getString("percent");
                                        if(StringUtils.isNotNull(percent) && ErpDataReqUtil.isInteger(percent)){
                                            totalPer = totalFragrancePercent.add(WagesUtils.bigDecimal(percent,6));
                                        }
                                    }
                                }
                                totalFragrancePercent = totalPer.multiply(WagesUtils.bigDecimal(percentage,6));
                            }
                        }
                    }
                    boolean result1 = tipsMapUtil.processList(list_, tipsMaterialInciName, rate);
                    if (!result1) {
                        list_.add(componentUtil);
                    }
                }
                tipsMapUtil.processListData(allList, list_);
            }
            Collections.sort(allList);

            Map<String, TUnProductNewSafety> mapProductSafetys = new HashMap<String, TUnProductNewSafety>();
            String chiName = ComponentUtil.getChiName(allList);
            String engName = ComponentUtil.getEngName(allList);

            List<TUnProductNewSafety> limitComponentsList = softwareService.queryProductNewSatetyList();
            for (TUnProductNewSafety productNewSafety : limitComponentsList) {
                mapProductSafetys.put(productNewSafety.getZwmc(), productNewSafety);
            }
            StringBuffer gtNum = new StringBuffer(""); // 大于0.1
            StringBuffer ltNum = new StringBuffer(""); // 小于0.1
            String gtNumStr = "";
            String ltNumStr = "";
            BigDecimal totalPercent = new BigDecimal(0);

            //安评报告使用目的
            Map<Object, Object> symdMaps = new HashMap<Object, Object>();
            Map<String, String> ckwxxhMap = new HashMap<String, String>(); // 参考文献序号
            JSONObject formulaObjParam = new JSONObject();
            formulaObjParam.put("pflx",softwareDevelopingFormula.getPflx());
            formulaObjParam.put("zybw",softwareDevelopingFormula.getZybw());
            List<Map<String, Object>> symdInfoDataList = softwareDevelopingFormulaMapper.queryFormulaSymdInfo(id);
            if (symdInfoDataList != null && symdInfoDataList.size() > 0) {
                for (Map<String, Object> map : symdInfoDataList) {
                    symdMaps.put(map.get("chiName"), map.get("symd"));
                }
            }
            for (ComponentUtil componentUtil : allList) {
                componentUtil.setIsGt(0);
                BigDecimal p = componentUtil.getPercert();
                if (StringUtils.isNotNull(p)) {
                    totalPercent = totalPercent.add(p);
                }
                String componentChiName = componentUtil.getChiName();
                componentUtil.setIsTips(tipsMapUtil.isTipsFun(componentChiName));
                if (disableMaterials.contains(componentChiName)) {
                    componentUtil.setStatus(1);
                } else if (repalceMaterials.contains(componentChiName)) {
                    componentUtil.setStatus(2);
                } else {
                    componentUtil.setStatus(0);
                }
                BigDecimal percert = componentUtil.getPercert();
                if (percert.doubleValue() > 0.1D) {
                    gtNum.append(",").append(componentChiName);
                } else {
                    ltNum.append(",").append(componentChiName);
                }
                if (!StringUtil.isEmpty(componentChiName)) {
                    TUnProductNewSafety productSafety = mapProductSafetys.get(componentChiName.trim());
                    if (productSafety != null && StringUtils.isNotNull(productSafety.getId())) {
                        componentUtil.setLabelCondition(productSafety.getSytjhzysx());
                        componentUtil.setMaxAllowConcentration(productSafety.getZdyxnd());
                        componentUtil.setOtherLimit(productSafety.getQtxzyq());
                        componentUtil.setScopeOfApplication(productSafety.getSyjsyfw());
                        componentUtil.setZllzglssyl(productSafety.getZllzglssyl());
                        componentUtil.setLxlzglssyl(productSafety.getLxlzglssyl());
                        componentUtil.setBzmx(productSafety.getBzmx());
                        FormulaDataUtil formulaDataUtil = new FormulaDataUtil();
                        formulaDataUtil.getComponentUtilInfoFgyx(symdMaps, ckwxxhMap, formulaObjParam, productSafety,
                                componentUtil);
                        if (formulaDataUtil.isInteger(componentUtil.getGcfZglssy())
                                && StringUtils.isNotNull(componentUtil.getPercert()) && componentUtil.getPercert()
                                .doubleValue() > Double.valueOf(componentUtil.getGcfZglssy())) {
                            isGt = 1;
                            componentUtil.setIsGt(1);
                        }
                        if ("原料安全信息待评估".equals(componentUtil.getGcfPgjl())) {
                            isResult = 1;
                        }
                        String conclusion = productSafety.getConclusion();
                        if(StringUtils.isNotNull(conclusion)){
                            if(conclusion.contains("无刺激性")){
                                isCiji = 1;
                            }
                            if(conclusion.contains("无致敏")){
                                isZhimin = 1;
                            }
                        }
                        JSONObject dataObj = new JSONObject();
                        dataObj.put("bzmx", productSafety.getBzmx());//备案（含明细）
                        dataObj.put("conclusion", productSafety.getConclusion());//备案（含明细）
                        dataObj.put("isNewMaterial", productSafety.getIsNewMaterial());//是否新原料
                        dataObj.put("finding", productSafety.getFinding());//安全级别
                        dataObj.put("zdsynd",productSafety.getZdyxnd());
                        dataObj.put("cirzlx",productSafety.getZl());
                        dataObj.put("cirlxx",productSafety.getLx());
                        dataObj.put("lxlzglssyl", productSafety.getLxlzglssyl());  //淋洗类产品最高历史使用量
                        dataObj.put("zllzglssyl", productSafety.getZllzglssyl());  //驻留类产品最高历史使用量
                        dataObj.put("syjsyfw", productSafety.getSyjsyfw()); //适用及(或)使用范围
                        dataObj.put("symd",productSafety.getSymd());
                        dataObj.put("lx",productSafety.getLx());
                        dataObj.put("zl",productSafety.getZl());
                        dataObj.put("xyl",productSafety.getXyl());
                        dataObj.put("newTotal",productSafety.getNewTotal());
                        dataObj.put("newRange",productSafety.getNewRange());
                        dataObj.put("otherRequest",productSafety.getOtherRequest());
                        dataObj.put("inputTotals",productSafety.getTotal());
                        String ewgScore = productSafety.getEwgScore();
                        dataObj.put("ewgScore",ewgScore);
                        String ewgColor = productSafety.getEwgColor();
                        dataObj.put("ewgColor",ewgColor);
                        if(StringUtils.isNotNull(ewgScore) && ewgScore.contains("-")){
                            dataObj.put("isSplit",1);
                        }else{
                            dataObj.put("isSplit",0);
                        }
                        dataObj.put("activity",productSafety.getActivity());
                        dataObj.put("pox",productSafety.getPox());
                        String yfSy = productSafety.getYfSy();
                        if(StringUtils.isNotNull(yfSy) && yfSy.contains("孕妇")){
                            dataObj.put("yfSy",yfSy);
                        }else{
                            dataObj.put("yfSy","");
                        }
                        dataObj.put("risk",productSafety.getRisk());
                        dataObj.put("cancer",productSafety.getCancer());
                        dataObj.put("allergies",productSafety.getAllergies());
                        dataObj.put("developmental",productSafety.getDevelopmental());


                        String bzDetail = "";
                        String bzmxDetail = "否";
                        String bz = productSafety.getBz();
                        dataObj.put("bz",bz);
                        if(StringUtils.isNotNull(bz) && ReferenceDataUtil.MATERIAL_IN_USE.equals(bz)){
                            bzmxDetail= "是";
                            bzDetail = ReferenceDataUtil.getBzmxData(productSafety.getBzmx());
                        }
                        dataObj.put("bzmxDetail",bzDetail);
                        dataObj.put("bzmx",bzmxDetail);
                        componentUtil.setDataObj(dataObj);
                    }
                }
            }
            if (StringUtils.isNotNull(gtNum)) {
                gtNumStr = gtNum.toString().substring(1);
            }
            if (StringUtils.isNotNull(ltNum)) {
                ltNumStr = ltNum.toString().substring(1);
            }
            gtNumStr = ComponentUtil.getGtRateChiName(allList,new ArrayList<String>(),new ArrayList<String>(),materialsMap,true);
            ltNumStr = ComponentUtil.getLtRateChiName(allList,new ArrayList<String>(),new ArrayList<String>(),materialsMap,true);
            String materialInciRemark = ComponentUtil.getMaterialInciRemarkName(allList,new ArrayList<String>(),new ArrayList<String>(),materialsMap,true);

            if(softwareDevelopingFormula!=null && compositionalDatas!=null && compositionalDatas.size()>0){
                boolean isProcess = true;
                Integer cirId = softwareDevelopingFormula.getCirId();
                Integer duliId = softwareDevelopingFormula.getDuliId();
                //获取毒理 驻留 淋洗数据
                List<JSONObject> duliCategory = rdIngredientsStandardMapper.queryDuliCategoryDataList();
                if(StringUtils.isNotNull(cirId) || StringUtils.isNotNull(duliId)){
                    isProcess = true;
                }
                if(isProcess){
                    //获取成分 历史使用量和毒理数据
                    List<Map<String,Object>> ingredientsDataList = rdIngredientsMapper.queryFormulaRdIngredientsData(compositionalDatas);
                    //获取中检院已上市产品原料信息2024
                    List<JSONObject> zjyDataList = rdIngredientsMapper.queryZjyIngredientsDataList(compositionalDatas);
                    ///获取中检院《国际化妆品安全评估数据索引》收录的部分原料使用信息  中检院 2025年4月10日发布
                    List<JSONObject> zjyAqpgDataList = rdIngredientsMapper.queryZjyAqpgIngredientsDataList(compositionalDatas);

                    if(ingredientsDataList!=null && ingredientsDataList.size()>0){
                        //获取成分   cir历史数据 获取毒理数据
                        for(ComponentUtil data : allList){
                            String name =  data.getChiName();
                            if(data.getDataObj()==null){
                                //System.out.println("name"+name+",data="+data);
                                data.setDataObj(new JSONObject());
                            }
                            String inputTotals = data.getDataObj().getString("inputTotals");
                            data.setPflx(pflx);
                            Integer isEssence = 0;
                            JSONObject returnObj = ComponentUtil.queryComponentCirData(name,inputTotals,zjyDataList,zjyAqpgDataList,String.valueOf(cirId),String.valueOf(duliId),ingredientsDataList,duliCategory);
                            if(returnObj.containsKey("noaelData") && StringUtils.isNotNull(returnObj.getString("noaelData"))){
                                data.setNoaelData(returnObj.getString("noaelData"));
                                data.setNoaelLabel(returnObj.getString("noaelLabel"));
                            }
                            data.setCirData(returnObj.getString("cirData"));
                            data.setOuBiao(returnObj.getString("ouBiao"));
                            data.setRiBiao(returnObj.getString("riBiao"));
                            data.setLxxData(returnObj.getString("lxxData"));
                            data.setZlxData(returnObj.getString("zlxData"));
                            data.setBabyData(returnObj.getString("babyData"));
                            data.setTotalsData(StringUtils.isNotNull(returnObj.getString("totalsData"))?returnObj.getString("totalsData"):inputTotals);
                            //中检院
                            data.setMaxTotals(returnObj.getString("maxTotals"));
                            data.setZjyDatas((List<JSONObject>)returnObj.get("zjyDatas"));
                            data.setZjySize(returnObj.getInteger("zjySize"));

                            //《国际化妆品安全评估数据索引》收录的部分原料使用信息  中检院 2025年4月10日发布
                            data.setAqpgMaxTotals(returnObj.getString("aqpgMaxTotals"));
                            data.setAqpgDatas((List<JSONObject>)returnObj.get("aqpgDatas"));
                            data.setAqpgSize(returnObj.getInteger("aqpgSize"));

                            //获取成分详情数据
                            data.setDuliOuBiaoLeaveOn(returnObj.getString("duliOuBiaoLeaveOn"));
                            data.setDuliRiBiaoLeaveOn(returnObj.getString("duliRiBiaoLeaveOn"));
                            data.setDuliRiBiaoTotals(returnObj.getString("duliRiBiaoTotals"));
                            data.setDuliOuBiaoRinseOff(returnObj.getString("duliOuBiaoRinseOff"));
                            data.setDuliRiBiaoRinseOff(returnObj.getString("duliRiBiaoRinseOff"));
                            data.setDuliOuBiaoTotals(returnObj.getString("duliOuBiaoTotals"));

                            if(ReferenceDataUtil.isEssence(name)){
                                isEssence = 1;
                            }
                            //获取自定义数据
                            JSONObject zydObj = referenceDataUtil.queryComponentZdyData(isEssence,name,dataList);
                            data.setSupplierTotals(zydObj.getString("supplierTotals"));
                            data.setSupplierLeaveOn(zydObj.getString("supplierLeaveOn"));
                            data.setSupplierRinseOff(zydObj.getString("supplierRinseOff"));
                            data.setCompanyTotals(zydObj.getString("companyTotals"));
                            data.setCompanyLeaveOn(zydObj.getString("companyLeaveOn"));
                            data.setCompanyRinseOff(zydObj.getString("companyRinseOff"));


                            data.setIsEssence(isEssence);
                            data.setIsIfra(zydObj.getInteger("isIfra"));


                            Integer componentType =  ReferenceDataUtil.getFormulaComponentType(data);
                            data.setComponentType(componentType);
                        }

                        //获取原料 历史使用量和毒理数据
                        for(Map<String,Object> map : dataList){
                            String materialCode =  map.get("materialCode") + "";
                            Object materialZdyData = map.get("materialZdyData");
                            List<Map<String, Object>> inicDataList = (List<Map<String, Object>>) map.get("inicDataList");
                            JSONObject returnObj = ComponentUtil.queryMaterialCirData(inicDataList,String.valueOf(cirId),String.valueOf(duliId),ingredientsDataList,materialZdyData,mapProductSafetys);
                            map.put("cirData",returnObj.getString("cirData"));
                            map.put("ouBiao",returnObj.getString("ouBiao"));
                            map.put("riBiao",returnObj.getString("riBiao"));
                            map.put("supplieData",returnObj.getString("supplieData"));
                            map.put("lxxData",returnObj.getString("lxxData"));
                            map.put("zlxData",returnObj.getString("zlxData"));
                            map.put("babyData",returnObj.getString("babyData"));
                            map.put("totalsData",returnObj.getString("totalsData"));


                            JSONObject formulaMaterialObj = referenceDataUtil.queryFormulaMaterialData(allList,map);
                            formulaMaterialObj.put("isNewMaterial",map.get("isNewMaterial"));
                            String percentage = map.get("percentage")+"";
                            Integer componentType = referenceDataUtil.getFormulaComponentType(percentage,pflx,formulaMaterialObj);
                            map.put("componentType",componentType);
                            //内容处理之后置空 防止页面内容过多 渲染过慢
                            map.put("materialZdyData","");
                        }
                     }
                }
            }

            JSONObject formulaObj = new JSONObject();
            formulaObj.put("dataList",dataList);  //配方列表
            formulaObj.put("totalPercentVal",new FormulaDataUtil().getNumberStr(totalPercert));
            formulaObj.put("sjTotalPercet",new FormulaDataUtil().getNumberStr(sjTotalPercet));
            //formulaObj.put("chiName",chiName);
            //formulaObj.put("engName",engName);
            //组分数据
            formulaObj.put("totalPercent",new FormulaDataUtil().getNumberStr(totalPercent));
            formulaObj.put("ltNumStr",ltNumStr);
            formulaObj.put("gtNumStr",gtNumStr);
            formulaObj.put("specMaterialData",specMaterialData);

            List<String> cjZmList = new ArrayList<String>();
            List<String> cjList = new ArrayList<String>();
            List<String> zmList = new ArrayList<String>();

            Integer duliId = softwareDevelopingFormula.getDuliId();
            if(StringUtils.isNotNull(duliId)){
                RdIngredientsStandard rdIngredientsStandard =  rdIngredientsStandardMapper.selectRdIngredientsStandardById(duliId.longValue());
                if(rdIngredientsStandard!=null){
                    BigDecimal calculatedDaily =  rdIngredientsStandard.getCalculatedDaily();  //欧标
                    BigDecimal dayAvg = rdIngredientsStandard.getDayAvg();  //日标
                    if(StringUtils.isNotNull(calculatedDaily)){
                        formulaObj.put("dayAvg",calculatedDaily+"(欧标)");
                        formulaObjParam.put("dayAvg",calculatedDaily);
                    }else if(StringUtils.isNotNull(dayAvg)){
                        formulaObj.put("dayAvg",dayAvg+"(日标)");
                        formulaObjParam.put("dayAvg",dayAvg);
                    }
                }
            }

            for (ComponentUtil componentUtil : allList) {
                String componentChiName = componentUtil.getChiName();
                if(StringUtils.isNotNull(componentChiName)){
                    Object res = materialsMap.get(componentChiName);
                    if(StringUtils.isNotNull(res)){
                        componentUtil.setChiNameNew(componentChiName+"<span style='color:purple'>("+res+")</span>");
                    }else{
                        componentUtil.setChiNameNew(componentChiName);
                    }
                    TUnProductNewSafety productSafety = mapProductSafetys.get(componentChiName.trim());
                    if (productSafety != null && StringUtils.isNotNull(productSafety.getId())) {
                        componentUtil.setLabelCondition(productSafety.getSytjhzysx());
                        componentUtil.setMaxAllowConcentration(productSafety.getZdyxnd());
                        componentUtil.setOtherLimit(productSafety.getQtxzyq());
                        componentUtil.setScopeOfApplication(productSafety.getSyjsyfw());
                        componentUtil.setZllzglssyl(productSafety.getZllzglssyl());
                        componentUtil.setLxlzglssyl(productSafety.getLxlzglssyl());
                        componentUtil.setBzmx(productSafety.getBzmx());
                        if(StringUtils.isNull(componentUtil.getTotalsData())){
                            componentUtil.setTotalsData(productSafety.getTotal());
                        }
                        FormulaDataUtil formulaDataUtil = new FormulaDataUtil();

                        formulaDataUtil.getComponentUtilInfoFgyxDetail(symdMaps, ckwxxhMap, formulaObjParam, productSafety,
                                componentUtil,essenceList,isChildrean);
                        componentUtil.setFinalConclusion(componentUtil.getGcfPgjl());
                        String isSqVal = componentUtil.getIsSq();
                        String isExceed_ = componentUtil.getIsExceed();
                        if("0".equals(isExceed_) && "1".equals(isSqVal)){
                            String conclusion = productSafety.getConclusion();
                            String jielun = "";
                            if(StringUtils.isNotNull(conclusion)){
                                if(conclusion.contains("刺激") && conclusion.contains("致敏")){
                                    jielun = "依据CIR评估报告,本配方中成分添加量在CIR历史用量之下，同时该产品依据《化妆品安全技术规范》2015版进行了皮肤变态反应测试和人体斑贴测试实验，测试结果显示本品无致敏性、无刺激性。故该原料在本品中的使用是安全的。";
                                }else if(conclusion.contains("刺激")){
                                    jielun = "依据CIR评估报告,本配方中该成分添加量在CIR历史使用浓度之下，同时该产品选取了30人，依据《化妆品安全技术规范》2015 版，进行了人体斑贴测试实验，测试结果显示本品无刺激性。故该成分在本品中的使用是安全的。";
                                }else if(conclusion.contains("致敏")){
                                    jielun = "依据CIR评估报告,本配方中该成分添加量在CIR历史浓度之下，同时该产品依据《化妆品安全技术规范》2015 版进行了皮肤变态反应测试，测试结果显示本品无致敏性。故该成分在本品中的使用是安全的。";
                                }
                                if(StringUtils.isNotNull(jielun)){
                                    componentUtil.setFinalConclusion(jielun);
                                }
                            }
                        }
                        if("0".equals(isExceed_)){
                            String conclusion = productSafety.getConclusion();
                            if(StringUtils.isNotNull(conclusion)){
                                if(conclusion.contains("AICIS") && !conclusion.contains("CIR")){
                                    componentUtil.setFinalConclusion(conclusion);
                                }
                            }
                        }
                        Integer componentType = componentUtil.getComponentType();
                        formulaDataUtil.getComponentUtilInfoFgyx(symdMaps, ckwxxhMap, formulaObjParam, productSafety,
                                componentUtil);
                        if(componentType!=null && componentType.intValue()==4 && StringUtils.isNotNull(componentUtil.getFinalConclusion())
                                && componentUtil.getFinalConclusion().contains("已上市产品原料使用信息")
                                && componentUtil.getFinalConclusion().contains("安全")){
                            componentUtil.setComponentType(1);
                            componentUtil.setIsColor(0);
                        }
                        if(componentType!=null && componentType.intValue()==4 && StringUtils.isNotNull(componentUtil.getFinalConclusion())
                                && componentUtil.getFinalConclusion().contains("国际化妆品安全评估数据索引")
                                && componentUtil.getFinalConclusion().contains("安全")){
                            componentUtil.setComponentType(1);
                            componentUtil.setIsColor(0);
                        }
                        if (formulaDataUtil.isInteger(componentUtil.getGcfZglssy())
                                && StringUtils.isNotNull(componentUtil.getPercert()) && componentUtil.getPercert()
                                .doubleValue() > Double.valueOf(componentUtil.getGcfZglssy())) {
                            isGt = 1;
                            componentUtil.setIsGt(1);
                        }
                        if ("原料安全信息待评估".equals(componentUtil.getGcfPgjl())) {
                            isResult = 1;
                        }
                        int ciji = 0;
                        int zhimin = 0;
                        String conclusion = productSafety.getConclusion();
                        String isAqpgZjy  = componentUtil.getIsAqpgZjy();
                        if(StringUtils.isNotNull(conclusion) && "0".equals(isAqpgZjy)){
                            if(conclusion.contains("无刺激性")){
                                isCiji = 1;
                                ciji = 1;
                            }
                            if(conclusion.contains("无致敏")){
                                isZhimin = 1;
                                zhimin = 1;
                            }
                        }
                        if(ciji==1 && zhimin==1){
                            cjZmList.add(componentChiName);
                        }else if(ciji==1){
                            cjList.add(componentChiName);
                        }else if(zhimin==1){
                            zmList.add(componentChiName);
                        }
                        JSONObject dataObj = new JSONObject();
                        dataObj.put("bzmx", productSafety.getBzmx());//备案（含明细）
                        dataObj.put("conclusion", productSafety.getConclusion());//备案（含明细）
                        dataObj.put("isNewMaterial", productSafety.getIsNewMaterial());//是否新原料
                        dataObj.put("finding", productSafety.getFinding());//安全级别
                        dataObj.put("zdsynd",productSafety.getZdyxnd());
                        dataObj.put("cirzlx",productSafety.getZl());
                        dataObj.put("cirlxx",productSafety.getLx());
                        dataObj.put("lxlzglssyl", productSafety.getLxlzglssyl());  //淋洗类产品最高历史使用量
                        dataObj.put("zllzglssyl", productSafety.getZllzglssyl());  //驻留类产品最高历史使用量
                        dataObj.put("syjsyfw", productSafety.getSyjsyfw()); //适用及(或)使用范围
                        dataObj.put("symd",productSafety.getSymd());
                        dataObj.put("lx",productSafety.getLx());
                        dataObj.put("zl",productSafety.getZl());
                        dataObj.put("xyl",productSafety.getXyl());
                        dataObj.put("newTotal",productSafety.getNewTotal());
                        dataObj.put("newRange",productSafety.getNewRange());
                        dataObj.put("otherRequest",productSafety.getOtherRequest());
                        dataObj.put("inputTotals",productSafety.getTotal());
                        String ewgScore = productSafety.getEwgScore();
                        dataObj.put("ewgScore",ewgScore);
                        String ewgColor = productSafety.getEwgColor();
                        dataObj.put("ewgColor",ewgColor);
                        if(StringUtils.isNotNull(ewgScore) && ewgScore.contains("-")){
                            dataObj.put("isSplit",1);
                        }else{
                            dataObj.put("isSplit",0);
                        }
                        dataObj.put("activity",productSafety.getActivity());
                        dataObj.put("pox",productSafety.getPox());
                        String yfSy = productSafety.getYfSy();
                        if(StringUtils.isNotNull(yfSy) && yfSy.contains("孕妇")){
                            dataObj.put("yfSy",yfSy);
                        }else{
                            dataObj.put("yfSy","");
                        }
                        dataObj.put("risk",productSafety.getRisk());
                        dataObj.put("cancer",productSafety.getCancer());
                        dataObj.put("allergies",productSafety.getAllergies());
                        dataObj.put("developmental",productSafety.getDevelopmental());


                        String bzDetail = "";
                        String bzmxDetail = "否";
                        String bz = productSafety.getBz();
                        dataObj.put("bz",bz);
                        if(StringUtils.isNotNull(bz) && ReferenceDataUtil.MATERIAL_IN_USE.equals(bz)){
                            bzmxDetail= "是";
                            bzDetail = ReferenceDataUtil.getBzmxData(productSafety.getBzmx());
                        }
                        dataObj.put("bzmxDetail",bzDetail);
                        dataObj.put("bzmx",bzmxDetail);

                        Long ystyId = productSafety.getYsTyId();
                        if(StringUtils.isNotNull(ystyId) && ystyId>0){
                            RdYsty dataInfo = rdYstyMapper.selectRdYstyById(ystyId);
                            if(dataInfo!=null){
                                dataObj.put("ystyName",dataInfo.getName());
                                dataObj.put("ystyMin",dataInfo.getMin());
                                dataObj.put("ystyMax",dataInfo.getMax());
                                dataObj.put("ystyType",dataInfo.getType());
                            }
                        }
                        componentUtil.setDataObj(dataObj);
                    }
                }else{
                    componentUtil.setChiNameNew(componentChiName);
                }
            }

            formulaObj.put("allList",allList);
            formulaObj.put("isResult",isResult);
            formulaObj.put("isGt",isGt);
            softwareDevelopingFormula.setFormulaObj(formulaObj.toJSONString());


            StringBuffer tipsBuff = new StringBuffer();
            if(cjZmList!=null && cjZmList.size()>0){
                tipsBuff.append("成分["+StringUtils.join(cjZmList,"、")+"]需确认配方刺激性和致敏性测试方法是否和结论一致");
                tipsBuff.append("。");
            }
            if(cjList!=null && cjList.size()>0){
                tipsBuff.append("成分["+StringUtils.join(cjList,"、")+"]需确认配方刺激性测试方法是否和结论一致");
                tipsBuff.append("。");
            }
            if(zmList!=null && zmList.size()>0){
                tipsBuff.append("成分["+StringUtils.join(zmList,"、")+"]需确认配方致敏性测试方法是否和结论一致");
                tipsBuff.append("。");
            }
            if(tipsBuff!=null && tipsBuff.toString().length()>0){
                String tips = tipsBuff.toString();
                softwareDevelopingFormula.setZmCjTips(tips.substring(0,tips.length()-1));
            }
        }
        //更新商品信息和原料评估内容
        String oldProductName = softwareDevelopingFormula.getProductName();
        String oldProductItemName = softwareDevelopingFormula.getItemNameText();
        String oldCustomerName = softwareDevelopingFormula.getCustomerName();
        if(StringUtils.isNotNull(softwareDevelopingFormula.getProjectNo())
           && StringUtils.isNotNull(softwareDevelopingFormula.getFormulaCode())
           && !softwareDevelopingFormula.getFormulaCode().startsWith("P")){
            JSONObject parms = new JSONObject();
            parms.put("projectNo",softwareDevelopingFormula.getProjectNo());
            JSONObject returnObj = projectService.queryProjectDataDetail(parms);
            Integer reuslt = new FormulaDataUtil().processFormulaDataInfo(softwareDevelopingFormula, returnObj);
            if(reuslt>0){
                String newProductName = softwareDevelopingFormula.getProductName();
                String newProjectItemName = softwareDevelopingFormula.getItemNameText();
                String newCustomerName = softwareDevelopingFormula.getCustomerName();
                boolean isFlag1 = !StringUtil.isEmpty(oldProductName) && !StringUtil.isEmpty(newProductName)
                        && !oldProductName.equals(newProductName);
                boolean isFlag2 = !StringUtil.isEmpty(newProjectItemName)
                        && !newProjectItemName.equals(oldProductItemName);
                boolean isFlag3 = !StringUtil.isEmpty(newCustomerName) && !newCustomerName.equals(oldCustomerName);
                if (isFlag1 || isFlag2 || isFlag3) {
                    SoftwareDevelopingFormula params = new SoftwareDevelopingFormula();
                    params.setId(softwareDevelopingFormula.getId());
                    params.setProductName(softwareDevelopingFormula.getProductName());
                    params.setCustomerName(softwareDevelopingFormula.getCustomerName());
                    params.setBrandName(softwareDevelopingFormula.getBrandName());
                    params.setSeriesName(softwareDevelopingFormula.getSeriesName());
                    softwareDevelopingFormulaMapper.updateSoftwareDevelopingFormula(params);
                }
            }
        }

        //获取P配方内容
        List<Map<String, Object>> mapsData = softwareDevelopingFormulaMapper.queryPFormulaMaterialInfo(id);
        if(mapsData!=null && mapsData.size()>0){
            for (Map<String, Object> maps : mapsData) {
                List<Map<String, Object>> materialMaps = (List<Map<String, Object>>) maps.get("materialDatas");
                BigDecimal totalPercent = new BigDecimal("0");
                for (Map<String, Object> materialMap : materialMaps) {
                    Object percentageObj = materialMap.get("percentage");
                    if (StringUtils.isNull(percentageObj)) {
                        percentageObj = "0";
                    }
                    String percentage = String.valueOf(percentageObj);
                    if (!StringUtil.isEmpty(percentage) && percentage.matches("\\d{1,20}\\.?\\d{0,20}")) {
                        percentage = percentage.trim();
                        BigDecimal per = new BigDecimal(percentage);
                        totalPercent = totalPercent.add(per);
                    }
                }
                maps.put("totalPercent", new FormulaDataUtil().getNumberStr(totalPercent));
            }
            softwareDevelopingFormula.setpFormulaMapData(mapsData);

        }

        Integer pFormulaCount = softwareDevelopingFormulaMapper.queryFormualBMaterialInfo(id);
        pFormulaCount = StringUtils.convertDefaultValue(pFormulaCount,0);
        softwareDevelopingFormula.setpFormulaCount(pFormulaCount);
        StringBuffer formulaCodeBuff = new StringBuffer();
        List<Map<String, Object>> maps = softwareDevelopingFormulaMapper.queryFormulaBMaterialDataList(id);
        for (Map<String, Object> map : maps) {
            Object formulaCode = map.get("formulaCode");
            List<Map<String, Object>> materialMaps = (List<Map<String, Object>>) map.get("materialDatas");
            List<String> materialCodeList = new ArrayList<String>();
            for (Map<String, Object> materialMap : materialMaps) {
                Object mCode = materialMap.get("materialCode");
                if (StringUtils.isNotNull(mCode) && String.valueOf(mCode).startsWith("B")) {
                    materialCodeList.add(String.valueOf(mCode));
                }
            }
            if (StringUtils.isNotNull(formulaCode)) {
                if (!String.valueOf(formulaCode).startsWith("P")) {
                    formulaCodeBuff.append(",").append(formulaCode);
                    if (materialCodeList != null && materialCodeList.size() > 0) {
                        formulaCodeBuff.append("<span style='color:red'>")
                                .append(Arrays.toString(materialCodeList.toArray())).append("</span>");
                    }
                }
            }
        }
        if (formulaCodeBuff != null && formulaCodeBuff.toString().length() > 0) {
             softwareDevelopingFormula.setFormulaCodeBuff(formulaCodeBuff.toString().substring(1));
        }
        //更新项目内容
        SoftwareDevelopingFormula params = new SoftwareDevelopingFormula();
        params.setId(softwareDevelopingFormula.getId());
        params.setIsGt(isGt);
        params.setIsResult(isResult);
        params.setIsCiji(isCiji);
        params.setIsZhimin(isZhimin);
        softwareDevelopingFormula.setIsCiji(isCiji);
        softwareDevelopingFormula.setIsZhimin(isZhimin);


        new Thread() {
            public void run() {
                softwareDevelopingFormulaMapper.updateSoftwareDevelopingFormula(params);
            }
        }.start();




        //是否锁住
        Integer isLock = 1;
        if(softwareDevelopingFormula!=null){
            if(!softwareDevelopingFormula.getStatus().contains("cancel")) {
                if (softwareDevelopingFormula.getStatus().contains("sc") || softwareDevelopingFormula.getStatus().contains("4")) {
                    isLock = 0;
                }
                if(softwareDevelopingFormula.getFormulaCode()!=null && softwareDevelopingFormula.getFormulaCode().startsWith("P")){
                    isLock = 2;
                }
            }
        }
        softwareDevelopingFormula.setIsLock(isLock);

        return softwareDevelopingFormula;
    }

    /**
     * 安评详情版数据
     * @param params
     * @return
     */
    @Override
    public SoftwareDevelopingFormula getSoftwareDevelopingFormulaProductSafetyAssessmentData(SoftwareDevelopingFormula params) {
        Long id = params.getId();
        Long dataId = params.getDataId();
        LegalIcp legalIcp = legalIcpMapper.selectLegalIcpById(dataId);
        List<JSONObject> essenceList = new ArrayList<JSONObject>();
        SoftwareDevelopingFormula softwareDevelopingFormula = softwareDevelopingFormulaMapper.selectSoftwareDevelopingProductSafetyAssessmentDataFormulaById(id);
        if(softwareDevelopingFormula!=null && StringUtils.isNotNull(softwareDevelopingFormula.getId())
           && StringUtils.isNotNull(softwareDevelopingFormula.getFormulaProductSafetyAssessmentData())){

        }else{
            String syrq = "";
            if(legalIcp!=null){
                syrq = legalIcp.getSyrq();
            }
            if(StringUtils.isNull(syrq)){
                syrq = softwareDevelopingFormula.getSyrq();
            }
            //是否儿童产品
            boolean isChildrean = false;
            if(StringUtils.isNotNull(syrq) && (syrq.contains("1") || syrq.contains("2"))){
                isChildrean = true;
            }
            Set<String> newMaterialSet = new HashSet<String>(); //是否存在新原料
            Set<String> namiMaterialSet = new HashSet<String>();//是否存在纳米级原料
            Set<String> essenceSet = new HashSet<String>(); //香精原料
            Set<String> fragranceSet = new HashSet<String>(); //香精或香料， 26致敏香料组分
            Integer isContainsEssence = 0;  //是否含有香精
            BigDecimal totalFragrancePercent = WagesUtils.bigDecimal("0");
            //使用目的
            Set<String> symdSet = new HashSet<String>();
            //成分里不能含有“季铵盐”字段的成分
            Set<String> jaySet = new HashSet<String>();
            //二氧化钛，氧化锌，CI 77891，CI 77891，四个成分，任意组合或单一，其总使用量应当≤25%
            BigDecimal totalPercentCI = WagesUtils.bigDecimal("0");
            //以下成分避免能用于儿童产品或条件使用
            Set<String> bzmxSet = new HashSet<String>();
            //风险物质维度
            Set<String> fxwzSet = new HashSet<String>();

            boolean isKlslh = false;  //是否含有库拉索芦荟
            boolean isYyxa = false;   //椰油酰胺丙基甜菜碱
            boolean isGanyou = false;   //椰油酰胺丙基甜菜碱

            //配方维度
            Integer isResult = 0; // 是否含有待评估
            Integer isGt = 0; // 是否含有超过历史使用量
            List<Map<String, Object>> formulaIdList = new ArrayList<Map<String, Object>>();
            List<JSONObject> formulaMaterialDatas = queryFormulaMaterialData(id);
            if(formulaMaterialDatas!=null && formulaMaterialDatas.size()>0){
                for(JSONObject obj : formulaMaterialDatas){
                    Integer type = obj.getInteger("type");
                    type = StringUtils.convertDefaultValue(type,0);
                    if(type==1){
                        Map<String, Object> formulaIdMap = new HashMap<String,Object>();
                        formulaIdMap.put("formulaId", obj.getLong("materialId"));
                        formulaIdMap.put("percentage",obj.getString("percentage"));
                        formulaIdList.add(formulaIdMap);
                    }
                }
                softwareDevelopingFormula.setFormulaMaterialDatas(JSONArray.toJSONString(formulaMaterialDatas));
            }

            //1 驻留  2 淋洗
            String pflx = softwareDevelopingFormula.getPflx();
            Set<String> fxwz = new HashSet<String>();
            //获取成分组分数据
            {
                //获取配方表,组分表数据
                List<Map<String, Object>> dataList = softwareDevelopingFormulaMapper.queryIngredientExcelList(id);
                //特殊原料信息
                List<Map<String, Object>> specMaterialData = new ArrayList<Map<String, Object>>();
                //停止原料
                List<String> disableMaterials = new ArrayList<String>();
                //替换原料
                List<String> repalceMaterials = new ArrayList<String>();
                ComponentUtil tipsMapUtil = new ComponentUtil();
                //获取色淀 纳米级极信息
                Map<String,Object> materialsMap = new HashMap<String,Object>();
                Set<String> materialSet = new HashSet<String>();
                if (formulaIdList != null && formulaIdList.size() > 0) {
                    List<Map<String, Object>> newDataList = softwareDevelopingFormulaMapper.queryIngredientNewExcelList(formulaIdList);
                    if (newDataList != null && newDataList.size() > 0) {
                        for (Map<String, Object> map : newDataList) {
                            Object oldFormulaID = map.get("formulaId");
                            Object oldPercentage = map.get("percentage");
                            if (StringUtils.isNull(oldPercentage)) {
                                oldPercentage = 0;
                            }
                            for (Map<String, Object> idMap : formulaIdList) {
                                Object formulaId = idMap.get("formulaId");
                                Object percentage = idMap.get("percentage");
                                if (StringUtils.isNull(percentage)) {
                                    percentage = 0;
                                }
                                if (String.valueOf(formulaId).equals(String.valueOf(oldFormulaID))) {
                                    BigDecimal res = new BigDecimal(String.valueOf(percentage))
                                            .multiply(new BigDecimal(String.valueOf(oldPercentage)))
                                            .divide(new BigDecimal(100));
                                    map.put("percentage", res);
                                }
                            }
                        }
                    }
                    if (dataList != null && dataList.size() > 0) {
                        if (newDataList != null && newDataList.size() > 0) {
                            dataList.addAll(newDataList);
                        }
                    } else {
                        if (newDataList != null && newDataList.size() > 0) {
                            dataList = newDataList;
                        }
                    }
                }

                //配方列表
                List<ComponentUtil> formulaList = new ArrayList<ComponentUtil>();
                //文献列表
                List<Map<String,Object>> mapList= new ArrayList<Map<String,Object>>();

                if (dataList != null && dataList.size() > 0) {
                    SortUtil.listSort(dataList, "percentage", true);
                }

                //获取数据库成分基础数据
                Map<String, TUnProductNewSafety> mapProductSafetys = new HashMap<String, TUnProductNewSafety>();
                List<TUnProductNewSafety> limitComponentsList = softwareService.queryProductNewSatetyList();
                for (TUnProductNewSafety productNewSafety : limitComponentsList) {
                    mapProductSafetys.put(productNewSafety.getZwmc(), productNewSafety);
                }


                BigDecimal totalPercert = new BigDecimal("0");
                BigDecimal sjTotalPercet = new BigDecimal("0");
                for (Map<String, Object> data : dataList) {
                    Object percentage = data.get("percentage");
                    totalPercert = totalPercert.add(new BigDecimal(String.valueOf(percentage)));
                    Object inciName = data.get("inciName");
                    Object materialCode = data.get("materialCode");
                    Object inciNameEng = data.get("inciNameEng");
                    Object proportion = data.get("proportion");
                    Object typeical = data.get("typeical");
                    Object sjProportion = data.get("sjProportion");
                    List<Symd> SELECT_SYMD_DATA_LIST = new ArrayList<Symd>();
                    List<String> symdDataList = new ArrayList<String>();
                    Object symdInfo = data.get("symdInfo");
                    if (StringUtils.isNotNull(symdInfo)) {
                        symdDataList = StringUtils.str2List(String.valueOf(symdInfo), "~",true,true);
                    }
                    data.put("symdDataList", symdDataList);
                    data.put("SYMD_DATA_LIST", SELECT_SYMD_DATA_LIST);
                    Object inicRemark = data.get("inicRemark");
                    Object inicNmjyl = data.get("inicNmjyl");
                    Object cfNmjyl = data.get("cfNmjyl");
                    Object isNewMaterial = data.get("isNewMaterial");
                    if(isChildrean){
                        if(StringUtils.isNotNull(isNewMaterial)){
                            if("是".equals(String.valueOf(isNewMaterial))){
                                newMaterialSet.add(String.valueOf(materialCode));
                            }
                        }
                        if(StringUtils.isNotNull(cfNmjyl)){
                            if(cfNmjyl.toString().contains("是")){
                                namiMaterialSet.add(String.valueOf(materialCode));
                            }
                        }
                    }
                    Object inicYzwsybw = data.get("inicYzwsybw");
                    Object inicBxjmcjyl = data.get("inicBxjmcjyl");
                    Object inicLbyl = data.get("inicLbyl");
                    String val = "";
                    StringBuffer sb = new StringBuffer();
                    if (StringUtils.isNotNull(inicRemark)) {
                        List<Sdzl> SDZL_DATA_LIST = new MaterialConstansUtil().SDZL_DATA_LIST;
                        for (Sdzl sdzl : SDZL_DATA_LIST) {
                            if (sdzl.getId() == Integer.valueOf(inicRemark + "")) {
                                val = DictUtils.getDictLabel("SOFTWARE_SDZL",String.valueOf(inicRemark));
                                sb.append(val);
                                break;
                            }
                        }
                    }
                    if (StringUtils.isNotNull(inicNmjyl)) {
                        if ("是".equals(inicNmjyl)) {
                            sb.append("<br />").append("纳米级");
                        }
                    }
                    if (StringUtils.isNotNull(inicYzwsybw)) {
                        sb.append("<br />").append("原植物使用部位").append(inicYzwsybw);
                    }
                    if (StringUtils.isNotNull(inicBxjmcjyl)) {
                        sb.append("<br />").append("变性剂").append(inicBxjmcjyl);
                    }
                    if (StringUtils.isNotNull(inicLbyl)) {
                        sb.append("<br />").append("类别原料").append(inicLbyl);
                    }
                    data.put("inicRemark", sb.toString());
                    List<Map<String, Object>> inicDataList = new ArrayList<Map<String, Object>>();
                    if (StringUtils.isNotNull(inciName)) {
                        if (inciName.toString().indexOf("~") > -1) {
                            String[] dataArr = inciName.toString().split("~");
                            String[] dataEngArr = inciNameEng.toString().split("~");
                            String[] dataProportionArr = proportion.toString().split("~");
                            String[] typeicalArr;
                            if(StringUtils.isNotNull(typeical)){
                                typeicalArr = typeical.toString().split("~");
                            }else{
                                typeicalArr = new String[dataProportionArr.length];
                            }
                            String[] dataSjProportionArr = StringUtils.isNull(sjProportion) ? new String[0]
                                    : sjProportion.toString().split("~");
                            if (dataArr != null && dataArr.length > 0) {
                                for (int i = 0; i < dataArr.length; i++) {
                                    Map<String, Object> mapData = new HashMap<String, Object>();
                                    if(StringUtils.isNotNull(dataArr[i])){
                                        materialSet.add(dataArr[i]);
                                    }
                                    mapData.put("inciName", dataArr[i]);
                                    mapData.put("inciNameEng", dataEngArr[i]);
                                    mapData.put("typeical", typeicalArr[i]);
                                    String minPro = "";
                                    String proportionSingle = dataProportionArr[i];
                                    String typeicalSingle = typeicalArr[i];
                                    String sjProportionSingle = dataSjProportionArr.length > i ? dataSjProportionArr[i]
                                            : "/";
                                    if (!StringUtil.isEmpty(proportionSingle) && proportionSingle.indexOf("-") > 0) {
                                        FormulaDataUtil formulaDateUtil = new FormulaDataUtil();
                                        minPro = formulaDateUtil.getLeftNums(proportionSingle);
                                        BigDecimal returnNum = formulaDateUtil.getMiddleNums(proportionSingle);
                                        if (returnNum.doubleValue() > 0) {
                                            if(StringUtils.isNotNull(typeicalSingle) && ErpDataReqUtil.isInteger(typeicalSingle.toString())){
                                                proportionSingle = typeicalSingle.toString();
                                            }else{
                                                proportionSingle = returnNum.toString();
                                            }
                                        }
                                    }
                                    mapData.put("materialProportion", proportionSingle);
                                    mapData.put("proportionSingle", proportionSingle);
                                    String proportionVal = ComponentUtil.processCalcPercent(proportionSingle, percentage + "");
                                    sjTotalPercet = sjTotalPercet.add(new BigDecimal(proportionVal));
                                    mapData.put("proportion", proportionVal);

                                    if (!StringUtil.isEmpty(sjProportionSingle) && sjProportionSingle.indexOf("-") > 0) {
                                        FormulaDataUtil formulaDateUtil = new FormulaDataUtil();
                                        BigDecimal returnNum = formulaDateUtil.getMiddleNums(sjProportionSingle);
                                        if (returnNum.doubleValue() > 0) {
                                            sjProportionSingle = returnNum.toString();
                                        }
                                    }
                                    if ((StringUtil.isEmpty(sjProportionSingle) || "/".equals(sjProportionSingle)
                                            || "\\".equals(sjProportionSingle)) && !StringUtil.isEmpty(minPro)) {
                                        sjProportionSingle = minPro;
                                    }
                                    mapData.put("sjProportionSingle", sjProportionSingle);
                                    mapData.put("sjProportion", ComponentUtil.processCalcPercent2(sjProportionSingle, percentage + ""));
                                    inicDataList.add(mapData);
                                }
                            }
                        } else {
                            Map<String, Object> mapData = new HashMap<String, Object>();
                            if(StringUtils.isNotNull(inciName)){
                                materialSet.add(inciName.toString());
                            }
                            mapData.put("inciName", inciName);
                            mapData.put("inciNameEng", inciNameEng);
                            String proportionSingle = proportion.toString();
                            String minPro = "";
                            String sjPoportionSingle = StringUtils.isNull(sjProportion) ? "/" : sjProportion.toString();
                            if (!StringUtil.isEmpty(proportionSingle) && proportionSingle.indexOf("-") > 0) {
                                FormulaDataUtil formulaDateUtil = new FormulaDataUtil();
                                BigDecimal returnNum = formulaDateUtil.getMiddleNums(proportionSingle);
                                minPro = formulaDateUtil.getLeftNums(proportionSingle);
                                if (returnNum.doubleValue() > 0) {
                                    if(StringUtils.isNotNull(typeical) && ErpDataReqUtil.isInteger(typeical.toString())){
                                        proportionSingle = typeical.toString();
                                    }else{
                                        proportionSingle = returnNum.toString();
                                    }
                                }
                            }
                            mapData.put("proportionSingle", proportionSingle);
                            mapData.put("materialProportion", proportionSingle);
                            mapData.put("typeical", typeical);
                            String proportionVal = ComponentUtil.processCalcPercent(proportionSingle, percentage + "");
                            sjTotalPercet = sjTotalPercet.add(new BigDecimal(proportionVal));
                            mapData.put("proportion", proportionVal);
                            if (!StringUtil.isEmpty(sjPoportionSingle) && sjPoportionSingle.indexOf("-") > 0) {
                                FormulaDataUtil formulaDateUtil = new FormulaDataUtil();
                                BigDecimal returnNum = formulaDateUtil.getMiddleNums(sjPoportionSingle);
                                if (returnNum.doubleValue() > 0) {
                                    sjPoportionSingle = returnNum.toString();
                                }
                            }
                            if ((StringUtil.isEmpty(sjPoportionSingle) || "/".equals(sjPoportionSingle)
                                    || "\\".equals(sjPoportionSingle)) && !StringUtil.isEmpty(minPro)) {
                                sjPoportionSingle = minPro;
                            }
                            mapData.put("sjProportionSingle", sjPoportionSingle);
                            mapData.put("sjProportion", ComponentUtil.processCalcPercent2(sjPoportionSingle, percentage + ""));
                            inicDataList.add(mapData);
                        }
                    }
                    data.put("inicDataList", inicDataList);
                }

                //获取原料信息
                for(String inciNameStr : materialSet){
                    String result = tipsMapUtil.queryMaterialInciResult(inciNameStr,dataList);
                    if(StringUtils.isNotNull(result)){
                        materialsMap.put(inciNameStr,result);
                    }
                }
                //组分信息
                List<ComponentUtil> allList = new ArrayList<ComponentUtil>();
                // 获取特殊原料替换信息
                List<Map<String, Object>> tipsMaterialFormulaDataList = softwareDevelopingFormulaMapper
                        .queryMaterialFormulaSpecialInfo(id);
                Set<String> compositionalDatas = new HashSet<String>();
                int seq = 1;
                //获取配方对应VCPR CODE
                RdVcrp rdVcrpInfo = rdIngredientsStandardMapper.queryFormualVcrpDataInfo(softwareDevelopingFormula.getDuliId());
                boolean isVcrpCode = false;
                if(rdVcrpInfo!=null && StringUtils.isNotNull(rdVcrpInfo.getCode())){
                    isVcrpCode = true;
                }
                for (int i = 0; i < dataList.size(); i++) {
                    String name = (String) dataList.get(i).get("inciName");
                    Object materialCode = dataList.get(i).get("materialCode");
                    String percentage = (String) (dataList.get(i).get("percentage") + "");
                    String inciNameEng = (String) dataList.get(i).get("inciNameEng");
                    String proportion = (String) dataList.get(i).get("proportion");
                    Object typeical =  dataList.get(i).get("typeical");
                    Object isEditIfra = dataList.get(i).get("isEditIfra");
                    Object status = dataList.get(i).get("status");

                    //配方原料 使用目的
                    Object symdInfo = dataList.get(i).get("symdInfo");
                    if(StringUtils.isNotNull(symdInfo)){
                        if(FormulaDataUtil.isFormulaSymd(String.valueOf(symdInfo))){
                            symdSet.add(String.valueOf(materialCode));
                        }
                    }
                    //原料使用目的
                    Object inicSymd = dataList.get(i).get("inicSymd");
                    if(StringUtils.isNotNull(inicSymd)){
                       if(FormulaDataUtil.isMaterialSymd(String.valueOf(inicSymd))){
                           symdSet.add(String.valueOf(materialCode));
                       }
                    }

                    Object mfId = dataList.get(i).get("mfId");
                    Object mid = dataList.get(i).get("mid");
                    String inciChiName[] = name.split("~");
                    String inciEngName[] = inciNameEng.split("~");
                    String proArr[] = proportion.split("~");
                    String[] typeicalArr;
                    if(StringUtils.isNotNull(typeical)){
                        typeicalArr = typeical.toString().split("~");
                    }else{
                        typeicalArr = new String[proArr.length];
                    }
                    List<ComponentUtil> list_ = new ArrayList<ComponentUtil>();
                    for (int j = 0; j < inciChiName.length; j++) {
                        String inciChiNameTips = inciChiName[j];
                        if(StringUtils.isNotNull(inciChiNameTips)) {
                            ComponentUtil formulaCom = new ComponentUtil();
                            formulaCom.setSeq(seq);
                            formulaCom.setCppfSymd(StringUtils.isNotNull(symdInfo)?symdInfo.toString():"");
                            TUnProductNewSafety productSafetyInfo = mapProductSafetys.get(inciChiNameTips.trim());
                            if(StringUtils.isNotNull(mfId)){
                                formulaCom.setXz(mfId.toString());
                            }else{
                                formulaCom.setXz("-1");
                            }
                            if (productSafetyInfo != null && StringUtils.isNotNull(productSafetyInfo.getId())) {
                                String bzmx = productSafetyInfo.getBzmx();
                                String xh = productSafetyInfo.getXh();
                                String zwmc = productSafetyInfo.getZwmc();
                                String ywmc = productSafetyInfo.getYwmc();
                                formulaCom.setChiName(zwmc);
                                formulaCom.setEngName(ywmc);
                                formulaCom.setXh(xh);
                                formulaCom.setBzmx(bzmx);

                            }
                            formulaList.add(formulaCom);
                        }
                        compositionalDatas.add(inciChiNameTips);
                        String tipsMaterialInciName = "";
                        Integer tips = tipsMapUtil.isTipsFun(inciChiNameTips);
                        if (tips != null && tips.intValue() == 1) {
                            Map<String, Object> tipsMap = new HashMap<String, Object>();
                            tipsMap.put("mfId", dataList.get(i).get("mfId"));
                            tipsMap.put("materialId", dataList.get(i).get("mid"));
                            tipsMap.put("materialCode", dataList.get(i).get("materialCode"));
                            tipsMap.put("inciName", inciChiName[j]);
                            tipsMap.put("formulaId", dataList.get(i).get("formulaId"));
                            tipsMap.put("formulaCode", dataList.get(i).get("formulaCode"));
                            tipsMaterialInciName = tipsMapUtil.getReplaceTipsMaterialName(tipsMap,
                                    tipsMaterialFormulaDataList);
                            tipsMap.put("replaceInciName", tipsMaterialInciName);
                            specMaterialData.add(tipsMap);
                        }
                        if ("1".equals(String.valueOf(status))) {
                            disableMaterials.add(inciChiName[j]);
                        }
                        if ("2".equals(String.valueOf(status))) {
                            repalceMaterials.add(inciChiName[j]);
                        }
                        ComponentUtil componentUtil = new ComponentUtil();
                        if (StringUtil.isEmpty(tipsMaterialInciName)) {
                            componentUtil.setChiName(inciChiName[j]);
                            componentUtil.setEngName(inciEngName[j]);
                            tipsMaterialInciName = inciChiName[j];
                        } else {
                            componentUtil.setChiName(tipsMaterialInciName);
                            componentUtil.setEngName(componentUtil.getTipsMaterialEngName(tipsMaterialInciName));
                        }
                        String proStr = proArr[j];
                        String typeicalSingle = typeicalArr[j];
                        Double rate = 0D;
                        if (!StringUtil.isEmpty(proStr) && proStr.matches("\\d{1,20}\\.?\\d{0,20}")) {
                            if (StringUtil.isEmpty(percentage)) {
                                percentage = "1";
                            }
                            percentage = percentage.trim();
                            BigDecimal per = new BigDecimal(percentage);
                            BigDecimal result1 = per.multiply(new BigDecimal(proStr)).divide(new BigDecimal(100));
                            rate = result1.doubleValue();
                            componentUtil.setPercert(result1);
                        } else {
                            if (StringUtil.isEmpty(percentage)) {
                                percentage = "1";
                            }
                            percentage = percentage.trim();
                            BigDecimal per = new BigDecimal(percentage);
                            BigDecimal result1 = new BigDecimal(percentage);
                            rate = Double.valueOf(percentage);
                            if (!StringUtil.isEmpty(proStr) && proStr.indexOf("-") > 0) {
                                FormulaDataUtil formulaDataUtil = new FormulaDataUtil();
                                BigDecimal returnNum = formulaDataUtil.getMiddleNums(proStr);
                                if (returnNum.doubleValue() > 0) {
                                    if(StringUtils.isNotNull(typeicalSingle) && ErpDataReqUtil.isInteger(typeicalSingle.toString())) {
                                        result1 = per.multiply(WagesUtils.bigDecimal(typeicalSingle,6)).divide(new BigDecimal(100));
                                        rate = result1.doubleValue();
                                    }else{
                                        result1 = per.multiply(returnNum).divide(new BigDecimal(100));
                                        rate = result1.doubleValue();
                                    }
                                }
                            }
                            componentUtil.setPercert(result1);
                        }
                        //判断是否为香精  判断比例是否合格
                        String chiName = componentUtil.getChiName();
                        if(ReferenceDataUtil.isEssence(chiName)){
                            isContainsEssence = 1;
                            JSONObject essenceObj = new JSONObject();
                            essenceObj.put("name",chiName);
                            essenceObj.put("status","2"); //默认不合规
                            if((isVcrpCode ||isChildrean) && StringUtils.isNotNull(isEditIfra) &&  "1".equals(isEditIfra.toString())){
                                if(isChildrean){
                                    Map<String,Object> vcrpParams = new HashMap<String,Object>();
                                    vcrpParams.put("mid",mid);
                                    vcrpParams.put("vcrpCode",FormulaDataUtil.CHILDREN_VCRP);
                                    JSONObject ifraData = softwareMaterialMapper.querySoftwareMaterialIfraDataListByChildrean(vcrpParams);
                                    if(ifraData!=null && ifraData.containsKey("maximum")){
                                        BigDecimal maximum = ifraData.getBigDecimal("maximum");
                                        if(componentUtil.getPercert().doubleValue()<=maximum.doubleValue()){
                                            essenceObj.put("status","1");
                                        }else{
                                            essenceObj.put("status","3");
                                            essenceSet.add(String.valueOf(materialCode));
                                        }
                                    }else{
                                        essenceSet.add(String.valueOf(materialCode));
                                    }
                                }else{
                                    Map<String,Object> vcrpParams = new HashMap<String,Object>();
                                    vcrpParams.put("mid",mid);
                                    vcrpParams.put("vcrpCode",rdVcrpInfo.getCode());
                                    List<JSONObject> ifraData = softwareMaterialMapper.querySoftwareMaterialIfraDataListByVcrpCode(vcrpParams);
                                    if(ifraData!=null && ifraData.size()==1){
                                        JSONObject ifraObj = ifraData.get(0);
                                        BigDecimal maximum = ifraObj.getBigDecimal("maximum");
                                        if(componentUtil.getPercert().doubleValue()<=maximum.doubleValue()){
                                            essenceObj.put("status","1");
                                        }else{
                                            essenceObj.put("status","3");
                                        }
                                    }
                                }
                            }else{
                                essenceSet.add(String.valueOf(materialCode));
                            }
                            essenceList.add(essenceObj);

                            //香精或香料， 26致敏香料组分
                            if(isChildrean){
                                SoftwareMaterial softwareMaterial = new SoftwareMaterial();
                                softwareMaterial.setId(Long.valueOf(mid.toString()));
                                List<JSONObject> materialFragranceDataList = softwareMaterialMapper.queryMaterialFragranceDataList(softwareMaterial);
                                if(materialFragranceDataList!=null && materialFragranceDataList.size()>0){
                                    BigDecimal totalPer = WagesUtils.bigDecimal("0");
                                    for(JSONObject obj : materialFragranceDataList){
                                       if(obj!=null && obj.containsKey("percent")){
                                           String percent = obj.getString("percent");
                                           if(StringUtils.isNotNull(percent) && ErpDataReqUtil.isInteger(percent)){
                                               totalPer = totalFragrancePercent.add(WagesUtils.bigDecimal(percent,6));
                                           }
                                       }
                                    }
                                    totalFragrancePercent = totalPer.multiply(WagesUtils.bigDecimal(percentage,6));
                                }
                            }
                        }
                        boolean result1 = tipsMapUtil.processList(list_, tipsMaterialInciName, rate);
                        if (!result1) {
                            list_.add(componentUtil);
                        }
                    }
                    seq++;
                    tipsMapUtil.processListData(allList, list_);
                }
                Collections.sort(allList);
                String chiName = ComponentUtil.getChiName(allList);
                Integer chiNameNum = ComponentUtil.getChiNameNum(allList);
                String engName = ComponentUtil.getEngName(allList);

                BigDecimal totalPercent = new BigDecimal(0);
                //安评报告使用目的
                Map<Object, Object> symdMaps = new HashMap<Object, Object>();
                Map<String, String> ckwxxhMap = new HashMap<String, String>(); // 参考文献序号
                JSONObject formulaObjParam = new JSONObject();
                formulaObjParam.put("pflx",softwareDevelopingFormula.getPflx());
                formulaObjParam.put("zybw",softwareDevelopingFormula.getZybw());
                List<Map<String, Object>> symdInfoDataList = softwareDevelopingFormulaMapper.queryFormulaSymdInfo(id);
                if (symdInfoDataList != null && symdInfoDataList.size() > 0) {
                    for (Map<String, Object> map : symdInfoDataList) {
                        symdMaps.put(map.get("chiName"), map.get("symd"));
                    }
                }
                for (ComponentUtil componentUtil : allList) {
                    componentUtil.setIsGt(0);
                    BigDecimal p = componentUtil.getPercert();
                    if (StringUtils.isNotNull(p)) {
                        totalPercent = totalPercent.add(p);
                    }
                    String componentChiName = componentUtil.getChiName();
                    componentUtil.setIsTips(tipsMapUtil.isTipsFun(componentChiName));
                    if (disableMaterials.contains(componentChiName)) {
                        componentUtil.setStatus(1);
                    } else if (repalceMaterials.contains(componentChiName)) {
                        componentUtil.setStatus(2);
                    } else {
                        componentUtil.setStatus(0);
                    }
                }

                if(softwareDevelopingFormula!=null && compositionalDatas!=null && compositionalDatas.size()>0){
                    boolean isProcess = true;
                    Integer cirId = softwareDevelopingFormula.getCirId();
                    Integer duliId = softwareDevelopingFormula.getDuliId();
                    //获取毒理 驻留 淋洗数据
                    List<JSONObject> duliCategory = rdIngredientsStandardMapper.queryDuliCategoryDataList();
                    if(StringUtils.isNotNull(cirId) || StringUtils.isNotNull(duliId)){
                        isProcess = true;
                    }
                    if(isProcess){
                        //获取成分 历史使用量和毒理数据
                        List<Map<String,Object>> ingredientsDataList = rdIngredientsMapper.queryFormulaRdIngredientsData(compositionalDatas);
                        //获取中检院已上市产品原料信息2024
                        List<JSONObject> zjyDataList = rdIngredientsMapper.queryZjyIngredientsDataList(compositionalDatas);
                        ///获取中检院《国际化妆品安全评估数据索引》收录的部分原料使用信息  中检院 2025年4月10日发布
                        List<JSONObject> zjyAqpgDataList = rdIngredientsMapper.queryZjyAqpgIngredientsDataList(compositionalDatas);

                        if(ingredientsDataList!=null && ingredientsDataList.size()>0){
                            //获取成分   cir历史数据 获取毒理数据
                            for(ComponentUtil data : allList){
                                String componentChiName =  data.getChiName();
                                if (!StringUtil.isEmpty(componentChiName)) {
                                    TUnProductNewSafety productSafety = mapProductSafetys.get(componentChiName.trim());
                                    if (productSafety != null && StringUtils.isNotNull(productSafety.getId())) {
                                        data.setLabelCondition(productSafety.getSytjhzysx());
                                        data.setMaxAllowConcentration(productSafety.getZdyxnd());
                                        data.setOtherLimit(productSafety.getQtxzyq());
                                        data.setScopeOfApplication(productSafety.getSyjsyfw());
                                        data.setZllzglssyl(productSafety.getZllzglssyl());
                                        data.setLxlzglssyl(productSafety.getLxlzglssyl());
                                        data.setBzmx(productSafety.getBzmx());

                                        JSONObject dataObj = new JSONObject();
                                        dataObj.put("bzmx", productSafety.getBzmx());//备案（含明细）
                                        dataObj.put("conclusion", productSafety.getConclusion());//备案（含明细）
                                        dataObj.put("isNewMaterial", productSafety.getIsNewMaterial());//是否新原料
                                        dataObj.put("finding", productSafety.getFinding());//安全级别
                                        dataObj.put("zdsynd",productSafety.getZdyxnd());
                                        dataObj.put("cirzlx",productSafety.getZl());
                                        dataObj.put("cirlxx",productSafety.getLx());
                                        dataObj.put("lxlzglssyl", productSafety.getLxlzglssyl());  //淋洗类产品最高历史使用量
                                        dataObj.put("zllzglssyl", productSafety.getZllzglssyl());  //驻留类产品最高历史使用量
                                        dataObj.put("syjsyfw", productSafety.getSyjsyfw()); //适用及(或)使用范围
                                        dataObj.put("symd",productSafety.getSymd());
                                        dataObj.put("lx",productSafety.getLx());
                                        dataObj.put("zl",productSafety.getZl());
                                        dataObj.put("xyl",productSafety.getXyl());
                                        dataObj.put("newTotal",productSafety.getNewTotal());
                                        dataObj.put("newRange",productSafety.getNewRange());
                                        dataObj.put("otherRequest",productSafety.getOtherRequest());
                                        dataObj.put("inputTotals",productSafety.getTotal());
                                        String ewgScore = productSafety.getEwgScore();
                                        dataObj.put("ewgScore",ewgScore);
                                        String ewgColor = productSafety.getEwgColor();
                                        dataObj.put("ewgColor",ewgColor);
                                        if(StringUtils.isNotNull(ewgScore) && ewgScore.contains("-")){
                                            dataObj.put("isSplit",1);
                                        }else{
                                            dataObj.put("isSplit",0);
                                        }
                                        dataObj.put("activity",productSafety.getActivity());
                                        dataObj.put("pox",productSafety.getPox());
                                        String yfSy = productSafety.getYfSy();
                                        if(StringUtils.isNotNull(yfSy) && yfSy.contains("孕妇")){
                                            dataObj.put("yfSy",yfSy);
                                        }else{
                                            dataObj.put("yfSy","");
                                        }
                                        dataObj.put("risk",productSafety.getRisk());
                                        dataObj.put("cancer",productSafety.getCancer());
                                        dataObj.put("allergies",productSafety.getAllergies());
                                        dataObj.put("developmental",productSafety.getDevelopmental());


                                        String bzDetail = "";
                                        String bzmxDetail = "否";
                                        String bz = productSafety.getBz();
                                        dataObj.put("bz",bz);
                                        if(StringUtils.isNotNull(bz) && ReferenceDataUtil.MATERIAL_IN_USE.equals(bz)){
                                            bzmxDetail= "是";
                                            bzDetail = ReferenceDataUtil.getBzmxData(productSafety.getBzmx());
                                        }
                                        dataObj.put("bzmxDetail",bzDetail);
                                        dataObj.put("bzmx",bzmxDetail);

                                        //风险物质
                                        String knhydfxwz = productSafety.getKnhydfxwz();
                                        data.setAqxKnhyfxw(knhydfxwz);
                                        data.setFxwzbx(productSafety.getFxwzbz());
                                        if(StringUtils.isNotNull(knhydfxwz)){
                                            knhydfxwz = knhydfxwz.replaceAll("、",";").replaceAll("；",";").replaceAll("，",";");
                                            Set<String> dataSet = StringUtils.str2Set(knhydfxwz,";");
                                            fxwz.addAll(dataSet);
                                        }
                                        data.setDataObj(dataObj);


                                        if(data.getDataObj()==null){
                                            //System.out.println("name"+name+",data="+data);
                                            data.setDataObj(new JSONObject());
                                        }
                                        String inputTotals = data.getDataObj().getString("inputTotals");
                                        data.setPflx(pflx);
                                        Integer isEssence = 0;
                                        JSONObject returnObj = ComponentUtil.queryComponentCirData(componentChiName,inputTotals,zjyDataList,zjyAqpgDataList,String.valueOf(cirId),String.valueOf(duliId),ingredientsDataList,duliCategory);
                                        if(returnObj.containsKey("noaelData") && StringUtils.isNotNull(returnObj.getString("noaelData"))){
                                            data.setNoaelData(returnObj.getString("noaelData"));
                                            data.setNoaelLabel(returnObj.getString("noaelLabel"));
                                        }
                                        data.setCirData(returnObj.getString("cirData"));
                                        data.setOuBiao(returnObj.getString("ouBiao"));
                                        data.setRiBiao(returnObj.getString("riBiao"));
                                        data.setLxxData(returnObj.getString("lxxData"));
                                        data.setZlxData(returnObj.getString("zlxData"));
                                        data.setBabyData(returnObj.getString("babyData"));
                                        data.setTotalsData(StringUtils.isNotNull(returnObj.getString("totalsData"))?returnObj.getString("totalsData"):inputTotals);

                                        //中检院
                                        data.setMaxTotals(returnObj.getString("maxTotals"));
                                        data.setZjyDatas((List<JSONObject>)returnObj.get("zjyDatas"));
                                        data.setZjySize(returnObj.getInteger("zjySize"));

                                        //《国际化妆品安全评估数据索引》收录的部分原料使用信息  中检院 2025年4月10日发布
                                        data.setAqpgMaxTotals(returnObj.getString("aqpgMaxTotals"));
                                        data.setAqpgDatas((List<JSONObject>)returnObj.get("aqpgDatas"));
                                        data.setAqpgSize(returnObj.getInteger("aqpgSize"));

                                        //获取成分详情数据
                                        data.setDuliOuBiaoLeaveOn(returnObj.getString("duliOuBiaoLeaveOn"));
                                        data.setDuliRiBiaoLeaveOn(returnObj.getString("duliRiBiaoLeaveOn"));
                                        data.setDuliRiBiaoTotals(returnObj.getString("duliRiBiaoTotals"));
                                        data.setDuliOuBiaoRinseOff(returnObj.getString("duliOuBiaoRinseOff"));
                                        data.setDuliRiBiaoRinseOff(returnObj.getString("duliRiBiaoRinseOff"));
                                        data.setDuliOuBiaoTotals(returnObj.getString("duliOuBiaoTotals"));

                                        if(ReferenceDataUtil.isEssence(componentChiName)){
                                            isEssence = 1;
                                        }
                                        //获取自定义数据
                                        JSONObject zydObj = referenceDataUtil.queryComponentZdyData(isEssence,componentChiName,dataList);
                                        data.setSupplierTotals(zydObj.getString("supplierTotals"));
                                        data.setSupplierLeaveOn(zydObj.getString("supplierLeaveOn"));
                                        data.setSupplierRinseOff(zydObj.getString("supplierRinseOff"));
                                        data.setCompanyTotals(zydObj.getString("companyTotals"));
                                        data.setCompanyLeaveOn(zydObj.getString("companyLeaveOn"));
                                        data.setCompanyRinseOff(zydObj.getString("companyRinseOff"));

                                        data.setIsEssence(isEssence);
                                        data.setIsIfra(zydObj.getInteger("isIfra"));

                                        Integer componentType =  ReferenceDataUtil.getFormulaComponentType(data);
                                        data.setComponentType(componentType);
                                    }
                                }
                            }
                        }
                    }
                }

                JSONObject formulaObj = new JSONObject();
                formulaObj.put("dataList",dataList);  //配方列表
                formulaObj.put("chiName",chiName);
                formulaObj.put("chiNameNum",chiNameNum);
                formulaObj.put("zybw","");
                {
                    String zybw = softwareDevelopingFormula.getZybw();
                    if(StringUtils.isNotNull(zybw)){
                        String zybwVal =  DictUtils.getDictLabel("project_zybw",zybw,"、");
                        formulaObj.put("zybw",zybwVal);
                    }
                }
                formulaObj.put("aqjsy","");
                formulaObj.put("usageMethod","");
                formulaObj.put("pflx","");
                formulaObj.put("syrq",syrq);
                {

                }
                formulaObj.put("cpjx","");
                {
                    String cpjx = softwareDevelopingFormula.getCpjx();
                    if(StringUtils.isNotNull(cpjx)){
                        String cpjxVal =  DictUtils.getDictLabel("project_cpjx",cpjx,"、");
                        formulaObj.put("cpjx",cpjxVal);
                    }
                }

                if(legalIcp!=null){
                    String zybw = legalIcp.getZybw();
                    if(StringUtils.isNotNull(zybw)){
                        formulaObjParam.put("zybw",zybw);
                        String zybwVal =  DictUtils.getDictLabel("project_zybw",zybw,"、");
                        formulaObj.put("zybw",zybwVal);
                    }
                    String usageMethod = legalIcp.getUsageMethod();
                    if(StringUtils.isNotNull(usageMethod)){
                        formulaObj.put("usageMethod",usageMethod);
                    }
                    String aqjsy = legalIcp.getAqjsy();
                    if(StringUtils.isNotNull(aqjsy)){
                        formulaObj.put("aqjsy",aqjsy);
                    }
                    String cpjx = legalIcp.getCpjx();
                    if(StringUtils.isNotNull(cpjx)){
                        String cpjxVal =  DictUtils.getDictLabel("project_cpjx",cpjx,"、");
                        formulaObj.put("cpjx",cpjxVal);
                    }
                    String syff = legalIcp.getSyff();
                    if(StringUtils.isNotNull(syff)){
                        pflx = syff;
                    }
                }
                if("1".equals(pflx)){
                    formulaObj.put("pflx","驻留类");
                }else if("2".equals(pflx)){
                    formulaObj.put("pflx","淋洗类");
                }
                formulaObj.put("dayAvg","");
                formulaObjParam.put("dayAvg","");
                formulaObj.put("sedFormula","SED=日均使用量×驻留因子×成分在配方中百分比×经皮吸收率÷体重");

                Integer duliId = softwareDevelopingFormula.getDuliId();
                if(StringUtils.isNotNull(duliId)){
                    RdIngredientsStandard rdIngredientsStandard =  rdIngredientsStandardMapper.selectRdIngredientsStandardById(duliId.longValue());
                    if(rdIngredientsStandard!=null){
                        BigDecimal calculatedDaily =  rdIngredientsStandard.getCalculatedDaily();  //欧标
                        BigDecimal dayAvg = rdIngredientsStandard.getDayAvg();  //日标
                        if(StringUtils.isNotNull(calculatedDaily)){
                            formulaObj.put("dayAvg",calculatedDaily+"(欧标)");
                            formulaObjParam.put("dayAvg",calculatedDaily);
                        }else if(StringUtils.isNotNull(dayAvg)){
                            formulaObj.put("dayAvg",dayAvg+"(日标)");
                            formulaObjParam.put("dayAvg",dayAvg);
                        }
                    }
                }
                //风险物质
                formulaObj.put("fxwz",StringUtils.join(fxwz,"、"));
                //可能存在的风险物质的安全评估
                List<String> assessmentOfPotentialRiskSubstances1 = FormulaDataUtil.getAssessmentOfPotentialRiskSubstances1();
                List<String> assessmentOfPotentialRiskSubstances2 = FormulaDataUtil.getAssessmentOfPotentialRiskSubstances2();
                formulaObj.put("assessmentOfPotentialRiskSubstances1",assessmentOfPotentialRiskSubstances1);
                formulaObj.put("assessmentOfPotentialRiskSubstances2",assessmentOfPotentialRiskSubstances2);

                String cpjx = formulaObj.getString("cpjx");
                if(StringUtils.isNull(cpjx)){
                    cpjx = softwareDevelopingFormula.getProductName();
                }
                //风险控制措施或建议
                List<String> riskControlMeasuresOrRecommendationsDatas = FormulaDataUtil.getRiskControlMeasuresOrRecommendations(cpjx,formulaObj.getString("pflx"),formulaObj.getString("zybw"),formulaObj.getString("aqjsy"));
                formulaObj.put("riskControlMeasuresOrRecommendationsDatas",riskControlMeasuresOrRecommendationsDatas);
                //安全评估结论
                List<JSONObject> conclusionOfSafetyAssessmentDatas1 = FormulaDataUtil.getConclusionOfSafetyAssessment1(cpjx,formulaObj.getString("pflx"),formulaObj.getString("zybw"));
                formulaObj.put("conclusionOfSafetyAssessmentDatas1",conclusionOfSafetyAssessmentDatas1);
                //安全评估结论
                List<JSONObject> conclusionOfSafetyAssessmentDatas2 = FormulaDataUtil.getConclusionOfSafetyAssessment2(cpjx,formulaObj.getString("pflx"),formulaObj.getString("zybw"));
                formulaObj.put("conclusionOfSafetyAssessmentDatas2",conclusionOfSafetyAssessmentDatas2);
                //安全评估结论
                List<JSONObject> conclusionOfSafetyAssessmentDatas3 = FormulaDataUtil.getConclusionOfSafetyAssessment3(cpjx,formulaObj.getString("pflx"),formulaObj.getString("zybw"));
                formulaObj.put("conclusionOfSafetyAssessmentDatas3",conclusionOfSafetyAssessmentDatas3);
                //安全评估结论
                List<JSONObject> conclusionOfSafetyAssessmentDatas4 = FormulaDataUtil.getConclusionOfSafetyAssessment4(cpjx,formulaObj.getString("pflx"),formulaObj.getString("zybw"));
                formulaObj.put("conclusionOfSafetyAssessmentDatas4",conclusionOfSafetyAssessmentDatas4);
                //目录
                formulaObj.put("formulaAppendiDatas",FormulaDataUtil.getFormulaAppendix());
                //准用着色剂（表6）  ≤3
                List<String> approvedColoringAgent = new ArrayList<String>();
                //准用防腐剂（表4）  ≤4
                List<String> approvedPreservatives = new ArrayList<String>();
                //准用防晒剂（表5）  ≤5
                List<String> approvedSunscreen = new ArrayList<String>();

                Integer isPass = 1;
                String ratingU = "1";
                String ratingUNS = "1";
                String ratingIZ = "1";
                Integer isSq = 0;
                List<String> exceedCf = new ArrayList<String>();
                for (ComponentUtil componentUtil : allList) {
                    String componentChiName = componentUtil.getChiName();
                    if(StringUtils.isNotNull(componentChiName)){
                        TUnProductNewSafety productSafety = mapProductSafetys.get(componentChiName.trim());
                        FormulaDataUtil formulaDataUtil = new FormulaDataUtil();
                        if(productSafety!=null){
                            componentUtil.setFiles(productSafety.getFiles());
                            componentUtil.setOtherFiles(productSafety.getOtherFiles());
                            formulaDataUtil.getComponentUtilInfoFgyxDetail(symdMaps, ckwxxhMap, formulaObjParam, productSafety,
                                    componentUtil,essenceList,isChildrean);
                            String isSqVal = componentUtil.getIsSq();
                            String isExceed_ = componentUtil.getIsExceed();
                            if("0".equals(isExceed_) && "1".equals(isSqVal)){
                                String conclusion = productSafety.getConclusion();
                                String jielun = "";
                                if(StringUtils.isNotNull(conclusion)){
                                    if(conclusion.contains("刺激") && conclusion.contains("致敏")){
                                        jielun = "依据CIR评估报告,本配方中成分添加量在CIR历史用量之下，同时该产品依据《化妆品安全技术规范》2015版进行了皮肤变态反应测试和人体斑贴测试实验，测试结果显示本品无致敏性、无刺激性。故该原料在本品中的使用是安全的。";
                                    }else if(conclusion.contains("刺激")){
                                        jielun = "依据CIR评估报告,本配方中该成分添加量在CIR历史使用浓度之下，同时该产品选取了30人，依据《化妆品安全技术规范》2015 版，进行了人体斑贴测试实验，测试结果显示本品无刺激性。故该成分在本品中的使用是安全的。";
                                    }else if(conclusion.contains("致敏")){
                                        jielun = "依据CIR评估报告,本配方中该成分添加量在CIR历史浓度之下，同时该产品依据《化妆品安全技术规范》2015 版进行了皮肤变态反应测试，测试结果显示本品无致敏性。故该成分在本品中的使用是安全的。";
                                    }
                                    if(StringUtils.isNotNull(jielun)){
                                        componentUtil.setGcfPgjl(jielun);
                                    }
                                }
                            }
                            if("0".equals(isExceed_)){
                                String conclusion = productSafety.getConclusion();
                                if(StringUtils.isNotNull(conclusion)){
                                    if(conclusion.contains("AICIS") && !conclusion.contains("CIR")){
                                        componentUtil.setGcfPgjl(conclusion);
                                    }
                                }
                            }
                            if("1".equals(isSqVal)){
                                isSq = 1;
                            }
                            if("2".equals(componentUtil.getRatingUNS())){
                                ratingUNS = "2";
                            }
                            if("2".equals(componentUtil.getRatingIZ())){
                                ratingIZ = "2";
                            }
                            if("2".equals(componentUtil.getRatingU())){
                                ratingU = "2";
                            }
                            if(isChildrean){
                                String bzmx = productSafety.getBzmx();
                                if(StringUtils.isNotNull(bzmx)){
                                    if(formulaDataUtil.approvedColoringAgent(bzmx)){
                                        approvedColoringAgent.add(componentChiName);
                                    }else if(formulaDataUtil.approvedPreservatives(bzmx)){
                                        approvedPreservatives.add(componentChiName);
                                    }else if(formulaDataUtil.approvedSunscreen(bzmx)){
                                        approvedSunscreen.add(componentChiName);
                                    }
                                }
                                String isExceed = componentUtil.getIsExceed();
                                if(StringUtils.isNotNull(isExceed) && "1".equals(isExceed)){
                                    exceedCf.add(componentChiName);
                                }
                                if(componentChiName.contains(FormulaDataUtil.CHILDREN_JAY)){
                                    jaySet.add(componentChiName);
                                }

                                if(FormulaDataUtil.getMaterialIsTips(componentChiName)){
                                    totalPercentCI = totalPercentCI.add(componentUtil.getPercert());
                                }

                                if(FormulaDataUtil.isChildrenBzmx(bzmx)){
                                    bzmxSet.add(componentChiName);
                                }

                                String knhydfxwz = productSafety.getKnhydfxwz();
                                if(FormulaDataUtil.isChildrenFxwz(knhydfxwz)){
                                    fxwzSet.add(componentChiName);
                                }

                                if(componentChiName.contains(FormulaDataUtil.CHILDREN_KLSLH)){
                                    isKlslh = true;
                                }
                                if(componentChiName.contains(FormulaDataUtil.CHILDREN_YYXABJT)){
                                    isYyxa = true;
                                }
                                if(componentChiName.equals(FormulaDataUtil.CHILDREN_GY)){
                                    isGanyou = true;
                                }
                            }
                        }
                        String gcfCkwx =  componentUtil.getGcfCkwx();
                        String gcfCkwxnr = componentUtil.getGcfCkwxnr();
                        if(!StringUtil.isEmpty(gcfCkwx) && formulaDataUtil.isInteger(gcfCkwx) && StringUtils.isNotNull(gcfCkwxnr)){
                            Map<String,Object> wxInfo = new HashMap<String,Object>();
                            String resStr = gcfCkwx+"."+componentUtil.getGcfCkwxnr();
                            wxInfo.put("wxContent", resStr);
                            boolean isExists = mapList.stream().anyMatch(mapXh -> mapXh.get("wxContent").toString().equals(resStr));
                            if(!isExists){
                                mapList.add(wxInfo);
                            }
                        }
                    }
                    if(StringUtils.isNotNull(componentChiName)){
                        Object res = materialsMap.get(componentChiName);
                        if(StringUtils.isNotNull(res)){
                            componentUtil.setChiNameNew(componentChiName+"<span style='color:purple'>("+res+")</span>");
                        }else{
                            componentUtil.setChiNameNew(componentChiName);
                        }
                    }else{
                        componentUtil.setChiNameNew(componentChiName);
                    }
                    List<JSONObject> zjyDatas = componentUtil.getZjyDatas();
                    //中检院数据
                    if(zjyDatas!=null && zjyDatas.size()>0){
                        List<String> zjySet = new ArrayList<String>();
                        List<String> zjySet2 = new ArrayList<String>();
                        for(JSONObject zjyData : zjyDatas){
                            String method = zjyData.getString("method");  //类型
                            if("1".equals(pflx) && "驻留".equals(method)){
                                String parts =zjyData.getString("parts");
                                BigDecimal usage =zjyData.getBigDecimal("usage");
                                zjySet.add(parts+":"+usage.stripTrailingZeros()+"%");
                            }else if("2".equals(pflx) && "淋洗".equals(method)){
                                String parts =zjyData.getString("parts");
                                BigDecimal usage =zjyData.getBigDecimal("usage");
                                zjySet.add(parts+":"+usage.stripTrailingZeros()+"%");
                            }
                        }

                        if("2".equals(pflx)){  //淋洗
                            for(JSONObject zjyData : zjyDatas){
                                String method = zjyData.getString("method");  //类型
                                if("驻留".equals(method)){
                                    String parts =zjyData.getString("parts");
                                    BigDecimal usage =zjyData.getBigDecimal("usage");
                                    zjySet2.add(parts+":"+usage.stripTrailingZeros()+"%");
                                }
                            }
                            StringBuffer sb = new StringBuffer();
                            if(zjySet2!=null && zjySet2.size()>0){
                                sb.append("驻留类:"+StringUtils.join(zjySet2,"、"));
                            }
                            if(zjySet!=null && zjySet.size()>0){
                                if(zjySet2!=null && zjySet2.size()>0){
                                    sb.append(";");
                                }
                                sb.append("淋洗类:"+StringUtils.join(zjySet,"、"));
                            }
                            componentUtil.setZjyDesc(sb.toString());
                        }else{
                            if(zjySet!=null && zjySet.size()>0){
                                componentUtil.setZjyDesc("驻留类:"+StringUtils.join(zjySet,"、"));
                            }
                        }
                    }
                }


                formulaObj.put("allList",allList);
                formulaObj.put("isChildrean",isChildrean?"1":"0");
                List<String> finalConclusionList = new ArrayList<String>();
                if(isSq!=null && isSq==1){
                    String finalConclusion = "含有评级为SQ成分，请确认！";
                    finalConclusionList.add(finalConclusion);
                    isPass = 2;
                }
                if(ratingU!=null && "2".equals(ratingU)){
                    String finalConclusion = "含有评级为U成分，请确认！";
                    finalConclusionList.add(finalConclusion);
                    isPass = 5;
                }
                if(ratingUNS!=null && "2".equals(ratingUNS)){
                    String finalConclusion = "含有评级为UNS成分，请确认！";
                    finalConclusionList.add(finalConclusion);
                    isPass = 4;
                }
                if(ratingIZ!=null && "2".equals(ratingIZ)){
                    String finalConclusion = "含有评级为I/Z成分，请确认！";
                    finalConclusionList.add(finalConclusion);
                    isPass = 3;
                }
                if(isChildrean){
                    formulaObj.put("approvedColoringAgent",StringUtils.join(approvedColoringAgent,"、"));
                    if(approvedColoringAgent!=null && approvedColoringAgent.size()>3){
                        String finalConclusion = "着色剂成分使用超过3个";
                        finalConclusionList.add(finalConclusion);
                        isPass = 2;
                    }
                    formulaObj.put("approvedPreservatives",StringUtils.join(approvedPreservatives,"、"));
                    if(approvedPreservatives!=null && approvedPreservatives.size()>4){
                        String finalConclusion = "防腐剂成分使用超过4个";
                        finalConclusionList.add(finalConclusion);
                        isPass = 2;
                    }
                    formulaObj.put("approvedSunscreen",StringUtils.join(approvedSunscreen,"、"));
                    if(approvedSunscreen!=null && approvedSunscreen.size()>5){
                        String finalConclusion = "防晒剂成分使用超过5个";
                        finalConclusionList.add(finalConclusion);
                        isPass = 2;
                    }

                    if(exceedCf!=null && exceedCf.size()>0){
                        String finalConclusion = "成分["+StringUtils.join(exceedCf,"、")+"]超过最大允许使用量";
                        finalConclusionList.add(finalConclusion);
                        isPass = 2;
                    }

                    if(bzmxSet!=null && bzmxSet.size()>0){
                        String finalConclusion = "存在成分["+StringUtils.join(bzmxSet,"、")+"]含有限用组分或防腐剂。";
                        finalConclusionList.add(finalConclusion);
                        isPass = 2;  //高风险
                    }
                    if(fxwzSet!=null && fxwzSet.size()>0){
                        String finalConclusion = "存在成分["+StringUtils.join(fxwzSet,"、")+"]含有风险物质。";
                        finalConclusionList.add(finalConclusion);
                        isPass = 2;
                    }

                    if(isKlslh){
                        String finalConclusion = "配方中含有库拉索芦荟等原料时，需识别与评估风险物质蒽醌，其评估可参考权威机构的评估结论。";
                        finalConclusionList.add(finalConclusion);
                        isPass = 2;
                    }
                    if(isYyxa){
                        String finalConclusion = "配方中含有椰油酰胺丙基甜菜碱时，需识别并评估其中的椰油酰胺丙基二甲胺、3,3-二甲基氨基丙胺和单氯乙酸，其限值可参考权威机构评估结论或我国相关标准。";
                        finalConclusionList.add(finalConclusion);
                        isPass = 2;
                    }
                    if(isGanyou){
                        String finalConclusion = "配方中含有甘油时，其纯度应≥ 95.0%，识别与评估二甘醇含量应≤0.1%。";
                        finalConclusionList.add(finalConclusion);
                        isPass = 2;
                    }

                    //PH值
                    String jcxmJson = softwareDevelopingFormula.getJcxmJson();
                    String ph = "";
                    if(StringUtils.isNotNull(jcxmJson)){
                       JSONArray jcxmArray = JSONArray.parseArray(jcxmJson);
                       if(jcxmArray!=null && jcxmArray.size()>0){
                           for(int i = 0;i<jcxmArray.size();i++){
                               JSONObject jcxmObj = jcxmArray.getJSONObject(i);
                               if(jcxmObj!=null){
                                   Integer type = jcxmObj.getInteger("id");
                                   if(type!=null && type==8){
                                       ph =  jcxmObj.getString("standardVal");
                                   }
                               }
                           }
                       }
                    }
                    if(StringUtils.isNotNull(ph)){
                       if(!FormulaDataUtil.comparePh(ph,pflx)){
                           String fw = "4.5-7.5";
                           if("2".equals(pflx)){
                               fw = "4.5-8.5";
                           }
                           String finalConclusion = "PH值未在允许范围["+fw+"]内，当前配方标准PH值为："+ph;
                           finalConclusionList.add(finalConclusion);
                           isPass = 2;
                       }
                    }else{
                        String finalConclusion = "未查询到PH值";
                        finalConclusionList.add(finalConclusion);
                        isPass = 2;
                    }

                    if(isContainsEssence==1){
                        String finalConclusion = "配方中含有香精、植物精油或香料成分时，需对其中的致敏成分进行识别并评估，如含有互生叶白千层（MELALEUCA ALTERNIFOLIA）叶油时，需识别和评估风险物质甲基丁香酚，其限值可参考权威机构评估结论。";
                        finalConclusionList.add(finalConclusion);
                    }

                    if(isContainsEssence==1){
                        if(totalFragrancePercent.doubleValue()==0){
                            String finalConclusion = "香精成分数据未维护，请核实！";
                            finalConclusionList.add(finalConclusion);
                            isPass = 2;
                        }
                        if(totalFragrancePercent.doubleValue()>0){
                            if("1".equals(pflx)){  //驻留
                                if (totalFragrancePercent.doubleValue() > 0.001) {
                                    String finalConclusion = "致敏香料组分含量为["+totalFragrancePercent+"%],已超过要求0.001%";
                                    finalConclusionList.add(finalConclusion);
                                    isPass = 2;
                                }
                            }else if("2".equals(pflx)){  //淋洗
                                if (totalFragrancePercent.doubleValue() > 0.01) {
                                    String finalConclusion = "致敏香料组分含量为["+totalFragrancePercent+"%],已超过要求0.01%";
                                    finalConclusionList.add(finalConclusion);
                                    isPass = 2;
                                }
                            }
                        }
                    }

                    if(newMaterialSet!=null && newMaterialSet.size()>0){
                        String finalConclusion = "存在新原料["+StringUtils.join(newMaterialSet,"、")+"]";
                        finalConclusionList.add(finalConclusion);
                        isPass = 0;
                    }
                    if(namiMaterialSet!=null && namiMaterialSet.size()>0){
                        String finalConclusion = "存在纳米原料["+StringUtils.join(namiMaterialSet,"、")+"]";
                        finalConclusionList.add(finalConclusion);
                        isPass = 0;
                    }

                    if(essenceSet!=null && essenceSet.size()>0){
                        String finalConclusion = "存在香精原料["+StringUtils.join(essenceSet,"、")+"]超过允许使用范围。";
                        finalConclusionList.add(finalConclusion);
                        isPass = 0;
                    }

                    if(symdSet!=null && symdSet.size()>0){
                        String finalConclusion = "存在使用目的儿童不能用的原料["+StringUtils.join(symdSet,"、")+"]。";
                        finalConclusionList.add(finalConclusion);
                        isPass = 0;
                    }
                    if(jaySet!=null && jaySet.size()>0){
                        String finalConclusion = "存在成分["+StringUtils.join(jaySet,"、")+"]含有季铵盐字段。";
                        finalConclusionList.add(finalConclusion);
                        isPass = 0;
                    }

                    if(totalPercentCI!=null && totalPercentCI.doubleValue()>25){
                        String finalConclusion = "二氧化钛，氧化锌，CI 77891，CI 77891成分总含量超过25%。";
                        finalConclusionList.add(finalConclusion);
                        isPass = 0;
                    }
                }
                formulaObj.put("finalConclusion",finalConclusionList);
                formulaObj.put("isPass",isPass);
                if(formulaList!=null && formulaList.size()>0){
                    Set<String> xzSet = new HashSet<String>();
                    for(ComponentUtil formulaData : formulaList){
                        String xz = formulaData.getXz();
                        JSONObject returnObj = getFormulaComponentData(xz,formulaList);
                        int rowspan = returnObj.getInteger("rowspan");
                        formulaData.setRowspan(1);
                        formulaData.setIsExist(0);
                        if(rowspan>1 && !xzSet.contains(xz)){
                            xzSet.add(xz);
                            formulaData.setRowspan(rowspan);
                        }else if(rowspan>1){
                            formulaData.setIsExist(1);
                        }
                    }
                }
                formulaObj.put("formulaList",formulaList);
                formulaObj.put("ratingIZ",ratingIZ);
                formulaObj.put("ratingUNS",ratingUNS);
                formulaObj.put("ratingU",ratingU);
                formulaObj.put("isSq",isSq);
                formulaObj.put("mapList",mapList);
                softwareDevelopingFormula.setFormulaObj(formulaObj.toJSONString());
            }
        }
        return softwareDevelopingFormula;
    }
    /**
     * 安评详情版数据
     * @param params
     * @return
     */
    @Override
    public List<ComponentUtil>  getSoftwareDevelopingFormulaProductSafetyAssessmentData2(SoftwareDevelopingFormula params) {
        List<ComponentUtil> allList = new ArrayList<ComponentUtil>();
        Long id = params.getId();
        Long dataId = params.getDataId();
        LegalIcp legalIcp =  new LegalIcp();
        SoftwareDevelopingFormula softwareDevelopingFormula = softwareDevelopingFormulaMapper.selectSoftwareDevelopingProductSafetyAssessmentDataFormulaById(id);
        {
            Integer isResult = 0; // 是否含有待评估
            Integer isGt = 0; // 是否含有超过历史使用量
            List<Map<String, Object>> formulaIdList = new ArrayList<Map<String, Object>>();
            List<JSONObject> formulaMaterialDatas = queryFormulaMaterialData(id);
            if(formulaMaterialDatas!=null && formulaMaterialDatas.size()>0){
                for(JSONObject obj : formulaMaterialDatas){
                    Integer type = obj.getInteger("type");
                    type = StringUtils.convertDefaultValue(type,0);
                    if(type==1){
                        Map<String, Object> formulaIdMap = new HashMap<String,Object>();
                        formulaIdMap.put("formulaId", obj.getLong("materialId"));
                        formulaIdMap.put("percentage",obj.getString("percentage"));
                        formulaIdList.add(formulaIdMap);
                    }
                }
                softwareDevelopingFormula.setFormulaMaterialDatas(JSONArray.toJSONString(formulaMaterialDatas));
            }

            //1 驻留  2 淋洗
            String pflx = softwareDevelopingFormula.getPflx();
            Set<String> fxwz = new HashSet<String>();
            //获取成分组分数据
            {
                //获取配方表,组分表数据
                List<Map<String, Object>> dataList = softwareDevelopingFormulaMapper.queryIngredientExcelList(id);
                //特殊原料信息
                List<Map<String, Object>> specMaterialData = new ArrayList<Map<String, Object>>();
                //停止原料
                List<String> disableMaterials = new ArrayList<String>();
                //替换原料
                List<String> repalceMaterials = new ArrayList<String>();
                ComponentUtil tipsMapUtil = new ComponentUtil();
                //获取色淀 纳米级极信息
                Map<String,Object> materialsMap = new HashMap<String,Object>();
                Set<String> materialSet = new HashSet<String>();
                if (formulaIdList != null && formulaIdList.size() > 0) {
                    List<Map<String, Object>> newDataList = softwareDevelopingFormulaMapper.queryIngredientNewExcelList(formulaIdList);
                    if (newDataList != null && newDataList.size() > 0) {
                        for (Map<String, Object> map : newDataList) {
                            Object oldFormulaID = map.get("formulaId");
                            Object oldPercentage = map.get("percentage");
                            if (StringUtils.isNull(oldPercentage)) {
                                oldPercentage = 0;
                            }
                            for (Map<String, Object> idMap : formulaIdList) {
                                Object formulaId = idMap.get("formulaId");
                                Object percentage = idMap.get("percentage");
                                if (StringUtils.isNull(percentage)) {
                                    percentage = 0;
                                }
                                if (String.valueOf(formulaId).equals(String.valueOf(oldFormulaID))) {
                                    BigDecimal res = new BigDecimal(String.valueOf(percentage))
                                            .multiply(new BigDecimal(String.valueOf(oldPercentage)))
                                            .divide(new BigDecimal(100));
                                    map.put("percentage", res);
                                }
                            }
                        }
                    }
                    if (dataList != null && dataList.size() > 0) {
                        if (newDataList != null && newDataList.size() > 0) {
                            dataList.addAll(newDataList);
                        }
                    } else {
                        if (newDataList != null && newDataList.size() > 0) {
                            dataList = newDataList;
                        }
                    }
                }

                //配方列表
                List<ComponentUtil> formulaList = new ArrayList<ComponentUtil>();
                //文献列表
                List<Map<String,Object>> mapList= new ArrayList<Map<String,Object>>();

                if (dataList != null && dataList.size() > 0) {
                    SortUtil.listSort(dataList, "percentage", true);
                }

                //获取数据库成分基础数据
                Map<String, TUnProductNewSafety> mapProductSafetys = new HashMap<String, TUnProductNewSafety>();
                List<TUnProductNewSafety> limitComponentsList = softwareService.queryProductNewSatetyList();
                for (TUnProductNewSafety productNewSafety : limitComponentsList) {
                    mapProductSafetys.put(productNewSafety.getZwmc(), productNewSafety);
                }


                BigDecimal totalPercert = new BigDecimal("0");
                BigDecimal sjTotalPercet = new BigDecimal("0");
                for (Map<String, Object> data : dataList) {
                    Object percentage = data.get("percentage");
                    totalPercert = totalPercert.add(new BigDecimal(String.valueOf(percentage)));
                    Object inciName = data.get("inciName");
                    Object inciNameEng = data.get("inciNameEng");
                    Object proportion = data.get("proportion");
                    Object typeical = data.get("typeical");
                    Object sjProportion = data.get("sjProportion");
                    List<Symd> SELECT_SYMD_DATA_LIST = new ArrayList<Symd>();
                    List<String> symdDataList = new ArrayList<String>();
                    Object symdInfo = data.get("symdInfo");
                    if (StringUtils.isNotNull(symdInfo)) {
                        symdDataList = StringUtils.str2List(String.valueOf(symdInfo), "~",true,true);
                    }
                    data.put("symdDataList", symdDataList);
                    data.put("SYMD_DATA_LIST", SELECT_SYMD_DATA_LIST);
                    Object inicRemark = data.get("inicRemark");
                    Object inicNmjyl = data.get("inicNmjyl");
                    Object inicYzwsybw = data.get("inicYzwsybw");
                    Object inicBxjmcjyl = data.get("inicBxjmcjyl");
                    Object inicLbyl = data.get("inicLbyl");
                    String val = "";
                    StringBuffer sb = new StringBuffer();
                    if (StringUtils.isNotNull(inicRemark)) {
                        List<Sdzl> SDZL_DATA_LIST = new MaterialConstansUtil().SDZL_DATA_LIST;
                        for (Sdzl sdzl : SDZL_DATA_LIST) {
                            if (sdzl.getId() == Integer.valueOf(inicRemark + "")) {
                                val = DictUtils.getDictLabel("SOFTWARE_SDZL",String.valueOf(inicRemark));
                                sb.append(val);
                                break;
                            }
                        }
                    }
                    if (StringUtils.isNotNull(inicNmjyl)) {
                        if ("是".equals(inicNmjyl)) {
                            sb.append("<br />").append("纳米级");
                        }
                    }
                    if (StringUtils.isNotNull(inicYzwsybw)) {
                        sb.append("<br />").append("原植物使用部位").append(inicYzwsybw);
                    }
                    if (StringUtils.isNotNull(inicBxjmcjyl)) {
                        sb.append("<br />").append("变性剂").append(inicBxjmcjyl);
                    }
                    if (StringUtils.isNotNull(inicLbyl)) {
                        sb.append("<br />").append("类别原料").append(inicLbyl);
                    }
                    data.put("inicRemark", sb.toString());
                    List<Map<String, Object>> inicDataList = new ArrayList<Map<String, Object>>();
                    if (StringUtils.isNotNull(inciName)) {
                        if (inciName.toString().indexOf("~") > -1) {
                            String[] dataArr = inciName.toString().split("~");
                            String[] dataEngArr = inciNameEng.toString().split("~");
                            String[] dataProportionArr = proportion.toString().split("~");
                            String[] typeicalArr;
                            if(StringUtils.isNotNull(typeical)){
                                typeicalArr = typeical.toString().split("~");
                            }else{
                                typeicalArr = new String[dataProportionArr.length];
                            }
                            String[] dataSjProportionArr = StringUtils.isNull(sjProportion) ? new String[0]
                                    : sjProportion.toString().split("~");
                            if (dataArr != null && dataArr.length > 0) {
                                for (int i = 0; i < dataArr.length; i++) {
                                    Map<String, Object> mapData = new HashMap<String, Object>();
                                    if(StringUtils.isNotNull(dataArr[i])){
                                        materialSet.add(dataArr[i]);
                                    }
                                    mapData.put("inciName", dataArr[i]);
                                    mapData.put("inciNameEng", dataEngArr[i]);
                                    mapData.put("typeical", typeicalArr[i]);
                                    String minPro = "";
                                    String proportionSingle = dataProportionArr[i];
                                    String typeicalSingle = typeicalArr[i];
                                    String sjProportionSingle = dataSjProportionArr.length > i ? dataSjProportionArr[i]
                                            : "/";
                                    if (!StringUtil.isEmpty(proportionSingle) && proportionSingle.indexOf("-") > 0) {
                                        FormulaDataUtil formulaDateUtil = new FormulaDataUtil();
                                        minPro = formulaDateUtil.getLeftNums(proportionSingle);
                                        BigDecimal returnNum = formulaDateUtil.getMiddleNums(proportionSingle);
                                        if (returnNum.doubleValue() > 0) {
                                            if(StringUtils.isNotNull(typeicalSingle) && ErpDataReqUtil.isInteger(typeicalSingle.toString())){
                                                proportionSingle = typeicalSingle.toString();
                                            }else{
                                                proportionSingle = returnNum.toString();
                                            }
                                        }
                                    }
                                    mapData.put("materialProportion", proportionSingle);
                                    mapData.put("proportionSingle", proportionSingle);
                                    String proportionVal = ComponentUtil.processCalcPercent(proportionSingle, percentage + "");
                                    sjTotalPercet = sjTotalPercet.add(new BigDecimal(proportionVal));
                                    mapData.put("proportion", proportionVal);

                                    if (!StringUtil.isEmpty(sjProportionSingle) && sjProportionSingle.indexOf("-") > 0) {
                                        FormulaDataUtil formulaDateUtil = new FormulaDataUtil();
                                        BigDecimal returnNum = formulaDateUtil.getMiddleNums(sjProportionSingle);
                                        if (returnNum.doubleValue() > 0) {
                                            sjProportionSingle = returnNum.toString();
                                        }
                                    }
                                    if ((StringUtil.isEmpty(sjProportionSingle) || "/".equals(sjProportionSingle)
                                            || "\\".equals(sjProportionSingle)) && !StringUtil.isEmpty(minPro)) {
                                        sjProportionSingle = minPro;
                                    }
                                    mapData.put("sjProportionSingle", sjProportionSingle);
                                    mapData.put("sjProportion", ComponentUtil.processCalcPercent2(sjProportionSingle, percentage + ""));
                                    inicDataList.add(mapData);
                                }
                            }
                        } else {
                            Map<String, Object> mapData = new HashMap<String, Object>();
                            if(StringUtils.isNotNull(inciName)){
                                materialSet.add(inciName.toString());
                            }
                            mapData.put("inciName", inciName);
                            mapData.put("inciNameEng", inciNameEng);
                            String proportionSingle = proportion.toString();
                            String minPro = "";
                            String sjPoportionSingle = StringUtils.isNull(sjProportion) ? "/" : sjProportion.toString();
                            if (!StringUtil.isEmpty(proportionSingle) && proportionSingle.indexOf("-") > 0) {
                                FormulaDataUtil formulaDateUtil = new FormulaDataUtil();
                                BigDecimal returnNum = formulaDateUtil.getMiddleNums(proportionSingle);
                                minPro = formulaDateUtil.getLeftNums(proportionSingle);
                                if (returnNum.doubleValue() > 0) {
                                    if(StringUtils.isNotNull(typeical) && ErpDataReqUtil.isInteger(typeical.toString())){
                                        proportionSingle = typeical.toString();
                                    }else{
                                        proportionSingle = returnNum.toString();
                                    }
                                }
                            }
                            mapData.put("proportionSingle", proportionSingle);
                            mapData.put("materialProportion", proportionSingle);
                            mapData.put("typeical", typeical);
                            String proportionVal = ComponentUtil.processCalcPercent(proportionSingle, percentage + "");
                            sjTotalPercet = sjTotalPercet.add(new BigDecimal(proportionVal));
                            mapData.put("proportion", proportionVal);
                            if (!StringUtil.isEmpty(sjPoportionSingle) && sjPoportionSingle.indexOf("-") > 0) {
                                FormulaDataUtil formulaDateUtil = new FormulaDataUtil();
                                BigDecimal returnNum = formulaDateUtil.getMiddleNums(sjPoportionSingle);
                                if (returnNum.doubleValue() > 0) {
                                    sjPoportionSingle = returnNum.toString();
                                }
                            }
                            if ((StringUtil.isEmpty(sjPoportionSingle) || "/".equals(sjPoportionSingle)
                                    || "\\".equals(sjPoportionSingle)) && !StringUtil.isEmpty(minPro)) {
                                sjPoportionSingle = minPro;
                            }
                            mapData.put("sjProportionSingle", sjPoportionSingle);
                            mapData.put("sjProportion", ComponentUtil.processCalcPercent2(sjPoportionSingle, percentage + ""));
                            inicDataList.add(mapData);
                        }
                    }
                    data.put("inicDataList", inicDataList);
                }

                //获取原料信息
                for(String inciNameStr : materialSet){
                    String result = tipsMapUtil.queryMaterialInciResult(inciNameStr,dataList);
                    if(StringUtils.isNotNull(result)){
                        materialsMap.put(inciNameStr,result);
                    }
                }
                //组分信息
                // 获取特殊原料替换信息
                List<Map<String, Object>> tipsMaterialFormulaDataList = softwareDevelopingFormulaMapper
                        .queryMaterialFormulaSpecialInfo(id);
                Set<String> compositionalDatas = new HashSet<String>();
                int seq = 1;
                for (int i = 0; i < dataList.size(); i++) {
                    String name = (String) dataList.get(i).get("inciName");
                    String percentage = (String) (dataList.get(i).get("percentage") + "");
                    String inciNameEng = (String) dataList.get(i).get("inciNameEng");
                    String proportion = (String) dataList.get(i).get("proportion");
                    Object typeical =  dataList.get(i).get("typeical");
                    Object status = dataList.get(i).get("status");
                    Object symdInfo = dataList.get(i).get("symdInfo");
                    Object mfId = dataList.get(i).get("mfId");
                    String inciChiName[] = name.split("~");
                    String inciEngName[] = inciNameEng.split("~");
                    String proArr[] = proportion.split("~");
                    String[] typeicalArr;
                    if(StringUtils.isNotNull(typeical)){
                        typeicalArr = typeical.toString().split("~");
                    }else{
                        typeicalArr = new String[proArr.length];
                    }
                    List<ComponentUtil> list_ = new ArrayList<ComponentUtil>();
                    for (int j = 0; j < inciChiName.length; j++) {
                        String inciChiNameTips = inciChiName[j];
                        if(StringUtils.isNotNull(inciChiNameTips)) {
                            ComponentUtil formulaCom = new ComponentUtil();
                            formulaCom.setSeq(seq);
                            formulaCom.setCppfSymd(StringUtils.isNotNull(symdInfo)?symdInfo.toString():"");
                            TUnProductNewSafety productSafetyInfo = mapProductSafetys.get(inciChiNameTips.trim());
                            if(StringUtils.isNotNull(mfId)){
                                formulaCom.setXz(mfId.toString());
                            }else{
                                formulaCom.setXz("-1");
                            }
                            if (productSafetyInfo != null && StringUtils.isNotNull(productSafetyInfo.getId())) {
                                String bzmx = productSafetyInfo.getBzmx();
                                String xh = productSafetyInfo.getXh();
                                String zwmc = productSafetyInfo.getZwmc();
                                String ywmc = productSafetyInfo.getYwmc();
                                formulaCom.setChiName(zwmc);
                                formulaCom.setEngName(ywmc);
                                formulaCom.setXh(xh);
                                formulaCom.setBzmx(bzmx);

                            }
                            formulaList.add(formulaCom);
                        }
                        compositionalDatas.add(inciChiNameTips);
                        String tipsMaterialInciName = "";
                        Integer tips = tipsMapUtil.isTipsFun(inciChiNameTips);
                        if (tips != null && tips.intValue() == 1) {
                            Map<String, Object> tipsMap = new HashMap<String, Object>();
                            tipsMap.put("mfId", dataList.get(i).get("mfId"));
                            tipsMap.put("materialId", dataList.get(i).get("mid"));
                            tipsMap.put("materialCode", dataList.get(i).get("materialCode"));
                            tipsMap.put("inciName", inciChiName[j]);
                            tipsMap.put("formulaId", dataList.get(i).get("formulaId"));
                            tipsMap.put("formulaCode", dataList.get(i).get("formulaCode"));
                            tipsMaterialInciName = tipsMapUtil.getReplaceTipsMaterialName(tipsMap,
                                    tipsMaterialFormulaDataList);
                            tipsMap.put("replaceInciName", tipsMaterialInciName);
                            specMaterialData.add(tipsMap);
                        }
                        if ("1".equals(String.valueOf(status))) {
                            disableMaterials.add(inciChiName[j]);
                        }
                        if ("2".equals(String.valueOf(status))) {
                            repalceMaterials.add(inciChiName[j]);
                        }
                        ComponentUtil componentUtil = new ComponentUtil();
                        if (StringUtil.isEmpty(tipsMaterialInciName)) {
                            componentUtil.setChiName(inciChiName[j]);
                            componentUtil.setEngName(inciEngName[j]);
                            tipsMaterialInciName = inciChiName[j];
                        } else {
                            componentUtil.setChiName(tipsMaterialInciName);
                            componentUtil.setEngName(componentUtil.getTipsMaterialEngName(tipsMaterialInciName));
                        }
                        String proStr = proArr[j];
                        String typeicalSingle = typeicalArr[j];
                        Double rate = 0D;
                        if (!StringUtil.isEmpty(proStr) && proStr.matches("\\d{1,20}\\.?\\d{0,20}")) {
                            if (StringUtil.isEmpty(percentage)) {
                                percentage = "1";
                            }
                            percentage = percentage.trim();
                            BigDecimal per = new BigDecimal(percentage);
                            BigDecimal result1 = per.multiply(new BigDecimal(proStr)).divide(new BigDecimal(100));
                            rate = result1.doubleValue();
                            componentUtil.setPercert(result1);
                        } else {
                            if (StringUtil.isEmpty(percentage)) {
                                percentage = "1";
                            }
                            percentage = percentage.trim();
                            BigDecimal per = new BigDecimal(percentage);
                            BigDecimal result1 = new BigDecimal(percentage);
                            rate = Double.valueOf(percentage);
                            if (!StringUtil.isEmpty(proStr) && proStr.indexOf("-") > 0) {
                                FormulaDataUtil formulaDataUtil = new FormulaDataUtil();
                                BigDecimal returnNum = formulaDataUtil.getMiddleNums(proStr);
                                if (returnNum.doubleValue() > 0) {
                                    if(StringUtils.isNotNull(typeicalSingle) && ErpDataReqUtil.isInteger(typeicalSingle.toString())) {
                                        result1 = per.multiply(WagesUtils.bigDecimal(typeicalSingle,6)).divide(new BigDecimal(100));
                                        rate = result1.doubleValue();
                                    }else{
                                        result1 = per.multiply(returnNum).divide(new BigDecimal(100));
                                        rate = result1.doubleValue();
                                    }
                                }
                            }
                            componentUtil.setPercert(result1);
                        }
                        boolean result1 = tipsMapUtil.processList(list_, tipsMaterialInciName, rate);
                        if (!result1) {
                            list_.add(componentUtil);
                        }
                    }
                    seq++;
                    tipsMapUtil.processListData(allList, list_);
                }
                Collections.sort(allList);

                String chiName = ComponentUtil.getChiName(allList);
                Integer chiNameNum = ComponentUtil.getChiNameNum(allList);
                String engName = ComponentUtil.getEngName(allList);

                BigDecimal totalPercent = new BigDecimal(0);
                //安评报告使用目的
                Map<Object, Object> symdMaps = new HashMap<Object, Object>();
                Map<String, String> ckwxxhMap = new HashMap<String, String>(); // 参考文献序号
                JSONObject formulaObjParam = new JSONObject();
                formulaObjParam.put("pflx",softwareDevelopingFormula.getPflx());
                List<Map<String, Object>> symdInfoDataList = softwareDevelopingFormulaMapper.queryFormulaSymdInfo(id);
                if (symdInfoDataList != null && symdInfoDataList.size() > 0) {
                    for (Map<String, Object> map : symdInfoDataList) {
                        symdMaps.put(map.get("chiName"), map.get("symd"));
                    }
                }
                for (ComponentUtil componentUtil : allList) {
                    componentUtil.setIsGt(0);
                    BigDecimal p = componentUtil.getPercert();
                    if (StringUtils.isNotNull(p)) {
                        totalPercent = totalPercent.add(p);
                    }
                    String componentChiName = componentUtil.getChiName();
                    componentUtil.setIsTips(tipsMapUtil.isTipsFun(componentChiName));
                    if (disableMaterials.contains(componentChiName)) {
                        componentUtil.setStatus(1);
                    } else if (repalceMaterials.contains(componentChiName)) {
                        componentUtil.setStatus(2);
                    } else {
                        componentUtil.setStatus(0);
                    }
                    BigDecimal percert = componentUtil.getPercert();
                    if (!StringUtil.isEmpty(componentChiName)) {
                        TUnProductNewSafety productSafety = mapProductSafetys.get(componentChiName.trim());
                        if (productSafety != null && StringUtils.isNotNull(productSafety.getId())) {
                            componentUtil.setLabelCondition(productSafety.getSytjhzysx());
                            componentUtil.setMaxAllowConcentration(productSafety.getZdyxnd());
                            componentUtil.setOtherLimit(productSafety.getQtxzyq());
                            componentUtil.setScopeOfApplication(productSafety.getSyjsyfw());
                            componentUtil.setZllzglssyl(productSafety.getZllzglssyl());
                            componentUtil.setLxlzglssyl(productSafety.getLxlzglssyl());
                            componentUtil.setBzmx(productSafety.getBzmx());

                            JSONObject dataObj = new JSONObject();
                            dataObj.put("bzmx", productSafety.getBzmx());//备案（含明细）
                            dataObj.put("conclusion", productSafety.getConclusion());//备案（含明细）
                            dataObj.put("isNewMaterial", productSafety.getIsNewMaterial());//是否新原料
                            dataObj.put("finding", productSafety.getFinding());//安全级别
                            dataObj.put("zdsynd",productSafety.getZdyxnd());
                            dataObj.put("cirzlx",productSafety.getZl());
                            dataObj.put("cirlxx",productSafety.getLx());
                            dataObj.put("lxlzglssyl", productSafety.getLxlzglssyl());  //淋洗类产品最高历史使用量
                            dataObj.put("zllzglssyl", productSafety.getZllzglssyl());  //驻留类产品最高历史使用量
                            dataObj.put("syjsyfw", productSafety.getSyjsyfw()); //适用及(或)使用范围
                            dataObj.put("symd",productSafety.getSymd());
                            dataObj.put("lx",productSafety.getLx());
                            dataObj.put("zl",productSafety.getZl());
                            dataObj.put("xyl",productSafety.getXyl());
                            dataObj.put("newTotal",productSafety.getNewTotal());
                            dataObj.put("newRange",productSafety.getNewRange());
                            dataObj.put("otherRequest",productSafety.getOtherRequest());
                            dataObj.put("inputTotals",productSafety.getTotal());
                            String ewgScore = productSafety.getEwgScore();
                            dataObj.put("ewgScore",ewgScore);
                            String ewgColor = productSafety.getEwgColor();
                            dataObj.put("ewgColor",ewgColor);
                            if(StringUtils.isNotNull(ewgScore) && ewgScore.contains("-")){
                                dataObj.put("isSplit",1);
                            }else{
                                dataObj.put("isSplit",0);
                            }
                            dataObj.put("activity",productSafety.getActivity());
                            dataObj.put("pox",productSafety.getPox());
                            String yfSy = productSafety.getYfSy();
                            if(StringUtils.isNotNull(yfSy) && yfSy.contains("孕妇")){
                                dataObj.put("yfSy",yfSy);
                            }else{
                                dataObj.put("yfSy","");
                            }
                            dataObj.put("risk",productSafety.getRisk());
                            dataObj.put("cancer",productSafety.getCancer());
                            dataObj.put("allergies",productSafety.getAllergies());
                            dataObj.put("developmental",productSafety.getDevelopmental());


                            String bzDetail = "";
                            String bzmxDetail = "否";
                            String bz = productSafety.getBz();
                            dataObj.put("bz",bz);
                            if(StringUtils.isNotNull(bz) && ReferenceDataUtil.MATERIAL_IN_USE.equals(bz)){
                                bzmxDetail= "是";
                                bzDetail = ReferenceDataUtil.getBzmxData(productSafety.getBzmx());
                            }
                            dataObj.put("bzmxDetail",bzDetail);
                            dataObj.put("bzmx",bzmxDetail);

                            //风险物质
                            String knhydfxwz = productSafety.getKnhydfxwz();
                            componentUtil.setAqxKnhyfxw(knhydfxwz);
                            componentUtil.setFxwzbx(productSafety.getFxwzbz());
                            if(StringUtils.isNotNull(knhydfxwz)){
                                knhydfxwz = knhydfxwz.replaceAll("、",";").replaceAll("；",";").replaceAll("，",";");
                                Set<String> dataSet = StringUtils.str2Set(knhydfxwz,";");
                                fxwz.addAll(dataSet);
                            }
                            componentUtil.setDataObj(dataObj);
                        }
                    }
                }
                JSONObject formulaObj = new JSONObject();
                formulaObj.put("dataList",dataList);  //配方列表
                formulaObj.put("chiName",chiName);
                formulaObj.put("chiNameNum",chiNameNum);
                formulaObj.put("zybw","");
                formulaObj.put("aqjsy","");
                formulaObj.put("usageMethod","");
                formulaObj.put("pflx","");
                formulaObj.put("cpjx","");

                if("1".equals(pflx)){
                    formulaObj.put("pflx","驻留类");
                }else if("2".equals(pflx)){
                    formulaObj.put("pflx","淋洗类");
                }
                formulaObj.put("dayAvg","");
                formulaObj.put("sedFormula","SED=日均使用量×驻留因子×成分在配方中百分比×经皮吸收率÷体重");


                //风险物质
                formulaObj.put("fxwz",StringUtils.join(fxwz,"、"));
                //可能存在的风险物质的安全评估
                List<String> assessmentOfPotentialRiskSubstances1 = FormulaDataUtil.getAssessmentOfPotentialRiskSubstances1();
                List<String> assessmentOfPotentialRiskSubstances2 = FormulaDataUtil.getAssessmentOfPotentialRiskSubstances2();
                formulaObj.put("assessmentOfPotentialRiskSubstances1",assessmentOfPotentialRiskSubstances1);
                formulaObj.put("assessmentOfPotentialRiskSubstances2",assessmentOfPotentialRiskSubstances2);

                String cpjx = formulaObj.getString("cpjx");
                if(StringUtils.isNull(cpjx)){
                    cpjx = softwareDevelopingFormula.getProductName();
                }
                //风险控制措施或建议
                List<String> riskControlMeasuresOrRecommendationsDatas = FormulaDataUtil.getRiskControlMeasuresOrRecommendations(cpjx,formulaObj.getString("pflx"),formulaObj.getString("zybw"),formulaObj.getString("aqjsy"));
                formulaObj.put("riskControlMeasuresOrRecommendationsDatas",riskControlMeasuresOrRecommendationsDatas);
                //安全评估结论
                List<JSONObject> conclusionOfSafetyAssessmentDatas1 = FormulaDataUtil.getConclusionOfSafetyAssessment1(cpjx,formulaObj.getString("pflx"),formulaObj.getString("zybw"));
                formulaObj.put("conclusionOfSafetyAssessmentDatas1",conclusionOfSafetyAssessmentDatas1);
                //安全评估结论
                List<JSONObject> conclusionOfSafetyAssessmentDatas2 = FormulaDataUtil.getConclusionOfSafetyAssessment2(cpjx,formulaObj.getString("pflx"),formulaObj.getString("zybw"));
                formulaObj.put("conclusionOfSafetyAssessmentDatas2",conclusionOfSafetyAssessmentDatas2);
                //安全评估结论
                List<JSONObject> conclusionOfSafetyAssessmentDatas3 = FormulaDataUtil.getConclusionOfSafetyAssessment3(cpjx,formulaObj.getString("pflx"),formulaObj.getString("zybw"));
                formulaObj.put("conclusionOfSafetyAssessmentDatas3",conclusionOfSafetyAssessmentDatas3);
                //安全评估结论
                List<JSONObject> conclusionOfSafetyAssessmentDatas4 = FormulaDataUtil.getConclusionOfSafetyAssessment4(cpjx,formulaObj.getString("pflx"),formulaObj.getString("zybw"));
                formulaObj.put("conclusionOfSafetyAssessmentDatas4",conclusionOfSafetyAssessmentDatas4);
                //目录
                formulaObj.put("formulaAppendiDatas",FormulaDataUtil.getFormulaAppendix());
                for (ComponentUtil componentUtil : allList) {
                    String componentChiName = componentUtil.getChiName();
                    if(StringUtils.isNotNull(componentChiName)){
                        TUnProductNewSafety productSafety = mapProductSafetys.get(componentChiName.trim());
                        FormulaDataUtil formulaDataUtil = new FormulaDataUtil();
                        if(productSafety!=null){
                            formulaDataUtil.getComponentUtilInfoFgyxDetail(symdMaps, ckwxxhMap, formulaObjParam, productSafety,
                                    componentUtil,new ArrayList<JSONObject>(),false);
                        }
                        String gcfCkwx =  componentUtil.getGcfCkwx();
                        String gcfCkwxnr = componentUtil.getGcfCkwxnr();
                        if(!StringUtil.isEmpty(gcfCkwx) && formulaDataUtil.isInteger(gcfCkwx) && StringUtils.isNotNull(gcfCkwxnr)){
                            Map<String,Object> wxInfo = new HashMap<String,Object>();
                            String resStr = gcfCkwx+"."+componentUtil.getGcfCkwxnr();
                            wxInfo.put("wxContent", resStr);
                            boolean isExists = mapList.stream().anyMatch(mapXh -> mapXh.get("wxContent").toString().equals(resStr));
                            if(!isExists){
                                mapList.add(wxInfo);
                            }
                        }
                        componentUtil.setGxms(productSafety.getGxms());

                    }
                    if(StringUtils.isNotNull(componentChiName)){
                        Object res = materialsMap.get(componentChiName);
                        if(StringUtils.isNotNull(res)){
                            componentUtil.setChiNameNew(componentChiName+"<span style='color:purple'>("+res+")</span>");
                        }else{
                            componentUtil.setChiNameNew(componentChiName);
                        }
                    }else{
                        componentUtil.setChiNameNew(componentChiName);
                    }
                }

                formulaObj.put("allList",allList);
                if(formulaList!=null && formulaList.size()>0){
                    Set<String> xzSet = new HashSet<String>();
                    for(ComponentUtil formulaData : formulaList){
                        String xz = formulaData.getXz();
                        JSONObject returnObj = getFormulaComponentData(xz,formulaList);
                        int rowspan = returnObj.getInteger("rowspan");
                        formulaData.setRowspan(1);
                        formulaData.setIsExist(0);
                        if(rowspan>1 && !xzSet.contains(xz)){
                            xzSet.add(xz);
                            formulaData.setRowspan(rowspan);
                        }else if(rowspan>1){
                            formulaData.setIsExist(1);
                        }
                    }
                }
            }
        }
        return allList;
    }


    /**
     * 获取配方信息
     * @param xz
     * @param list
     * @return
     */
    private  JSONObject getFormulaComponentData(String xz,List<ComponentUtil> list){
        JSONObject returnObj = new JSONObject();
        int rowspan = 0;
        if(list!=null && list.size()>0){
            for(ComponentUtil componentUtil : list){
                String name = componentUtil.getXz();
                if(StringUtils.isNotNull(name) && name.equals(xz)){
                    rowspan++;
                }
            }
        }
        returnObj.put("rowspan",rowspan);
        return returnObj;
    }


    @Override
    public SoftwareDevelopingFormula processSoftwareDevelopingFormulaDetailById(Long id) {
        SoftwareDevelopingFormula softwareDevelopingFormula = softwareDevelopingFormulaMapper.selectSoftwareDevelopingFormulaById(id);
        Integer isResult = 0; // 是否含有待评估
        Integer isGt = 0; // 是否含有超过历史使用量
        Integer isCiji = 0; // 是否含有刺激
        Integer isZhimin = 0; // 是否含有致敏
        List<JSONObject> essenceList = new ArrayList<JSONObject>();
        //是否儿童产品
        boolean isChildrean = false;
        String syrq = softwareDevelopingFormula.getSyrq();
        Integer isContainsEssence = 0;  //是否含有香精
        BigDecimal totalFragrancePercent = WagesUtils.bigDecimal("0");
        if(StringUtils.isNotNull(syrq) && (syrq.contains("1") || syrq.contains("2"))){
            isChildrean = true;
        }
        //获取配方对应VCPR CODE
        RdVcrp rdVcrpInfo = rdIngredientsStandardMapper.queryFormualVcrpDataInfo(softwareDevelopingFormula.getDuliId());
        boolean isVcrpCode = false;
        if(rdVcrpInfo!=null && StringUtils.isNotNull(rdVcrpInfo.getCode())){
            isVcrpCode = true;
        }
        Set<String> essenceSet = new HashSet<String>(); //香精原料
        List<Map<String, Object>> formulaIdList = new ArrayList<Map<String, Object>>();
        List<JSONObject> formulaMaterialDatas = queryFormulaMaterialData(id);
        if(formulaMaterialDatas!=null && formulaMaterialDatas.size()>0){
            for(JSONObject obj : formulaMaterialDatas){
                Integer type = obj.getInteger("type");
                type = StringUtils.convertDefaultValue(type,0);
                if(type==1){
                    Map<String, Object> formulaIdMap = new HashMap<String,Object>();
                    formulaIdMap.put("formulaId", obj.getLong("materialId"));
                    formulaIdMap.put("percentage",obj.getString("percentage"));
                    formulaIdList.add(formulaIdMap);
                }
            }
            softwareDevelopingFormula.setFormulaMaterialDatas(JSONArray.toJSONString(formulaMaterialDatas));
        }

        //1 驻留  2 淋洗
        String pflx = softwareDevelopingFormula.getPflx();
        //获取成分组分数据
        {
            //获取配方表,组分表数据
            List<Map<String, Object>> dataList = softwareDevelopingFormulaMapper.queryIngredientExcelList(id);
            //特殊原料信息
            List<Map<String, Object>> specMaterialData = new ArrayList<Map<String, Object>>();
            //停止原料
            List<String> disableMaterials = new ArrayList<String>();
            //替换原料
            List<String> repalceMaterials = new ArrayList<String>();
            ComponentUtil tipsMapUtil = new ComponentUtil();
            //获取色淀 纳米级极信息
            Map<String,Object> materialsMap = new HashMap<String,Object>();
            Set<String> materialSet = new HashSet<String>();
            if (formulaIdList != null && formulaIdList.size() > 0) {
                List<Map<String, Object>> newDataList = softwareDevelopingFormulaMapper.queryIngredientNewExcelList(formulaIdList);
                if (newDataList != null && newDataList.size() > 0) {
                    for (Map<String, Object> map : newDataList) {
                        Object oldFormulaID = map.get("formulaId");
                        Object oldPercentage = map.get("percentage");
                        if (StringUtils.isNull(oldPercentage)) {
                            oldPercentage = 0;
                        }
                        for (Map<String, Object> idMap : formulaIdList) {
                            Object formulaId = idMap.get("formulaId");
                            Object percentage = idMap.get("percentage");
                            if (StringUtils.isNull(percentage)) {
                                percentage = 0;
                            }
                            if (String.valueOf(formulaId).equals(String.valueOf(oldFormulaID))) {
                                BigDecimal res = new BigDecimal(String.valueOf(percentage))
                                        .multiply(new BigDecimal(String.valueOf(oldPercentage)))
                                        .divide(new BigDecimal(100));
                                map.put("percentage", res);
                            }
                        }
                    }
                }
                if (dataList != null && dataList.size() > 0) {
                    if (newDataList != null && newDataList.size() > 0) {
                        dataList.addAll(newDataList);
                    }
                } else {
                    if (newDataList != null && newDataList.size() > 0) {
                        dataList = newDataList;
                    }
                }
            }
            if (dataList != null && dataList.size() > 0) {
                SortUtil.listSort(dataList, "percentage", true);
            }
            BigDecimal totalPercert = new BigDecimal("0");
            BigDecimal sjTotalPercet = new BigDecimal("0");
            for (Map<String, Object> data : dataList) {
                Object percentage = data.get("percentage");
                totalPercert = totalPercert.add(new BigDecimal(String.valueOf(percentage)));
                Object inciName = data.get("inciName");
                Object inciNameEng = data.get("inciNameEng");
                Object proportion = data.get("proportion");
                Object typeical = data.get("typeical");
                Object sjProportion = data.get("sjProportion");
                List<Symd> SELECT_SYMD_DATA_LIST = new ArrayList<Symd>();
                List<String> symdDataList = new ArrayList<String>();
                Object symdInfo = data.get("symdInfo");
                if (StringUtils.isNotNull(symdInfo)) {
                    symdDataList = StringUtils.str2List(String.valueOf(symdInfo), "~",true,true);
                }
                data.put("symdDataList", symdDataList);
                data.put("SYMD_DATA_LIST", SELECT_SYMD_DATA_LIST);
                Object inicRemark = data.get("inicRemark");
                Object inicNmjyl = data.get("inicNmjyl");
                Object inicYzwsybw = data.get("inicYzwsybw");
                Object inicBxjmcjyl = data.get("inicBxjmcjyl");
                Object inicLbyl = data.get("inicLbyl");
                String val = "";
                StringBuffer sb = new StringBuffer();
                if (StringUtils.isNotNull(inicRemark)) {
                    List<Sdzl> SDZL_DATA_LIST = new MaterialConstansUtil().SDZL_DATA_LIST;
                    for (Sdzl sdzl : SDZL_DATA_LIST) {
                        if (sdzl.getId() == Integer.valueOf(inicRemark + "")) {
                            val = DictUtils.getDictLabel("SOFTWARE_SDZL",String.valueOf(inicRemark));
                            sb.append(val);
                            break;
                        }
                    }
                }
                if (StringUtils.isNotNull(inicNmjyl)) {
                    if ("是".equals(inicNmjyl)) {
                        sb.append("<br />").append("纳米级");
                    }
                }
                if (StringUtils.isNotNull(inicYzwsybw)) {
                    sb.append("<br />").append("原植物使用部位").append(inicYzwsybw);
                }
                if (StringUtils.isNotNull(inicBxjmcjyl)) {
                    sb.append("<br />").append("变性剂").append(inicBxjmcjyl);
                }
                if (StringUtils.isNotNull(inicLbyl)) {
                    sb.append("<br />").append("类别原料").append(inicLbyl);
                }
                data.put("inicRemark", sb.toString());
                List<Map<String, Object>> inicDataList = new ArrayList<Map<String, Object>>();
                if (StringUtils.isNotNull(inciName)) {
                    if (inciName.toString().indexOf("~") > -1) {
                        String[] dataArr = inciName.toString().split("~");
                        String[] dataEngArr = inciNameEng.toString().split("~");
                        String[] dataProportionArr = proportion.toString().split("~");
                        String[] typeicalArr;
                        if(StringUtils.isNotNull(typeical)){
                            typeicalArr = typeical.toString().split("~");
                        }else{
                            typeicalArr = new String[dataProportionArr.length];
                        }
                        String[] dataSjProportionArr = StringUtils.isNull(sjProportion) ? new String[0]
                                : sjProportion.toString().split("~");
                        if (dataArr != null && dataArr.length > 0) {
                            for (int i = 0; i < dataArr.length; i++) {
                                Map<String, Object> mapData = new HashMap<String, Object>();
                                if(StringUtils.isNotNull(dataArr[i])){
                                    materialSet.add(dataArr[i]);
                                }
                                mapData.put("inciName", dataArr[i]);
                                mapData.put("inciNameEng", dataEngArr[i]);
                                mapData.put("typeical", typeicalArr[i]);
                                String minPro = "";
                                String proportionSingle = dataProportionArr[i];
                                String typeicalSingle = typeicalArr[i];
                                String sjProportionSingle = dataSjProportionArr.length > i ? dataSjProportionArr[i]
                                        : "/";
                                if (!StringUtil.isEmpty(proportionSingle) && proportionSingle.indexOf("-") > 0) {
                                    FormulaDataUtil formulaDateUtil = new FormulaDataUtil();
                                    minPro = formulaDateUtil.getLeftNums(proportionSingle);
                                    BigDecimal returnNum = formulaDateUtil.getMiddleNums(proportionSingle);
                                    if (returnNum.doubleValue() > 0) {
                                        if(StringUtils.isNotNull(typeicalSingle) && ErpDataReqUtil.isInteger(typeicalSingle.toString())){
                                            proportionSingle = typeicalSingle.toString();
                                        }else{
                                            proportionSingle = returnNum.toString();
                                        }
                                    }
                                }
                                mapData.put("materialProportion", proportionSingle);
                                mapData.put("proportionSingle", proportionSingle);
                                String proportionVal = ComponentUtil.processCalcPercent(proportionSingle, percentage + "");
                                sjTotalPercet = sjTotalPercet.add(new BigDecimal(proportionVal));
                                mapData.put("proportion", proportionVal);

                                if (!StringUtil.isEmpty(sjProportionSingle) && sjProportionSingle.indexOf("-") > 0) {
                                    FormulaDataUtil formulaDateUtil = new FormulaDataUtil();
                                    BigDecimal returnNum = formulaDateUtil.getMiddleNums(sjProportionSingle);
                                    if (returnNum.doubleValue() > 0) {
                                        sjProportionSingle = returnNum.toString();
                                    }
                                }
                                if ((StringUtil.isEmpty(sjProportionSingle) || "/".equals(sjProportionSingle)
                                        || "\\".equals(sjProportionSingle)) && !StringUtil.isEmpty(minPro)) {
                                    sjProportionSingle = minPro;
                                }
                                mapData.put("sjProportionSingle", sjProportionSingle);
                                mapData.put("sjProportion", ComponentUtil.processCalcPercent2(sjProportionSingle, percentage + ""));
                                inicDataList.add(mapData);
                            }
                        }
                    } else {
                        Map<String, Object> mapData = new HashMap<String, Object>();
                        if(StringUtils.isNotNull(inciName)){
                            materialSet.add(inciName.toString());
                        }
                        mapData.put("inciName", inciName);
                        mapData.put("inciNameEng", inciNameEng);
                        String proportionSingle = proportion.toString();
                        String minPro = "";
                        String sjPoportionSingle = StringUtils.isNull(sjProportion) ? "/" : sjProportion.toString();
                        if (!StringUtil.isEmpty(proportionSingle) && proportionSingle.indexOf("-") > 0) {
                            FormulaDataUtil formulaDateUtil = new FormulaDataUtil();
                            BigDecimal returnNum = formulaDateUtil.getMiddleNums(proportionSingle);
                            minPro = formulaDateUtil.getLeftNums(proportionSingle);
                            if (returnNum.doubleValue() > 0) {
                                if(StringUtils.isNotNull(typeical) && ErpDataReqUtil.isInteger(typeical.toString())){
                                    proportionSingle = typeical.toString();
                                }else{
                                    proportionSingle = returnNum.toString();
                                }
                            }
                        }
                        mapData.put("proportionSingle", proportionSingle);
                        mapData.put("materialProportion", proportionSingle);
                        mapData.put("typeical", typeical);
                        String proportionVal = ComponentUtil.processCalcPercent(proportionSingle, percentage + "");
                        sjTotalPercet = sjTotalPercet.add(new BigDecimal(proportionVal));
                        mapData.put("proportion", proportionVal);
                        if (!StringUtil.isEmpty(sjPoportionSingle) && sjPoportionSingle.indexOf("-") > 0) {
                            FormulaDataUtil formulaDateUtil = new FormulaDataUtil();
                            BigDecimal returnNum = formulaDateUtil.getMiddleNums(sjPoportionSingle);
                            if (returnNum.doubleValue() > 0) {
                                sjPoportionSingle = returnNum.toString();
                            }
                        }
                        if ((StringUtil.isEmpty(sjPoportionSingle) || "/".equals(sjPoportionSingle)
                                || "\\".equals(sjPoportionSingle)) && !StringUtil.isEmpty(minPro)) {
                            sjPoportionSingle = minPro;
                        }
                        mapData.put("sjProportionSingle", sjPoportionSingle);
                        mapData.put("sjProportion", ComponentUtil.processCalcPercent2(sjPoportionSingle, percentage + ""));
                        inicDataList.add(mapData);
                    }
                }
                data.put("inicDataList", inicDataList);
            }

            //获取原料信息
            for(String inciNameStr : materialSet){
                String result = tipsMapUtil.queryMaterialInciResult(inciNameStr,dataList);
                if(StringUtils.isNotNull(result)){
                    materialsMap.put(inciNameStr,result);
                }
            }
            //组分信息
            List<ComponentUtil> allList = new ArrayList<ComponentUtil>();
            // 获取特殊原料替换信息
            List<Map<String, Object>> tipsMaterialFormulaDataList = softwareDevelopingFormulaMapper
                    .queryMaterialFormulaSpecialInfo(id);
            Set<String> compositionalDatas = new HashSet<String>();
            for (int i = 0; i < dataList.size(); i++) {
                String name = (String) dataList.get(i).get("inciName");
                String percentage = (String) (dataList.get(i).get("percentage") + "");
                String inciNameEng = (String) dataList.get(i).get("inciNameEng");
                String proportion = (String) dataList.get(i).get("proportion");
                Object typeical =  dataList.get(i).get("typeical");
                Object status = dataList.get(i).get("status");
                String inciChiName[] = name.split("~");
                String inciEngName[] = inciNameEng.split("~");
                String proArr[] = proportion.split("~");
                String[] typeicalArr;
                if(StringUtils.isNotNull(typeical)){
                    typeicalArr = typeical.toString().split("~");
                }else{
                    typeicalArr = new String[proArr.length];
                }
                List<ComponentUtil> list_ = new ArrayList<ComponentUtil>();
                Object mid = dataList.get(i).get("mid");
                Object isEditIfra = dataList.get(i).get("isEditIfra");
                Object materialCode = dataList.get(i).get("materialCode");
                for (int j = 0; j < inciChiName.length; j++) {
                    String inciChiNameTips = inciChiName[j];
                    compositionalDatas.add(inciChiNameTips);
                    String tipsMaterialInciName = "";
                    Integer tips = tipsMapUtil.isTipsFun(inciChiNameTips);
                    if (tips != null && tips.intValue() == 1) {
                        Map<String, Object> tipsMap = new HashMap<String, Object>();
                        tipsMap.put("mfId", dataList.get(i).get("mfId"));
                        tipsMap.put("materialId", dataList.get(i).get("mid"));
                        tipsMap.put("materialCode", dataList.get(i).get("materialCode"));
                        tipsMap.put("inciName", inciChiName[j]);
                        tipsMap.put("formulaId", dataList.get(i).get("formulaId"));
                        tipsMap.put("formulaCode", dataList.get(i).get("formulaCode"));
                        tipsMaterialInciName = tipsMapUtil.getReplaceTipsMaterialName(tipsMap,
                                tipsMaterialFormulaDataList);
                        tipsMap.put("replaceInciName", tipsMaterialInciName);
                        specMaterialData.add(tipsMap);
                    }
                    if ("1".equals(String.valueOf(status))) {
                        disableMaterials.add(inciChiName[j]);
                    }
                    if ("2".equals(String.valueOf(status))) {
                        repalceMaterials.add(inciChiName[j]);
                    }
                    ComponentUtil componentUtil = new ComponentUtil();
                    if (StringUtil.isEmpty(tipsMaterialInciName)) {
                        componentUtil.setChiName(inciChiName[j]);
                        componentUtil.setEngName(inciEngName[j]);
                        tipsMaterialInciName = inciChiName[j];
                    } else {
                        componentUtil.setChiName(tipsMaterialInciName);
                        componentUtil.setEngName(componentUtil.getTipsMaterialEngName(tipsMaterialInciName));
                    }
                    String proStr = proArr[j];
                    String typeicalSingle = typeicalArr[j];
                    Double rate = 0D;
                    if (!StringUtil.isEmpty(proStr) && proStr.matches("\\d{1,20}\\.?\\d{0,20}")) {
                        if (StringUtil.isEmpty(percentage)) {
                            percentage = "1";
                        }
                        percentage = percentage.trim();
                        BigDecimal per = new BigDecimal(percentage);
                        BigDecimal result1 = per.multiply(new BigDecimal(proStr)).divide(new BigDecimal(100));
                        rate = result1.doubleValue();
                        componentUtil.setPercert(result1);
                    } else {
                        if (StringUtil.isEmpty(percentage)) {
                            percentage = "1";
                        }
                        percentage = percentage.trim();
                        BigDecimal per = new BigDecimal(percentage);
                        BigDecimal result1 = new BigDecimal(percentage);
                        rate = Double.valueOf(percentage);
                        if (!StringUtil.isEmpty(proStr) && proStr.indexOf("-") > 0) {
                            FormulaDataUtil formulaDataUtil = new FormulaDataUtil();
                            BigDecimal returnNum = formulaDataUtil.getMiddleNums(proStr);
                            if (returnNum.doubleValue() > 0) {
                                if(StringUtils.isNotNull(typeicalSingle) && ErpDataReqUtil.isInteger(typeicalSingle.toString())) {
                                    result1 = per.multiply(WagesUtils.bigDecimal(typeicalSingle,6)).divide(new BigDecimal(100));
                                    rate = result1.doubleValue();
                                }else{
                                    result1 = per.multiply(returnNum).divide(new BigDecimal(100));
                                    rate = result1.doubleValue();
                                }
                            }
                        }
                        componentUtil.setPercert(result1);
                    }
                    //判断是否为香精  判断比例是否合格
                    String chiName = componentUtil.getChiName();
                    if(ReferenceDataUtil.isEssence(chiName)){
                        isContainsEssence = 1;
                        JSONObject essenceObj = new JSONObject();
                        essenceObj.put("name",chiName);
                        essenceObj.put("status","2"); //默认不合规
                        if((isVcrpCode ||isChildrean) && StringUtils.isNotNull(isEditIfra) &&  "1".equals(isEditIfra.toString())){
                            if(isChildrean){
                                Map<String,Object> vcrpParams = new HashMap<String,Object>();
                                vcrpParams.put("mid",mid);
                                vcrpParams.put("vcrpCode",FormulaDataUtil.CHILDREN_VCRP);
                                JSONObject ifraData = softwareMaterialMapper.querySoftwareMaterialIfraDataListByChildrean(vcrpParams);
                                if(ifraData!=null && ifraData.containsKey("maximum")){
                                    BigDecimal maximum = ifraData.getBigDecimal("maximum");
                                    if(componentUtil.getPercert().doubleValue()<=maximum.doubleValue()){
                                        essenceObj.put("status","1");
                                    }else{
                                        essenceObj.put("status","3");
                                        essenceSet.add(String.valueOf(materialCode));
                                    }
                                }else{
                                    essenceSet.add(String.valueOf(materialCode));
                                }
                            }else{
                                Map<String,Object> vcrpParams = new HashMap<String,Object>();
                                vcrpParams.put("mid",mid);
                                vcrpParams.put("vcrpCode",rdVcrpInfo.getCode());
                                List<JSONObject> ifraData = softwareMaterialMapper.querySoftwareMaterialIfraDataListByVcrpCode(vcrpParams);
                                if(ifraData!=null && ifraData.size()==1){
                                    JSONObject ifraObj = ifraData.get(0);
                                    BigDecimal maximum = ifraObj.getBigDecimal("maximum");
                                    if(maximum!=null && componentUtil.getPercert().doubleValue()<=maximum.doubleValue()){
                                        essenceObj.put("status","1");
                                    }else{
                                        essenceObj.put("status","3");
                                    }
                                }
                            }
                        }else{
                            essenceSet.add(String.valueOf(materialCode));
                        }
                        essenceList.add(essenceObj);

                        //香精或香料， 26致敏香料组分
                        if(isChildrean){
                            SoftwareMaterial softwareMaterial = new SoftwareMaterial();
                            softwareMaterial.setId(Long.valueOf(mid.toString()));
                            List<JSONObject> materialFragranceDataList = softwareMaterialMapper.queryMaterialFragranceDataList(softwareMaterial);
                            if(materialFragranceDataList!=null && materialFragranceDataList.size()>0){
                                BigDecimal totalPer = WagesUtils.bigDecimal("0");
                                for(JSONObject obj : materialFragranceDataList){
                                    if(obj!=null && obj.containsKey("percent")){
                                        String percent = obj.getString("percent");
                                        if(StringUtils.isNotNull(percent) && ErpDataReqUtil.isInteger(percent)){
                                            totalPer = totalFragrancePercent.add(WagesUtils.bigDecimal(percent,6));
                                        }
                                    }
                                }
                                totalFragrancePercent = totalPer.multiply(WagesUtils.bigDecimal(percentage,6));
                            }
                        }
                    }
                    boolean result1 = tipsMapUtil.processList(list_, tipsMaterialInciName, rate);
                    if (!result1) {
                        list_.add(componentUtil);
                    }
                }
                tipsMapUtil.processListData(allList, list_);
            }
            Collections.sort(allList);

            Map<String, TUnProductNewSafety> mapProductSafetys = new HashMap<String, TUnProductNewSafety>();
            String chiName = ComponentUtil.getChiName(allList);
            String engName = ComponentUtil.getEngName(allList);

            List<TUnProductNewSafety> limitComponentsList = softwareService.queryProductNewSatetyList();
            for (TUnProductNewSafety productNewSafety : limitComponentsList) {
                mapProductSafetys.put(productNewSafety.getZwmc(), productNewSafety);
            }
            StringBuffer gtNum = new StringBuffer(""); // 大于0.1
            StringBuffer ltNum = new StringBuffer(""); // 小于0.1
            String gtNumStr = "";
            String ltNumStr = "";
            BigDecimal totalPercent = new BigDecimal(0);

            //安评报告使用目的
            Map<Object, Object> symdMaps = new HashMap<Object, Object>();
            Map<String, String> ckwxxhMap = new HashMap<String, String>(); // 参考文献序号
            JSONObject formulaObjParam = new JSONObject();
            formulaObjParam.put("pflx",softwareDevelopingFormula.getPflx());
            formulaObjParam.put("zybw",softwareDevelopingFormula.getZybw());
            List<Map<String, Object>> symdInfoDataList = softwareDevelopingFormulaMapper.queryFormulaSymdInfo(id);
            if (symdInfoDataList != null && symdInfoDataList.size() > 0) {
                for (Map<String, Object> map : symdInfoDataList) {
                    symdMaps.put(map.get("chiName"), map.get("symd"));
                }
            }
            for (ComponentUtil componentUtil : allList) {
                componentUtil.setIsGt(0);
                BigDecimal p = componentUtil.getPercert();
                if (StringUtils.isNotNull(p)) {
                    totalPercent = totalPercent.add(p);
                }
                String componentChiName = componentUtil.getChiName();
                componentUtil.setIsTips(tipsMapUtil.isTipsFun(componentChiName));
                if (disableMaterials.contains(componentChiName)) {
                    componentUtil.setStatus(1);
                } else if (repalceMaterials.contains(componentChiName)) {
                    componentUtil.setStatus(2);
                } else {
                    componentUtil.setStatus(0);
                }
                BigDecimal percert = componentUtil.getPercert();
                if (percert.doubleValue() > 0.1D) {
                    gtNum.append(",").append(componentChiName);
                } else {
                    ltNum.append(",").append(componentChiName);
                }
                if (!StringUtil.isEmpty(componentChiName)) {
                    TUnProductNewSafety productSafety = mapProductSafetys.get(componentChiName.trim());
                    if (productSafety != null && StringUtils.isNotNull(productSafety.getId())) {
                        componentUtil.setLabelCondition(productSafety.getSytjhzysx());
                        componentUtil.setMaxAllowConcentration(productSafety.getZdyxnd());
                        componentUtil.setOtherLimit(productSafety.getQtxzyq());
                        componentUtil.setScopeOfApplication(productSafety.getSyjsyfw());
                        componentUtil.setZllzglssyl(productSafety.getZllzglssyl());
                        componentUtil.setLxlzglssyl(productSafety.getLxlzglssyl());
                        componentUtil.setBzmx(productSafety.getBzmx());
                        FormulaDataUtil formulaDataUtil = new FormulaDataUtil();
                        formulaDataUtil.getComponentUtilInfoFgyx(symdMaps, ckwxxhMap, formulaObjParam, productSafety,
                                componentUtil);
                        if (formulaDataUtil.isInteger(componentUtil.getGcfZglssy())
                                && StringUtils.isNotNull(componentUtil.getPercert()) && componentUtil.getPercert()
                                .doubleValue() > Double.valueOf(componentUtil.getGcfZglssy())) {
                            isGt = 1;
                            componentUtil.setIsGt(1);
                        }
                        if ("原料安全信息待评估".equals(componentUtil.getGcfPgjl())) {
                            isResult = 1;
                        }
                        String conclusion = productSafety.getConclusion();
                        if(StringUtils.isNotNull(conclusion)){
                            if(conclusion.contains("无刺激性")){
                                isCiji = 1;
                            }
                            if(conclusion.contains("无致敏")){
                                isZhimin = 1;
                            }
                        }
                        JSONObject dataObj = new JSONObject();
                        dataObj.put("bzmx", productSafety.getBzmx());//备案（含明细）
                        dataObj.put("conclusion", productSafety.getConclusion());//备案（含明细）
                        dataObj.put("isNewMaterial", productSafety.getIsNewMaterial());//是否新原料
                        dataObj.put("finding", productSafety.getFinding());//安全级别
                        dataObj.put("zdsynd",productSafety.getZdyxnd());
                        dataObj.put("cirzlx",productSafety.getZl());
                        dataObj.put("cirlxx",productSafety.getLx());
                        dataObj.put("lxlzglssyl", productSafety.getLxlzglssyl());  //淋洗类产品最高历史使用量
                        dataObj.put("zllzglssyl", productSafety.getZllzglssyl());  //驻留类产品最高历史使用量
                        dataObj.put("syjsyfw", productSafety.getSyjsyfw()); //适用及(或)使用范围
                        dataObj.put("symd",productSafety.getSymd());
                        dataObj.put("lx",productSafety.getLx());
                        dataObj.put("zl",productSafety.getZl());
                        dataObj.put("xyl",productSafety.getXyl());
                        dataObj.put("newTotal",productSafety.getNewTotal());
                        dataObj.put("newRange",productSafety.getNewRange());
                        dataObj.put("otherRequest",productSafety.getOtherRequest());
                        dataObj.put("inputTotals",productSafety.getTotal());
                        String ewgScore = productSafety.getEwgScore();
                        dataObj.put("ewgScore",ewgScore);
                        String ewgColor = productSafety.getEwgColor();
                        dataObj.put("ewgColor",ewgColor);
                        if(StringUtils.isNotNull(ewgScore) && ewgScore.contains("-")){
                            dataObj.put("isSplit",1);
                        }else{
                            dataObj.put("isSplit",0);
                        }
                        dataObj.put("activity",productSafety.getActivity());
                        dataObj.put("pox",productSafety.getPox());
                        String yfSy = productSafety.getYfSy();
                        if(StringUtils.isNotNull(yfSy) && yfSy.contains("孕妇")){
                            dataObj.put("yfSy",yfSy);
                        }else{
                            dataObj.put("yfSy","");
                        }
                        dataObj.put("risk",productSafety.getRisk());
                        dataObj.put("cancer",productSafety.getCancer());
                        dataObj.put("allergies",productSafety.getAllergies());
                        dataObj.put("developmental",productSafety.getDevelopmental());


                        String bzDetail = "";
                        String bzmxDetail = "否";
                        String bz = productSafety.getBz();
                        dataObj.put("bz",bz);
                        if(StringUtils.isNotNull(bz) && ReferenceDataUtil.MATERIAL_IN_USE.equals(bz)){
                            bzmxDetail= "是";
                            bzDetail = ReferenceDataUtil.getBzmxData(productSafety.getBzmx());
                        }
                        dataObj.put("bzmxDetail",bzDetail);
                        dataObj.put("bzmx",bzmxDetail);
                        componentUtil.setDataObj(dataObj);
                    }
                }
            }
            if (StringUtils.isNotNull(gtNum)) {
                gtNumStr = gtNum.toString().substring(1);
            }
            if (StringUtils.isNotNull(ltNum)) {
                ltNumStr = ltNum.toString().substring(1);
            }
            gtNumStr = ComponentUtil.getGtRateChiName(allList,new ArrayList<String>(),new ArrayList<String>(),materialsMap,true);
            ltNumStr = ComponentUtil.getLtRateChiName(allList,new ArrayList<String>(),new ArrayList<String>(),materialsMap,true);
            String materialInciRemark = ComponentUtil.getMaterialInciRemarkName(allList,new ArrayList<String>(),new ArrayList<String>(),materialsMap,true);

            if(softwareDevelopingFormula!=null && compositionalDatas!=null && compositionalDatas.size()>0){
                boolean isProcess = true;
                Integer cirId = softwareDevelopingFormula.getCirId();
                Integer duliId = softwareDevelopingFormula.getDuliId();
                //获取毒理 驻留 淋洗数据
                List<JSONObject> duliCategory = rdIngredientsStandardMapper.queryDuliCategoryDataList();
                if(StringUtils.isNotNull(cirId) || StringUtils.isNotNull(duliId)){
                    isProcess = true;
                }
                if(isProcess){
                    //获取成分 历史使用量和毒理数据
                    List<Map<String,Object>> ingredientsDataList = rdIngredientsMapper.queryFormulaRdIngredientsData(compositionalDatas);
                    //获取中检院已上市产品原料信息2024
                    List<JSONObject> zjyDataList = rdIngredientsMapper.queryZjyIngredientsDataList(compositionalDatas);
                    ///获取中检院《国际化妆品安全评估数据索引》收录的部分原料使用信息  中检院 2025年4月10日发布
                    List<JSONObject> zjyAqpgDataList = rdIngredientsMapper.queryZjyAqpgIngredientsDataList(compositionalDatas);

                    if(ingredientsDataList!=null && ingredientsDataList.size()>0){
                        //获取成分   cir历史数据 获取毒理数据
                        for(ComponentUtil data : allList){
                            String name =  data.getChiName();
                            if(data.getDataObj()==null){
                                //System.out.println("name"+name+",data="+data);
                                data.setDataObj(new JSONObject());
                            }
                            String inputTotals = data.getDataObj().getString("inputTotals");
                            data.setPflx(pflx);
                            Integer isEssence = 0;
                            JSONObject returnObj = ComponentUtil.queryComponentCirData(name,inputTotals,zjyDataList,zjyAqpgDataList,String.valueOf(cirId),String.valueOf(duliId),ingredientsDataList,duliCategory);
                            if(returnObj.containsKey("noaelData") && StringUtils.isNotNull(returnObj.getString("noaelData"))){
                                data.setNoaelData(returnObj.getString("noaelData"));
                                data.setNoaelLabel(returnObj.getString("noaelLabel"));
                            }
                            data.setCirData(returnObj.getString("cirData"));
                            data.setOuBiao(returnObj.getString("ouBiao"));
                            data.setRiBiao(returnObj.getString("riBiao"));
                            data.setLxxData(returnObj.getString("lxxData"));
                            data.setZlxData(returnObj.getString("zlxData"));
                            data.setBabyData(returnObj.getString("babyData"));
                            data.setTotalsData(StringUtils.isNotNull(returnObj.getString("totalsData"))?returnObj.getString("totalsData"):inputTotals);
                            //中检院
                            data.setMaxTotals(returnObj.getString("maxTotals"));
                            data.setZjyDatas((List<JSONObject>)returnObj.get("zjyDatas"));
                            data.setZjySize(returnObj.getInteger("zjySize"));

                            //《国际化妆品安全评估数据索引》收录的部分原料使用信息  中检院 2025年4月10日发布
                            data.setAqpgMaxTotals(returnObj.getString("aqpgMaxTotals"));
                            data.setAqpgDatas((List<JSONObject>)returnObj.get("aqpgDatas"));
                            data.setAqpgSize(returnObj.getInteger("aqpgSize"));

                            //获取成分详情数据
                            data.setDuliOuBiaoLeaveOn(returnObj.getString("duliOuBiaoLeaveOn"));
                            data.setDuliRiBiaoLeaveOn(returnObj.getString("duliRiBiaoLeaveOn"));
                            data.setDuliRiBiaoTotals(returnObj.getString("duliRiBiaoTotals"));
                            data.setDuliOuBiaoRinseOff(returnObj.getString("duliOuBiaoRinseOff"));
                            data.setDuliRiBiaoRinseOff(returnObj.getString("duliRiBiaoRinseOff"));
                            data.setDuliOuBiaoTotals(returnObj.getString("duliOuBiaoTotals"));

                            if(ReferenceDataUtil.isEssence(name)){
                                isEssence = 1;
                            }
                            //获取自定义数据
                            JSONObject zydObj = referenceDataUtil.queryComponentZdyData(isEssence,name,dataList);
                            data.setSupplierTotals(zydObj.getString("supplierTotals"));
                            data.setSupplierLeaveOn(zydObj.getString("supplierLeaveOn"));
                            data.setSupplierRinseOff(zydObj.getString("supplierRinseOff"));
                            data.setCompanyTotals(zydObj.getString("companyTotals"));
                            data.setCompanyLeaveOn(zydObj.getString("companyLeaveOn"));
                            data.setCompanyRinseOff(zydObj.getString("companyRinseOff"));


                            data.setIsEssence(isEssence);
                            data.setIsIfra(zydObj.getInteger("isIfra"));


                            Integer componentType =  ReferenceDataUtil.getFormulaComponentType(data);
                            data.setComponentType(componentType);
                        }


                    }
                }
            }

            JSONObject formulaObj = new JSONObject();
            formulaObj.put("dataList",dataList);  //配方列表
            formulaObj.put("totalPercentVal",new FormulaDataUtil().getNumberStr(totalPercert));
            formulaObj.put("sjTotalPercet",new FormulaDataUtil().getNumberStr(sjTotalPercet));
            //formulaObj.put("chiName",chiName);
            //formulaObj.put("engName",engName);
            //组分数据
            formulaObj.put("totalPercent",new FormulaDataUtil().getNumberStr(totalPercent));
            formulaObj.put("ltNumStr",ltNumStr);
            formulaObj.put("gtNumStr",gtNumStr);
            formulaObj.put("specMaterialData",specMaterialData);

            List<String> cjZmList = new ArrayList<String>();
            List<String> cjList = new ArrayList<String>();
            List<String> zmList = new ArrayList<String>();



            for (ComponentUtil componentUtil : allList) {
                String componentChiName = componentUtil.getChiName();
                if(StringUtils.isNotNull(componentChiName)){
                    Object res = materialsMap.get(componentChiName);
                    if(StringUtils.isNotNull(res)){
                        componentUtil.setChiNameNew(componentChiName+"<span style='color:purple'>("+res+")</span>");
                    }else{
                        componentUtil.setChiNameNew(componentChiName);
                    }
                    TUnProductNewSafety productSafety = mapProductSafetys.get(componentChiName.trim());
                    if (productSafety != null && StringUtils.isNotNull(productSafety.getId())) {
                        componentUtil.setLabelCondition(productSafety.getSytjhzysx());
                        componentUtil.setMaxAllowConcentration(productSafety.getZdyxnd());
                        componentUtil.setOtherLimit(productSafety.getQtxzyq());
                        componentUtil.setScopeOfApplication(productSafety.getSyjsyfw());
                        componentUtil.setZllzglssyl(productSafety.getZllzglssyl());
                        componentUtil.setLxlzglssyl(productSafety.getLxlzglssyl());
                        componentUtil.setBzmx(productSafety.getBzmx());
                        if(StringUtils.isNull(componentUtil.getTotalsData())){
                            componentUtil.setTotalsData(productSafety.getTotal());
                        }
                        FormulaDataUtil formulaDataUtil = new FormulaDataUtil();

                        formulaDataUtil.getComponentUtilInfoFgyxDetail(symdMaps, ckwxxhMap, formulaObjParam, productSafety,
                                componentUtil,essenceList,isChildrean);
                        componentUtil.setFinalConclusion(componentUtil.getGcfPgjl());
                        String isSqVal = componentUtil.getIsSq();
                        String isExceed_ = componentUtil.getIsExceed();
                        if("0".equals(isExceed_) && "1".equals(isSqVal)){
                            String conclusion = productSafety.getConclusion();
                            String jielun = "";
                            if(StringUtils.isNotNull(conclusion)){
                                if(conclusion.contains("刺激") && conclusion.contains("致敏")){
                                    jielun = "依据CIR评估报告,本配方中成分添加量在CIR历史用量之下，同时该产品依据《化妆品安全技术规范》2015版进行了皮肤变态反应测试和人体斑贴测试实验，测试结果显示本品无致敏性、无刺激性。故该原料在本品中的使用是安全的。";
                                }else if(conclusion.contains("刺激")){
                                    jielun = "依据CIR评估报告,本配方中该成分添加量在CIR历史使用浓度之下，同时该产品选取了30人，依据《化妆品安全技术规范》2015 版，进行了人体斑贴测试实验，测试结果显示本品无刺激性。故该成分在本品中的使用是安全的。";
                                }else if(conclusion.contains("致敏")){
                                    jielun = "依据CIR评估报告,本配方中该成分添加量在CIR历史浓度之下，同时该产品依据《化妆品安全技术规范》2015 版进行了皮肤变态反应测试，测试结果显示本品无致敏性。故该成分在本品中的使用是安全的。";
                                }
                                if(StringUtils.isNotNull(jielun)){
                                    componentUtil.setFinalConclusion(jielun);
                                }
                            }
                        }
                        if("0".equals(isExceed_)){
                            String conclusion = productSafety.getConclusion();
                            if(StringUtils.isNotNull(conclusion)){
                                if(conclusion.contains("AICIS") && !conclusion.contains("CIR")){
                                    componentUtil.setFinalConclusion(conclusion);
                                }
                            }
                        }
                        Integer componentType = componentUtil.getComponentType();
                        formulaDataUtil.getComponentUtilInfoFgyx(symdMaps, ckwxxhMap, formulaObjParam, productSafety,
                                componentUtil);
                        if(componentType!=null && componentType.intValue()==4 && StringUtils.isNotNull(componentUtil.getFinalConclusion())
                                && componentUtil.getFinalConclusion().contains("已上市产品原料使用信息")
                                && componentUtil.getFinalConclusion().contains("安全")){
                            componentUtil.setComponentType(1);
                            componentUtil.setIsColor(0);
                        }
                        if(componentType!=null && componentType.intValue()==4 && StringUtils.isNotNull(componentUtil.getFinalConclusion())
                                && componentUtil.getFinalConclusion().contains("国际化妆品安全评估数据索引")
                                && componentUtil.getFinalConclusion().contains("安全")){
                            componentUtil.setComponentType(1);
                            componentUtil.setIsColor(0);
                        }
                        if (formulaDataUtil.isInteger(componentUtil.getGcfZglssy())
                                && StringUtils.isNotNull(componentUtil.getPercert()) && componentUtil.getPercert()
                                .doubleValue() > Double.valueOf(componentUtil.getGcfZglssy())) {
                            isGt = 1;
                            componentUtil.setIsGt(1);
                        }
                        if ("原料安全信息待评估".equals(componentUtil.getGcfPgjl())) {
                            isResult = 1;
                        }
                        int ciji = 0;
                        int zhimin = 0;
                        String conclusion = productSafety.getConclusion();
                        String isAqpgZjy  = componentUtil.getIsAqpgZjy();
                        if(StringUtils.isNotNull(conclusion) && "0".equals(isAqpgZjy)){
                            if(conclusion.contains("无刺激性")){
                                isCiji = 1;
                                ciji = 1;
                            }
                            if(conclusion.contains("无致敏")){
                                isZhimin = 1;
                                zhimin = 1;
                            }
                        }
                        if(ciji==1 && zhimin==1){
                            cjZmList.add(componentChiName);
                        }else if(ciji==1){
                            cjList.add(componentChiName);
                        }else if(zhimin==1){
                            zmList.add(componentChiName);
                        }
                        JSONObject dataObj = new JSONObject();
                        dataObj.put("bzmx", productSafety.getBzmx());//备案（含明细）
                        dataObj.put("conclusion", productSafety.getConclusion());//备案（含明细）
                        dataObj.put("isNewMaterial", productSafety.getIsNewMaterial());//是否新原料
                        dataObj.put("finding", productSafety.getFinding());//安全级别
                        dataObj.put("zdsynd",productSafety.getZdyxnd());
                        dataObj.put("cirzlx",productSafety.getZl());
                        dataObj.put("cirlxx",productSafety.getLx());
                        dataObj.put("lxlzglssyl", productSafety.getLxlzglssyl());  //淋洗类产品最高历史使用量
                        dataObj.put("zllzglssyl", productSafety.getZllzglssyl());  //驻留类产品最高历史使用量
                        dataObj.put("syjsyfw", productSafety.getSyjsyfw()); //适用及(或)使用范围
                        dataObj.put("symd",productSafety.getSymd());
                        dataObj.put("lx",productSafety.getLx());
                        dataObj.put("zl",productSafety.getZl());
                        dataObj.put("xyl",productSafety.getXyl());
                        dataObj.put("newTotal",productSafety.getNewTotal());
                        dataObj.put("newRange",productSafety.getNewRange());
                        dataObj.put("otherRequest",productSafety.getOtherRequest());
                        dataObj.put("inputTotals",productSafety.getTotal());
                        String ewgScore = productSafety.getEwgScore();
                        dataObj.put("ewgScore",ewgScore);
                        String ewgColor = productSafety.getEwgColor();
                        dataObj.put("ewgColor",ewgColor);
                        if(StringUtils.isNotNull(ewgScore) && ewgScore.contains("-")){
                            dataObj.put("isSplit",1);
                        }else{
                            dataObj.put("isSplit",0);
                        }
                        dataObj.put("activity",productSafety.getActivity());
                        dataObj.put("pox",productSafety.getPox());
                        String yfSy = productSafety.getYfSy();
                        if(StringUtils.isNotNull(yfSy) && yfSy.contains("孕妇")){
                            dataObj.put("yfSy",yfSy);
                        }else{
                            dataObj.put("yfSy","");
                        }
                        dataObj.put("risk",productSafety.getRisk());
                        dataObj.put("cancer",productSafety.getCancer());
                        dataObj.put("allergies",productSafety.getAllergies());
                        dataObj.put("developmental",productSafety.getDevelopmental());


                        String bzDetail = "";
                        String bzmxDetail = "否";
                        String bz = productSafety.getBz();
                        dataObj.put("bz",bz);
                        if(StringUtils.isNotNull(bz) && ReferenceDataUtil.MATERIAL_IN_USE.equals(bz)){
                            bzmxDetail= "是";
                            bzDetail = ReferenceDataUtil.getBzmxData(productSafety.getBzmx());
                        }
                        dataObj.put("bzmxDetail",bzDetail);
                        dataObj.put("bzmx",bzmxDetail);


                        componentUtil.setDataObj(dataObj);
                    }
                }else{
                    componentUtil.setChiNameNew(componentChiName);
                }
            }

            formulaObj.put("allList",allList);
            formulaObj.put("isResult",isResult);
            formulaObj.put("isGt",isGt);
            softwareDevelopingFormula.setFormulaObj(formulaObj.toJSONString());
        }

        //更新项目内容
        SoftwareDevelopingFormula params = new SoftwareDevelopingFormula();
        params.setId(softwareDevelopingFormula.getId());
        params.setIsGt(isGt);
        params.setIsResult(isResult);
        params.setIsCiji(isCiji);
        params.setIsZhimin(isZhimin);

        new Thread() {
            public void run() {
                softwareDevelopingFormulaMapper.updateSoftwareDevelopingFormula(params);
            }
        }.start();



        return softwareDevelopingFormula;
    }
    @Override
    public SoftwareDevelopingFormula selectSoftwareDevelopingFormulaSimpleById(Long id) {
        SoftwareDevelopingFormula softwareDevelopingFormula = softwareDevelopingFormulaMapper.selectSoftwareDevelopingFormulaById(id);
        Integer isResult = 0; // 是否含有待评估
        Integer isGt = 0; // 是否含有超过历史使用量
        List<Map<String, Object>> formulaIdList = new ArrayList<Map<String, Object>>();
        List<JSONObject> formulaMaterialDatas = queryFormulaMaterialData(id);
        if(formulaMaterialDatas!=null && formulaMaterialDatas.size()>0){
            softwareDevelopingFormula.setFormulaMaterialDatas(JSONArray.toJSONString(formulaMaterialDatas));
        }

        //更新商品信息和原料评估内容
        String oldProductName = softwareDevelopingFormula.getProductName();
        String oldProductItemName = softwareDevelopingFormula.getItemNameText();
        String oldCustomerName = softwareDevelopingFormula.getCustomerName();
        if(StringUtils.isNotNull(softwareDevelopingFormula.getProjectNo())
           && StringUtils.isNotNull(softwareDevelopingFormula.getFormulaCode())
           && !softwareDevelopingFormula.getFormulaCode().startsWith("P")){
            JSONObject parms = new JSONObject();
            parms.put("projectNo",softwareDevelopingFormula.getProjectNo());
            JSONObject returnObj = projectService.queryProjectDataDetail(parms);
            Integer reuslt = new FormulaDataUtil().processFormulaDataInfo(softwareDevelopingFormula, returnObj);
            if(reuslt>0){
                String newProductName = softwareDevelopingFormula.getProductName();
                String newProjectItemName = softwareDevelopingFormula.getItemNameText();
                String newCustomerName = softwareDevelopingFormula.getCustomerName();
                boolean isFlag1 = !StringUtil.isEmpty(oldProductName) && !StringUtil.isEmpty(newProductName)
                        && !oldProductName.equals(newProductName);
                boolean isFlag2 = !StringUtil.isEmpty(newProjectItemName)
                        && !newProjectItemName.equals(oldProductItemName);
                boolean isFlag3 = !StringUtil.isEmpty(newCustomerName) && !newCustomerName.equals(oldCustomerName);
                if (isFlag1 || isFlag2 || isFlag3) {
                    SoftwareDevelopingFormula params = new SoftwareDevelopingFormula();
                    params.setId(softwareDevelopingFormula.getId());
                    params.setProductName(softwareDevelopingFormula.getProductName());
                    params.setCustomerName(softwareDevelopingFormula.getCustomerName());
                    params.setBrandName(softwareDevelopingFormula.getBrandName());
                    params.setSeriesName(softwareDevelopingFormula.getSeriesName());
                    softwareDevelopingFormulaMapper.updateSoftwareDevelopingFormula(params);
                }
            }
        }

        return softwareDevelopingFormula;
    }


    @Override
    public SoftwareDevelopingFormula selectSoftwareDevelopingFormulaById(Long id) {
        SoftwareDevelopingFormula softwareDevelopingFormula = softwareDevelopingFormulaMapper.selectSoftwareDevelopingFormulaById(id);
        //更新商品信息和原料评估内容
        String oldProductName = softwareDevelopingFormula.getProductName();
        String oldProductItemName = softwareDevelopingFormula.getItemNameText();
        String oldCustomerName = softwareDevelopingFormula.getCustomerName();
        if(StringUtils.isNotNull(softwareDevelopingFormula.getProjectNo())
           && StringUtils.isNotNull(softwareDevelopingFormula.getFormulaCode())
           && !softwareDevelopingFormula.getFormulaCode().startsWith("P")){
            JSONObject parms = new JSONObject();
            parms.put("projectNo",softwareDevelopingFormula.getProjectNo());
            JSONObject returnObj = projectService.queryProjectDataDetail(parms);
            Integer reuslt = new FormulaDataUtil().processFormulaDataInfo(softwareDevelopingFormula, returnObj);
            if(reuslt>0){
                String newProductName = softwareDevelopingFormula.getProductName();
                String newProjectItemName = softwareDevelopingFormula.getItemNameText();
                String newCustomerName = softwareDevelopingFormula.getCustomerName();
                boolean isFlag1 = !StringUtil.isEmpty(oldProductName) && !StringUtil.isEmpty(newProductName)
                        && !oldProductName.equals(newProductName);
                boolean isFlag2 = !StringUtil.isEmpty(newProjectItemName)
                        && !newProjectItemName.equals(oldProductItemName);
                boolean isFlag3 = !StringUtil.isEmpty(newCustomerName) && !newCustomerName.equals(oldCustomerName);
                if (isFlag1 || isFlag2 || isFlag3) {
                    SoftwareDevelopingFormula params = new SoftwareDevelopingFormula();
                    params.setId(softwareDevelopingFormula.getId());
                    params.setProductName(softwareDevelopingFormula.getProductName());
                    params.setCustomerName(softwareDevelopingFormula.getCustomerName());
                    params.setBrandName(softwareDevelopingFormula.getBrandName());
                    params.setSeriesName(softwareDevelopingFormula.getSeriesName());
                    softwareDevelopingFormulaMapper.updateSoftwareDevelopingFormula(params);
                }
            }
        }
        return softwareDevelopingFormula;
    }

    @Override
    public int addFormulaSpecZxbz(JSONObject specObj) {
        SoftwareMaterialFormulaSpecFfbz softwareMaterialFormulaSpecFfbz = new SoftwareMaterialFormulaSpecFfbz();
        Long currentTemplateId= specObj.getLong("currentTemplateId");
        String itemArray= specObj.getString("itemArray");
        softwareMaterialFormulaSpecFfbz = JSONObject.parseObject(specObj.toJSONString(),SoftwareMaterialFormulaSpecFfbz.class);
        softwareMaterialFormulaSpecFfbz.setFormulaId(specObj.getLong("formulaId"));
        softwareMaterialFormulaSpecFfbz.setTemplateId(currentTemplateId);
        softwareMaterialFormulaSpecFfbz.setJcxmJson(itemArray);
        softwareMaterialFormulaSpecFfbz.setCreatedTime(DateUtils.getNowDate());
        softwareMaterialFormulaSpecFfbz.setOperator(SecurityUtils.getUsername());
        if(StringUtils.isNotNull(itemArray)){
            JSONArray array = JSONArray.parseArray(itemArray);
            for(int i = 0;i<array.size();i++){
                JSONObject obj = array.getJSONObject(i);
                String id = obj.getString("id");
                String standardVal = obj.getString("standardVal");
                if(StringUtils.isNotNull(standardVal) && StringUtils.isNotNull(id)){
                    if("13".equals(id)){  //比重
                        softwareMaterialFormulaSpecFfbz.setBizhong(standardVal);
                    }else if("21".equals(id)){  //黏度
                        softwareMaterialFormulaSpecFfbz.setNiandu(standardVal);
                    }
                }
            }
        }
        softwareMaterialFormulaSpecFfbzMapper.deleteSoftwareMaterialFormulaSpecFfbzByFormulaId(softwareMaterialFormulaSpecFfbz.getFormulaId());
        softwareMaterialFormulaSpecFfbzMapper.insertSoftwareMaterialFormulaSpecFfbz(softwareMaterialFormulaSpecFfbz);

        SoftwareDevelopingFormula softwareDevelopingFormula = new SoftwareDevelopingFormula();
        softwareDevelopingFormula.setId(softwareMaterialFormulaSpecFfbz.getFormulaId());
        softwareDevelopingFormula.setExecNumberId(specObj.getLong("execNumberId"));
        softwareDevelopingFormula.setExecNumber(specObj.getString("execNumber"));
        softwareDevelopingFormula.setJcxmJson(itemArray);
        softwareDevelopingFormula.setCurrentTemplateId(currentTemplateId);
        softwareDevelopingFormulaMapper.updateSoftwareDevelopingFormula(softwareDevelopingFormula);
        return 1;
    }
    @Override
    public int addUserFormulaSpecZxbz(JSONObject specObj) {
        SoftwareMaterialFormulaSpecFfbz softwareMaterialFormulaSpecFfbz = new SoftwareMaterialFormulaSpecFfbz();
        String itemArray= specObj.getString("itemArray");
        String type = specObj.getString("type"); //类型
        Long formulaId = specObj.getLong("formulaId");
        softwareMaterialFormulaSpecFfbz.setZidingyi1(type);
        softwareMaterialFormulaSpecFfbz.setFormulaId(formulaId);
        softwareMaterialFormulaSpecFfbz.setJcxmJson(itemArray);
        softwareMaterialFormulaSpecFfbz.setCreatedTime(DateUtils.getNowDate());
        softwareMaterialFormulaSpecFfbz.setOperator(SecurityUtils.getUsername());
        if(specObj.containsKey("specId") && StringUtils.isNotNull(specObj.getString("specId"))){
             softwareMaterialFormulaSpecFfbz.setId(specObj.getLong("specId"));
             softwareMaterialFormulaSpecFfbz.setNote(SecurityUtils.getUsername());
            softwareMaterialFormulaSpecFfbzMapper.updateSoftwareMaterialFormulaSpec(softwareMaterialFormulaSpecFfbz);

        }else {
            String prefix = "lab-";
            if ("3".equals(type)) {
                prefix = "pilot-";
            } else if ("4".equals(type)) {
                prefix = "fg-";
            } else if ("0".equals(type)) {
                prefix = "sp-";
            }
            Integer count = softwareMaterialFormulaSpecFfbzMapper.queryFormulaSpecCount(formulaId, prefix);
            count = StringUtils.convertDefaultValue(count, 0);
            softwareMaterialFormulaSpecFfbz.setZidingyi2(prefix + (count + 1));
            softwareMaterialFormulaSpecFfbzMapper.insertSoftwareMaterialFormulaSpec(softwareMaterialFormulaSpecFfbz);
        }
        return 1;
    }

    @Override
    public JSONObject addFormulaSpecPage(Long id) {
        JSONObject obj = new JSONObject();

        return null;
    }

    @Override
    public int addFormulaSpecMaterialData(SoftwareDevelopingFormula softwareDevelopingFormula) {
        Long formulaId = softwareDevelopingFormula.getId();
        String specMaterialDatas = softwareDevelopingFormula.getSpecMaterialDatas();
        List<Map<String,Object>> dataMaps = new ArrayList<Map<String,Object>>();
        JSONArray array = JSONArray.parseArray(specMaterialDatas);
        if(array!=null && array.size()>0){
            for(int i = 0 ;i<array.size();i++){
                Map<String,Object> map = new HashMap<String, Object>();
                JSONObject obj = array.getJSONObject(i);
                map.put("formulaId", obj.getString("formulaId"));
                map.put("inciName", obj.getString("inciName"));
                map.put("mfId", obj.getString("mfId"));
                map.put("materialId", obj.getString("materialId"));
                map.put("materialCode", obj.getString("materialCode"));
                map.put("replaceInciName", obj.getString("replaceInciName"));
                map.put("operator",SecurityUtils.getUsername());
                dataMaps.add(map);
            }
        }
        if(dataMaps!=null && dataMaps.size()>0){
            softwareDevelopingFormulaMapper.deleteTipsMaterialFormulaInfo(formulaId);
            softwareDevelopingFormulaMapper.batchTipsMaterialFormulaInfo(dataMaps);
        }
        return 1;
    }

    @Override
    public int addFormulaSymdForm(SoftwareDevelopingFormula softwareDevelopingFormula) {
        Long formulaId = softwareDevelopingFormula.getId();
        String formulaSymd = softwareDevelopingFormula.getFormulaSymd();
        List<Map<String,Object>> maps = new ArrayList<Map<String,Object>>();
        JSONArray array = JSONArray.parseArray(formulaSymd);
        if(array!=null && array.size()>0){
            for(int i = 0;i<array.size();i++){
                JSONObject obj = array.getJSONObject(i);
                Map<String,Object> map = new HashMap<String, Object>();
                map.put("formulaId", formulaId);
                map.put("chiName", obj.getString("chiName"));
                map.put("symdInfo", obj.getString("cppfSymd"));
                map.put("operator",SecurityUtils.getUsername());
                maps.add(map);
            }
        }
        if(maps!=null && maps.size()>0){
            softwareDevelopingFormulaMapper.deleteFormulaSmdInfo(formulaId);
            softwareDevelopingFormulaMapper.batchFormulaSymdInfo(maps);
        }
        return 1;
    }

    /**
     * 生成P配方
     * @param softwareDevelopingFormula
     * @return
     */
    @Override
    public Map<String, Object> generatePFormulaInfo(SoftwareDevelopingFormula softwareDevelopingFormula) {
        Long formulaId = softwareDevelopingFormula.getId();
        Map<String, Object> result = new HashMap<String,Object>();
        result.put("status",0);
        boolean isPrice = true;
        JSONArray array = JSONArray.parseArray(softwareDevelopingFormula.getFormulaMaterialDatas());
        if(array!=null && array.size()>0){
            Integer count = softwareDevelopingFormulaMapper.queryPFormulaCountInfo(formulaId);
            if(count<3){
                SoftwareDevelopingFormula dataInfo = softwareDevelopingFormulaMapper.selectSoftwareDevelopingFormulaById(formulaId);
                String maxFormulaCode = softwareDevelopingFormulaMapper.queryMaxPFormulaInfo(formulaId);
                BigDecimal totalPercent = WagesUtils.bigDecimal("0");
                for(int i = 0;i<array.size();i++){
                    JSONObject obj = array.getJSONObject(i);
                    BigDecimal percentage = obj.getBigDecimal("percentage");
                    totalPercent= totalPercent.add(percentage);
                    SoftwareMaterialFormula newMaterialFormulaInfo = new SoftwareMaterialFormula();
                    newMaterialFormulaInfo.setId(obj.getLong("id"));
                    newMaterialFormulaInfo.setIsUse(1);
                    Integer r = softwareMaterialFormulaMapper.updateFormulaMaterialIsUseById(newMaterialFormulaInfo);
                    if(r<=0){
                        result.put("status",1);
                        result.put("msg","原料["+obj.getString("materialCode")+"]已被使用过,请勿重复使用!");
                        return result;
                    }
                }
                Integer index = 0;
                if(StringUtils.isNotNull(maxFormulaCode) && maxFormulaCode.startsWith("P")){
                    String num = maxFormulaCode.substring(1, maxFormulaCode.indexOf("-"));
                    if(num.matches("\\d+")){
                        index = Integer.valueOf(num);
                    }
                }
                String formulaCode = "P"+(index+1)+"-"+dataInfo.getFormulaCode();
                String laboratoryCode = "P"+(index+1)+"-"+dataInfo.getLaboratoryCode();
                dataInfo.setFormulaCode(formulaCode);
                dataInfo.setLaboratoryCode(laboratoryCode);
                dataInfo.setPrice(null);
                dataInfo.setGongyijianshu(null);
                dataInfo.setFtlTime(null);
                dataInfo.setBaTime(null);
                dataInfo.setBaCode(null);
                dataInfo.setWaxcStatus(null);
                dataInfo.setFilCode(null);
                dataInfo.setFilCodeNote(null);
                dataInfo.setBaCodeNote(null);
                dataInfo.setStatus("0");
                dataInfo.setBaStatus(null);
                dataInfo.setWaxcName(null);
                dataInfo.setWaxcOthername(null);
                dataInfo.setFormulaPid(formulaId);
                dataInfo.setCreatedTime(new Date());
                dataInfo.setLastModifiedTime(new Date());
                dataInfo.setNote(new FormulaDataUtil().getNumberStr(totalPercent));
                softwareDevelopingFormulaMapper.insertSoftwareDevelopingFormula(dataInfo);
                Map<String,Object> formulaParams = new HashMap<String,Object>();
                formulaParams.put("formulaId",dataInfo.getId());
                formulaParams.put("deptId",dataInfo.getDeptId());
                formulaParams.put("shareDeptId",null);
                formulaParams.put("type",1);
                formulaParams.put("userName",SecurityUtils.getUsername());
                softwareDevelopingFormulaMapper.saveFormulaDeptDataInfo(formulaParams);
                Long fId = dataInfo.getId();
                BigDecimal totalNewPercentage = WagesUtils.bigDecimal("0");
                BigDecimal formulaPrice = WagesUtils.bigDecimal("0");
                List<SoftwareMaterialFormula> dataList = new ArrayList<SoftwareMaterialFormula>();
                for(int i = 0;i<array.size();i++){
                    JSONObject obj = array.getJSONObject(i);
                    SoftwareMaterialFormula softwareMaterialFormula = new SoftwareMaterialFormula();
                    softwareMaterialFormula = JSONObject.parseObject(obj.toJSONString(),SoftwareMaterialFormula.class);
                    softwareMaterialFormula.setFormulaId(fId);
                    if(i<array.size()-1){
                        BigDecimal percentage = WagesUtils.bigDecimal(softwareMaterialFormula.getPercentage(),10);
                        BigDecimal rate = percentage.divide(totalPercent,4, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_HALF_UP);
                        BigDecimal newPercentage = rate.multiply(new BigDecimal("100"));
                        totalNewPercentage = totalNewPercentage.add(newPercentage);
                         softwareMaterialFormula.setPercentage(newPercentage.toPlainString());
                    }else{
                        BigDecimal syPercentage = new BigDecimal("100").subtract(totalNewPercentage);
                        softwareMaterialFormula.setPercentage(syPercentage.toPlainString());
                    }
                    BigDecimal price = softwareMaterialFormula.getPrice();
                    if(StringUtils.isNull(price)){
                        isPrice = false;
                    }
                    price = StringUtils.convertDefaultValue(price,WagesUtils.bigDecimal("0"));
                    BigDecimal totalPrice =  WagesUtils.bigDecimal(softwareMaterialFormula.getPercentage(),10).divide(new BigDecimal("100")).multiply(price);
                    formulaPrice = formulaPrice.add(totalPrice);
                    if(StringUtils.isNotNull(formulaPrice) && formulaPrice.doubleValue()>0){
                        softwareMaterialFormula.setPrice(totalPrice);
                    }
                    softwareMaterialFormula.setOperator(SecurityUtils.getUsername());
                    softwareMaterialFormula.setCreatedTime(DateUtils.getNowDate());
                    dataList.add(softwareMaterialFormula);
                }

                if(dataList!=null && dataList.size()>0){
                    SoftwareDevelopingFormula params = new SoftwareDevelopingFormula();
                    params.setId(fId);
                    params.setPrice(formulaPrice);
                    if(isPrice){
                        params.setIsCalc(1);
                    }else{
                        params.setIsCalc(0);
                    }
                    softwareDevelopingFormulaMapper.updateSoftwareDevelopingFormula(params);
                    Integer num = softwareMaterialFormulaMapper.batchInsertFormulaMaterialData(dataList);
                    if(num>0){
                        processPBnoteFun(formulaId);
                    }
                }
            }else{
                result.put("status",1);
                result.put("msg","至多允许生成三个P配方");
            }
        }else{
            result.put("status",1);
            result.put("msg","请选择需要的原料");
        }
        return result;
    }

    /**
     * 生成B代码
     * @param softwareDevelopingFormula
     * @return
     */
    @Override
    public Map<String, Object> generateBMaterialInfo(SoftwareDevelopingFormula softwareDevelopingFormula) {
        Long formulaId = softwareDevelopingFormula.getId();
        Map<String, Object> returnMapData = new HashMap<String,Object>();
        returnMapData.put("status",0);
        String materialCode1 = softwareDevelopingFormulaMapper.queryMaterialCountInfo(formulaId);
        if(StringUtils.isNotNull(materialCode1)){
            returnMapData.put("status", 1);
            returnMapData.put("msg", "该配方已生成了B代码,代码为:["+materialCode1+"]");
        }else{
            SoftwareDevelopingFormula dataInfo = softwareDevelopingFormulaMapper.selectSoftwareDevelopingFormulaById(formulaId);
            List<Map<String,Object>> mapsList = softwareMaterialFormulaMapper.queryFormulaMaterialDataList(formulaId);
            List<BigDecimal> orderingcycleDec = new ArrayList<BigDecimal>();
            List<Map<String,Object>> materialMapData = new ArrayList<Map<String,Object>>();
            for(Map<String,Object> map : mapsList){
                Object inciName = map.get("inciName");
                Object inciNameEng = map.get("inciNameEng");
                Object casNo = map.get("casNo");
                Object percentage = map.get("percentage");
                Object proportion = map.get("proportion");
                Object useLimitDetail = map.get("useLimitDetail");
                Object symd = map.get("symd");
                Object lxlNumber = map.get("lxlNumber");
                Object zlxNumber = map.get("zlxNumber");
                Object typeical = map.get("typeical");
                Object orderingcycle = map.get("orderingcycle");
                Object xh = map.get("xh");
                if(StringUtils.isNotNull(orderingcycle) && ErpDataReqUtil.isInteger(String.valueOf(orderingcycle))){
                    orderingcycleDec.add(WagesUtils.bigDecimal(orderingcycle));
                }
                if(StringUtils.isNotNull(inciName)){
                    String [] inciNameArr = String.valueOf(inciName).split("~");
                    int length = inciNameArr.length;
                    String [] inciNameEngArr = ComponentUtil.getArr(inciNameEng,length);
                    String [] casNoArr = ComponentUtil.getArr(casNo,length);
                    String [] proportionArr = ComponentUtil.getArr(proportion,length);
                    String [] useLimitDetailArr = ComponentUtil.getArr(useLimitDetail,length);
                    String [] symdArr = ComponentUtil.getArr(symd,length);
                    String [] lxlNumberArr = ComponentUtil.getArr(lxlNumber,length);
                    String [] zlxNumberArr = ComponentUtil.getArr(zlxNumber,length);
                    String [] typeicalArr = ComponentUtil.getArr(typeical,length);
                    String [] xhArr = ComponentUtil.getArr(xh,length);
                    for(int i = 0;i<inciNameArr.length;i++){
                        Map<String,Object> mapInfo = new HashMap<String,Object>();
                        mapInfo.put("inciName", ComponentUtil.getArrValue(inciNameArr,i,length));
                        mapInfo.put("inciNameEng", ComponentUtil.getArrValue(inciNameEngArr,i,inciNameEngArr.length));
                        mapInfo.put("casNo", ComponentUtil.getArrValue(casNoArr,i,casNoArr.length));
                        Object pro = ComponentUtil.getArrValue(proportionArr,i,proportionArr.length);
                        String proVal = ComponentUtil.processProportion(pro);
                        String p = "/";
                        if(!StringUtil.isEmpty(proVal) && !"/".equals(proVal) && !"\\".equals(proVal)){
                            if(StringUtils.isNotNull(percentage) && !"/".equals(percentage) && !"\\".equals(percentage)){
                                BigDecimal b1 = new BigDecimal(String.valueOf(proVal));
                                BigDecimal b2 = new BigDecimal(String.valueOf(percentage));
                                BigDecimal totalValues = b1.multiply(b2).divide(new BigDecimal(100));
                                p = new FormulaDataUtil().getNumberStr(totalValues);
                            }
                        }
                        mapInfo.put("proportion", p);
                        mapInfo.put("useLimitDetail", ComponentUtil.getArrValue(useLimitDetailArr,i,useLimitDetailArr.length));
                        mapInfo.put("symd", ComponentUtil.getArrValue(symdArr,i,symdArr.length));
                        mapInfo.put("lxlNumber", ComponentUtil.getArrValue(lxlNumberArr,i,lxlNumberArr.length));
                        mapInfo.put("zlxNumber", ComponentUtil.getArrValue(zlxNumberArr,i,zlxNumberArr.length));
                        mapInfo.put("typeical", ComponentUtil.getArrValue(typeicalArr,i,typeicalArr.length));
                        mapInfo.put("xh", ComponentUtil.getArrValue(xhArr,i,xhArr.length));
                        if(ComponentUtil.isExists(ComponentUtil.getArrValue(inciNameArr,i,length),materialMapData)){
                            materialMapData.add(mapInfo);
                        }
                    }
                }
            }

            if(orderingcycleDec.size()>0){
                Collections.sort(orderingcycleDec);
            }
            SoftwareMaterial softwareMaterial = new SoftwareMaterial();
            //获取所在一级部门
            //Long deptId = sysUserMapper.queryUserParentDeptIdInfo(SecurityUtils.getUserId());
            Long deptId = sysHrUserMapper.queryUserFormulaRoleDeptId(SecurityUtils.getUserId());
            deptId = StringUtils.convertDefaultValue(deptId,-1L);
            String materialCode = "";
            if("7".equals(String.valueOf(deptId))){  //宜侬
                materialCode = softwareMaterialMapper.queryNewErpCodeInfo2("B");
            }else if("8".equals(String.valueOf(deptId))){  //瀛彩
                materialCode = softwareMaterialMapper.queryNewErpCodeInfo1("B");
            }else{
                materialCode = softwareMaterialMapper.queryNewErpCodeInfo("B");
            }
            if(StringUtils.isNotNull(materialCode)){
                materialCode = StringUtils.generatorCode("B", materialCode);
            }
            softwareMaterial.setMaterialCode(materialCode);
            String code2 = materialCode.substring(1, materialCode.length());
            softwareMaterial.setErpCode(9+code2);
            StringBuffer inciNameBuff = new StringBuffer();
            StringBuffer inciNameEngBuff = new StringBuffer();
            StringBuffer casNoBuff = new StringBuffer();
            StringBuffer proportionBuff = new StringBuffer();
            StringBuffer useLimitDetailBuff = new StringBuffer();
            StringBuffer symdBuff = new StringBuffer();
            StringBuffer lxlNumberBuff = new StringBuffer();
            StringBuffer zlxNumberBuff = new StringBuffer();
            StringBuffer typeicalBuff = new StringBuffer();
            StringBuffer xhBuff = new StringBuffer();
            for(Map<String,Object> map : materialMapData){
                Object inciName = map.get("inciName");
                Object inciNameEng = map.get("inciNameEng");
                Object casNo = map.get("casNo");
                Object proportion = map.get("proportion");
                Object useLimitDetail = map.get("useLimitDetail");
                Object symd = map.get("symd");
                Object lxlNumber = map.get("lxlNumber");
                Object zlxNumber = map.get("zlxNumber");
                Object typeical = map.get("typeical");
                Object xh = map.get("xh");
                inciNameBuff.append("~").append(StringUtils.convertDefaultValue(inciName, "/"));
                inciNameEngBuff.append("~").append(StringUtils.convertDefaultValue(inciNameEng, "/"));
                casNoBuff.append("~").append(StringUtils.convertDefaultValue(casNo, "/"));
                proportionBuff.append("~").append(StringUtils.convertDefaultValue(proportion, "/"));
                useLimitDetailBuff.append("~").append(StringUtils.convertDefaultValue(useLimitDetail, "/"));
                symdBuff.append("~").append(StringUtils.convertDefaultValue(symd, "/"));
                lxlNumberBuff.append("~").append(StringUtils.convertDefaultValue(lxlNumber, "/"));
                zlxNumberBuff.append("~").append(StringUtils.convertDefaultValue(zlxNumber, "/"));
                typeicalBuff.append("~").append(StringUtils.convertDefaultValue(typeical, "/"));
                xhBuff.append("~").append(StringUtils.convertDefaultValue(xh, "/"));
            }
            softwareMaterial.setMaterialGoodsName(dataInfo.getFormulaCode());
            softwareMaterial.setInciName(inciNameBuff.toString().substring(1));
            softwareMaterial.setInciNameEng(inciNameEngBuff.toString().substring(1));
            softwareMaterial.setProportion(proportionBuff.toString().substring(1));
            softwareMaterial.setCasNo(casNoBuff.toString().substring(1));
            softwareMaterial.setUseLimitDetail(useLimitDetailBuff.toString().substring(1));
            softwareMaterial.setSymd(symdBuff.toString().substring(1));
            softwareMaterial.setLxlNumber(lxlNumberBuff.toString().substring(1));
            softwareMaterial.setZlxNumber(zlxNumberBuff.toString().substring(1));
            softwareMaterial.setXh(xhBuff.toString().substring(1));
            softwareMaterial.setFormulaPid(formulaId);
            softwareMaterial.setCreatedTime(new Date());
            softwareMaterial.setMaterialAgent("上海宜侬化妆品科技中心");
            softwareMaterial.setMaterialSupplier("上海宜侬化妆品科技中心");
            softwareMaterial.setLastModifiedTime(new Date());
            softwareMaterial.setType(2);

            if(orderingcycleDec.size()>0){
                softwareMaterial.setOrderingcycle(orderingcycleDec.get(orderingcycleDec.size()-1));
            }

            if(StringUtils.isNotNull(dataInfo.getPrice())){
                softwareMaterial.setPrice(dataInfo.getPrice());
            }
            softwareMaterial.setOperator(SecurityUtils.getUsername());
            softwareMaterial.setCreatedTime(DateUtils.getNowDate());
            int result = softwareMaterialMapper.insertSoftwareMaterial(softwareMaterial);
            //保存供应商和价格信息
            SoftwareMaterialProducer softwareMaterialProducer = new SoftwareMaterialProducer();
            softwareMaterialProducer.setMaterialId(softwareMaterial.getId());
            softwareMaterialProducer.setMaterialSubCode(softwareMaterial.getMaterialCode()+"-1");
            softwareMaterialProducer.setMaterialGoodsName(softwareMaterial.getMaterialGoodsName());
            softwareMaterialProducer.setUsePriority("1");
            softwareMaterialProducer.setMaterialSupplier("上海宜侬化妆品科技中心");
            softwareMaterialProducerMapper.insertSoftwareMaterialProducer(softwareMaterialProducer);
            SoftwareMaterialProducerAgent softwareMaterialProducerAgent = new SoftwareMaterialProducerAgent();
            softwareMaterialProducerAgent.setMaterialId(softwareMaterial.getId());
            softwareMaterialProducerAgent.setMaterialSupplierId(softwareMaterialProducer.getId());
            softwareMaterialProducerAgent.setKey(UUID.randomUUID().toString());
            softwareMaterialProducerAgent.setMaterialAgentId(174L);
            if(orderingcycleDec.size()>0){
                softwareMaterialProducerAgent.setOrderingcycle(orderingcycleDec.get(orderingcycleDec.size()-1));
            }
            softwareMaterialProducerAgent.setMaterialAgent("上海宜侬化妆品科技中心");
            softwareMaterialProducerAgent.setPrice(softwareMaterial.getPrice());
            softwareMaterialProducerAgent.setCreateBy(SecurityUtils.getUsername());
            softwareMaterialProducerAgent.setCreateTime(DateUtils.getNowDate());
            softwareMaterialProducerAgentMapper.insertSoftwareMaterialProducerAgent(softwareMaterialProducerAgent);
            if(result>0){
                processPBnoteFun(dataInfo.getFormulaPid());
            }else{
                returnMapData.put("status", 1);
                returnMapData.put("msg", "操作失败");
            }
        }
        return returnMapData;
    }


    /**
     * 生成B代码配方
     * @param softwareDevelopingFormula
     * @return
     */
    @Override
    public Map<String, Object> genNewformulaInfo(SoftwareDevelopingFormula softwareDevelopingFormula) {
        Long formulaId = softwareDevelopingFormula.getId();
        Map<String, Object> returnMapData = new HashMap<String,Object>();
        returnMapData.put("status",0);
        SoftwareDevelopingFormula dataInfo = softwareDevelopingFormulaMapper.selectSoftwareDevelopingFormulaById(formulaId);
        List<SoftwareMaterialFormula> formulaMaterilaDataList = softwareMaterialFormulaMapper.queryFormulaMaterialObjDataList(formulaId);
        //获取B代码
        List<SoftwareMaterial> materialDataList = softwareMaterialMapper.queryBMaterialInfo(formulaId);
        //获取已生成配方的代码
        List<Long> materialIdList = softwareDevelopingFormulaMapper.queryPFormulaIdDataInfo(formulaId);
        {
            List<SoftwareMaterialFormula> materialFormulaDataList1 = new ArrayList<SoftwareMaterialFormula>();
            if (formulaMaterilaDataList != null && formulaMaterilaDataList.size() > 0) {
                for (SoftwareMaterialFormula mapData : formulaMaterilaDataList) {
                    if (!materialIdList.contains(mapData.getMaterialId())) {
                        materialFormulaDataList1.add(mapData);
                    }
                }
            }
            for (SoftwareMaterial materialInfo : materialDataList) {
                SoftwareMaterialFormula mf = new SoftwareMaterialFormula();
                if (materialInfo != null && StringUtils.isNotNull(materialInfo.getId())) {
                    mf.setMaterialId(materialInfo.getId());
                    materialFormulaDataList1.add(mf);
                }
            }
            if (materialFormulaDataList1 != null && materialFormulaDataList1.size() > 0) {
                List<String> newIdList = new ArrayList<String>();
                for (SoftwareMaterialFormula materialFormulaInfo : materialFormulaDataList1) {
                    if (materialFormulaInfo != null && StringUtils.isNotNull(materialFormulaInfo.getMaterialId())) {
                        newIdList.add(materialFormulaInfo.getMaterialId() + "");
                    }
                }

                //判断已生成配方
                List<Map<String, Object>> mapsData = softwareDevelopingFormulaMapper.queryFormulaMaterialNewInfo(formulaId);
                for (Map<String, Object> maps : mapsData) {
                    Object formulaCode = maps.get("formulaCode");
                    if (StringUtils.isNotNull(formulaCode) && !String.valueOf(formulaCode).startsWith("P")) {
                        List<Map<String, Object>> materialMaps = (List<Map<String, Object>>) maps.get("materialDatas");
                        List<String> oldIdList = new ArrayList<String>();
                        for (Map<String, Object> materialMap : materialMaps) {
                            Object materialId = materialMap.get("materialId");
                            if (StringUtils.isNotNull(materialId)) {
                                oldIdList.add(String.valueOf(materialId));
                            }
                        }
                        newIdList.sort(Comparator.comparing(String::hashCode));
                        oldIdList.sort(Comparator.comparing(String::hashCode));
                        if (newIdList.toString().equals(oldIdList.toString())) {
                            returnMapData.put("status", 1);
                            returnMapData.put("msg", "已生成过相同配方,配方编码为[" + maps.get("formulaCode") + "]");
                            return returnMapData;
                        }
                    }
                }
            }
        }

        String res = "abc";
        if(!StringUtil.isEmpty(res)){
            //获取今年配方数
            Integer formulaCount = softwareDevelopingFormulaMapper.queryFormulaCount();
            formulaCount = StringUtils.convertDefaultValue(formulaCount, 0);
            formulaCount = formulaCount + 1;
            String code = DateUtils.dateTime(DateUtils.getNowDate(),DateUtils.YY) + StringUtils.addLeftToEqualLength(String.valueOf(formulaCount),8);
            dataInfo.setFormulaCode(code);
            dataInfo.setPrice(null);
            dataInfo.setGongyijianshu(null);
            dataInfo.setFtlTime(null);
            dataInfo.setBaTime(null);
            dataInfo.setBaCode(null);
            dataInfo.setWaxcStatus(null);
            dataInfo.setFilCode(null);
            dataInfo.setFilCodeNote(null);
            dataInfo.setBaCodeNote(null);
            dataInfo.setProductName(dataInfo.getProductName()+"（备案专用）");
            dataInfo.setStatus("0");
            dataInfo.setBaStatus(null);
            dataInfo.setWaxcName(null);
            dataInfo.setWaxcOthername(null);
            dataInfo.setFormulaPid(formulaId);
            dataInfo.setCreatedTime(new Date());
            dataInfo.setOperator(SecurityUtils.getUsername());
            dataInfo.setCreatedTime(DateUtils.getNowDate());
            dataInfo.setLastModifiedTime(new Date());
            softwareDevelopingFormulaMapper.insertSoftwareDevelopingFormula(dataInfo);
            Map<String,Object> formulaParams = new HashMap<String,Object>();
            formulaParams.put("formulaId",dataInfo.getId());
            formulaParams.put("deptId",dataInfo.getDeptId());
            formulaParams.put("shareDeptId",null);
            formulaParams.put("type",1);
            formulaParams.put("userName",SecurityUtils.getUsername());
            softwareDevelopingFormulaMapper.saveFormulaDeptDataInfo(formulaParams);
        }else{
            returnMapData.put("status", 0);
            returnMapData.put("msg", "编码错误,请重试!");
            return returnMapData;
        }
        List<SoftwareMaterialFormula> materialFormulaDataList = new ArrayList<SoftwareMaterialFormula>();
        BigDecimal formulaPrice = WagesUtils.bigDecimal("0");
        if(formulaMaterilaDataList!=null && formulaMaterilaDataList.size()>0){
            for(SoftwareMaterialFormula mapData : formulaMaterilaDataList){
                 if(!materialIdList.contains(mapData.getMaterialId())){
                    mapData.setFormulaId(dataInfo.getId());
                    mapData.setType(0);
                    materialFormulaDataList.add(mapData);
                    formulaPrice = formulaPrice.add(mapData.getPrice());
                }
            }
        }
        for(SoftwareMaterial materialInfo : materialDataList){
            SoftwareMaterialFormula mf = new SoftwareMaterialFormula();
            if(materialInfo!=null && StringUtils.isNotNull(materialInfo.getId())){
                mf.setMaterialId(materialInfo.getId());
                if(StringUtils.isNotNull(materialInfo.getNote()) && materialInfo.getNote().matches("([1-9]\\d*\\.?\\d*)|(0\\.\\d*[1-9])")){
                    mf.setPercentage(materialInfo.getNote());
                    try {
                        BigDecimal materialPrice  = materialInfo.getPrice();
                        materialPrice = StringUtils.convertDefaultValue(materialPrice, WagesUtils.bigDecimal("1"));
                        BigDecimal totalPrice =  WagesUtils.bigDecimal(mf.getPercentage(),10).divide(new BigDecimal("100")).multiply(materialPrice);
                        formulaPrice = formulaPrice.add(totalPrice);
                        if(totalPrice!=null && totalPrice.doubleValue()>0){
                            mf.setPrice(totalPrice);
                        }
                    } catch (Exception e) {
                        System.out.println("格式转换出错!"+materialInfo.getMaterialCode()+"---"+materialInfo.getId()+"====");
                    }
                }
                mf.setFormulaId(dataInfo.getId());
                String inicSymd = materialInfo.getInicSymd();
                String symdInfo = DictUtils.getDictLabel("SOFTWARE_MATERIAL_SYMD",inicSymd,",");
                mf.setSymdInfo(symdInfo);
                mf.setOperator(SecurityUtils.getUsername());
                mf.setType(0);
                materialFormulaDataList.add(mf);
            }
        }
        if(materialFormulaDataList!=null && materialFormulaDataList.size()>0){
            SoftwareDevelopingFormula updateFormulaInfo = new SoftwareDevelopingFormula();
            updateFormulaInfo.setId(dataInfo.getId());
            updateFormulaInfo.setPrice(formulaPrice);
            softwareDevelopingFormulaMapper.updateSoftwareDevelopingFormula(updateFormulaInfo);
            int num = softwareMaterialFormulaMapper.batchInsertFormulaMaterialData(materialFormulaDataList);
            if(num>0){
                returnMapData.put("status", 0);
                returnMapData.put("msg", "操作成功");
            }else{
                returnMapData.put("status", 1);
                returnMapData.put("msg", "操作失败");
            }
        }else{
            returnMapData.put("status", 1);
            returnMapData.put("msg", "请添加原料");
        }
        return returnMapData;
    }

    @Override
    public List<JSONObject> querySoftwareFormulaReleaseDataList(SoftwareDevelopingFormula softwareDevelopingFormula) {
        if((StringUtils.isNotNull(softwareDevelopingFormula.getProjectNo()) || "2".equals(softwareDevelopingFormula.getRequestType()))
        && StringUtils.isNotNull(softwareDevelopingFormula.getLabNos())){
            softwareDevelopingFormula.setLabArr(JSONArray.parseArray(softwareDevelopingFormula.getLabNos()));
            if("2".equals(softwareDevelopingFormula.getRequestType())){
                softwareDevelopingFormula.setProjectNo(null);
            }
            List<JSONObject> dataList = softwareDevelopingFormulaMapper.querySoftwareFormulaReleaseDataList(softwareDevelopingFormula);
            return dataList;
        }else{
            return new ArrayList<JSONObject>();
        }
    }

    @Override
    public List<JSONObject> querySoftwareFormulaReleaseByIdDataList(SoftwareDevelopingFormula softwareDevelopingFormula) {
        if(StringUtils.isNotNull(softwareDevelopingFormula.getId())){
            List<Long> formulaIdList = softwareFormulaReleaseMapper.querySoftwareFormulaReleaseDataList(softwareDevelopingFormula.getId());
            if(formulaIdList!=null && formulaIdList.size()>0){
                List<JSONObject> dataList = softwareDevelopingFormulaMapper.querySoftwareFormulaReleaseByIdDataList(formulaIdList);
                return dataList;
            }else{
                return new ArrayList<JSONObject>();
            }
        }else{
            return new ArrayList<JSONObject>();
        }
     }

    @Override
    public List<JSONObject> queryMaterialFormulaSpecDataList(Long formulaId) {
        List<JSONObject> list = softwareMaterialFormulaMapper.queryMaterialFormulaSpecDataList(formulaId);
        if(list!=null && list.size()>0){
            for(JSONObject dataInfo : list){
                String jcxmJson = dataInfo.getString("jcxmJson");
                if(StringUtils.isNotNull(jcxmJson)){
                    JSONArray array = JSONArray.parseArray(jcxmJson);
                    if(array!=null &&array.size()>0){
                        for(int i = 0;i<array.size();i++){
                            JSONObject obj = array.getJSONObject(i);
                            String id = obj.getString("id");
                            String standard = obj.getString("standardVal");
                            if("1".equals(id)){
                                dataInfo.put("waiguan",standard);
                            }
                            if("2".equals(id)){
                                dataInfo.put("qiwei",standard);
                            }
                            if("7".equals(id)){
                                dataInfo.put("yanse",standard);
                            }
                            if("8".equals(id)){
                                dataInfo.put("ph",standard);
                            }
                            if("76".equals(id)){
                                dataInfo.put("naire",standard);
                            }
                            if("77".equals(id)){
                                dataInfo.put("naihan",standard);
                            }
                        }
                    }

                }
                dataInfo.put("jcxmJson","");
            }
        }
        return list;
    }



    @Override
    public JSONObject queryFormulaStabilityRecordDataList(Long formulaId) {
        JSONObject returnObj = new JSONObject();
        List<Stability> stabilityDataList = stabilityMapper.queryFormulaStabilityDataList(formulaId);
        List<Stability> relationStabilityDataList = stabilityMapper.queryFormulaRelationStabilityDataList(formulaId);
        returnObj.put("stabilityDataList",stabilityDataList);
        returnObj.put("relationStabilityDataList",relationStabilityDataList);
        return returnObj;
    }

    @Override
    public JSONObject queryMaterialFormulaSpecDataDetail(Long id) {
        JSONObject obj = softwareMaterialFormulaMapper.queryMaterialFormulaSpecDataDetail(id);
        return obj;
    }

    @Override
    public JSONObject queryFormulaLegalGy(SoftwareDevelopingFormula softwareDevelopingFormula) {
        JSONObject returnObj = new JSONObject();
       List<JSONObject> dataList = softwareMaterialFormulaMapper.queryFormulaSubItemDataList(softwareDevelopingFormula.getId());
       Set<String> itemSet = new HashSet<String>();
       if(dataList!=null && dataList.size()>0){
           for(JSONObject obj : dataList){
               String subItem = obj.getString("subItem");
               if(StringUtils.isNotNull(subItem)){
                   String item = subItem.substring(0, 1);
                   itemSet.add(item);
               }
           }
       }
       List<String> gyjs = new ArrayList<>();
       List<String> zfyl = new ArrayList<>();
       TreeSet<String> set = new TreeSet<String>(((o1,o2)->o1.compareTo(o2)));
       set.addAll(itemSet);
       int index = 1;
       List<String> SUB_ITEM = new FormulaConstantsUtil().SUB_ITEM;
       for(String item : set){
           if(SUB_ITEM.contains(item)){
               gyjs.add(index + "、将"+item+"相");
               String str  = item+"相原料:";
               Set<String> subCode = new HashSet<String>();
               for(JSONObject obj : dataList){
                   String subItem = obj.getString("subItem");
                   if(StringUtils.isNotNull(subItem) && subItem.startsWith(item)){
                       subCode.add(subItem);
                   }
               }
               if(subCode.size()>0){
                   TreeSet<String> subSet = new TreeSet<String>(((o1,o2)->{
                       String reg = "[^\\d]";
                       String t1 = o1;
                       t1 = StringUtils.convertDefaultValue(t1,"/");
                       String t2 = o2;
                       t2 = StringUtils.convertDefaultValue(t2,"/");
                       try {
                           int first = Integer.parseInt(StringUtils.convertDefaultValue(t1.replaceAll(reg, ""), "-1"))+t1.getBytes()[0]*100;
                           int second = Integer.parseInt(StringUtils.convertDefaultValue(t2.replaceAll(reg, ""), "-1"))+t2.getBytes()[0]*100;
                           if (first > second) {
                               return 1;
                           } else if (first < second) {
                               return -1;
                           } else {
                               return 0;
                           }
                       } catch (NumberFormatException e) {
                           System.out.println(e.getMessage());
                           return 0;
                       }catch (Exception e) {
                           System.out.println(e.getMessage());
                           return 0;
                       }

                   }));
                   subSet.addAll(subCode);
                   str = str + StringUtils.join(subSet,",");
                   zfyl.add(str);
               }
               index++;
           }
       }
       if(index>1){
           gyjs.add((index++)+"、出料，取样检测、料体转移");
           gyjs.add((index++)+"、灌装");
           gyjs.add((index++)+"、包装，成品检验，入库");
       }
        returnObj.put("gyjs",gyjs);
       returnObj.put("zfyl",zfyl);
       return returnObj;
    }

    @Override
    public void addFormulaShareDataInfo(SoftwareDevelopingFormula softwareDevelopingFormula) {
       String shareDeptIds =  softwareDevelopingFormula.getShareDeptIds();
       List<Long> deptIds = StringUtils.str2LongList(shareDeptIds,",",true,true);
       if(deptIds!=null && deptIds.size()>0){
           softwareDevelopingFormulaMapper.deleteFormulaDeptDataInfo(softwareDevelopingFormula.getId());
           for(Long deptId : deptIds){
               Map<String,Object> params = new HashMap<String,Object>();
               params.put("deptId",deptId);
               params.put("shareDeptId",softwareDevelopingFormula.getDeptId());
               params.put("formulaId",softwareDevelopingFormula.getId());
               params.put("userName",SecurityUtils.getUsername());
               params.put("type",0);
               softwareDevelopingFormulaMapper.saveFormulaDeptDataInfo(params);
           }
       }
    }

    private void processPBnoteFun(Long formulaId) {
        new Thread(){
            public void run(){
                try {
                    sleep(3000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                //获取P配方
                List<String> pFormulaInfo = softwareDevelopingFormulaMapper.queryPFormulaDataList(formulaId);
                //获取B代码
                List<String> bMaterialInfo = softwareMaterialMapper.queryBMaterialDataList(formulaId);
                StringBuilder sb = new StringBuilder();
                if(pFormulaInfo!=null && pFormulaInfo.size()>0){
                    sb.append("含有P配方:").append(Arrays.toString(pFormulaInfo.toArray()));
                    sb.append("     ");
                }
                if(bMaterialInfo!=null && bMaterialInfo.size()>0){
                    sb.append("含有B代码:").append(Arrays.toString(bMaterialInfo.toArray()));
                }
                SoftwareDevelopingFormula formualInfo = new SoftwareDevelopingFormula();
                formualInfo.setBpNote(sb.toString());
                formualInfo.setId(formulaId);
                softwareDevelopingFormulaMapper.updateBpNoteInfo(formualInfo);
            }
        }.start();
    }

    /**
     * 保存修改记录数据
     * @param materialFormulaDataList  原始数据
     * @param materialFormulaList  新建数据
     */
    private void proccessModifiedLog(final List<SoftwareMaterialFormula> materialFormulaDataList,
                                     final List<SoftwareMaterialFormula> materialFormulaList,
                                     String userName) {
        new Thread(){
            @Override
            public void run() {
                List<Map<String,Object>> mapData = new ArrayList<Map<String,Object>>();
                //新增/修改 数据
                for(SoftwareMaterialFormula newDataInfo : materialFormulaList){
                    Map<String,Object> materialFormulaInfo = isExists(newDataInfo,materialFormulaDataList);
                    Map<String,Object> saveMap = new HashMap<String,Object>();
                    if(materialFormulaInfo.containsKey("status")){
                        Integer status = Integer.valueOf(materialFormulaInfo.get("status")+"");
                        saveMap.put("formulaId", newDataInfo.getFormulaId());
                        saveMap.put("materialId", newDataInfo.getMaterialId());
                        if(status==1){  //修改
                            SoftwareMaterialFormula oldMaterialFormulaInfo = (SoftwareMaterialFormula) materialFormulaInfo.get("newData");
                            saveMap.put("remark", "调整比例");
                            saveMap.put("percentageNew", newDataInfo.getPercentage());
                            saveMap.put("percentageOld", oldMaterialFormulaInfo.getPercentage());
                            saveMap.put("operator", userName);
                            mapData.add(saveMap);
                        }else if(status==2){ //新增
                            saveMap.put("percentageNew", newDataInfo.getPercentage());
                            saveMap.put("percentageOld", 0);
                            saveMap.put("operator", userName);
                            saveMap.put("remark", "新增原料");
                            mapData.add(saveMap);
                        }
                    }
                }
                //删除数据
                for(SoftwareMaterialFormula oldMaterialFormulaInfo:materialFormulaDataList){
                    boolean isDelete = isDelete(oldMaterialFormulaInfo,materialFormulaList);
                    Map<String,Object> saveMap = new HashMap<String,Object>();
                    if(isDelete){
                        saveMap.put("formulaId", oldMaterialFormulaInfo.getFormulaId());
                        saveMap.put("materialId", oldMaterialFormulaInfo.getMaterialId());
                        saveMap.put("remark", "删除原料");
                        saveMap.put("percentageNew", 0);
                        saveMap.put("percentageOld", oldMaterialFormulaInfo.getPercentage());
                        saveMap.put("operator", userName);
                        mapData.add(saveMap);
                    }
                }

                if(mapData!=null && mapData.size()>0){
                    softwareDevelopingFormulaMapper.batchMaterialFormulaRecordList(mapData);
                }

            }

            //是否删除
            private boolean isDelete(SoftwareMaterialFormula oldMaterialFormulaInfo,
                                     List<SoftwareMaterialFormula> materialFormulaList) {
                boolean isDelete = true;
                for(SoftwareMaterialFormula mf: materialFormulaList){
                    if(oldMaterialFormulaInfo.getMaterialId().longValue()==mf.getMaterialId().longValue()){
                        isDelete = false;
                    }
                }
                return isDelete;
            }

            //是否新增/更新
            private Map<String,Object> isExists(SoftwareMaterialFormula newDataInfo, List<SoftwareMaterialFormula> materialFormulaDataList) {
                Map<String,Object> map = new HashMap<String,Object>();
                boolean isExists = false;
                for(SoftwareMaterialFormula materialFormulaInfo : materialFormulaDataList){
                    if(materialFormulaInfo.getMaterialId().longValue()==newDataInfo.getMaterialId().longValue()){
                        if(StringUtils.isNotNull(materialFormulaInfo.getPercentage()) &&StringUtils.isNotNull(newDataInfo.getPercentage())){
                            if(WagesUtils.bigDecimal(materialFormulaInfo.getPercentage(),10).doubleValue()!=WagesUtils.bigDecimal(newDataInfo.getPercentage(),10).doubleValue()){
                                map.put("status", 1);  //存在修改
                                map.put("newData", materialFormulaInfo);
                            }
                        }
                        isExists = true;
                    }
                }
                if(!isExists){
                    map.put("status", 2);  //新增
                    map.put("newData", newDataInfo);
                }
                return map;
            }
        }.start();
    }

    @Override
    public void rdBomDiff() throws Exception {
        Set<String> diffSet = new HashSet<>();
//        Set<String> erpCodeSet = erpService.selectErpBomChangeDataList(new BigDecimal("3"), "103");
        Set<String> erpCodeSet = formulaMapper.selectFormulaErpList();
        for (String erpCode: erpCodeSet) {
            JSONArray array = erpService.getBomIncludeUnAuditByCode(erpCode);
            Set<String> erpMaterialCodeSet = new HashSet<>();
            for (int i = 0; i < array.size(); i++) {
                JSONObject o = array.getJSONObject(i);
                String materialCode = FilterStr.filterChinese(o.getString("mb002"));
                erpMaterialCodeSet.add(materialCode);
            }
            List<SoftwareMaterialFormula> erpMaterialList = new ArrayList<>();
            for (String erpMaterialCode : erpMaterialCodeSet) {
                SoftwareMaterialFormula erpMaterial = new SoftwareMaterialFormula();
                erpMaterial.setMaterialCode(erpMaterialCode);
                BigDecimal sum = BigDecimal.ZERO;
                for (int i = 0; i < array.size(); i++) {
                    JSONObject o = array.getJSONObject(i);
                    String materialCode = FilterStr.filterChinese(o.getString("mb002"));
                    if(materialCode.equals(erpMaterialCode)) {
                        sum = sum.add(o.getBigDecimal("consumption").multiply(BigDecimal.valueOf(100)));
                    }
                    erpMaterial.setNewPercentage(sum);
                }
                erpMaterialList.add(erpMaterial);
            }

            List<SoftwareDevelopingFormula> formulaList = softwareDevelopingFormulaMapper.selectSoftwareDevelopingFormulaListByErpCode(erpCode);
            if(CollUtil.isNotEmpty(formulaList)) {
                for (SoftwareDevelopingFormula item : formulaList) {
                    Set<String> materialCodeSet = new HashSet<>();
                    List<SoftwareMaterialFormula> rdMaterialList = softwareMaterialFormulaMapper.selectSoftwareMaterialFormulaListByFormulaId(item.getId());
                    for (SoftwareMaterialFormula m : rdMaterialList) {
                        materialCodeSet.add(m.getMaterialCode());
                    }
                    for (SoftwareMaterialFormula m : erpMaterialList) {
                        materialCodeSet.add(m.getMaterialCode());
                    }
                    String bomSame = "0";
                    for (String materialCode : materialCodeSet) {
                        SoftwareMaterialFormula rd = formulaObj(rdMaterialList,materialCode);
                        SoftwareMaterialFormula erp = formulaObj(erpMaterialList,materialCode);
                        if(rd!=null && erp!=null
                        && StringUtils.isNotEmpty(rd.getMaterialCode())
                        && StringUtils.isNotEmpty(erp.getMaterialCode())
                        && rd.getMaterialCode().trim().equals(erp.getMaterialCode().trim())
                        && rd.getNewPercentage()!=null
                        && erp.getNewPercentage()!=null
                        && rd.getNewPercentage().compareTo(erp.getNewPercentage()) == 0
                        ) {
                        } else {
                            bomSame = "1";
                            diffSet.add(materialCode);
                        }
                    }

                    SoftwareDevelopingFormula params = new SoftwareDevelopingFormula();
                    params.setId(item.getId());
                    params.setBomSame(bomSame);
                    softwareDevelopingFormulaMapper.updateSoftwareDevelopingFormula(params);
                }
            }
        }
        if(diffSet.size() > 0) {
            MessageParams msg = new MessageParams();
//            Set<String> wordUserIds = CollUtil.newHashSet("wangfengjuan","chenzhongming","huruonan");
//            msg.setWordUserIds(wordUserIds);
//            msg.setMsgType("textcard");
//            msg.setTitle("对比异常提醒");
//            msg.setDescription(StringUtils.setToStr(diffSet,",") + "bom不一致");
//            msg.setUrl("https://enow.enowmill.com");
//
//            messageService.sendWorkMessage(msg);
        }
    }

    @Override
    public List<Long> querySoftwareFormulaDataListByUpdateTime() {
        return softwareDevelopingFormulaMapper.querySoftwareFormulaDataListByUpdateTime();
    }

    @Override
    public AjaxResult operatorAiData(AiDto aiDto) {
        String code = aiDto.getCode();
        if("queryFormulaDataListDiscardFQ".equals(code)){  //获取数据列表
           System.out.println("进入获取列表操作.....");
           if(StringUtils.isNotNull(aiDto.getStartTime()) && StringUtils.isNotNull(aiDto.getEndTime())){
                  String regex = "^(\\d{4})-(\\d{2})-(\\d{2})$";
                  Pattern pattern = Pattern.compile(regex);
                  Matcher matcher1 = pattern.matcher(aiDto.getStartTime());
                  Matcher matcher2 = pattern.matcher(aiDto.getEndTime());
                  if(matcher1.matches() && matcher2.matches()){
                      aiDto.setStartTime(aiDto.getStartTime()+" 00:00:00");
                      aiDto.setEndTime(aiDto.getEndTime()+" 23:59:59");
                      Date startTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,aiDto.getStartTime());
                      Date endTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,aiDto.getEndTime());
                      BigDecimal applyDays = WagesUtils.bigDecimal(sysConfigService.selectConfigByKey("AI_DAYS"));
                      if(DateUtils.differDays(startTime,endTime) <= applyDays.longValue()){
                          List<JSONObject> dataList = softwareDevelopingFormulaMapper.queryFormulaAiDataList(aiDto);
                          if(dataList!=null && dataList.size()>0){
                              for(JSONObject data : dataList){
                                  String deptId = data.getString("companyName");
                                  String companyName = "";
                                  if("7".equals(deptId)){
                                      companyName = "宜侬";
                                  }else if("8".equals(deptId)){
                                      companyName = "瀛彩";
                                  }else if("32".equals(deptId)){
                                      companyName = "广州办";
                                  }
                                  data.put("companyName",companyName);
                                  String gxxc = data.getString("gxxc");
                                  if(StringUtils.isNotNull(gxxc)){
                                      List<Gxxc> GXXC_DATA_LIST = new FormulaConstantsUtil().GXXC_DATA_LIST;
                                      StringBuffer sb = new StringBuffer();
                                      if(!StringUtils.isNull(gxxc)){
                                          List<String> idList = StringUtils.str2List(gxxc, ",",true,true);
                                          for(int jjj=0;jjj<GXXC_DATA_LIST.size();jjj++){
                                              Gxxc dataInfo = GXXC_DATA_LIST.get(jjj);
                                              if(idList.contains(dataInfo.getId())){
                                                  sb.append(dataInfo.getTitle());
                                                  if(jjj<GXXC_DATA_LIST.size()-1){
                                                      sb.append(",");
                                                  }
                                              }
                                          }
                                      }
                                      gxxc = sb.toString();
                                  }
                                  if(StringUtils.isNotNull(gxxc) && gxxc.endsWith(",")){
                                      gxxc = gxxc.substring(0,gxxc.length()-1);
                                  }
                                  data.put("gxxc",gxxc);
                                  data.put("appearance","");
                                  data.put("color","");
                                  data.put("odor","");
                                  data.put("proportion","");
                                  data.put("ph","");
                                  data.put("viscosity","");
                                  data.put("coldResistant","");
                                  data.put("heatResistant","");
                                  data.put("relativeDensity","");
                                  data.put("breakingStrength","");
                                  data.put("risingMeltingPoint","");
                                  data.put("centrifuge","");
                                  data.put("peroxideValue","");
                                  data.put("hardness","");
                                  data.put("userExperience","");
                                  data.put("foamingPower","");
                                  data.put("moistureContent","");
                                  data.put("rubbingtTst","");
                                  data.put("conductivity","");
                                  data.put("hydrophobicity","");
                                  data.put("particle","");
                                  data.put("acidValue","");
                                  data.put("granularity","");
                                  data.put("refractiveIndex","");
                                  data.put("lossOnDrying","");
                                  data.put("effectiveSubstanceContent","");
                                  data.put("totalEffectiveSubstances","");
                                  data.put("activeMatter","");
                                  data.put("iodineValue","");
                                  data.put("boilingPoint","");
                                  data.put("solubility","");
                                  data.put("freezingPoint","");
                                  data.put("saponificationValue","");
                                  data.put("totalPhosphorusPentoxide","");
                                  data.put("ignitionpoint","");
                                  data.put("decompositionTemperature","");
                                  data.put("flashPoint","");
                                  String jcxmJson = data.getString("jcxmJson");
                                  data.remove("jcxmJson");
                                  if(StringUtils.isNotNull(jcxmJson)){
                                      JSONArray jcxmArray = JSONArray.parseArray(jcxmJson);
                                      if(jcxmArray!=null && jcxmArray.size()>0){
                                          for(int j = 0;j<jcxmArray.size();j++){
                                              JSONObject jcxmObj = jcxmArray.getJSONObject(j);
                                              Integer id = jcxmObj.getInteger("id");
                                              String standardVal = jcxmObj.getString("standardVal");
                                              if(StringUtils.isNotNull(standardVal)){
                                                  if(id==1){  //外观
                                                      data.put("appearance",standardVal);
                                                  }else if(id==2){  //气味
                                                      data.put("odor",standardVal);
                                                  }else if(id==7){  //颜色
                                                      data.put("color",standardVal);
                                                  }else if(id==77){//耐寒
                                                      data.put("coldResistant",standardVal);
                                                  }else if(id==76){//耐热
                                                      data.put("heatResistant",standardVal);
                                                  }else if(id==8){  ///PH
                                                      data.put("ph",standardVal);
                                                  }else if(id==13){  //比重
                                                      data.put("proportion",standardVal);
                                                  }else if(id==107){  //相对密度（20℃/20℃）
                                                      data.put("relativeDensity",standardVal);
                                                  }else if(id==21){  //黏度
                                                      data.put("viscosity",standardVal);
                                                  }else if(id==55){  //断裂强度(g)
                                                      data.put("breakingStrength",standardVal);
                                                  }else if(id==3){  //上升熔点（℃）
                                                      data.put("risingMeltingPoint",standardVal);
                                                  }else if(id==58){  //离心
                                                      data.put("centrifuge",standardVal);
                                                  }else if(id==109){  //过氧化值(mmol/kg)
                                                      data.put("peroxideValue",standardVal);
                                                  }else if(id==52){  //硬度(gf)
                                                      data.put("hardness",standardVal);
                                                  }else if(id==51){  //使用感/肤感
                                                      data.put("userExperience",standardVal);
                                                  }else if(id==79){  //发泡力（泡沫）（40℃/mm）
                                                      data.put("foamingPower",standardVal);
                                                  }else if(id==80){  //水份含量(%)
                                                      data.put("moistureContent",standardVal);
                                                  }else if(id==54){  //涂擦测试
                                                      data.put("rubbingtTst",standardVal);
                                                  }else if(id==88){  //电导率(μS/cm)
                                                      data.put("conductivity",standardVal);
                                                  }else if(id==56){  //疏水性
                                                      data.put("hydrophobicity",standardVal);
                                                  }else if(id==57){  //颗粒度（细度）(0.125mm)%
                                                      data.put("particle",standardVal);
                                                  }else if(id==110){  //酸值(mgKOH/g)
                                                      data.put("acidValue",standardVal);
                                                  }else if(id==44){  //粒度(um)
                                                      data.put("granularity",standardVal);
                                                  }else if(id==20){  //折光指数(20℃)
                                                      data.put("refractiveIndex",standardVal);
                                                  }else if(id==43){  //干燥失重(%)
                                                      data.put("lossOnDrying",standardVal);
                                                  }else if(id==89){  //有效物含量/总固体含量(%)
                                                      data.put("effectiveSubstanceContent",standardVal);
                                                  }else if(id==90){  //总有效物/总活性物(%)
                                                      data.put("totalEffectiveSubstances",standardVal);
                                                  }else if(id==96){  //活性物含量(%)
                                                      data.put("activeMatter",standardVal);
                                                  }else if(id==47){  //碘值(mg/g)
                                                      data.put("iodineValue",standardVal);
                                                  }else if(id==9){  //沸点（℃）
                                                      data.put("boilingPoint",standardVal);
                                                  }else if(id==11){  //溶解性(g)
                                                      data.put("solubility",standardVal);
                                                  }else if(id==22){  //凝固点（℃）
                                                      data.put("freezingPoint",standardVal);
                                                  }else if(id==95){  //皂化值/(mgKOH/g)
                                                      data.put("saponificationValue",standardVal);
                                                  }else if(id==103){  //总五氧化二磷（%）
                                                      data.put("totalPhosphorusPentoxide",standardVal);
                                                  }else if(id==10){  //燃点（℃）
                                                      data.put("ignitionpoint",standardVal);
                                                  }else if(id==6){  //分解温度（℃）
                                                      data.put("decompositionTemperature",standardVal);
                                                  }else if(id==4){  //闪点（℃）
                                                      data.put("flashPoint",standardVal);
                                                  }
                                                  String labNo = data.getString("labNo");
                                                  String xiangrongxingResult = data.getString("xiangrongxingResult");
                                                  String xrxResult = "";
                                                  if(StringUtils.isNotNull(xiangrongxingResult)){
                                                      if("0".equals(xiangrongxingResult)){
                                                          xrxResult = "进行中";
                                                      }else  if("1".equals(xiangrongxingResult)){
                                                          xrxResult = "测试通过";
                                                      }else  if("2".equals(xiangrongxingResult)){
                                                          xrxResult = "测试失败";
                                                      }else  if("3".equals(xiangrongxingResult)){
                                                          xrxResult = "条件接受";
                                                      }
                                                  }
                                                  data.put("compatibility",xrxResult);  //相容性
                                                  String weishenwuResult = data.getString("weishenwuResult");
                                                  String wswResult = "";  //防腐挑战
                                                  if(StringUtils.isNotNull(weishenwuResult)){
                                                      if("0".equals(weishenwuResult)){
                                                          wswResult = "高风险";
                                                      }else  if("1".equals(weishenwuResult)){
                                                          wswResult = "中风险";
                                                      }else  if("2".equals(weishenwuResult)){
                                                          wswResult = "低风险";
                                                      }else  if("3".equals(weishenwuResult)){
                                                          wswResult = "无风险";
                                                      } else  if("4".equals(weishenwuResult)){
                                                          wswResult = "测试没通过";
                                                      }
                                                  }
                                                  data.put("antiCorrosionChallenge",wswResult);  //防腐挑战
                                                  String wendingxingResult = data.getString("wendingxingResult");

                                                  String wdxResult = "";  //稳定性
                                                  if(StringUtils.isNotNull(wendingxingResult)){
                                                      if("0".equals(wendingxingResult)){
                                                          wdxResult = "进行中";
                                                      }else if("1".equals(wendingxingResult)){
                                                          wdxResult = "测试通过";
                                                      }else if("2".equals(wendingxingResult)){
                                                          wdxResult = "测试失败";
                                                      }else if("3".equals(wendingxingResult)){
                                                          wdxResult = "条件接受";
                                                      }
                                                  }else{
//                                                      JSONObject dataFormulaObj = new JSONObject();
//                                                      dataFormulaObj.put("formulaId",data.getLong("id"));
//                                                      dataFormulaObj.put("laboratoryCode",labNo);
//                                                      List<JSONObject> stabilityDataList = softwareMapper.queryFormulaStabilityDataList(dataFormulaObj);
//                                                      Long stabilityId = null;
//                                                      if(stabilityDataList!=null && stabilityDataList.size()>0){
//                                                          if(stabilityDataList.size()==1){
//                                                              JSONObject dataObj = stabilityDataList.get(0);
//                                                              String stabilityStatus =  dataObj.getString("stabilityStatus");
//                                                              if("0".equals(stabilityStatus)){
//                                                                  wdxResult = "进行中";
//                                                              }else if("1".equals(stabilityStatus)){
//                                                                  wdxResult = "测试通过";
//                                                              }else if("2".equals(stabilityStatus)){
//                                                                  wdxResult = "测试失败";
//                                                              }else if("3".equals(stabilityStatus)){
//                                                                  wdxResult = "条件接受";
//                                                              }
//                                                              stabilityId = dataObj.getLong("id");
//                                                          }else{
//                                                              JSONObject dataObj = processFormulaStabilityData(stabilityDataList);
//                                                              if(dataObj!=null && dataObj.containsKey("stabilityStatus")
//                                                                      && StringUtils.isNotEmpty(dataObj.getString("stabilityStatus"))){
//                                                                  String stabilityStatus =  dataObj.getString("stabilityStatus");
//                                                                  if("0".equals(stabilityStatus)){
//                                                                      wdxResult = "进行中";
//                                                                  }else if("1".equals(stabilityStatus)){
//                                                                      wdxResult = "测试通过";
//                                                                  }else if("2".equals(stabilityStatus)){
//                                                                      wdxResult = "测试失败";
//                                                                  }else if("3".equals(stabilityStatus)){
//                                                                      wdxResult = "条件接受";
//                                                                  }
//                                                                  stabilityId = dataObj.getLong("id");
//                                                              }
//                                                          }
//                                                      }
                                                  }
                                                  data.put("stability",wdxResult);
                                              }
                                          }
                                      }
                                  }
                              }
                              return AjaxResult.success(dataList);
                          }else {
                              return AjaxResult.success(new ArrayList<JSONObject>());
                          }
                      }else{
                          return AjaxResult.error("仅允许一次性查询十天数据!");
                      }
                  }else{
                      return AjaxResult.error("日期格式有误!");
                  }
           }else{
               return AjaxResult.error("参数异常!");
           }
        }else if("queryFormulaDataDetailDiscardFQ".equals(code)){  //获取配方详情
            JSONObject returnObj = new JSONObject();
            returnObj.put("materialDatas",new ArrayList<JSONObject>());
            returnObj.put("componentDatas",new ArrayList<JSONObject>());
            if(StringUtils.isNotNull(aiDto.getFormulaCode())){
                Long formulaId = softwareDevelopingFormulaMapper.selectFormulaIdByFormulaCode(aiDto.getFormulaCode());
                if(StringUtils.isNotNull(formulaId)){
                    List<JSONObject> dataList = softwareMapper.queryFormulaMaterialDataList(formulaId);
                    for(int i = 0;i<dataList.size();i++) {
                        JSONObject obj = dataList.get(i);
                        String gx = obj.getString("gx");
                        if(StringUtils.isNotNull(gx)){
                            String val =  DictUtils.getDictLabel("SOFTWARE_ZSGX",gx,",");
                            gx = val;
                        }
                        obj.put("gxxc",gx);
                        String inspectionItems = obj.getString("inspectionItems");
                        String standardVal = "";
                        if(StringUtils.isNotNull(inspectionItems)){
                            JSONArray inspectionItemsArray = JSONArray.parseArray(inspectionItems);
                            if(inspectionItemsArray!=null && inspectionItemsArray.size()>0){
                                for(int kk = 0;kk<inspectionItemsArray.size();kk++){
                                    JSONObject inspectionObj = inspectionItemsArray.getJSONObject(kk);
                                    Integer id  = inspectionObj.getInteger("id");
                                    if(id==8){
                                        standardVal = inspectionObj.getString("standardVal");
                                    }
                                }
                            }
                        }
                        obj.put("ph",standardVal);
                        obj.put("proportion",obj.getString("percentage"));
                        obj.remove("id");
                        obj.remove("gx");
                        obj.remove("percentage");
                        obj.remove("inspectionItems");

                    }
                    SoftwareDevelopingFormula params = new SoftwareDevelopingFormula();
                    params.setId(formulaId);
                    List<ComponentUtil> allList = getSoftwareDevelopingFormulaProductSafetyAssessmentData2(params);
                    List<JSONObject> list = new ArrayList<JSONObject>();
                    int index = 0;
                    for(ComponentUtil data : allList){
                        JSONObject obj = new JSONObject();
                        index++;
                        obj.put("seq",index);
                        obj.put("chiName",data.getChiName());
                        obj.put("proportion",data.getPercert());
                        obj.put("symdInfo",data.getCppfSymd());
                        obj.put("gxgs",data.getGxms());
                        list.add(obj);
                    }
                    returnObj.put("materialDatas",dataList);
                    returnObj.put("componentDatas",list);
                    return AjaxResult.success(returnObj);
                }else{
                    return AjaxResult.error("数据查询异常!");
                }
            }else{
                return AjaxResult.error("缺失参数!");
            }
        }else if("queryMaterialGxDataDiscardFQ".equals(code)){  //获取原料功效数据
            List<SysDictData> list = sysDictDataMapper.selectDictDataByType("SOFTWARE_ZSGX");
            List<JSONObject> dataList = new ArrayList<JSONObject>();
            if(list!=null && list.size()>0){
               for(SysDictData sysDictData : list){
                   JSONObject obj = new JSONObject();
                   obj.put("dictValue",sysDictData.getDictValue());
                   obj.put("dictLabel",sysDictData.getDictLabel());
                   dataList.add(obj);
               }
               return AjaxResult.success(dataList);
            }else{
                return AjaxResult.error("暂无数据!");
            }
        }else if("queryFormulaGxDataDiscardFQ".equals(code)){  //获取成分功效数据
            List<Gxxc> list = new FormulaConstantsUtil().GXXC_DATA_LIST;
            List<JSONObject> dataList = new ArrayList<JSONObject>();
            if(list!=null && list.size()>0){
               for(Gxxc gxxc : list){
                   JSONObject obj = new JSONObject();
                   obj.put("dictValue",gxxc.getId());
                   obj.put("dictLabel",gxxc.getTitle());
                   dataList.add(obj);
               }
               return AjaxResult.success(dataList);
            }else{
                return AjaxResult.error("暂无数据!");
            }
        }else if("queryPreparationProcessDataInfo".equals(code)){  //获取成分功效数据
            String token = UUID.randomUUID().toString().replaceAll("-","");
            MesToken mesToken = new MesToken();
            mesToken.setToken(token);
            mesToken.setExpireTime(DateUtils.addSeconds(DateUtils.getNowDate(),20));
            mesTokenMapper.insertMesToken(mesToken);
            JSONObject data = new JSONObject();
            data.put("token",mesToken.getToken());
            data.put("expireTime",DateUtils.dateTime(mesToken.getExpireTime(),DateUtils.YYYY_MM_DD_HH_MM_SS));
            return AjaxResult.success(data);
        }
        return AjaxResult.error("参数异常!");
    }

    @Override
    public AjaxResult operatorEnowInnerAiData(AiDto aiDto) {
        List<JSONObject> dataList = softwareDevelopingFormulaMapper.queryEnowFormulaDataList(aiDto.getCode());
        if(dataList!=null && dataList.size()>0){
            Long formulaId = getFormulaId(dataList);
            if(formulaId>0){
                SoftwareFormula params = new SoftwareFormula();
                params.setId(formulaId);
                JSONObject obj = softwareService.queryInspectionFormulaWaBoxDetailData(params);
                JSONArray allList = obj.getJSONArray("allList");
                JSONArray newAllList = new JSONArray();
                for(int i = 0;i<allList.size();i++){
                    JSONObject obj1 = allList.getJSONObject(i);
                    JSONObject dataObj = new JSONObject();
                    dataObj.put("seq",obj1.getInteger("seq"));
                    dataObj.put("chiName",obj1.getString("chiNameNew"));
                    dataObj.put("percert",obj1.getString("percert"));
                    newAllList.add(dataObj);
                }
                JSONObject formulaInfo = obj.getJSONObject("formulaInfo");
                formulaInfo.remove("pflx");
                formulaInfo.remove("PROJECT_NO");
                formulaInfo.remove("id");
                formulaInfo.remove("productName");
                formulaInfo.remove("itemNameTextOld");
                formulaInfo.remove("customerName");
                formulaInfo.remove("formulaCode");
                obj.put("formulaInfo",formulaInfo);
                obj.put("allList",newAllList);
                obj.remove("dataList");
                obj.remove("pftljName");
                obj.remove("materialInciRemark");
                obj.remove("materialInciRemarkCount");
                return AjaxResult.success(obj);
            }else{
                return AjaxResult.error("数据处理异常!");
            }
        }else{
            return AjaxResult.error("获取数据失败!");
        }
    }

    @Override
    @Transactional
    public void processRdSchedulingPersonnerlWorkingHours() {
        String rdScheduleDeptIds = sysConfigService.selectConfigByKey("RD_SCHEDULE_DEPT");
        //获取当前排班最新日期
        JSONObject dataInfo =  engineerWorkRecordMapper.queryScheduleEngineerWorkRecordData();
        String currentDate = DateUtils.dateTime(new Date());
        if(dataInfo!=null && dataInfo.containsKey("workDate") && StringUtils.isNotNull(dataInfo.getString("workDate"))){
            currentDate = dataInfo.getString("workDate");
        }
        Date currentDateTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, currentDate+ " 00:00:01");
        long days = DateUtils.differDays(DateUtils.getNowDate(),currentDateTime);
        if(days<17){
            List<Long> userIdList = new ArrayList<Long>();
            if(StringUtils.isNotNull(rdScheduleDeptIds)){
                List<Long> deptIds = StringUtils.str2LongList(rdScheduleDeptIds,",",true,true);
                if(deptIds!=null && deptIds.size()>0){
                    for(Long deptId : deptIds){
                       List<Long> userData =  sysHrUserMapper.queryUserInfoByDeptId(deptId);
                       if(userData!=null && userData.size()>0){
                           userIdList.addAll(userData);
                       }
                    }
                }
            }
            List<JSONObject> dataUserList = new ArrayList<JSONObject>();
            for (int i = 1;i<10;i++){
                Date saveTime = DateUtils.addDays(currentDateTime,i);
                int type = getAttendanceType(saveTime);
                if(type==0) {
                    for(Long userId : userIdList){
                        //判断是否请假
                        boolean isLeave = flowTools.isLeave(userId,saveTime);
                        if(!isLeave){
                            JSONObject dataObj = new JSONObject();
                            dataObj.put("userId",userId);
                            dataObj.put("workDate",DateUtils.dateTime(saveTime));
                            dataObj.put("workHours","8");
                            dataObj.put("remainingHours","8");
                            dataObj.put("createBy","admin");
                            dataUserList.add(dataObj);
                        }
                    }
                }
            }

            if(dataUserList!=null && dataUserList.size()>0){
                engineerWorkRecordMapper.batchEngineerWorkRecordData(dataUserList);
            }
        }
    }

    @Override
    public JSONObject queryMaterialFormulaSpecZxbzDataList(Long id) {
        return softwareDevelopingFormulaMapper.queryMaterialFormulaSpecZxbzDataList(id);
    }


    public int getAttendanceType(Date time){
        Calendar c = Calendar.getInstance();
        c.setTime(time);
        String weekday = String.valueOf((c.get(Calendar.DAY_OF_WEEK) - 1) == 0 ? 7 : (c.get(Calendar.DAY_OF_WEEK) - 1));
        int type = 1;//0工作日，1休息日，2节假日
        String [] weekdays = new String[]{"1","2","3","4","5"};
        for(String week : weekdays){
            if(weekday.equals(week)){
                type = 0;
            }
        }
        TLegalHolidays tLegalHolidays = new TLegalHolidays();
        tLegalHolidays.setYear(String.valueOf(c.get(Calendar.YEAR)));
        List<TLegalHolidays> holidaysList = tLegalHolidaysService.selectTLegalHolidaysList(tLegalHolidays);
        if(holidaysList != null && holidaysList.size() > 0){
            for(TLegalHolidays holidays : holidaysList){
                if(holidays.getDaysList() != null && holidays.getDaysList().size() > 0){
                    for(Map<String, Object> map : holidays.getDaysList()){
                        String day = (String) map.get("date");
                        if(DateUtils.dateTime(time).equals(day)){
                            if(holidays.getName().equals("补班")){
                                type = 0;
                            }else if(holidays.getName().equals("休息")){
                                type = 1;
                            }else{
                                type = 2;
                            }
                        }
                    }
                }
            }
        }
        return type;
    }


    private Long getFormulaId(List<JSONObject> dataList){
        Long formulaId = 0L;
        //获取数据
        for(JSONObject data : dataList){
            Long id = data.getLong("id");
            String status = data.getString("status");
            if(StringUtils.isNotNull(status) && status.contains("4")){
                formulaId = id;
                break;
            }
        }
        if(formulaId==0){
            for(JSONObject data : dataList){
                Long id = data.getLong("id");
                String hhStatus = data.getString("hhStatus");
                if(StringUtils.isNotNull(hhStatus) && "1".equals(hhStatus)){
                    formulaId = id;
                    break;
                }
            }
        }
        if(formulaId==0){
            for(JSONObject data : dataList){
                Long id = data.getLong("id");
                String ckcfStatus = data.getString("ckcfStatus");
                if(StringUtils.isNotNull(ckcfStatus) && "1".equals(ckcfStatus)){
                    formulaId = id;
                    break;
                }
            }
        }
        if(formulaId==0){
            formulaId = dataList.get(0).getLong("id");
        }
        return formulaId;
    }

    private JSONObject processFormulaStabilityData(List<JSONObject> stabilityDataList) {
        JSONObject returnObj = new JSONObject();
        for(JSONObject dataObj : stabilityDataList){
            String stabilityStatus =  dataObj.getString("stabilityStatus");
            if("1".equals(stabilityStatus)){
                return dataObj;
            }
        }
        for(JSONObject dataObj : stabilityDataList){
            String stabilityStatus =  dataObj.getString("stabilityStatus");
            if("3".equals(stabilityStatus)){
                return dataObj;
            }
        }
        for(JSONObject dataObj : stabilityDataList){
            String stabilityStatus =  dataObj.getString("stabilityStatus");
            if("0".equals(stabilityStatus)){
                return dataObj;
            }
        }
        for(JSONObject dataObj : stabilityDataList){
            String stabilityStatus =  dataObj.getString("stabilityStatus");
            if("2".equals(stabilityStatus)){
                return dataObj;
            }
        }
        return returnObj;
    }

    private SoftwareMaterialFormula formulaObj(List<SoftwareMaterialFormula> list, String materialCode) {
        for (SoftwareMaterialFormula item : list) {
            if(materialCode.trim().equals(item.getMaterialCode())) {
                return item;
            }
        }
        return null;
    }

}
