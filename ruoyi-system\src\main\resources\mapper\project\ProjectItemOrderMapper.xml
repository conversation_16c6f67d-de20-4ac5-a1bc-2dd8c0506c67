<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.mapper.ProjectItemOrderMapper">

    <sql id="selectProjectItemOrderVo">
        SELECT
            pio.id,
            pio.item_id,
            pio.CODE,
            pio.dyn_code,
            pio.STATUS,
            pio.customer_id,
            pio.project_product_id,
            pio.step,
            pio.current_step,
            pio.sh_status,
            pio.sh_status1,
            pio.confirm_code,
            pio.qw_time,
            pio.yj_time,
            pio.delay_yj_time,
            pio.first_yj_time,
            pio.sj_time,
            pio.timely_rate,
            pio.amendments,
            pio.amendments1,
            pio.FIELDS,
            pio.files,
            pio.imgs,
            pio.lab_no,
            pio.remark,
            pio.del_flag,
            pio.user_id,
            pio.create_by,
            pio.is_feed,
            pio.project_type,
            pio.create_time,
            pio.update_time,
            ifnull(pio.update_by,"") update_by,
            pio.order_status projectItemOrderStatus,
            pio.is_revoke,
            pio.bc_reply_time,
            pio.gongyi_reply_time,
            pio.gongyi_reply_user,
            pio.bc_reply_user,
            pio.archive_code,
            p.`project_no` projectCode,
            IF( P.PROJECT_TYPE = 0, p.product_name,(select GROUP_CONCAT(bpp.product_name) from t_before_project_product bpp where FIND_IN_SET(bpp.id,pi.project_product_id))) product_name,
            p.project_no,
            c.name customer_name,
            c.short_name customerShortName,
            c.customer_level,
            cb.brand_name,
            pi.`code` projectItemCode,
            pi.item_name projectItemName,
            p.id project_id,
            pi.fields item_fields,
            pi.type project_item_type,
            pi.status item_status,
            pi.item_name,
            p.item_names,
            p.order_status,
            p.order_yj_time,
            p.order_num,
            p.item_status project_item_status,
            p.yj_sp_ddl,
            p.level projectLevel,
            p.laboratory,
            IFNULL(p.project_yw_name,c.customer_yw) customerAssist,
            p.from_type,
            pio.is_step,
            pio.sample_price
        FROM
            t_project_item_order pio
            inner join t_project_item pi on pi.id = pio.item_id
            INNER JOIN t_project p on p.id = pi.project_id
            left join t_customer c on p.customer_id = c.id
            left join t_customer_brand cb on p.customer_brand_id = cb.brand_code
    </sql>

    <select id="selectProjectItemOrderList" parameterType="ProjectItemOrder" resultType="ProjectItemOrder" >
        <include refid="selectProjectItemOrderVo"/>
        <where>
            and pio.del_flag = 0
            <if test="projectType!=null">
                and pio.project_type = #{projectType}
            </if>
            <if test="searchUserId!=null">
                and p.customer_id in (SELECT customer_id FROM t_customer_assist WHERE status = 0 and user_id = #{searchUserId})
            </if>
            <if test="bcReplyStatus != null and bcReplyStatus !=  ''" >
                <choose>
                    <when test="bcReplyStatus == '0'">and pio.bc_reply_time is null </when>
                    <when test="bcReplyStatus == '1'">and pio.bc_reply_time is not null </when>
                </choose>
            </if>
            <if test="id != null">and pio.id = #{id}</if>
            <if test="projectId != null"> and p.id = #{projectId} </if>
            <if test="itemId != null "> and pio.item_id = #{itemId}</if>
            <if test="currentStep != null  and currentStep != ''"> and pio.current_step like concat('%', #{currentStep}, '%') </if>
            <if test="code != null  and code != ''"> and pio.code like concat('%', #{code}, '%') </if>
            <if test="status != null  and status != ''"> and pio.status = #{status}</if>
            <if test="confirmCode != null  and confirmCode != ''"> and pio.confirm_code like concat('%', #{confirmCode}, '%') </if>
            <if test="projectItemType != null and projectItemType != ''">
                and pi.type = #{projectItemType}
                <if test="itemSqlx != null and itemSqlx != null">
                    <choose>
                        <when test="projectItemType == 'PROJECT_NRW'">
                            and pi.fields->'$.dylx' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_RQKF'">
                            and pi.fields->'$.bcfl' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_SHEJI'">
                            and pi.fields->'$.sjlx' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_PPT'">
                            and pi.fields->'$.talx' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_QCF'">
                            and pi.fields->'$.sqlx' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_OFFER'">
                            and pi.fields->'$.bjlx' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_ZS'">
                            and pi.fields->'$.zslx' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_BOM_APPLY'">
                            and pi.fields->'$.applyType' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_GXCSSQ'">
                            and pi.fields->'$.sqlx' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_BA'">
                            and pi.fields->'$.balb' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_SCKXXPG'">
                            and pi.fields->'$.sqlx' = #{itemSqlx}
                        </when>
                    </choose>
                </if>
            </if>
            <if test="orderTypes != null and orderTypes.size() > 0">
                and pi.type in
                <foreach collection="orderTypes" item="orderType" separator="," open="(" close=")" >
                    #{orderType}
                </foreach>
            </if>
            <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="params.beginQw != null and params.beginQw != ''">
                AND date_format(pio.qw_time,'%y%m%d') &gt;= date_format(#{params.beginQw},'%y%m%d')
            </if>
            <if test="params.endQw != null and params.endQw != ''">
                AND date_format(pio.qw_time,'%y%m%d') &lt;= date_format(#{params.endQw},'%y%m%d')
            </if>
            <if test="params.beginApply != null and params.beginApply != ''">
                AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginApply},'%y%m%d')
            </if>
            <if test="params.endApply != null and params.endApply != ''">
                AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endApply},'%y%m%d')
            </if>
            <if test="params.beginDelayYjRange != null and params.beginDelayYjRange != ''">
                AND date_format(pio.delay_yj_time,'%y%m%d') &gt;= date_format(#{params.beginDelayYjRange},'%y%m%d')
            </if>
            <if test="params.endDelayYjRange != null and params.endDelayYjRange != ''">
                AND date_format(pio.delay_yj_time,'%y%m%d') &lt;= date_format(#{params.endDelayYjRange},'%y%m%d')
            </if>
            <if test="params.beginYj != null and params.beginYj != ''">
                AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
            </if>
            <if test="params.endYj != null and params.endYj != ''">
                AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
            </if>
            <if test="params.beginSj != null and params.beginSj != ''">
                AND date_format(pio.sj_time,'%y%m%d') &gt;= date_format(#{params.beginSj},'%y%m%d')
            </if>
            <if test="params.endSj != null and params.endSj != ''">
                AND date_format(pio.sj_time,'%y%m%d') &lt;= date_format(#{params.endSj},'%y%m%d')
            </if>
            <if test="params.beginCreate != null and params.beginCreate != ''">
                AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
            </if>
            <if test="params.endCreate != null and params.endCreate != ''">
                AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
            </if>
            <if test=" params.statusArray != null and params.statusArray.length > 0 ">
                and pio.STATUS in
                <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.progressArray != null and params.progressArray.length > 0 ">
                and p.progress in
                <foreach collection="params.progressArray" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">
                and pio.current_step in
                <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">
                and p.customer_id != #{params.customerIgnoreId}
            </if>
            <if test="params.customerId != null and params.customerId != ''">
                and p.customer_id = #{params.customerId}
            </if>
            <if test="createBy != null and createBy != ''">
                and pio.create_by = #{createBy}
            </if>
            <if test="projectIds != null and projectIds.size() > 0" >
                and pi.project_id in
                <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," >
                    #{projectId}
                </foreach>
            </if>
            <if test="idArr != null and idArr.size() > 0" >
                and pio.id in
                <foreach collection="idArr" separator="," open="(" close=")" item="id">
                    #{id}
                </foreach>
            </if>
        </where>
        order by pio.id desc
    </select>

    <select id="selectProjectItemOrderListBySamplePrice" parameterType="ProjectItemOrder" resultType="ProjectItemOrder" >
        SELECT
        pio.id,
        pio.item_id,
        pio.CODE,
        pio.dyn_code,
        pio.STATUS,
        pio.customer_id,
        pio.project_product_id,
        pio.step,
        pio.current_step,
        pio.sh_status,
        pio.sh_status1,
        pio.confirm_code,
        pio.qw_time,
        pio.yj_time,
        pio.delay_yj_time,
        pio.first_yj_time,
        pio.sj_time,
        pio.timely_rate,
        pio.amendments,
        pio.amendments1,
        pio.FIELDS,
        pio.files,
        pio.imgs,
        pio.lab_no,
        pio.remark,
        pio.del_flag,
        pio.user_id,
        pio.create_by,
        pio.is_feed,
        pio.project_type,
        pio.create_time,
        pio.update_time,
        ifnull(pio.update_by,"") update_by,
        pio.order_status projectItemOrderStatus,
        pio.is_revoke,
        pio.bc_reply_time,
        pio.gongyi_reply_time,
        pio.gongyi_reply_user,
        pio.bc_reply_user,
        pio.archive_code,
        pio.sample_price,
        p.`project_no` projectCode,
        IF( P.PROJECT_TYPE = 0, p.product_name,(select GROUP_CONCAT(bpp.product_name) from t_before_project_product bpp where FIND_IN_SET(bpp.id,pi.project_product_id))) product_name,
        p.project_no,
        c.name customer_name,
        c.short_name customerShortName,
        c.customer_level,
        cb.brand_name,
        pi.`code` projectItemCode,
        pi.item_name projectItemName,
        p.id project_id,
        pi.fields item_fields,
        pi.type project_item_type,
        pi.status item_status,
        pi.item_name,
        p.item_names,
        p.order_status,
        p.order_yj_time,
        p.order_num,
        p.item_status project_item_status,
        p.yj_sp_ddl,
        p.level projectLevel,
        p.laboratory,
        IFNULL(p.project_yw_name,c.customer_yw) customerAssist,
        p.from_type
        FROM
        t_project_item_order pio
        inner join t_project_item pi on pi.id = pio.item_id
        INNER JOIN t_project p on p.id = pi.project_id
        left join t_customer c on p.customer_id = c.id
        left join t_customer_brand cb on p.customer_brand_id = cb.brand_code
        <where>
            and pio.del_flag = 0 and pio.add_style = 0
            <if test="projectType!=null">
                and pio.project_type = #{projectType}
            </if>
            <if test="searchUserId!=null">
                and p.customer_id in (SELECT customer_id FROM t_customer_assist WHERE status = 0 and user_id = #{searchUserId})
            </if>
            <if test="isEntered==1">
                and pio.sample_price is null
            </if>
            <if test="isEntered==2">
                and pio.sample_price is not null
            </if>
            <if test="bcReplyStatus != null and bcReplyStatus !=  ''" >
                <choose>
                    <when test="bcReplyStatus == '0'">and pio.bc_reply_time is null </when>
                    <when test="bcReplyStatus == '1'">and pio.bc_reply_time is not null </when>
                </choose>
            </if>
            <if test="id != null">and pio.id = #{id}</if>
            <if test="projectId != null"> and p.id = #{projectId} </if>
            <if test="code != null  and code != ''"> and pio.code like concat('%', #{code}, '%') </if>
            <if test="status != null  and status != ''"> and pio.status = #{status}</if>
            <if test="projectItemType != null and projectItemType != ''">
                and pi.type = #{projectItemType}
                <if test="itemSqlx != null and itemSqlx != null">
                    <choose>
                        <when test="projectItemType == 'PROJECT_NRW'">
                            and pi.fields->'$.dylx' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_RQKF'">
                            and pi.fields->'$.bcfl' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_SHEJI'">
                            and pi.fields->'$.sjlx' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_PPT'">
                            and pi.fields->'$.talx' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_QCF'">
                            and pi.fields->'$.sqlx' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_OFFER'">
                            and pi.fields->'$.bjlx' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_ZS'">
                            and pi.fields->'$.zslx' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_BOM_APPLY'">
                            and pi.fields->'$.applyType' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_GXCSSQ'">
                            and pi.fields->'$.sqlx' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_BA'">
                            and pi.fields->'$.balb' = #{itemSqlx}
                        </when>
                        <when test="projectItemType == 'PROJECT_SCKXXPG'">
                            and pi.fields->'$.sqlx' = #{itemSqlx}
                        </when>
                    </choose>
                </if>
            </if>
            <if test="orderTypes != null and orderTypes.size() > 0">
                and pi.type in
                <foreach collection="orderTypes" item="orderType" separator="," open="(" close=")" >
                    #{orderType}
                </foreach>
            </if>
            <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="params.beginQw != null and params.beginQw != ''">
                AND date_format(pio.qw_time,'%y%m%d') &gt;= date_format(#{params.beginQw},'%y%m%d')
            </if>
            <if test="params.endQw != null and params.endQw != ''">
                AND date_format(pio.qw_time,'%y%m%d') &lt;= date_format(#{params.endQw},'%y%m%d')
            </if>
            <if test="params.beginApply != null and params.beginApply != ''">
                AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginApply},'%y%m%d')
            </if>
            <if test="params.endApply != null and params.endApply != ''">
                AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endApply},'%y%m%d')
            </if>
            <if test="params.beginDelayYjRange != null and params.beginDelayYjRange != ''">
                AND date_format(pio.delay_yj_time,'%y%m%d') &gt;= date_format(#{params.beginDelayYjRange},'%y%m%d')
            </if>
            <if test="params.endDelayYjRange != null and params.endDelayYjRange != ''">
                AND date_format(pio.delay_yj_time,'%y%m%d') &lt;= date_format(#{params.endDelayYjRange},'%y%m%d')
            </if>
            <if test="params.beginYj != null and params.beginYj != ''">
                AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
            </if>
            <if test="params.endYj != null and params.endYj != ''">
                AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
            </if>
            <if test="params.beginSj != null and params.beginSj != ''">
                AND date_format(pio.sj_time,'%y%m%d') &gt;= date_format(#{params.beginSj},'%y%m%d')
            </if>
            <if test="params.endSj != null and params.endSj != ''">
                AND date_format(pio.sj_time,'%y%m%d') &lt;= date_format(#{params.endSj},'%y%m%d')
            </if>
            <if test="params.beginCreate != null and params.beginCreate != ''">
                AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
            </if>
            <if test="params.endCreate != null and params.endCreate != ''">
                AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
            </if>
            <if test=" params.statusArray != null and params.statusArray.length > 0 ">
                and pio.STATUS in
                <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.progressArray != null and params.progressArray.length > 0 ">
                and p.progress in
                <foreach collection="params.progressArray" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="params.customerId != null and params.customerId != ''">
                and p.customer_id = #{params.customerId}
            </if>
            <if test="createBy != null and createBy != ''">
                and pio.create_by = #{createBy}
            </if>
            <if test="projectIds != null and projectIds.size() > 0" >
                and pi.project_id in
                <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," >
                    #{projectId}
                </foreach>
            </if>
            <if test="idArr != null and idArr.size() > 0" >
                and pio.id in
                <foreach collection="idArr" separator="," open="(" close=")" item="id">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY pio.id desc
    </select>

    <update id="updateProjectItemOrderConfirmCode" parameterType="ProjectItemOrder">
        update t_project_item_order
        set CONFIRM_CODE = (SELECT GROUP_CONCAT(confirm_code) from t_project_item_order_code pioc where pioc.project_order_id = #{id})
        where id = #{id}
    </update>

    <select id="selectProjectItemOrderReleaseList" parameterType="ProjectItemOrder" resultType="ProjectItemOrder" >
        <include refid="selectProjectItemOrderVo"/>
        <where>
            and pio.del_flag = 0  and pi.type in ('PROJECT_INSPECTION_AND_FILING','PROJECT_BA') and pi.fields->'$.applyType' IN (4, 1)
             and pio.add_style = 0
            <if test="id != null">and pio.id = #{id}</if>
            <if test="isApply==0"> and pio.is_apply_filling = 0 and pio.sh_status = 1</if>
            <if test="isApply==1"> and pio.is_apply_filling > 0</if>
            <if test="projectId != null"> and p.id = #{projectId} </if>
            <if test="itemId != null "> and pio.item_id = #{itemId}</if>
            <if test="laboratory != null and laboratory!='' "> and p.laboratory = #{laboratory}</if>
            <if test="projectNo != null and projectNo!='' "> and p.project_no = #{projectNo}</if>
            <if test="labNo != null and labNo!='' "> and pio.fields like concat('%', #{labNo}, '%')</if>
            <if test="currentStep != null  and currentStep != ''"> and pio.current_step like concat('%', #{currentStep}, '%') </if>
            <if test="code != null  and code != ''"> and pio.code like concat('%', #{code}, '%') </if>
            <if test="status != null  and status != ''"> and pio.status = #{status}</if>
            <if test="confirmCode != null  and confirmCode != ''"> and pio.confirm_code like concat('%', #{confirmCode}, '%') </if>
            <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="params.beginQw != null and params.beginQw != ''">
                AND date_format(pio.qw_time,'%y%m%d') &gt;= date_format(#{params.beginQw},'%y%m%d')
            </if>
            <if test="params.endQw != null and params.endQw != ''">
                AND date_format(pio.qw_time,'%y%m%d') &lt;= date_format(#{params.endQw},'%y%m%d')
            </if>
            <if test="params.beginApply != null and params.beginApply != ''">
                AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginApply},'%y%m%d')
            </if>
            <if test="params.endApply != null and params.endApply != ''">
                AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endApply},'%y%m%d')
            </if>
            <if test="params.beginDelayYjRange != null and params.beginDelayYjRange != ''">
                AND date_format(pio.delay_yj_time,'%y%m%d') &gt;= date_format(#{params.beginDelayYjRange},'%y%m%d')
            </if>
            <if test="params.endDelayYjRange != null and params.endDelayYjRange != ''">
                AND date_format(pio.delay_yj_time,'%y%m%d') &lt;= date_format(#{params.endDelayYjRange},'%y%m%d')
            </if>
            <if test="params.beginYj != null and params.beginYj != ''">
                AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
            </if>
            <if test="params.endYj != null and params.endYj != ''">
                AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
            </if>
            <if test="params.beginSj != null and params.beginSj != ''">
                AND date_format(pio.sj_time,'%y%m%d') &gt;= date_format(#{params.beginSj},'%y%m%d')
            </if>
            <if test="params.endSj != null and params.endSj != ''">
                AND date_format(pio.sj_time,'%y%m%d') &lt;= date_format(#{params.endSj},'%y%m%d')
            </if>
            <if test="params.beginCreate != null and params.beginCreate != ''">
                AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
            </if>
            <if test="params.endCreate != null and params.endCreate != ''">
                AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
            </if>
            <if test=" params.statusArray != null and params.statusArray.length > 0 ">
                and pio.STATUS in
                <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.progressArray != null and params.progressArray.length > 0 ">
                and p.progress in
                <foreach collection="params.progressArray" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">
                and pio.current_step in
                <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">
                and p.customer_id != #{params.customerIgnoreId}
            </if>
            <if test="params.customerId != null and params.customerId != ''">
                and p.customer_id = #{params.customerId}
            </if>
            <if test="createBy != null and createBy != ''">
                and pio.create_by = #{createBy}
            </if>
            <if test="projectIds != null and projectIds.size() > 0" >
                and pi.project_id in
                <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," >
                    #{projectId}
                </foreach>
            </if>
            <if test="idArr != null and idArr.size() > 0" >
                and pio.id in
                <foreach collection="idArr" separator="," open="(" close=")" item="id">
                    #{id}
                </foreach>
            </if>
        </where>
        order by pio.id desc
    </select>

    <!-- 项目费用列表  pio.id>97746 and
       or (pi.type in ('PROJECT_RQKF') and IF(pi.type='PROJECT_INSPECTION_AND_FILING',pio.`fields`->> '$.songjianFields.payStyles',pio.`fields`->> '$.payStyles') LIKE '%0%')
    -->
     <select id="selectProjectItemOrderFeeList" resultType="com.alibaba.fastjson.JSONObject" parameterType="ProjectItemOrder">
         SELECT
            IF( P.PROJECT_TYPE = 0, p.product_name,(select GROUP_CONCAT(bpp.product_name) from t_before_project_product bpp where FIND_IN_SET(bpp.id,pi.project_product_id))) product_name,
             p.project_no projectNo,
             pio.customer_id customerId,
             pio.customer_id paymentCustomerId,
             pio.id id,
             pio.code,
             pi.type,
             IF(pi.type='PROJECT_INSPECTION_AND_FILING',pio.`fields`->> '$.songjianFields.payStyles',pio.`fields`->> '$.payStyles') payStyles,
             IF(pi.type='PROJECT_INSPECTION_AND_FILING',pio.`fields`->> '$.songjianFields.fukuanMoney',pio.`fields`->> '$.money') fukuanMoney,
             IF(pi.type='PROJECT_INSPECTION_AND_FILING',pio.`fields`->> '$.songjianFields.paymentPercentage',pio.`fields`->> '$.paymentPercentage') paymentPercentage,
             IF(pi.type='PROJECT_INSPECTION_AND_FILING',pio.`fields`->> '$.songjianFields.ourPercentage',pio.`fields`->> '$.ourPercentage') ourPercentage,
             IF(pi.type='PROJECT_INSPECTION_AND_FILING',pio.`fields`->> '$.songjianFields.customerPercentage',pio.`fields`->> '$.customerPercentage') customerPercentage,
             IF(pi.type='PROJECT_INSPECTION_AND_FILING',pio.`fields`->> '$.songjianFields.supplierPercentage',pio.`fields`->> '$.supplierPercentage') supplierPercentage,
             IF(pi.type='PROJECT_INSPECTION_AND_FILING',pio.`fields`->> '$.songjianFields.supplierId',pio.`fields`->> '$.supplierId') supplierId,
             IF(pi.type='PROJECT_INSPECTION_AND_FILING',pio.`fields`->> '$.songjianFields.returnMode',pio.`fields`->> '$.returnMode') returnMode,
             IF(pi.type='PROJECT_INSPECTION_AND_FILING',pio.`fields`->> '$.songjianFields.returnNums',pio.`fields`->> '$.returnNums') returnNums,
             pi.create_by createBy,
             DATE_FORMAT(pio.create_time,'%Y-%m-%d %H:%i:%s') createTime,
             pio.is_apply_order isApplyOrder,
             pio.is_apply_purchase isApplyPurchase
         FROM t_project_item  pi
         INNER JOIN t_project_item_order pio on pi.id = pio.item_id
         INNER JOIN t_project p on pi.project_id = p.id
         where pio.id>97746 and pio.status in ('0','3') and  pi.type in ('PROJECT_SCKXXPG','PROJECT_RQKF','PROJECT_INSPECTION_AND_FILING','PROJECT_GXCSSQ')
            and  (pi.type in ('PROJECT_SCKXXPG','PROJECT_RQKF','PROJECT_GXCSSQ') or pi.`fields`-> '$.applyType' != '2')
         <if test="code!=null and code!=''">and pio.code like concat(#{code},'%')</if>
         <if test="productName!=null and productName!=''">and p.product_name like concat('%',#{productName},'%')</if>
         <if test="customerId!=null">and p.customer_id = #{customerId}</if>
         <if test="applyType==1">
           and (
             IF(pi.type='PROJECT_INSPECTION_AND_FILING',pio.`fields`->> '$.songjianFields.payStyles',pio.`fields`->> '$.payStyles') LIKE '%0%'
             OR
             IF(pi.type='PROJECT_INSPECTION_AND_FILING',pio.`fields`->> '$.songjianFields.payStyles',pio.`fields`->> '$.payStyles') LIKE '%1%'
             OR
             IF(pi.type='PROJECT_INSPECTION_AND_FILING',pio.`fields`->> '$.songjianFields.payStyles',pio.`fields`->> '$.payStyles') LIKE '%2%'
             )
            <if test="dataId!=null">
                and  pio.customer_id = #{dataId}
                and  pio.is_apply_order = 0
            </if>
         </if>
         <if test="applyType==2">
           and (IF(pi.type='PROJECT_INSPECTION_AND_FILING',pio.`fields`->> '$.songjianFields.payStyles',pio.`fields`->> '$.payStyles') LIKE '%3%')
             <if test="dataId!=null">
                 and IF(pi.type='PROJECT_INSPECTION_AND_FILING',pio.`fields`->> '$.songjianFields.supplierId',pio.`fields`->> '$.supplierId') = #{dataId}
                 and pio.is_apply_purchase = 0
             </if>
         </if>
         order by pio.id desc
     </select>

    <select id="selectProjectItemOrderById" parameterType="Long" resultType="ProjectItemOrder">
        SELECT
            pio.id,
            pio.item_id,
            pio.CODE,
            pio.dyn_code,
            pio.STATUS,
            pio.customer_id,
            pio.project_product_id,
            pio.step,
            pio.current_step,
            pio.sh_status,
            pio.sh_status1,
            pio.confirm_code,
            pio.qw_time,
            pio.yj_time,
            pio.delay_yj_time,
            pio.first_yj_time,
            pio.sj_time,
            pio.timely_rate,
            pio.amendments,
            pio.amendments1,
            pio.FIELDS,
            pio.audit_fields,
            pio.bom_change_array,
            pio.bom_change_reply_array,
            pio.files,
            pio.imgs,
            pio.lab_no,
            pio.remark,
            pi.remark itemRemark,
            pio.del_flag,
            pio.user_id,
            pio.create_by,
            pio.is_feed,
            pio.project_type,
            pio.create_time,
            pio.update_time,
            ifnull(pio.update_by,"") update_by,
            pio.order_status projectItemOrderStatus,
            pio.is_revoke,
            pio.bc_reply_time,
            pio.gongyi_reply_time,
            pio.gongyi_reply_user,
            pio.bc_reply_user,
            pio.archive_code,
            pio.product_tabs,
            pio.sample_price,
            p.`project_no` projectCode,
            IF( P.PROJECT_TYPE = 0, p.product_name,(select GROUP_CONCAT(bpp.product_name) from t_before_project_product bpp where FIND_IN_SET(bpp.id,pi.project_product_id))) product_name,
            p.project_no,
            c.name customer_name,
            c.short_name customerShortName,
            c.customer_level,
            cb.brand_name,
            pi.`code` projectItemCode,
            pi.item_name projectItemName,
            p.id project_id,
            pi.fields item_fields,
            pi.type project_item_type,
            pi.status item_status,
            pi.item_name,
            p.item_names,
            p.order_status,
            p.order_yj_time,
            p.order_num,
            p.item_status project_item_status,
            p.yj_sp_ddl,
            p.level projectLevel,
            p.laboratory,
            IFNULL(p.project_yw_name,c.customer_yw) customerAssist,
            pio.preview_export_form,
            pio.workstation_diagram_datas,
            tpp.name itemText,
            pio.is_step,
            u.work_user_id,
            eso.id engineerId
         FROM
            t_project_item_order pio
            inner join t_project_item pi on pi.id = pio.item_id
            INNER JOIN t_project p on p.id = pi.project_id
            left join t_customer c on p.customer_id = c.id
            left join engineer_sample_order eso on (pio.id = eso.project_order_id and eso.del_flag = 0 and eso.completion_status in ('0','1','2'))
            left join sys_user u on eso.user_id = u.user_id
            left join t_customer_brand cb on p.customer_brand_id = cb.brand_code
            left join t_project_product tpp on (pi.item_name = tpp.guid and tpp.project_id = pi.project_id)
        where pio.id = #{id}
    </select>

    <select id="selectProjectItemOrderPackagingMaterialsById" parameterType="Long" resultType="ProjectItemOrder">
        select id,packaging_material_datas packagingMaterialDatas
        from t_project_item_order pio
        where pio.id = #{id}
    </select>

    <!-- 根据实验室编号查询客户和项目 -->
    <select id="selectBaOrderByLabCode" resultType="ProjectItemOrder">
        SELECT
            o.id,
            o.project_id,
            o.customer_id
        FROM
            t_project_item_order o
            INNER JOIN t_project_item pi ON pi.id = o.item_id
        WHERE
            pi.type = 'PROJECT_INSPECTION_AND_FILING'
            AND o.`fields` -> '$.sjData' LIKE concat('%',#{labCode}, '%')
    </select>

    <select id="projectItemOrderInfoBySysbm" parameterType="ProjectItemOrder" resultType="ProjectItemOrder">
        <include refid="selectProjectItemOrderVo"/>
        where pio.id = (
            select PROJECT_ORDER_ID from t_project_item_order_code where CONFIRM_CODE = #{confirmCode} order by id desc
        )
    </select>

    <!-- 获取项目信息 -->
    <select id="queryProjectInfo" parameterType="java.lang.Long" resultType="Project">
        SELECT
            p.id,
            p.project_no CODE,
            p.project_no,
            p.laboratory,
            pi.`fields`,
            IF
                ( pi.type = 'PROJECT_BOM_APPLY', pio.FIELDS, '' ) orderFields,
            p.customer_id,
            pi.laboratory itemLabNo,
            p.project_type
        FROM
            t_project p
            INNER JOIN t_project_item_order pio ON p.id = pio.project_id
            INNER JOIN t_project_item pi ON pio.item_id = pi.id
        WHERE
            pio.id = #{projectOrderId}
    </select>

    <insert id="insertProjectItemOrder" parameterType="ProjectItemOrder" useGeneratedKeys="true" keyProperty="id">
        insert into t_project_item_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="code != null">code,</if>
            <if test="status != null">status,</if>
            <if test="step != null">step,</if>
            <if test="currentStep != null">current_step,</if>
            <if test="auditFields != null">audit_fields,</if>
            <if test="shStatus != null">sh_status,</if>
            <if test="shStatus1 != null">sh_status1,</if>
            <if test="confirmCode != null">confirm_code,</if>
            <if test="qwTime != null">qw_time,</if>
            <if test="yjTime != null">yj_time,</if>
            <if test="firstYjTime != null">first_yj_time,</if>
            <if test="sjTime != null">sj_time,</if>
            <if test="timelyRate != null">timely_rate,</if>
            <if test="amendments != null">amendments,</if>
            <if test="amendments1 != null">amendments1,</if>
            <if test="fields != null">fields,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="preItemFields != null">item_fields,</if>
            <if test="dynCode != null">dyn_code,</if>
            <if test="userId != null">user_id,</if>
            <if test="projectItemOrderStatus != null">order_status,</if>
            <if test="projectId != null">project_id,</if>
            <if test="samplePrice != null">sample_price,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="files != null">files,</if>
            <if test="imgs != null">imgs,</if>
            <if test="addStyle != null">add_style,</if>
            <if test="bomChangeArray != null">bom_change_array,</if>
            <if test="projectProductId != null">project_product_id,</if>
            <if test="projectType != null">project_type,</if>
            <if test="labNo != null">lab_no,</if>
            <if test="bomChangeReplyArray != null">bom_change_reply_array,</if>
            <if test="productTabs != null">product_tabs,</if>
            <if test="isStep != null">is_step,</if>
            <if test="categoryText != null">category_text,</if>
            <if test="fromType != null">from_type,</if>
         </trim>
         <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="code != null">#{code},</if>
            <if test="status != null">#{status},</if>
            <if test="step != null">#{step},</if>
            <if test="currentStep != null">#{currentStep},</if>
            <if test="auditFields != null">#{auditFields},</if>
            <if test="shStatus != null">#{shStatus},</if>
            <if test="shStatus1 != null">#{shStatus1},</if>
            <if test="confirmCode != null">#{confirmCode},</if>
            <if test="qwTime != null">#{qwTime},</if>
            <if test="yjTime != null">#{yjTime},</if>
            <if test="firstYjTime != null">#{firstYjTime},</if>
            <if test="sjTime != null">#{sjTime},</if>
            <if test="timelyRate != null">#{timelyRate},</if>
            <if test="amendments != null">#{amendments},</if>
            <if test="amendments1 != null">#{amendments1},</if>
            <if test="fields != null">#{fields},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="preItemFields != null">#{preItemFields},</if>
            <if test="dynCode != null">#{dynCode},</if>
            <if test="userId != null">#{userId},</if>
            <if test="projectItemOrderStatus != null">#{projectItemOrderStatus},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="samplePrice != null">#{samplePrice},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="files != null">#{files},</if>
            <if test="imgs != null">#{imgs},</if>
            <if test="addStyle != null">#{addStyle},</if>
            <if test="bomChangeArray != null">#{bomChangeArray},</if>
            <if test="projectProductId != null">#{projectProductId},</if>
            <if test="projectType != null">#{projectType},</if>
            <if test="labNo != null">#{labNo},</if>
            <if test="bomChangeReplyArray != null">#{bomChangeReplyArray},</if>
            <if test="productTabs != null">#{productTabs},</if>
            <if test="isStep != null">#{isStep},</if>
            <if test="categoryText != null">category_text,</if>
            <if test="fromType != null">from_type,</if>
        </trim>
       </insert>

    <update id="updateProjectItemOrder" parameterType="ProjectItemOrder">
        update t_project_item_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="samplePrice != null">sample_price = #{samplePrice},</if>
            <if test="code != null">code = #{code},</if>
            <if test="status != null">status = #{status},</if>
            <if test="step != null">step = #{step},</if>
            <if test="currentStep != null">current_step = #{currentStep},</if>
            <if test="shStatus != null">sh_status = #{shStatus},</if>
            <if test="shStatus1 != null">sh_status1 = #{shStatus1},</if>
            <if test="confirmCode != null">confirm_code = #{confirmCode},</if>
            <if test="qwTime != null">qw_time = #{qwTime},</if>
            <if test="yjTime != null">yj_time = #{yjTime},</if>
            <if test="delayYjTime != null">delay_yj_time = #{delayYjTime},</if>
            <if test="firstYjTime != null">first_yj_time = #{firstYjTime},</if>
            <if test="sjTime != null">sj_time = #{sjTime},</if>
            <if test="timelyRate != null">timely_rate = #{timelyRate},</if>
            <if test="amendments != null">amendments = #{amendments},</if>
            <if test="amendments1 != null">amendments1 = #{amendments1},</if>
            <if test="fields != null">fields = #{fields},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="isRevoke != null">is_revoke = #{isRevoke},</if>
            <if test="auditFields != null">audit_fields = #{auditFields},</if>
            <if test="preItemFields != null">item_fields = #{preItemFields},</if>
            <if test="projectItemOrderStatus != null">order_status = #{projectItemOrderStatus},</if>
            <if test="files != null">files = #{files},</if>
            <if test="imgs != null">imgs = #{imgs},</if>
            <if test="isFeed != null">is_feed = #{isFeed},</if>
            <if test="feedStatus != null">feed_status = #{feedStatus},</if>
            <if test="isSync != null">is_sync = #{isSync},</if>
            <if test="bcReplyTime != null">bc_reply_time = #{bcReplyTime},</if>
            <if test="bomChangeArray != null">bom_change_array = #{bomChangeArray},</if>
            <if test="bomChangeReplyArray != null">bom_change_reply_array = #{bomChangeReplyArray},</if>
            <if test="gongyiReplyTime != null">gongyi_reply_time = #{gongyiReplyTime},</if>
            <if test="archiveCode != null">archive_code = #{archiveCode},</if>
            <if test="gongyiReplyUser != null">gongyi_reply_user = #{gongyiReplyUser},</if>
            <if test="bcReplyUser != null">bc_reply_user = #{bcReplyUser},</if>
            <if test="productTabs != null">product_tabs = #{productTabs},</if>
            <if test="workstationDiagramDatas != null">workstation_diagram_datas = #{workstationDiagramDatas},</if>
            <if test="labNo != null">lab_no = #{labNo},</if>
            <if test="previewExportForm != null">preview_export_form = #{previewExportForm},</if>
            <if test="categoryText != null">category_text = #{categoryText},</if>
            <if test="fromType != null">from_type = #{fromType},</if>
            <if test="mergeFields=='revokeOpr'">
                bc_reply_user = null,
                bc_reply_time = null,
                gongyi_reply_time = null,
                gongyi_reply_user = null
            </if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateProjectItemOrderOfferRevoke" parameterType="ProjectItemOrder">
        update t_project_item_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="code != null">code = #{code},</if>
            <if test="status != null">status = #{status},</if>
            <if test="step != null">step = #{step},</if>
            <if test="currentStep != null">current_step = #{currentStep},</if>
            <if test="shStatus != null">sh_status = #{shStatus},</if>
            <if test="shStatus1 != null">sh_status1 = #{shStatus1},</if>
            <if test="confirmCode != null">confirm_code = #{confirmCode},</if>
            <if test="qwTime != null">qw_time = #{qwTime},</if>
            <if test="yjTime != null">yj_time = #{yjTime},</if>
            <if test="delayYjTime != null">delay_yj_time = #{delayYjTime},</if>
            <if test="firstYjTime != null">first_yj_time = #{firstYjTime},</if>
            <if test="sjTime != null">sj_time = #{sjTime},</if>
            <if test="timelyRate != null">timely_rate = #{timelyRate},</if>
            <if test="amendments != null">amendments = #{amendments},</if>
            <if test="amendments1 != null">amendments1 = #{amendments1},</if>
            <if test="fields != null">fields = #{fields},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="isRevoke != null">is_revoke = #{isRevoke},</if>
            <if test="auditFields != null">audit_fields = #{auditFields},</if>
            <if test="preItemFields != null">item_fields = #{preItemFields},</if>
            <if test="projectItemOrderStatus != null">order_status = #{projectItemOrderStatus},</if>
            <if test="files != null">files = #{files},</if>
            <if test="imgs != null">imgs = #{imgs},</if>
            <if test="isFeed != null">is_feed = #{isFeed},</if>
            <if test="feedStatus != null">feed_status = #{feedStatus},</if>
            <if test="isSync != null">is_sync = #{isSync},</if>
            <if test="bomChangeArray != null">bom_change_array = #{bomChangeArray},</if>
            <if test="bomChangeReplyArray != null">bom_change_reply_array = #{bomChangeReplyArray},</if>
            <if test="archiveCode != null">archive_code = #{archiveCode},</if>
            <if test="productTabs != null">product_tabs = #{productTabs},</if>
            <if test="workstationDiagramDatas != null">workstation_diagram_datas = #{workstationDiagramDatas},</if>
            <if test="labNo != null">lab_no = #{labNo},</if>
            <if test="previewExportForm != null">preview_export_form = #{previewExportForm},</if>
            <if test="mergeFields=='packagingMaterialDevelopment'">
                bc_reply_user = null,
                bc_reply_time = null
            </if>
            <if test="mergeFields=='technology'">
                gongyi_reply_time = null,
                gongyi_reply_user = null
            </if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateProjectItemOrderZdy" parameterType="ProjectItemOrder">
        update t_project_item_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="step != null">step = #{step},</if>
            <if test="currentStep != null">current_step = #{currentStep},</if>
            <if test="shStatus != null">sh_status = #{shStatus},</if>
            <if test="shStatus1 != null">sh_status1 = #{shStatus1},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="isRevoke != null">is_revoke = #{isRevoke},</if>
            <if test="yjTime != null">yj_time = #{yjTime},</if>
            <if test="firstYjTime != null">first_yj_time = #{firstYjTime},</if>
            <if test="samplePrice != null">sample_price = #{samplePrice},</if>
        </trim>
        ,first_yj_time = null,
        yj_time = #{yjTime}
        where id = #{id}
    </update>

    <update id="updateProjectItemOrderRevoke" parameterType="ProjectItemOrder">
        update t_project_item_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="step != null">step = #{step},</if>
            <if test="currentStep != null">current_step = #{currentStep},</if>
            <if test="shStatus != null">sh_status = #{shStatus},</if>
            <if test="shStatus1 != null">sh_status1 = #{shStatus1},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="isRevoke != null">is_revoke = #{isRevoke},</if>
        </trim>
        ,first_yj_time = null,
        yj_time = #{yjTime}
        where id = #{id} and is_revoke = 0 and step != 3 and status = '0'
    </update>
    <update id="updateProjectItemOrderRevokeNrw" parameterType="ProjectItemOrder">
        update t_project_item_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="step != null">step = #{step},</if>
            <if test="currentStep != null">current_step = #{currentStep},</if>
            <if test="shStatus != null">sh_status = #{shStatus},</if>
            <if test="shStatus1 != null">sh_status1 = #{shStatus1},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="isRevoke != null">is_revoke = #{isRevoke},</if>
        </trim>
        ,first_yj_time = null,
        yj_time = #{yjTime}
        where id = #{id} and is_revoke = 0 and status = '0'
    </update>

    <update id="deleteProjectItemOrderByIds" parameterType="String">
        update t_project_item_order set del_flag = 2,update_time = now() where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 获取数目 -->
    <select id="queryProjectItemOrderCount" parameterType="ProjectItemOrder" resultType="java.lang.Integer">
        select count(*) from t_project_item_order where item_id = #{itemId}
    </select>

    <!-- 获取未完成订单 ,'1'-->
    <select id="selectItemIsComplete" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(*)
        from t_project_item_order
        where item_id = #{id}
        and status in ('0')
        and del_flag = 0
    </select>

    <!--获取下级订单信息-->
    <select id="queryProjectItemOrderDataInfo" parameterType="ProjectItem" resultType="ProjectItemOrder">
        SELECT
            pio.id,
            pio.confirm_code,
            pio. CODE,
            pi.item_name
        FROM
            t_project_item_order pio
            LEFT JOIN t_project_item pi ON pio.item_id = pi.id
        WHERE
          pi.project_id = #{projectId}
          AND pi.type = #{type }
          AND pio.`status` = 3
          AND pio.del_flag = 0
    </select>

    <!-- 获取数据 -->
    <select id="getProjectHistroyItemBom" parameterType="java.lang.Long" resultType="java.util.Map">
        select
            pi.id,
            pi.`fields`,
            pio.`fields` orderFields
        from t_project_item pi
            left join t_project_item_order pio on pi.id = pio.item_id
        where pi.type = 'PROJECT_BOM_APPLY'
        and pi.id = #{projectId}
        and pio.confirm_code is not null
        and pio.sj_time is not null
        order by pio.id desc
        limit 1
    </select>

    <!-- 更新订单状态 -->
    <update id="updateProjectItemOrderStatus" parameterType="ProjectItemOrder">
        update t_project_item_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="shStatus != null">sh_status = #{shStatus},</if>
            <if test="shStatus1 != null">sh_status1 = #{shStatus1},</if>
            <if test="currentStep != null">current_step = #{currentStep},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where item_id = #{itemId} and status = '0'
    </update>

    <update id="updateProjectExecutionStatus" parameterType="ProjectItemOrder">
        update t_project_execution set status = 2 where project_order_id in (
            select id from t_project_item_order where  item_id = #{itemId} and status = '0'
        ) and status = 0
    </update>

    <!-- 查询报价订单数据 -->
    <select id="projectItemOfferDataList" parameterType="ProjectItemOrder" resultType="java.util.Map">
            SELECT
                pio.id,
                pio.fields,
                pi.`fields` itemFields
            FROM
                t_project_item_order pio
                inner join t_project_item pi on pio.item_id = pi.id
            WHERE
                pi.project_id = (
                     SELECT
                         PROJECT_ID
                     FROM
                         t_project_item
                     WHERE
                         ID = #{itemId}
                )
              AND pi.TYPE = 'PROJECT_RQKF'
    </select>

    <!-- 查询报价订单数据 -->
    <select id="projectItemOfferDataListNew" parameterType="ProjectItemOrder" resultType="java.util.Map">
        SELECT
            pio.id,
            pio.fields,
            pi.`fields` itemFields,
            pio.audit_fields auditFields
        FROM
            t_project_item_order pio
            inner join t_project_item pi on pio.item_id = pi.id
        WHERE
            pio.id = #{id}
    </select>

    <!-- 查询报价订单数据 -->
    <select id="projectItemOfferDataListArrNew" parameterType="ProjectItemOrder" resultType="java.util.Map">
            SELECT
                pio.id,
                pio.fields,
                pi.`fields` itemFields,
                pio.audit_fields auditFields,
                pio.confirm_code confirmCode
            FROM
                t_project_item_order pio
                inner join t_project_item pi on pio.item_id = pi.id
            WHERE
                pio.id in
               <foreach collection="idArr" separator="," open="(" close=")" item="item">
                   #{item}
               </foreach>
    </select>

    <!-- 查询报价订单数据 -->
    <select id="projectItemOfferDataListFinidshNew" parameterType="ProjectItemOrder" resultType="java.util.Map">
            SELECT
                pio.id,
                pio.fields,
                pi.`fields` itemFields,
                pio.confirm_code confirmCode,
                pi.confirm_code itemConfirmCode
            FROM
                t_project_item_order pio
                inner join t_project_item pi on pio.item_id = pi.id
            WHERE
                pi.id in
                <foreach collection="idArr" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
    </select>

    <!-- 查询报价订单-BOM数据 -->
    <select id="projectItemBomDataList" parameterType="ProjectItemOrder" resultType="java.util.Map">
            SELECT
                pio.id,
                pio.fields,
                pio.confirm_code confirmCode
            FROM
                t_project_item_order pio
            WHERE
                pio.id in
                <foreach collection="idArr" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
    </select>

    <!-- 查询所有的提醒消息 -->
    <select id="selectOrderRemindList" resultType="ProjectRemind" >
    SELECT
        pio.id projectItemOrderId,
        pio.yj_time yjTime,
        pio.customer_id customerId,
        pio.create_by createBy,
        c.`name` customerName,
        pio.`code` code,
        p.id projectId,
        p.product_name productName,
        pi.type projectType,
        TIMESTAMPDIFF(DAY,pio.yj_time ,NOW()) yqDays,
        pio.user_id,
        (
            select
                GROUP_CONCAT(process_user_id)
            from
                t_project_execution_user
            where execution_id = pe.id
                  and is_audit = 0
                and process_user_id != pio.user_id
        ) process_users,
        (
            select
                GROUP_CONCAT(process_post_name)
            from
                t_project_execution_user
            where execution_id = pe.id
              and is_audit = 0
            ) process_roles
    FROM
        t_project_item_order pio
        inner join t_project_item pi on pi.id = pio.item_id
        inner join t_project p on p.id = pi.project_id
        inner join t_customer c on c.id = p.customer_id
        left join t_project_execution pe on (pe.project_order_id = pio.id and pe.current_step = '执行' and pe.status = 0)
    where
        pio.status = '0'
      and pi.`status` = '0'
      and p.`status` = '0'
    HAVING yqDays > 0
    </select>

    <!-- 获取打样单 -->
    <select id="queryNrwArrangementDataList" parameterType="ProjectItemOrder" resultType="java.util.Map">
        select pe.id,pio.code,pio.yj_time,pe.status,
               pio.fields,
               ifnull(c.short_name,c.`name`) name,
               DATE_FORMAT(pio.qw_time,'%Y-%m-%d') qw_time,
               IF( P.PROJECT_TYPE = 0, p.product_name,(select GROUP_CONCAT(bpp.product_name) from t_before_project_product bpp where FIND_IN_SET(bpp.id,pi.project_product_id))) product_name,
               c.customer_level,
               p.`level`,
               p.bhy_cf,
               p.gn_sq,
               p.gxtj,
               ifnull(pe.lab_no,p.laboratory) laboratory,
               pio.create_by,
               ifnull(p.ck_cp,"") ck_cp,
               p.zb,
               pi.difficulty,
               IFNULL(p.project_yw_name,c.customer_yw) customerAssist
            from  t_project_item_order pio
                  inner join t_project_item pi on pio.item_id = pi.id
                  inner join t_project p on pi.project_id = p.id
                  inner join t_customer c on p.customer_id = c.id
                  inner join t_project_execution pe on pio.id = pe.project_order_id and pe.current_step in ('复审','执行')
          where pi.type = 'PROJECT_NRW'
            and ((pio.sh_status = 1
              and pio.sh_status1 = 0) or pio.is_step = 1)
          and pio.status = '0'
          and pe.add_style = 0
          order by pio.id desc
    </select>

    <!-- 获取数据 -->
    <select id="selectProjectOrderConfirmCodeNum" resultType="java.lang.Integer">
        select count(id) from t_project_item_order_code where CONFIRM_CODE  = #{confirmCode} and CUSTOMER_ID != #{customerId}
    </select>

    <!-- 获取数据 本客户下是否含有相同编码 -->
    <select id="selectProjectOrderCustomerConfirmCodeNum"  resultType="java.lang.Integer">
        select count(id) from t_project_item_order_code where CONFIRM_CODE  = #{confirmCode} and CUSTOMER_ID = #{customerId}
    </select>

    <!-- 批量录入 -->
    <insert id="batchInsertConfirmCodeInfo" parameterType="java.util.Map">
        insert into t_project_item_order_code(PROJECT_ORDER_ID,PROJECT_EXECUTION_ID,CONFIRM_CODE,CREATE_TIME,CUSTOMER_ID)
        VALUES
        <foreach collection ="list" item="orderCode" separator =",">
            (#{orderCode.projectOrderId}, #{orderCode.executionId}, #{orderCode.confirmCode},now(), #{orderCode.customerId})
        </foreach >
    </insert>

    <delete id="batchDeleteConfirmCodeInfo" parameterType="TProjectExecution">
        delete from t_project_item_order_code where PROJECT_ORDER_ID = #{projectOrderId} and PROJECT_EXECUTION_ID = #{id}
    </delete>

    <!-- 获取内容物打样次数 -->
    <select id="selectNrwCount" resultType="Integer">
        select
           count(p.id)
        from
            t_project_item_order p
            inner join t_project_item pi on p.item_id = pi.id
        where p.item_id = #{itemId} and p.`status` = '3'  and pi.fields -> '$.dylx' in  ('0')
    </select>

    <!--获取三级确认编码-->
    <select id="projectItemOrderConfirmCodeByItemId" parameterType="ProjectItemOrder" resultType="ProjectItemOrder">
        SELECT
            id,
            IFNULL(confirm_code,CODE) confirm_code
        FROM
            t_project_item_order
        WHERE
            item_id = #{itemId}
            and status = '3'
            and del_flag = 0
    </select>

    <select id="selectProjectItemOrderExcelList" parameterType="ProjectItemOrder" resultType="ProjectItemOrderExcel" >
        SELECT
        pio.id,
        pio.item_id,
        pio.CODE itemOrderCode,
        pio.dyn_code,
        pio.STATUS orderStatus,
        pio.customer_id,
        pio.step,
        pio.current_step,
        pio.sh_status,
        pio.sh_status1,
        pio.confirm_code itemConfirmCode,
        pio.qw_time,
        pio.yj_time,
        pio.first_yj_time,
        pio.sj_time,
        pio.timely_rate,
        pio.amendments,
        pio.amendments1,
        pio.FIELDS,
        pio.audit_fields,
        pio.remark,
        pio.del_flag,
        pio.user_id,
        pio.create_by applyUser,
        pio.create_time applyTime,
        pio.update_time,
        ifnull(pio.update_by,"") update_by,
        pio.order_status projectItemOrderStatus,
        pio.is_revoke,
        p.`project_no` projectNo,
        p.product_name,
        ifnull(c.short_name,c.name) customer_name,
        c.customer_level,
        IFNULL(p.project_yw_name,c.customer_yw) customerAssist,
        pi.`code` projectItemCode,
        pi.item_name projectItemName,
        p.id project_id,
        pi.fields item_fields,
        pi.type project_item_type,
        pi.status item_status,
        pi.confirm_code finalConfirmCode,
        pi.item_name,
        p.item_names,
        p.order_yj_time,
        p.order_num,
        p.laboratory,
        p.item_status project_item_status,
        p.yj_sp_ddl,
        p.product_type,
        p.level projectLevel,
        p.pursose
        FROM
        t_project_item_order pio
        inner join t_project_item pi on pi.id = pio.item_id
        INNER JOIN t_project p on p.id = pi.project_id
        left join t_customer c on p.customer_id = c.id
        <where>
            and pio.del_flag = 0 and pio.project_type = 0
            <if test="searchUserId!=null">
                and p.customer_id in (SELECT customer_id FROM t_customer_assist WHERE status = 0 and user_id = #{searchUserId})
            </if>
            <if test="id != null">and pio.id = #{id}</if>
            <if test="projectId != null"> and p.id = #{projectId} </if>
            <if test="itemId != null "> and pio.item_id = #{itemId}</if>
            <if test="code != null  and code != ''"> and pio.code like concat('%', #{code}, '%') </if>
            <if test="status != null  and status != ''"> and pio.status = #{status}</if>
            <if test="confirmCode != null  and confirmCode != ''"> and pio.confirm_code like concat('%', #{confirmCode}, '%') </if>
            <if test="projectItemType != null and projectItemType != ''">and pi.type = #{projectItemType}</if>
            <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="params.beginQw != null and params.beginQw != ''">
                AND date_format(pio.qw_time,'%y%m%d') &gt;= date_format(#{params.beginQw},'%y%m%d')
            </if>
            <if test="params.endQw != null and params.endQw != ''">
                AND date_format(pio.qw_time,'%y%m%d') &lt;= date_format(#{params.endQw},'%y%m%d')
            </if>
            <if test="params.beginApply != null and params.beginApply != ''">
                AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginApply},'%y%m%d')
            </if>
            <if test="params.endApply != null and params.endApply != ''">
                AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endApply},'%y%m%d')
            </if>
            <if test="params.beginYj != null and params.beginYj != ''">
                AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
            </if>
            <if test="params.endYj != null and params.endYj != ''">
                AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
            </if>
            <if test="params.beginSj != null and params.beginSj != ''">
                AND date_format(pio.sj_time,'%y%m%d') &gt;= date_format(#{params.beginSj},'%y%m%d')
            </if>
            <if test="params.endSj != null and params.endSj != ''">
                AND date_format(pio.sj_time,'%y%m%d') &lt;= date_format(#{params.endSj},'%y%m%d')
            </if>
            <if test="params.beginCreate != null and params.beginCreate != ''">
                AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
            </if>
            <if test="params.endCreate != null and params.endCreate != ''">
                AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
            </if>
            <if test="params.beginDelayYjRange != null and params.beginDelayYjRange != ''">
                AND date_format(pio.delay_yj_time,'%y%m%d') &gt;= date_format(#{params.beginDelayYjRange},'%y%m%d')
            </if>
            <if test="params.endDelayYjRange != null and params.endDelayYjRange != ''">
                AND date_format(pio.delay_yj_time,'%y%m%d') &lt;= date_format(#{params.endDelayYjRange},'%y%m%d')
            </if>
            <if test=" params.statusArray != null and params.statusArray.length > 0 ">
                and pio.STATUS in
                <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.progressArray != null and params.progressArray.length > 0 ">
                and p.progress in
                <foreach collection="params.progressArray" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">
                and pio.current_step in
                <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">
                and p.customer_id != #{params.customerIgnoreId}
            </if>
            <if test="params.customerId != null and params.customerId != ''">
                and p.customer_id = #{params.customerId}
            </if>
            <if test="createBy != null and createBy != ''">
                and pio.create_by = #{createBy}
            </if>
        </where>
        order by pio.id desc
    </select>

    <!-- 获取订单执行记录数据 -->
    <select id="queryProjectItemOrderExecutionDataList" parameterType="java.util.List" resultType="java.util.Map">
        SELECT
            tpe.id,
            tpe.current_step,
            tpe.`fields`,
            ifnull( u.nick_name, '' ) nick_name,
            tpe.project_item_code,
            ifnull( tpe.audit_remark, '' ) auditRemark,
            tpe.remark,
            tpe.step,
            tpe.STATUS,
            DATE_FORMAT( ifnull( tpe.audit_time, tpe.update_time ), '%Y-%m-%d %H:%i:%s' ) auditTime,
            DATE_FORMAT( tpe.create_time, '%Y-%m-%d %H:%i:%s' ) createTime,
            tpe.assgin_user_name,
            tpe.`status`,
            tpe.project_order_id
        FROM
        t_project_execution tpe
        LEFT JOIN sys_user u ON tpe.process_user_id = u.user_id
        where tpe.project_order_id in
        <foreach collection="list" item="id" separator="," close=")" open="(">
            #{id}
        </foreach>
    </select>

    <!-- 获取订单反馈列表信息 -->
    <select id="queryProjectItemOrderFeedbackDataList" parameterType="java.util.List" resultType="java.util.Map">
        SELECT
        id,
        fk_source,
        fk_detail,
        item_order_id
        FROM
        t_project_item_order_feedback
        where item_order_id in
        <foreach collection="list" item="id" separator="," close=")" open="(">
            #{id}
        </foreach>
        order by id
    </select>

    <select id="selectShipmentsOrder" parameterType="ProjectItemOrder" resultType="ProjectItemOrder">
        SELECT
        o.id,
        p.product_name,
        p.project_no,
        o.confirm_code,
        pi.type projectItemType,
        o.sj_time
        FROM
        t_project_item_order o
        inner join t_project_item pi on pi.id = o.item_id and pi.`status` = 3
        inner join t_project p on p.id = pi.project_id
        where
          o.`status` = 3
        and pi.type in ('PROJECT_NRW','PROJECT_ZSCYQ','PROJECT_RQKF')
        and DATE_SUB(CURDATE(), INTERVAL 7 DAY) &lt;= date(o.sj_time)
        and p.id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <!-- 根据实验室编码获取价格 -->
    <select id="prjectItemOrderOfferNrwPriceInfo" parameterType="ProjectItemOrder" resultType="java.util.Map">
        SELECT
            id,price,date_format(create_time,'%Y-%m-%d') createTime
        FROM
            t_project_nrw_price
        WHERE
            LAB_CODE = #{confirmCode}
        ORDER BY
            id DESC limit 1
    </select>

    <!-- 根据指定类型获取实验室编码-->
    <select id="projectItemOrderBcBomDataList" parameterType="ProjectItemOrder" resultType="java.util.Map">
        SELECT
        tpio.`fields`,
        tpio.audit_fields auditFields,
        tpi.`fields` itemFields,
        tpi.type,
        tpio.confirm_code confirmCode,
        tpio.id,
        tpi.confirm_code itemConfirmCode,
        tpi.id itemId
        FROM
        t_project_item_order tpio
        INNER JOIN t_project_item tpi ON tpio.item_id = tpi.id
        where tpio.project_id = #{projectId}
        <if test="projectItemType==1">
            and tpio.status = '3'
            and tpi.type in ('PROJECT_RQKF','PROJECT_BOM_APPLY')
        </if>
        <if test="projectItemType==2">
            and tpi.status = '3'
            and tpi.type = 'PROJECT_RQKF'
        </if>
        <if test="projectItemType==3">
            and tpio.status = '3'
            and tpi.type = 'PROJECT_OFFER'
            and tpi.fields->>'$.bjlx' = 'PROJECT_RQBCBJ'
        </if>
    </select>

    <!-- 获取报价列表数据 -->
    <select id="projectItemOfferBcDataList" parameterType="ProjectItemOrder" resultType="java.util.Map">
        SELECT
            pio.id,
            pio.fields
        FROM
            t_project_item_order pio
        WHERE
            pio.id in
            <foreach collection="idArr" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
    </select>

    <!-- 根据工位图确定编码 -->
    <select id="projectItemOrderSckxxGwtDataList" parameterType="ProjectItemOrder" resultType="java.util.Map">
        SELECT
            tpio.`fields`,
            tpio.audit_fields auditFields,
            tpi.`fields` itemFields,
            tpi.type,
            tpio.confirm_code confirmCode,
            tpio.id,
            tpi.confirm_code itemConfirmCode,
            tpi.id itemId
        FROM
            t_project_item_order tpio
            INNER JOIN t_project_item tpi ON tpio.item_id = tpi.id
        where tpio.project_id = #{projectId}
        and tpio.status = '3'
        and tpi.type = 'PROJECT_SCKXXPG'
        and tpi.fields->>'$.sqlx' = '3'
    </select>

    <!-- 获取报价列表数据 -->
    <select id="projectItemOfferJgfDataList" parameterType="ProjectItemOrder" resultType="java.util.Map">
        SELECT
        pio.id,
        pio.fields,
        pio.audit_fields auditFields,
        pio.confirm_code confirmCode
        FROM
        t_project_item_order pio
        WHERE
        pio.id in
        <foreach collection="idArr" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="queryProjectItemOrderDataList" resultType="ProjectItemOrder" parameterType="java.lang.String">
        SELECT
            tpio.id,
            tpio.sj_time createTime,
            tpio.project_id projectId
        FROM
            t_project_item_order tpio
            INNER JOIN t_project_item tpi ON tpio.item_id = tpi.id
        WHERE
            tpi.type = #{itemType}
            and tpio.status = '3'
            and tpio.feed_status in (0,1)
        ORDER BY
            tpio.id DESC
    </select>

    <select id="selectProjectItemOrderFeedInfo" parameterType="ProjectItemOrder" resultType="ProjectItemOrderFeedback">
        select id,create_time createTime from t_project_item_order_feedback where item_order_id = #{id} order by id  limit 1
    </select>

    <!--获取订单反馈信息-->
    <select id="queryProjectItemOrderFeedData" resultType="ProjectItemOrder" parameterType="java.lang.Long">
        select id,
               sj_time,
               is_feed,
               project_id,
               if(pio.is_feed = 1,
                  (select create_time
                   from t_project_item_order_feedback
                   where item_order_id = pio.id
                   order by id desc limit 1),null) feedTime
        from t_project_item_order pio
        where item_id = #{orderItemId}
          and `status` = '3'
        order by id desc limit 1
    </select>

    <select id="selectProjectItemOrderLiteList" parameterType="ProjectItemOrder" resultType="ProjectItemOrder">
        SELECT
        o.id,
        o.`code`,
        o.create_time,
        o.sj_time,
        pi.type projectItemType,
        o.`fields`
        FROM
        t_project_item_order o
        INNER JOIN t_project_item pi ON pi.id = o.item_id
        <where>
            <if test="projectId != null">and pi.project_id = #{projectId}</if>
            <if test="projectItemType != null and projectItemType != ''">
                and pi.type = #{projectItemType}
            </if>
        </where>
        order by o.id desc
    </select>

    <update id="updateProjectItemOrderMergeOrderFields" parameterType="ProjectItemOrder">
        update t_project_item_order set merge_fields = #{mergeFields} where id = #{id}
    </update>

    <select id="queryProjectMergeItemOrderDataList" resultType="ProjectItemOrder" parameterType="java.lang.String">
        SELECT
            tpi.id itemId,
            tpio.id,
            tpi.type projectItemType,
            tpio.`fields`,
            tpi.`fields` itemFields
        FROM
            t_project_item_order tpio
                INNER JOIN t_project_item tpi ON tpio.item_id = tpi.id
        WHERE
            tpi.type = #{type}
        ORDER BY
            tpio.id DESC
    </select>

    <!--获取送检备案数据-->
    <select id="queryProjectSjbaOrderCount" resultType="java.lang.Integer">
        SELECT count(*)
        from t_project_item_order tpio
        INNER JOIN t_project_item tpi on tpio.item_id = tpi.id
        WHERE tpi.type = 'PROJECT_INSPECTION_AND_FILING'
        and tpi.fields -> '$.applyType'  in  (0,1,4)
        and tpio.is_sync = 0
        and tpio.add_style = 0
        and tpio.id >= 55412
    </select>

    <!-- 获取送检备案数据 -->
    <select id="queryProjectSjbaOrderDataList"  resultType="com.ruoyi.project.domain.excel.ProjectSjbaDataExport">
        SELECT
            tpio.id,
            tpio.CODE,
            tpio.create_time createTime,
            tpio.project_id projectId,
            tpio.customer_id customerId,
            tpio.STATUS,
            tpio.step,
            tpio.current_step currentStep,
            tpi.FIELDS
        FROM
            t_project_item_order tpio
            INNER JOIN t_project_item tpi ON tpio.item_id = tpi.id
        WHERE
            tpi.type = 'PROJECT_INSPECTION_AND_FILING'
          AND tpi.FIELDS -> '$.applyType' IN ( 0, 1, 4 )
          AND tpio.is_sync = 0
          AND tpio.add_style = 0
          AND tpio.id >= 55412
        ORDER BY
            tpio.id DESC
        LIMIT #{start}, #{size}
    </select>

    <!--删除数据-->
    <delete id="deleteProjectSjbaData">
        delete from t_project_proceess_sjba
        where project_order_id in
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>

    <!--更新状态-->
    <update id="updateProjectItemOrderSync" parameterType="com.ruoyi.project.domain.excel.ProjectSjbaDataExport">
        update t_project_item_order set is_sync = 1 where  id = #{id}
    </update>

    <!--批量保存数据-->
    <insert id="batchInsertProjectSjbaData">
        INSERT INTO `enow_spa`.`t_project_proceess_sjba`
            (`project_order_id`, `project_order_no`, `apply_type`, `project_id`, `customer_id`,
             `order_status`, `order_step`,
             `order_current_step`, `order_create_time`, `create_time`,
             `first_submit_time`, `first_audit_time`, `first_audit_status`, `first_hs`,
             `yanfa_submit_time`, `yanfa_audit_time`, `yanfa_audit_status`, `yanfa_hs`,
             `yanfazhichi_submit_time`, `yanfazhichi_audit_time`, `yanfazhichi_audit_status`, `yanfazhichi_hs`,
             `shengji_submit_time`, `shengji_audit_time`, `shengji_audit_status`, `shengji_hs`,
             `process_submit_time`, `process_audit_time`, `process_audit_status`, `process_hs`,
              `yanfa_fields`,`yanfazhichi_fields`,`shengji_fields`
             )
             VALUES
            <foreach collection="dataList" separator="," item="item">
                (#{item.id}, #{item.code}, #{item.applyType}, #{item.projectId}, #{item.customerId}, #{item.status}, #{item.step},
                 #{item.currentStep}, #{item.createTime}, now(), #{item.firstSubmitTime}, #{item.firstAuditTime},
                 #{item.firstAuditStatus}, #{item.firstHs},
                 #{item.yanfaSubmitTime}, #{item.yanfaAuditTime}, #{item.yanfaAuditStatus}, #{item.yanfaHs},
                 #{item.yanfazhichiSubmitTime}, #{item.yanfazhichiAuditTime}, #{item.yanfazhichiAuditStatus}, #{item.yanfazhichiHs},
                 #{item.shengjiSubmitTime}, #{item.shengjiAuditTime}, #{item.shengjiAuditStatus}, #{item.shengjiHs},
                 #{item.processSubmitTime}, #{item.processAuditTime}, #{item.processAuditStatus}, #{item.processHs},
                 #{item.yanfaFields}, #{item.yanfazhichiFields}, #{item.shengjiFields}
                 )
            </foreach>
    </insert>

    <select id="queryProjectSjbaSecondOrderDataList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            tpio.id,
            tpio.fields,
            p.project_no projectNo,
            p.before_project_no beforeProjectNo
        FROM
            t_project_item_order tpio
            INNER JOIN t_project_item tpi ON tpio.item_id = tpi.id
            INNER JOIN t_project p ON tpio.project_id = p.id
        WHERE
            tpi.type = 'PROJECT_INSPECTION_AND_FILING'
          AND tpi.FIELDS -> '$.applyType' IN ( 0, 1, 4 )
          AND tpio.step = 2
          AND tpio.STATUS = '0'
        ORDER BY
            tpio.id DESC
    </select>

    <select id="queryProjectItemSjOrderDataList" parameterType="java.lang.Long" resultType="TProjectExecution">
        SELECT
            pe.id,
            pe.process_user_id,
            pe.`fields`,
            tpio.`fields` orderFields,
            pe.audit_remark
        FROM
            t_project_item_order tpio
            INNER JOIN t_project_item pio ON tpio.item_id = pio.id
            INNER JOIN t_project_execution pe ON tpio.id = pe.project_order_id
        WHERE
            pe.project_item_code = 'PROJECT_INSPECTION_AND_FILING'
          AND pe.STATUS = 1
          AND tpio.project_id = #{projectId}
          AND pe.merge_key = 'yanfazhichi'
          AND pio.FIELDS -> '$.applyType' = '0'
        ORDER BY
            pe.id DESC
            LIMIT 1
    </select>

    <select id="queryProjectItemGongyiOrderDataList" parameterType="java.lang.Long" resultType="TProjectExecution">
        SELECT
            pe.id,
            pe.process_user_id,
            pe.`fields`,
            tpio.`fields` orderFields,
            pe.audit_remark,
            pe.STATUS
        FROM
            t_project_item_order tpio
            INNER JOIN t_project_item pio ON tpio.item_id = pio.id
            INNER JOIN t_project_execution pe ON tpio.id = pe.project_order_id
        WHERE
            pe.project_item_code = 'PROJECT_INSPECTION_AND_FILING'
          AND pe.STATUS IN ( 0, 1 )
          AND tpio.project_id = #{projectId}
          AND pe.merge_key = 'shengji'
          AND pio.FIELDS -> '$.applyType' = '0'
        ORDER BY
            pe.id DESC
            LIMIT 1
    </select>

    <update id="updateProjectItemOrderApplyInfo" parameterType="java.util.Map">
        update t_project_item_order
        set del_flag = 0
        <if test="type==3">
            ,is_apply_filling = ifnull(is_apply_filling,0) +1
        </if>
        <if test="type==2">
            ,is_apply_hh = ifnull(is_apply_hh,0) +1
        </if>
        <if test="type==1">
            ,is_apply_cf = ifnull(is_apply_cf,0) +1
        </if>
        where id = #{id}
    </update>

    <select id="selectLabCodes" resultType="ProjectItemOrder" parameterType="ProjectItemOrder" >
        SELECT
            o.id,
            pi.project_id,
            p.project_no,
            p.product_name,
            o.confirm_code,
            pi.confirm_code selectedCode,
            pi.item_name,
            pi.type projectItemType,
            p.item_names,
            o.sj_time
        FROM
            t_project_item_order o
            INNER JOIN t_project_item pi ON pi.id = o.item_id
            INNER JOIN t_project p ON p.id = pi.project_id
        WHERE
            pi.type = 'PROJECT_NRW'
            and p.`status` ='0'
            and o.`status` = '3'
            and pi.fields->> '$.dylx' = '0'
            and p.customer_id = #{customerId}
    </select>

    <select id="queryProjectItemOrderExecutionDataInfo" resultType="TProjectExecution" parameterType="java.util.Map">
        SELECT
            pe.id,
            pe.step,
            pe.current_step,
            pio.CODE itemOrderCode
        FROM
            t_project_execution pe
            INNER JOIN t_project_item_order pio ON pe.project_order_id = pio.id
        WHERE
            pe.project_order_id = #{itemOrderId}
            and pe.status = 0
            AND merge_key = #{mergeKey}
        ORDER BY
            pe.id DESC
            LIMIT 1
    </select>

    <select id="queryProjectItemOrderExecutionOtherDataInfo" resultType="TProjectExecution" parameterType="java.util.Map">
        SELECT
            pe.id,
            pe.step,
            pe.current_step,
            pio.CODE itemOrderCode
        FROM
            t_project_execution pe
            INNER JOIN t_project_item_order pio ON pe.project_order_id = pio.id
        WHERE
            pe.project_order_id = #{itemOrderId}
            and pe.status = 0
            AND pe.step = #{step}
        ORDER BY
            pe.id DESC
            LIMIT 1
    </select>

    <select id="queryProjectItemOrderOfferDataList" resultType="ProjectItemOrder" parameterType="ProjectItemOrder">
        SELECT
            pio.id,
            p.project_no projectNo,
            pi.fields->>'$.bjlx' projectItemType,
            IF
                (
                    P.PROJECT_TYPE = 0,
                    p.product_name,(
                        SELECT
                            GROUP_CONCAT( bpp.product_name )
                        FROM
                            t_before_project_product bpp
                        WHERE
                            FIND_IN_SET( bpp.id, pi.project_product_id ))) product_name,
            pio.code,
            pio.create_time,
            c.id customerId,
            c.NAME customerName,
            pio.fields,
            pi.fields itemFields,
            c.customer_level customerLevel,
            pio.bc_reply_time,
            pio.gongyi_reply_time,
            pio.bc_reply_user,
            pio.gongyi_reply_user,
            pio.current_step,
            pi.status itemStatus,
            pio.create_time,
            pio.project_id,
            pi.project_product_id,
            pio.project_type,
            IFNULL(p.project_yw_name,c.customer_yw)  customerYw,
            pio.create_by createBy
        FROM
            t_project_item_order pio
                INNER JOIN t_project_item pi ON pio.item_id = pi.id
                INNER JOIN t_project p ON pio.project_id = p.id
                INNER JOIN t_customer c ON pio.customer_id = c.id
          where pi.type = 'PROJECT_OFFER' and pio.add_style = '0'
          <if test="projectItemType!=null and projectItemType!=''">
             and pi.fields->>'$.bjlx' = #{projectItemType}
          </if>
        <if test="params.shStatus != null and params.shStatus.length > 0 ">
            AND pio.id in (
            SELECT pe.PROJECT_ORDER_ID
            FROM t_project_execution pe
            INNER JOIN t_project_execution_user peu
            WHERE  pe.project_item_code in ('PROJECT_NRWBJ','PROJECT_CHPBJ') and (peu.process_user_id = #{processUserId}
            OR peu.process_post_name IN (
            SELECT  r.role_name   FROM  sys_user u
            INNER JOIN sys_user_role ur ON u.user_id = ur.user_id
            INNER JOIN sys_role r ON ur.role_id = r.role_id
            WHERE
            u.user_id = #{processUserId}
            ))
            <if test="params.shStatus != null and params.shStatus.length > 0 ">
                and pe.status in
                <foreach collection="params.shStatus" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
             </if>
            )
        </if>
          <if test="code!=null and code!=''">
             and pio.code like concat(#{code},'%')
          </if>
          <if test="projectNo!=null and projectNo!=''">
             and p.project_no like concat(#{projectNo},'%')
          </if>
          <if test="createBy!=null and createBy!=''">
             and pio.create_by like concat('%',#{createBy},'%')
          </if>
        <if test="params.currentStepArr != null and params.currentStepArr.length > 0">
            and pio.current_step in
            <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
          <if test="productName!=null and productName!=''">
             and IF
              (
              P.PROJECT_TYPE = 0,
              p.product_name,(
              SELECT
              GROUP_CONCAT( bpp.product_name )
              FROM
              t_before_project_product bpp
              WHERE
              FIND_IN_SET( bpp.id, pi.project_product_id )))
              like concat('%',#{productName},'%')
          </if>
          <if test="customerId!=null">
             and pio.customer_id = #{customerId}
          </if>
          <if test="params.beginCreate != null and params.beginCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
          </if>
          <if test="params.endCreate != null and params.endCreate != ''">
            AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
          </if>
          AND pio.del_flag = 0
         order by pio.bc_reply_time desc,pio.gongyi_reply_time desc,pio.id desc
    </select>

    <sql id="conditionSql" >
        and pio.del_flag = 0
        <if test="searchUserId!=null">
            and p.customer_id in (
            SELECT customer_id
            FROM t_customer_assist
            WHERE status = 0
            and user_id = #{searchUserId}
            )
        </if>
        <if test="projectItemType != null and projectItemType != ''" >
            and pi.type = #{projectItemType}
        </if>
        <if test="bcReplyStatus != null and bcReplyStatus !=  ''" >
            <choose>
                <when test="bcReplyStatus == '0'">and pio.bc_reply_time is null </when>
                <when test="bcReplyStatus == '1'">and pio.bc_reply_time is not null </when>
            </choose>
        </if>
        <if test="id != null">and pio.id = #{id}</if>
        <if test="projectId != null"> and p.id = #{projectId} </if>
        <if test="itemId != null "> and pio.item_id = #{itemId}</if>
        <if test="currentStep != null  and currentStep != ''"> and pio.current_step like concat('%', #{currentStep}, '%') </if>
        <if test="code != null  and code != ''"> and pio.code like concat('%', #{code}, '%') </if>
        <if test="status != null  and status != ''"> and pio.status = #{status}</if>
        <if test="confirmCode != null  and confirmCode != ''"> and pio.confirm_code like concat('%', #{confirmCode}, '%') </if>
        <if test="orderTypes != null and orderTypes.size() > 0">
            and pi.type in
            <foreach collection="orderTypes" item="orderType" separator="," open="(" close=")" >
                #{orderType}
            </foreach>
        </if>
        <if test="productName != null  and productName != ''">and p.product_name like concat('%', #{productName}, '%')</if>
        <if test="params.beginQw != null and params.beginQw != ''">
            AND date_format(pio.qw_time,'%y%m%d') &gt;= date_format(#{params.beginQw},'%y%m%d')
        </if>
        <if test="params.endQw != null and params.endQw != ''">
            AND date_format(pio.qw_time,'%y%m%d') &lt;= date_format(#{params.endQw},'%y%m%d')
        </if>
        <if test="params.beginApply != null and params.beginApply != ''">
            AND date_format(pio.create_time,'%y%m%d') &gt;= date_format(#{params.beginApply},'%y%m%d')
        </if>
        <if test="params.endApply != null and params.endApply != ''">
            AND date_format(pio.create_time,'%y%m%d') &lt;= date_format(#{params.endApply},'%y%m%d')
        </if>
        <if test="params.beginDelayYjRange != null and params.beginDelayYjRange != ''">
            AND date_format(pio.delay_yj_time,'%y%m%d') &gt;= date_format(#{params.beginDelayYjRange},'%y%m%d')
        </if>
        <if test="params.endDelayYjRange != null and params.endDelayYjRange != ''">
            AND date_format(pio.delay_yj_time,'%y%m%d') &lt;= date_format(#{params.endDelayYjRange},'%y%m%d')
        </if>
        <if test="params.beginYj != null and params.beginYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginYj},'%y%m%d')
        </if>
        <if test="params.endYj != null and params.endYj != ''">
            AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endYj},'%y%m%d')
        </if>
        <if test="params.beginSj != null and params.beginSj != ''">
            AND date_format(pio.sj_time,'%y%m%d') &gt;= date_format(#{params.beginSj},'%y%m%d')
        </if>
        <if test="params.endSj != null and params.endSj != ''">
            AND date_format(pio.sj_time,'%y%m%d') &lt;= date_format(#{params.endSj},'%y%m%d')
        </if>
        <if test="params.beginCreate != null and params.beginCreate != ''">
            AND date_format(pio.yj_time,'%y%m%d') &gt;= date_format(#{params.beginCreate},'%y%m%d')
        </if>
        <if test="params.endCreate != null and params.endCreate != ''">
            AND date_format(pio.yj_time,'%y%m%d') &lt;= date_format(#{params.endCreate},'%y%m%d')
        </if>
        <if test=" params.statusArray != null and params.statusArray.length > 0 ">
            and pio.STATUS in
            <foreach collection="params.statusArray" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test=" params.progressArray != null and params.progressArray.length > 0 ">
            and p.progress in
            <foreach collection="params.progressArray" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test=" params.currentStepArr != null and params.currentStepArr.length > 0 ">
            and pio.current_step in
            <foreach collection="params.currentStepArr" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="params.customerIgnoreId != null and params.customerIgnoreId != ''">
            and p.customer_id != #{params.customerIgnoreId}
        </if>
        <if test="params.customerId != null and params.customerId != ''">
            and p.customer_id = #{params.customerId}
        </if>
        <if test="createBy != null and createBy != ''">
            and pio.create_by = #{createBy}
        </if>
        <if test="projectIds != null and projectIds.size() > 0" >
            and pi.project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," >
                #{projectId}
            </foreach>
        </if>
        <if test="idArr != null and idArr.size() > 0" >
            and pio.id in
            <foreach collection="idArr" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="projectNo != null and projectNo!='' "> and p.project_no = #{projectNo}</if>
    </sql>

    <select id="selectBcDevProjectOrderList" parameterType="ProjectItemOrder" resultType="ProjectItemOrder" >
        <include refid="selectProjectItemOrderVo"/>
        <where>
            (pi.type = 'PROJECT_RQKF' or (pi.type = 'PROJECT_OFFER' and pi.fields->>'$.bjlx' in ('PROJECT_CHPBJ','PROJECT_RQBCBJ') ))
            and pio.add_style = 0
            <if test="processUserId != null">
                AND pio.id in (
                    SELECT
                        pe.PROJECT_ORDER_ID
                    FROM t_project_execution pe
                    INNER JOIN t_project_execution_user peu on pe.id = peu.execution_id
                    WHERE  (
                        peu.process_user_id = #{processUserId}
                        OR peu.process_post_name IN (
                            SELECT
                                r.role_name
                            FROM sys_user u
                            INNER JOIN sys_user_role ur ON u.user_id = ur.user_id
                            INNER JOIN sys_role r ON ur.role_id = r.role_id
                            WHERE
                            u.user_id = #{processUserId}
                        )
                    )
                    <if test="executionStatus != null">
                        and pe.status = #{executionStatus}
                    </if>
                )
            </if>
            <include refid="conditionSql" />
        </where>
        order by pio.id desc
    </select>

    <select id="selectSopProjectOrderList" parameterType="ProjectItemOrder" resultType="ProjectItemOrder" >
        <include refid="selectProjectItemOrderVo"/>
        <where>
             ((pi.type = 'PROJECT_OFFER' and pi.fields->>'$.bjlx' in ('PROJECT_CHPBJ') )
            or (pi.type = 'PROJECT_SCKXXPG')
            or (pi.type = 'PROJECT_BOM_APPLY'))
            and pio.add_style = 0
            <if test="processUserId != null">
                AND pio.id in (
                    SELECT
                    pe.PROJECT_ORDER_ID
                    FROM t_project_execution pe
                    INNER JOIN t_project_execution_user peu on pe.id = peu.execution_id
                    WHERE  (
                        peu.process_user_id = #{processUserId}
                        OR peu.process_post_name IN (
                            SELECT
                            r.role_name
                            FROM sys_user u
                            INNER JOIN sys_user_role ur ON u.user_id = ur.user_id
                            INNER JOIN sys_role r ON ur.role_id = r.role_id
                            WHERE
                            u.user_id = #{processUserId}
                        )
                    )
                    <if test="executionStatus != null">
                        and pe.status = #{executionStatus}
                    </if>
                )
            </if>
            <include refid="conditionSql" />
        </where>
        order by pio.id desc
    </select>

    <select id="queryProjectItemOrderFormulaDataList" resultType="com.alibaba.fastjson.JSONObject" parameterType="java.util.Map">
        SELECT
            poof.id,
            poof.lab_no labNo,
            poof.formula_code formulaCode
        FROM
            t_project_offer_order_formula poof
        WHERE
            poof.project_order_id = #{projectItemOrderId}
          <if test="isMain!=null"> AND poof.is_main = #{isMain}</if>
    </select>

    <select id="queryProjectItemOrderExecutionList" parameterType="java.lang.Long" resultType="TProjectExecution">
        select id,status from t_project_execution where project_order_id = #{projectOrderId} and step = 2
        order by id desc limit 1
    </select>

    <select id="queryProjectItemOrderExecutionByMergeList" parameterType="java.util.Map" resultType="TProjectExecution">
        select id,status from t_project_execution where project_order_id = #{projectOrderId} and step = 2
        and merge_key = #{mergeKey}
        order by id desc limit 1
    </select>

    <select id="queryProjectOfferFormulaDataList" resultType="ProjectOfferOrder" parameterType="java.lang.Long">
        SELECT
            poof.id,
            poof.project_order_id projectOrderId,
            poof.lab_no labNo,
            poof.gzl gzl,
            poof.gzl_unit gzlUnit,
            poof.ygl ygl,
            poof.ygl_unit yglUnit,
            poof.order_num orderNum,
            poof.code productCode,
            poof.formula_code formulaCode,
            poof.product_name productName,
            poof.project_name projectName,
            poof.moq_price moqPrice,
            poof.order_price orderPrice,
            poof.out_single_price outSinglePrice,
            poof.out_kg_price outKgPrice,
            poof.loss_price lossPrice,
            poof.profit_rate profitRate,
            poof.moq_loss_code moqLossCode,
            poof.order_loss_code orderLossCode,
            poof.moq_price_bc moqPriceBc,
            poof.order_price_bc orderPriceBc,
            poof.out_single_price_bc outSinglePriceBc,
            poof.bc_rate bcRate,
            poof.loss_price_bc lossPriceBc,
            poof.profit_rate_bc profitRateBc,
            poof.moq_loss_code_bc moqLossCodeBc,
            poof.order_loss_code_bc orderLossCodeBc,
            poof.remark,
            poof.hour_cost_single_price hourCostSinglePrice,
            poof.fill_price fillPrice,
            poof.fill_cost_price fillCostPrice,
            poof.fill_out_price fillOutPrice,
            poof.profit_rate_fill profitRateFill,
            poof.archive_code archiveCode,
            poof.nums,
            poof.productivity,
            poof.hour_costs hourCosts,
            poof.fill_remark fillRemark,
            poof.bc_remark bcRemark,
            poof.formula_id formulaId,
            pio.code orderCode,
            poof.formula_data_list formulaDataList,
            poof.freight_money freightMoney,
            poof.goods_rate goodsRate,
            poof.strategic_price strategicPrice,
            poof.offer_remark offerRemark,
            poof.formula_count formulaCount,
            poof.create_time,
            poof.create_by,
            poof.update_time,
            poof.update_by,
            pio.product_tabs productTabs
        FROM
            t_project_offer_order_formula poof
            inner join t_project_item_order pio on poof.project_order_id = pio.id
        WHERE
            poof.PROJECT_ORDER_ID = #{projectOrderId}
        ORDER BY
            poof.id DESC
    </select>

    <select id="queryProjectOfferChangeRecordDataList" resultType="ProjectOfferOrder" parameterType="java.lang.Long">
        SELECT
            poof.id,
            poof.project_order_id projectOrderId,
            poof.lab_no labNo,
            poof.formula_code formulaCode,
            poof.product_name productName,
            poof.moq_price moqPrice,
            poof.order_price orderPrice,
            poof.out_single_price outSinglePrice,
            poof.loss_price lossPrice,
            poof.profit_rate profitRate,
            poof.moq_loss_code moqLossCode,
            poof.order_loss_code orderLossCode,
            poof.moq_price_bc moqPriceBc,
            poof.order_price_bc orderPriceBc,
            poof.out_single_price_bc outSinglePriceBc,
            poof.loss_price_bc lossPriceBc,
            poof.profit_rate_bc profitRateBc,
            poof.moq_loss_code_bc moqLossCodeBc,
            poof.order_loss_code_bc orderLossCodeBc,
            poof.remark,
            poof.fill_price fillPrice,
            poof.fill_out_price fillOutPrice,
            poof.profit_rate_fill profitRateFill,
            poof.archive_code archiveCode,
            poof.nums,
            poof.productivity,
            poof.hour_costs hourCosts,
            poof.fill_remark fillRemark,
            poof.bc_remark bcRemark,
            pio.code orderCode,
            poof.create_by,
            poof.create_time
        FROM
            t_project_offer_order_formula_record poof
            inner join t_project_item_order pio on poof.project_order_id = pio.id
        WHERE
            poof.PROJECT_ORDER_ID = #{projectOrderId}
        ORDER BY
            poof.id DESC
    </select>

    <select id="queryMultiProjectOfferChangeRecordDataList" resultType="ProjectOfferOrder" parameterType="ProjectOfferOrder">
        SELECT
            poof.id,
            poof.project_order_id projectOrderId,
            poof.lab_no labNo,
            poof.formula_code formulaCode,
            poof.product_name productName,
            poof.moq_price moqPrice,
            poof.order_price orderPrice,
            poof.out_single_price outSinglePrice,
            poof.loss_price lossPrice,
            poof.profit_rate profitRate,
            poof.moq_loss_code moqLossCode,
            poof.order_loss_code orderLossCode,
            poof.moq_price_bc moqPriceBc,
            poof.order_price_bc orderPriceBc,
            poof.out_single_price_bc outSinglePriceBc,
            poof.loss_price_bc lossPriceBc,
            poof.profit_rate_bc profitRateBc,
            poof.moq_loss_code_bc moqLossCodeBc,
            poof.order_loss_code_bc orderLossCodeBc,
            poof.remark,
            poof.fill_price fillPrice,
            poof.fill_out_price fillOutPrice,
            poof.profit_rate_fill profitRateFill,
            poof.archive_code archiveCode,
            poof.nums,
            poof.productivity,
            poof.hour_costs hourCosts,
            poof.fill_remark fillRemark,
            poof.bc_remark bcRemark,
            pio.code orderCode,
            poof.create_by,
            poof.create_time
        FROM
            t_project_offer_order_formula_record poof
            inner join t_project_item_order pio on poof.project_order_id = pio.id
        WHERE
            poof.PROJECT_ORDER_ID = #{projectOrderId} and poof.code = #{productCode} and poof.order_num = #{orderNum}
        ORDER BY
            poof.id DESC
    </select>

    <select id="queryProjectOfferFormulaDataDetailById" resultType="com.alibaba.fastjson.JSONObject" parameterType="java.lang.Long">
        SELECT
            poof.id,
            poof.project_order_id projectOrderId,
            poof.lab_no labNo,
            poof.formula_code formulaCode,
            poof.product_name productName,
            poof.moq_price moqPrice,
            poof.order_price orderPrice,
            poof.out_single_price outSinglePrice,
            poof.loss_price lossPrice,
            poof.profit_rate profitRate,
            poof.moq_loss_code moqLossCode,
            poof.order_loss_code orderLossCode,
            poof.moq_price_bc moqPriceBc,
            poof.order_price_bc orderPriceBc,
            poof.out_single_price_bc outSinglePriceBc,
            poof.loss_price_bc lossPriceBc,
            poof.profit_rate_bc profitRateBc,
            poof.moq_loss_code_bc moqLossCodeBc,
            poof.order_loss_code_bc orderLossCodeBc,
            poof.remark,
            poof.fill_price fillPrice,
            poof.fill_out_price fillOutPrice,
            poof.profit_rate_fill profitRateFill,
            poof.archive_code archiveCode,
            poof.nums,
            poof.productivity,
            poof.hour_costs hourCosts,
            poof.fill_remark fillRemark,
            poof.bc_remark bcRemark,
            pio.code orderCode
        FROM
            t_project_offer_order_formula poof
            inner join t_project_item_order pio on poof.project_order_id = pio.id
        WHERE
            poof.id = #{id}
    </select>

    <select id="queryProjectOfferFormulaDataDetailsList" resultType="ProjectOfferOrder" parameterType="java.lang.Long">
        SELECT
            id,
            project_order_id projectOrderId,
            formula_id formulaId,
            lab_no labNo,
            ygl,
            ygl_unit yglUnit,
            gzl gzl,
            gzl_unit gzlUnit,
            formula_code formulaCode,
            product_name productName,
            moq_price moqPrice,
            order_price orderPrice,
            out_single_price outSinglePrice,
            loss_price lossPrice,
            profit_rate profitRate,
            moq_loss_code moqLossCode,
            order_loss_code orderLossCode,
            remark
        FROM
            t_project_offer_order_formula_detail
        WHERE
            PROJECT_ORDER_ID = #{projectOrderId}
        ORDER BY
            id DESC
    </select>

    <select id="queryMultiProjectOfferFormulaDetailsDataList" resultType="ProjectOfferOrder" parameterType="ProjectOfferOrder">
        SELECT
            id,
            project_order_id projectOrderId,
            formula_id formulaId,
            lab_no labNo,
            ygl,
            ygl_unit,
            gzl gzl,
            gzl_unit gzlUnit,
            order_num,
            project_name,
            formula_code formulaCode,
            product_name productName,
            moq_price moqPrice,
            order_price orderPrice,
            out_single_price outSinglePrice,
            loss_price lossPrice,
            profit_rate profitRate,
            moq_loss_code moqLossCode,
            order_loss_code orderLossCode,
            remark
        FROM
            t_project_offer_order_formula_detail poof
        WHERE
            poof.PROJECT_ORDER_ID = #{projectOrderId} and poof.code = #{productCode} and poof.order_num = #{orderNum}
        ORDER BY
            poof.id DESC
    </select>

    <update id="updateProjectItemOrderPackagingDatas" parameterType="ProjectItemOrder">
        update t_project_item_order set packaging_material_datas = #{packagingMaterialDatas}  where id = #{id}
    </update>

    <update id="updateProjectOfferOrderPackagingDatas" parameterType="ProjectItemOrder">
        update t_project_offer_order_formula set packaging_material_datas = #{packagingMaterialDatas},packaging_material_datas_type = #{packagingMaterialDatasType}  where id = #{projectOfferOrderId}
    </update>

    <select id="queryProjectOfferPackagingMaterialDataList" resultType="ProjectBc" parameterType="java.lang.Long">
        SELECT
            b.id,
            b.resource_type,
            b.op_type,
            b.material_id,
            b.bc_code,
            b.type,
            b.material_type,
            b.zrq_type,
            b.project_id,
            b.CODE,
            b.erp_code,
            b.NAME,
            b.spec,
            b.dosage,
            b.base,
            b.bzlx,
            b.bzcl,
            b.length,
            b.width,
            b.height,
            b.model,
            b.attribute,
            b.ecgy,
            b.imgs,
            b.files,
            b.supplier_id,
            b.dev_status,
            b.md001,
            b.config_array,
            b.stage,
            b.del_flag,
            b.create_by,
            b.create_time,
            b.update_by,
            b.update_time,
            b.remark,
            ob.nums,
            obc.price,obc.price_array,
            obc.price_date
        FROM
           t_project_bc b
           inner join t_project_order_bc ob on ob.project_bc_id = b.id
           LEFT JOIN t_project_order_bc_reply obc on obc.project_bc_id = b.id
        where ob.project_order_id = #{projectOrderId}
    </select>

    <select id="selectConfirmCodeOrderList" resultType="ProjectItemOrder" >
        SELECT
            o.id,
            pi.project_id,
            p.project_no,
            p.product_name,
            o.confirm_code,
            pi.item_name,
            pi.type projectItemType,
            p.item_names,
            o.sj_time
        FROM
            t_project_item_order o
            INNER JOIN t_project_item pi ON pi.id = o.item_id
            INNER JOIN t_project p ON p.id = pi.project_id
        WHERE
            pi.type = 'PROJECT_NRW'
            and o.confirm_code is not null
            and p.id = #{projectId}
    </select>

    <select id="queryProjectOfferPackagingMaterialDataObj" resultType="ProjectOfferOrder" parameterType="ProjectOfferOrder">
        select id,project_order_id,packaging_material_datas,packaging_material_datas_type,formula_data_list from t_project_offer_order_formula where id = #{id}
    </select>

    <select id="selectProjectItemOrderProductTabsById" resultType="ProjectItemOrder" parameterType="java.lang.Long">
         SELECT
            pio.id,
            p.project_no projectCode,
            pio.product_tabs,
            IF
                (
                    P.PROJECT_TYPE = 0,
                    p.product_type,(
                        SELECT
                            GROUP_CONCAT( bpp.product_type )
                        FROM
                            t_before_project_product bpp
                        WHERE
                            FIND_IN_SET( bpp.id, pi.project_product_id ))) product_type
        FROM
            t_project_item_order pio
                INNER JOIN t_project_item pi ON pio.item_id = pi.id
                INNER JOIN t_project p ON pio.project_id = p.id
         WHERE
            pio.id = #{id}
    </select>

    <update id="updateProjectOfferOrderDataInfo" parameterType="com.alibaba.fastjson.JSONObject">
        update t_project_offer_order_formula
        <set>
            <if test="outKgPrice != null">OUT_KG_PRICE = #{outKgPrice},</if>
            <if test="freightMoney != null">freight_money = #{freightMoney},</if>
            <if test="fillOutPrice != null">FILL_OUT_PRICE = #{fillOutPrice},</if>
            <if test="strategicPrice != null">strategic_price = #{strategicPrice},</if>
            <if test="offerRemark != null">offer_remark = #{offerRemark},</if>
            <if test="goodsRate != null">goods_rate = #{goodsRate},</if>
            <if test="hourCostSinglePrice != null">hour_cost_single_price = #{hourCostSinglePrice},</if>
             update_time = now()
        </set>
        where id = #{id}
    </update>

    <insert id="batchProjectOfferDownloadLogData" parameterType="java.util.Map">
        INSERT INTO  `t_project_offer_order_formula_download` (
            `PROJECT_ORDER_ID`,
            `PROJECT_NAME`,
            `CODE`,
            `FORMULA_TYPE`,
            `LOSS_RATE`,
            `LAB_NO`,
            `ORDER_NUM`,
            `YGL`,
            `YGL_UNIT`,
            `GZL`,
            `GZL_UNIT`,
            `PRODUCT_NAME`,
            `FORMULA_CODE`,
            `FORMULA_ID`,
            `IS_MAIN`,
            `SINGLE_PRICE`,
            `MOQ_PRICE`,
            `ORDER_PRICE`,
            `OUT_KG_PRICE`,
            `out_single_price`,
            `LOSS_PRICE`,
            `PROFIT_RATE`,
            `MOQ_LOSS_CODE`,
            `ORDER_LOSS_CODE`,
            `TYPE`,
            `del_flag`,
            `create_by`,
            `create_time`,
            `remark`,
            `MOQ_PRICE_BC`,
            `ORDER_PRICE_BC`,
            `OUT_SINGLE_PRICE_BC`,
            `LOSS_PRICE_BC`,
            `PROFIT_RATE_BC`,
            `MOQ_LOSS_CODE_BC`,
            `ORDER_LOSS_CODE_BC`,
            `BC_REMARK`,
            `FILL_PRICE`,
            `FILL_OUT_PRICE`,
            `PROFIT_RATE_FILL`,
            `ARCHIVE_CODE`,
            `nums`,
            `productivity`,
            `hour_costs`,
            `fill_remark`,
            `cost_remark`,
            `formula_data_list`,
            `formula_count`,
            `freight_money`,
            `packaging_material_datas`,
            `strategic_price`,
            `offer_remark`,`packaging_material_datas_type`,
            `preview_export_form`,hour_cost_single_price,goods_rate,bc_rate)
        SELECT 	`PROJECT_ORDER_ID`,
                  `PROJECT_NAME`,
                  `CODE`,
                  `FORMULA_TYPE`,
                  `LOSS_RATE`,
                  `LAB_NO`,
                  `ORDER_NUM`,
                  `YGL`,
                  `YGL_UNIT`,
                  `GZL`,
                  `GZL_UNIT`,
                  `PRODUCT_NAME`,
                  `FORMULA_CODE`,
                  `FORMULA_ID`,
                  `IS_MAIN`,
                  `SINGLE_PRICE`,
                  `MOQ_PRICE`,
                  `ORDER_PRICE`,
                  `OUT_KG_PRICE`,
                  `out_single_price`,
                  `LOSS_PRICE`,
                  `PROFIT_RATE`,
                  `MOQ_LOSS_CODE`,
                  `ORDER_LOSS_CODE`,
                  `TYPE`,
                  `del_flag`,
                   #{userName},
                   now(),
                  `remark`,
                  `MOQ_PRICE_BC`,
                  `ORDER_PRICE_BC`,
                  `OUT_SINGLE_PRICE_BC`,
                  `LOSS_PRICE_BC`,
                  `PROFIT_RATE_BC`,
                  `MOQ_LOSS_CODE_BC`,
                  `ORDER_LOSS_CODE_BC`,
                  `BC_REMARK`,
                  `FILL_PRICE`,
                  `FILL_OUT_PRICE`,
                  `PROFIT_RATE_FILL`,
                  `ARCHIVE_CODE`,
                  `nums`,
                  `productivity`,
                  `hour_costs`,
                  `fill_remark`,
                  `cost_remark`,
                  `formula_data_list`,
                  `formula_count`,
                  `freight_money`,
                  `packaging_material_datas`,
                  `strategic_price`,
                  `offer_remark`,`packaging_material_datas_type`,
                   #{previewExportForm},hour_cost_single_price,goods_rate,bc_rate
        FROM t_project_offer_order_formula where PROJECT_ORDER_ID = #{projectOrderId}
    </insert>

    <select id="queryProjectItemOrderApplyCount" parameterType="ProjectItemOrder" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_project_item_order pio
         INNER JOIN t_project_item pi on pio.item_id = pi.ID
        where pio.`STATUS` in ('0','3') and pi.type = 'PROJECT_NRW' AND PIO.PROJECT_ID = #{projectId}
        <if test="itemName!=null and itemName!=''">
            and pi.item_name = #{itemName}
        </if>
        and pi.project_product_id = #{projectProductId}
    </select>

    <delete id="deleteProjectItemOrderPriceData" parameterType="java.lang.Long">
        delete  from t_project_nrw_price where PROJECT_ORDER_ID = #{projectOrderId}
    </delete>

    <insert id="batchInsertProjectItemOrderPriceData">
        insert into t_project_nrw_price(PROJECT_ORDER_ID,LAB_CODE,PRICE,RATE_TYPE,CREATE_TIME,CREATE_BY)
        VALUES
        <foreach collection ="list" item="orderCode" separator =",">
            (#{orderCode.projectOrderId}, #{orderCode.labCode}, #{orderCode.price}, #{orderCode.rateType},now(), #{orderCode.createBy})
        </foreach >
    </insert>

    <select id="queryProjectItemOrderNrwOffer" resultType="ProjectItemOrder">
        SELECT pio.id,PIO.`fields`  from t_project_item_order pio
         INNER JOIN t_project_item pi on pio.item_id = pi.id
        where
              pi.type = 'PROJECT_OFFER'
              and pio.status  = '3'
              and pi.fields->'$.bjlx' = 'PROJECT_NRWBJ'
              and pio.create_time >= '2024-08-03 00:00:00'
        order by pio.id desc
    </select>

    <select id="queryProjectOfferNrwHistoryDataInfo" resultType="com.alibaba.fastjson.JSONObject" parameterType="java.lang.String">
         SELECT id,price,rate_type rateType  from t_project_nrw_price where LAB_CODE = #{labCode} order by id desc limit 1
    </select>

    <select id="queryProjectItemOrderInfo" resultType="ProjectItemOrder">
        SELECT
            pi.ID itemId,
            pi.confirm_code note,
            pi.PROJECT_ID,
            pi.code
        FROM
            t_project_item pi
        where
            PI.ID > 9509
            AND pi.fields->'$.dylx' = '0'
            AND pi.type = 'PROJECT_NRW'
            AND pi.`status` = '3'
            AND PI.IS_DOWNLOAD = 0
        ORDER BY pi.ID ASC
        LIMIT 1000
    </select>

    <update id="updateProjectItemOrderDownload" parameterType="java.lang.Long">
        update t_project_item set  is_download = 1 where id = #{itemId}
    </update>

    <update id="updateProcessProjectNrwOrder" parameterType="TProjectExecution">
        UPDATE t_project_item_order pio1
            INNER JOIN t_project_item_order pio2 ON ( pio1.dyn_code = pio2.dyn_code AND pio2.project_type = 0 )
            SET pio2.`STATUS` = pio1.STATUS,
                pio2.CONFIRM_CODE = pio1.CONFIRM_CODE,
                pio2.sj_time = pio1.sj_time,
                pio2.timely_rate = pio1.timely_rate,
                pio2.sh_status = pio1.sh_status,
                pio2.sh_status1 = pio1.sh_status1,
                pio2.audit_fields = pio1.audit_fields,
                pio2.`FIELDS` = pio1.`FIELDS`
        WHERE
            pio1.`code` = #{itemOrderCode}
    </update>

    <!--
    	pio.id,
        pio.`status`,
        pi.fields->'$.dylx',
        pi.type
    -->
    <select id="selectProjectDyNumsByProjectId" resultType="Integer" >
        SELECT
            count(1)
        FROM
            t_project_item_order pio
            inner join t_project_item pi on pi.id = pio.item_id
        where
            pi.type = 'PROJECT_NRW'
            and pio.`status` in ('0','3')
            and pi.fields->'$.dylx' = '0'
            and pi.del_flag = '0'
            and pio.del_flag = '0'
            and pi.project_id = #{projectId}
    </select>


    <!--获取打样单信息-->
    <select id="queryAutoScheduleSampleOrder" parameterType="java.lang.Long" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            pio.id,
            pio.code,
            p.product_type productType,
            pio.fields,
            c.service_mode serviceMode,
            pio.remark,
            pi.fields itemFields,
            pio.project_id projectId,
            pio.project_product_id projectProductId,
            pi.item_name itemName,
            date_format(pio.`create_time`,'%Y-%m-%d %H:%i:%s') createTime
        from t_project_item_order pio
        INNER JOIN t_project_item pi on pio.item_id = pi.id
        INNER JOIN t_project p on pio.project_id = p.id
        INNER JOIN T_CUSTOMER c on p.customer_id = c.id
        where pio.id = #{id}
    </select>

    <!--获取打样单信息-->
    <select id="queryAutoScheduleSampleOrderSoftware" parameterType="java.lang.Long" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            pio.id,
            pio.code,
            pio.category_text productType,
            pio.fields,
            c.service_mode serviceMode,
            pio.remark,
            pi.fields itemFields,
            pio.project_id projectId,
            pio.project_product_id projectProductId,
            pi.item_name itemName,
            date_format(pio.`create_time`,'%Y-%m-%d %H:%i:%s') createTime
        from t_project_item_order pio
        INNER JOIN t_project_item pi on pio.item_id = pi.id
        INNER JOIN t_project p on pio.project_id = p.id
        INNER JOIN T_CUSTOMER c on p.customer_id = c.id
        where pio.id = #{id}
    </select>

    <!--获取打样单信息-->
    <select id="queryAutoScheduleSampleBeforeOrder" parameterType="java.lang.Long" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            pio.id,
            pio.code,
            bpp.product_type productType,
            pio.fields,
            c.service_mode serviceMode,
            pio.remark,
            pi.fields itemFields,
            pio.project_id projectId,
            pio.project_product_id projectProductId,
            pi.item_name itemName,
            date_format(pio.`create_time`,'%Y-%m-%d %H:%i:%s') createTime
        from t_project_item_order pio
        INNER JOIN t_project_item pi on pio.item_id = pi.id
        INNER JOIN t_before_project_product bpp on pio.PROJECT_PRODUCT_ID = bpp.id
        INNER JOIN T_CUSTOMER c on pio.customer_id = c.id
        where pio.id = #{id}
    </select>

    <select id="queryProjectItemOrderAutoApplyCount" resultType="java.lang.Integer" parameterType="java.lang.Long">
        SELECT count(*) from t_project_item_order where item_id = (
            select item_id from t_project_item_order where id = #{projectItemOrderId}
        ) and add_style = 0 and del_flag = 0
    </select>

    <select id="selectProjectProductDyNums" resultType="Integer" >
        SELECT
            count(1)
        FROM
            t_project_item_order pio
            inner join t_project_item pi on pi.id = pio.item_id
        where
            pi.type = 'PROJECT_NRW'
            and pio.`status` in ('0','3')
            and pi.fields->'$.dylx' = '0'
            and pi.del_flag = '0'
            and pio.del_flag = '0'
            and pi.project_id = #{projectId}
            and pi.item_name = #{guid}
    </select>

    <select id="queryAutoEngineerDataList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT id,code,PROJECT_TYPE projectType,user_id userId from t_project_item_order where is_step = 1 and `STATUS` = 0 and add_style = 0
    </select>

    <!-- 更新项目订单样品报价 -->
    <update id="updateProjectItemOrderSamplePrice">
        UPDATE t_project_item_order
        SET sample_price = #{samplePrice},
            update_time = NOW()
        WHERE id = #{projectItemOrderId}
    </update>

</mapper>
