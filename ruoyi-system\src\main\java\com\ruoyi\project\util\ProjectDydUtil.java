package com.ruoyi.project.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.WagesUtils;
import com.ruoyi.project.mapper.ProjectItemOrderMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Component
public class ProjectDydUtil {


    public static final Long YN_DY_ITEM_ID =  103007L; //宜侬研发 打样 二级打样单ID
    public static final Long YC_DY_ITEM_ID =  103009L; //瀛彩研发 打样 二级打样单ID

    @Resource
    private ProjectItemOrderMapper projectItemOrderMapper;


    public static String getText(String text, Date time){
        String result = "";
        if(StringUtils.isNotNull(text)){
            JSONArray array = JSONArray.parseArray(text);
            List<ProjectDydInfo> dataList = new ArrayList<ProjectDydInfo>();
            for(int i = 0;i<array.size();i++){
                JSONObject obj = array.getJSONObject(i);
                ProjectDydInfo projectDydInfo = new ProjectDydInfo();
                projectDydInfo.setText(obj.getString("text"));
                projectDydInfo.setTime(obj.getDate("time"));
                dataList.add(projectDydInfo);
            }
            if(dataList!=null && dataList.size()>0){
                Collections.sort(dataList);
                for(ProjectDydInfo projectDydInfo : dataList){
                    if(time.after(projectDydInfo.getTime())){
                        result = projectDydInfo.getText();
                        return result;
                    }
                }
            }
        }
        return result;
    }


    /**
     * 获取需要指定几天时间完成
     * @param checkType 判断类型
     * @param dataVal   获取值
     * @return
     */
    public BigDecimal getDays(String checkType,String dataVal,String dylb,Long projectOrderId){
        BigDecimal result = BigDecimal.ZERO;
        if("1".equals(checkType)){  //客户等级
            if(StringUtils.isNotEmpty(dataVal)){
                //获取打样次数
                Integer dyCount = projectItemOrderMapper.queryProjectItemOrderAutoApplyCount(projectOrderId);
                dyCount = StringUtils.convertDefaultValue(dyCount,0);
                if("VVIP".equals(dataVal)){
                    if(dyCount<=1){
                        result = WagesUtils.bigDecimal("3");
                    }else if(dyCount<=2){
                        result = WagesUtils.bigDecimal("2");
                    }else if(dyCount<=3){
                        result = WagesUtils.bigDecimal("1");
                    }else{
                        result = WagesUtils.bigDecimal("1");
                    }
                }else if("AVIP".equals(dataVal)){
                    result = WagesUtils.bigDecimal("3");
                }else if("SVIP".equals(dataVal)){
                    if(dyCount<=1){
                        result = WagesUtils.bigDecimal("5");
                    }else if(dyCount<=2){
                        result = WagesUtils.bigDecimal("3");
                    }else if(dyCount<=3){
                        result = WagesUtils.bigDecimal("2");
                    }else{
                        result = WagesUtils.bigDecimal("1");
                    }
                }else if("BVIP".equals(dataVal)){
                    if(dyCount<=1){
                        result = WagesUtils.bigDecimal("10");
                    }else if(dyCount<=2){
                        result = WagesUtils.bigDecimal("10");
                    }else if(dyCount<=3){
                        result = WagesUtils.bigDecimal("2");
                    }else{
                        result = WagesUtils.bigDecimal("1");
                    }
                }
            }
        }else{
            if(StringUtils.isNotNull(dylb)){
                //打样难度
                if("0".equals(dylb) || "3".equals(dylb) || "6".equals(dylb)){
                    result = BigDecimal.ONE;
                }else if("1".equals(dylb) || "4".equals(dylb) || "7".equals(dylb)){
                    result = BigDecimal.ONE;
                }else if("2".equals(dylb) || "5".equals(dylb) || "8".equals(dylb)){
                    result = WagesUtils.bigDecimal("3");
                }
            }
        }
        return result;
    }
}
